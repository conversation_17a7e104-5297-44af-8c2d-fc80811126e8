import { AWSPolicyGenerator, AuthResponse } from './aws-policy-generator'
import { loadSecrets } from '../utils/lambdaLayerSecretManager.util'
import { initializeApp, cert, App } from 'firebase-admin/app'
import { getAuth } from 'firebase-admin/auth'
import cookie from 'cookie'
import { DataSource } from 'typeorm'
import { FirebaseSystemConfig, RedisConfig } from 'contracts'
import { Redis } from 'ioredis'
import { getSystemConfigKey } from 'utils'
import { mappingUrlWithCompanyId } from '../utils/mapping'
import { AES128MessagePipe } from '../utils/aes128'
import { snakeCase } from 'lodash'

export type AuthorizerErrorMessage = 'Unauthorized' /** 401 */ | 'AccessDenied' /** 403 */
export type FirebaseConfigErrorMessage = 'FirebaseConfigInvalid'
export type RedisErrorMessge = 'RedisInvalid'

const initializeFirebaseConfig = async (redisConfig: RedisConfig, xCompany: any, callback: any) => {
  let firebaseConfig: Record<string, any> = {}

  // Initialize redis
  const cache = new Redis({
    host: redisConfig.host,
    port: Number(redisConfig.port),
    password: redisConfig.password,
    tls: process.env['REDIS_TLS'] === 'true' ? {} : undefined,
    db: process.env.REDIS_DB ? Number(process.env.REDIS_DB) : 0,
  })

  // Mapping url with company
  const companyId = mappingUrlWithCompanyId(xCompany)

  // Formatter cache config key
  const cacheFirebaseConfigKey = getSystemConfigKey(`${companyId}:firebase_backend`)

  // Get cache
  const cacheFirebaseConfig = await cache.get(cacheFirebaseConfigKey)

  // Get aes key
  const aesKey = process.env['AES128_KEY']

  // Get aes salt
  const aesSalt = process.env['AES128_SALT']

  // Prevent key or salt invalid
  if (!aesKey || !aesSalt) {
    throw new Error('AES_KEY and AES_SALT must be defined in env')
  }

  const aes128MessgePipe = new AES128MessagePipe(aesKey, aesSalt)

  // Check cache is valid
  if (cacheFirebaseConfig) {
    // Decrypt firebase config
    const firebaseSystemConfig = JSON.parse(aes128MessgePipe.decrypt(Buffer.from(cacheFirebaseConfig, 'base64')))

    // Mapping firebase config snake case
    for (const [key, value] of Object.entries(firebaseSystemConfig)) {
      firebaseConfig[snakeCase(key)] = value
    }
  } else {
    // Datasource connection to db
    const dataSource = new DataSource({
      type: 'postgres',
      host: process.env['POSTGRES_HOST'],
      port: Number(process.env['POSTGRES_PORT']),
      username: process.env['POSTGRES_USER'],
      password: process.env['POSTGRES_PASSWORD'],
      database: process.env['POSTGRES_DATABASE'],
    })

    // Initialize connection
    const connection = await dataSource.initialize()

    // Query get firebase config from system-config
    const result = await connection.manager.query<Array<{ data: Array<string> }>>(
      'SELECT company_id, data FROM core.system_config WHERE company_id = $1 AND config_key = $2',
      [companyId, 'firebase_backend']
    )

    // Prevent config not found
    if (!result.length) {
      return callback('FirebaseConfigInvalid')
    }

    // Decrypt firebase config
    const decryptFirebaseConfig = aes128MessgePipe.decrypt(Buffer.from(result[0].data[0], 'base64'))

    // Extract data from result
    const firebaseSystemConfig: FirebaseSystemConfig = JSON.parse(decryptFirebaseConfig) as FirebaseSystemConfig

    // Config firebase admin
    firebaseConfig = {
      type: firebaseSystemConfig.type,
      project_id: firebaseSystemConfig.projectId,
      private_key_id: firebaseSystemConfig.privateKeyId,
      private_key: firebaseSystemConfig.privateKey,
      client_email: firebaseSystemConfig.clientEmail,
      client_id: firebaseSystemConfig.clientId,
      auth_uri: firebaseSystemConfig.authUri,
      token_uri: firebaseSystemConfig.tokenUri,
      auth_provider_x509_cert_url: firebaseSystemConfig.authProviderCertUrl,
      client_x509_cert_url: firebaseSystemConfig.clientCertUrl,
      universe_domain: firebaseSystemConfig.universeDomain,
    }

    // Close pg connection
    await dataSource.close()
  }

  // Close redis connection
  await cache.quit()

  // return firebase config
  return firebaseConfig
}

interface HandlerContext {
  [key: string]: string | boolean | number
}

interface HandlerCallback {
  (error: AuthorizerErrorMessage | FirebaseConfigErrorMessage | RedisErrorMessge | null, resp?: AuthResponse): void
}

// eslint-disable-next-line turbo/no-undeclared-env-vars
const isOffline = process.env.STAGE === 'local'

// Declare firebase admin app
let firebaseApp: App

/**
 * An authroization that created for the user authorizer
 */
export const auth = async (event: any, _context: HandlerContext, callback: HandlerCallback) => {
  console.log(event)

  // Bypass http method option
  if (event?.requestContext?.httpMethod === 'OPTIONS') {
    // Generate auth policy
    const authPolicy = AWSPolicyGenerator.generate(Date.now().toString(), 'Allow', event.methodArn)

    // Callback with auth policy
    return callback(null, authPolicy)
  }

  // This code must be wrote at only one place.
  let firebaseConfig: Record<string, any>

  try {
    // Load neccessary environment from secret manager
    if (!isOffline) {
      await loadSecrets()
    }

    if (!cookie.parse(event.headers['cookie'])) return callback('Unauthorized')

    // Get redis config
    const redisHost = process.env['REDIS_HOST']
    const redisPort = process.env['REDIS_PORT']
    const redisPassword = process.env['REDIS_PASSWORD']

    // Check redis config
    if (!redisHost || !redisPort || !redisPassword) {
      return callback('RedisInvalid')
    }

    // Get url path
    const path = event['path']

    // Regex get company id in url string
    const regexResult = /\/media\/company\/(.*?)\//.exec(path)

    // prevent invalid regex
    if (!regexResult) {
      return callback('Unauthorized')
    }

    // Get firebase config from initailize firebase config function
    firebaseConfig = await initializeFirebaseConfig(
      { host: redisHost, port: Number(redisPort), password: redisPassword },
      regexResult[1],
      callback
    )

    // Empty case will never happen cause prevent on custom authorizer configuration
    const cookies = cookie.parse(event.headers['cookie'])

    // Extract token
    const token = cookies[`x-sv-auth-${process.env.STAGE}`]

    // Prevent firebase app already initialize
    if (!firebaseApp) {
      // Assign firebase app variable
      firebaseApp = initializeApp({
        credential: cert(firebaseConfig),
      })
    }

    const payload = await getAuth(firebaseApp).verifyIdToken(token)
    const authPolicy = AWSPolicyGenerator.generate(payload.uid, 'Allow', event.methodArn)
    return callback(null, authPolicy)
  } catch (error: unknown) {
    console.error('Failed to validate authorization', error)
    return callback('Unauthorized')
  }
}
