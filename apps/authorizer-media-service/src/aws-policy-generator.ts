/// AWS Policy Generator creates the proper access to the function.
/// http://docs.aws.amazon.com/apigateway/latest/developerguide/use-custom-authorizer.html
export interface AuthResponse {
  principalId?: string
  policyDocument?: PolicyDocument
  context?: { [key: string]: string | boolean | number }
}

interface PolicyDocument {
  Version: string
  Statement: Statement[]
}

interface Statement {
  Action: string
  Effect: string
  Resource: string
}

export class AWSPolicyGenerator {
  public static generate(principalId: string, effect: string, resource: string, context?: any): AuthResponse {
    const authResponse: AuthResponse = {}

    authResponse.principalId = principalId
    if (effect && resource) {
      const policyDocument: any = {}
      policyDocument.Version = '2012-10-17'
      policyDocument.Statement = []
      const statementOne: any = {}
      statementOne.Action = 'execute-api:Invoke'
      statementOne.Effect = effect
      statementOne.Resource = resource
      policyDocument.Statement[0] = statementOne
      authResponse.policyDocument = policyDocument
    }

    if (context) {
      authResponse.context = context
    }

    return authResponse
  }
}
