// ** Mapping url in x-company request headers with company id **
const urlWithCompanyId = {
  WW: 'WW',
  'ww-remobile-dev.axonstech.com': 'WW',
  'ww-remobile-uat.axonstech.com': 'WW',
  'ww-remobile.axonstech.com': 'WW',
  'ww-remobile-cms-dev.axonstech.com': 'WW',
  'ww-remobile-cms-uat.axonstech.com': 'WW',
  'ww-remobile-cms.axonstech.com': 'WW',
}

// ** Mapping url with key from x-company request headers with company id **
export const mappingUrlWithCompanyId = (url: string): string | null => {
  // Get company id
  const companyId = urlWithCompanyId[url as keyof typeof urlWithCompanyId]

  // Prevent url invalid
  if (!companyId) {
    return null
  }

  // Return company id
  return companyId
}
