import { IEncryptDecryptMessagePipe } from 'contracts'
import { <PERSON>ipherGC<PERSON>, Decipher, createDecipheriv, createCipheriv, pbkdf2Sync, randomBytes } from 'crypto'

const NONCE_SIZE = 12
const GCM_TAG_SIZE = 16
const KEY_LENGTH_SPEC = 128 / 8
const ALGORITHM = 'aes-128-gcm'
const DERIVED_KEY_SHA_SPEC = 'sha512'

/**
 * A class that create an easy message encode/decode mechanic for a generic message.
 *
 * Algorithm used.
 * - aes-128-gcm
 * - salt is a derived key using pbkddf2 (sha512)
 * - iv is attached to the payload with fixed size as NONCE
 */
export class AES128MessagePipe implements IEncryptDecryptMessagePipe {
  private getCipher: (_iv: Buffer) => CipherGCM
  private getDecipher: (_iv: Buffer, _authTag: Buffer) => Decipher

  constructor(key: string, salt: string) {
    const derivedKey = pbkdf2Sync(key, salt, 1000, KEY_LENGTH_SPEC, DERIVED_KEY_SHA_SPEC)
    this.getDecipher = (iv: Buffer, authTag: Buffer) => {
      return createDecipheriv(ALGORITHM, derivedKey, iv).setAuthTag(authTag)
    }
    this.getCipher = (iv: Buffer) => {
      return createCipheriv(ALGORITHM, derivedKey, iv)
    }
  }

  public encrypt(payload: string): Buffer {
    const nonce = randomBytes(NONCE_SIZE)
    const cipher = this.getCipher(nonce)
    const encrypted = Buffer.concat([nonce, cipher.update(payload, 'utf8'), cipher.final(), cipher.getAuthTag()])
    return encrypted
  }

  public decrypt(buffer: Buffer): string {
    const nonce = buffer.subarray(0, NONCE_SIZE)
    const cipherText = buffer.subarray(NONCE_SIZE, buffer.length - GCM_TAG_SIZE)
    const tag = buffer.subarray(buffer.length - GCM_TAG_SIZE)
    const decipher = this.getDecipher(nonce, tag)
    let out = decipher.update(cipherText, undefined, 'utf8')
    out += decipher.final('utf8')
    return out
  }
}
