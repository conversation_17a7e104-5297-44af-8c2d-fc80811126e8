import fetch from 'node-fetch'

interface Credential {
  AES128_KEY: string
  AES128_SALT: string
  GENERATE_CONTRACT_SECRET_KEY: string
  POSTGRES_HOST: string
  POSTGRES_PORT: string
  POSTGRES_USER: string
  POSTGRES_PASSWORD: string
  POSTGRES_DATABASE: string
  POSTGRES_SCHEMA: string
  REDIS_HOST: string
  REDIS_PORT: string
  REDIS_PASSWORD: string
  REDIS_DB: string
  REDIS_TLS: string
  REDIS_TTL: string
}

interface SecretManagerResponse {
  ARN: string
  CreatedDate: string
  Name: string
  SecretBinary: any
  SecretString: string
  VersionId: string
  VersionStages: string[]
  ResultMetadata: string
}

// https://docs.aws.amazon.com/secretsmanager/latest/userguide/retrieving-secrets_lambda.html
export const loadSecrets = async () => {
  try {
    if (!process.env.SM_APPLICATION_ENV_ARN) {
      throw new Error('process.env.SM_APPLICATION_ENV_ARN is not set.')
    }
    // The url used for connecting is static with all those Lambda
    const headers = {
      'Content-Type': 'application/json',
      'X-Aws-Parameters-Secrets-Token': process.env.AWS_SESSION_TOKEN ?? '',
    }
    let ready = false
    let json: SecretManagerResponse = {} as any
    do {
      const res = await fetch(
        `http://localhost:2773/secretsmanager/get?secretId=${process.env.SM_APPLICATION_ENV_ARN}`,
        {
          headers,
        }
      )
      ready = res.ok
      if (!ready) {
        console.error('Layer was not ready', res)
      } else {
        json = await res.json()
      }
    } while (!ready)
    const cred = JSON.parse(json.SecretString) as Credential
    Object.entries(cred).forEach(([key, value]) => {
      process.env[key] = value
    })
  } catch (e) {
    console.error('Error loading secrets', e)
    throw e
  }
}
