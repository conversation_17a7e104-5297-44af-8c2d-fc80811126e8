{"name": "authorizer-media-service", "version": "0.0.1", "scripts": {"dev": "export STAGE=local && export SLS_DEBUG=* && sls offline --stage local --reloadHandler", "package": "sls package", "test:unit": "jest --coverage --detectOpenHandles --config=jest.config.js __tests__/unit", "test:integration": "jest --coverage --detectOpenHandles --config=jest.config.js __tests__/integration", "azuredevops:sls:clean:package": "rm -rf .serverless", "azuredevops:sls:deploy": "export SLS_DEBUG=* && serverless deploy --stage $STAGE --verbose", "azuredevops:sls:package": "export SLS_DEBUG=* && serverless package --stage $STAGE --verbose"}, "dependencies": {"contracts": "*", "utils": "*", "cookie": "^0.6.0", "firebase-admin": "^11.11.0", "ioredis": "^5.3.2", "pg": "^8.11.3", "typeorm": "^0.3.17", "lodash": "^4.17.21"}, "devDependencies": {"@types/aws-lambda": "^8.10.124", "@types/cookie": "^0.6.0", "@types/node-fetch": "^2.6.9", "aws-lambda": "^1.0.7"}}