import type { AWS } from '@serverless/typescript'
type DeepPartial<T> = T extends object
  ? {
      [P in keyof T]?: DeepPartial<T[P]>
    }
  : T
const isOfflineMode = process.env.STAGE === 'local'
const authorizerName = 'nemo-services-authorizer-media'
const lambdaSecretManagerLayerArn =
  'arn:aws:lambda:ap-southeast-1:044395824272:layer:AWS-Parameters-and-Secrets-Lambda-Extension:11'
const serverlessConfiguration: DeepPartial<AWS> = {
  service: authorizerName,
  custom: {
    'serverless-offline': {
      lambdaPort: 11000,
      httpPort: 11001,
      noPrependStageInUrl: true,
      ignoreJWTSignature: true,
      resourceRoutes: true,
    },
    esbuild: {
      bundle: true,
      minify: true,
      sourcemap: true,
      exclude: ['aws-sdk'],
      watch: {
        pattern: ['src/**/*.ts'],
        ignore: ['temp/**/*'],
      },
      platform: 'node',
      concurrency: 10,
    },
    env: {
      dev: {
        workload_type: 'SIT/DEV',
      },
      uat: {
        workload_type: 'UAT',
      },
      prd: {
        workload_type: 'PRD',
      },
    },
    vpcDiscovery: isOfflineMode
      ? {}
      : {
          vpcName: '${ssm:/nemo-mobile/${self:provider.stage}/vpc_name}',
          subnets: [
            {
              tagKey: 'subnet-type',
              tagValues: ['private'],
            },
          ],
          securityGroups: [
            {
              tagKey: 'Name',
              tagValues: ['${ssm:/nemo-mobile/${self:provider.stage}/lambda_security_group_name}'],
            },
          ],
        },
  },
  plugins: [
    'serverless-esbuild', // For build + packaging
    'serverless-offline', // For offline development
    'serverless-plugin-resource-tagging', // for resources tagging https://www.serverless.com/plugins/serverless-plugin-resource-tagging
    'serverless-vpc-discovery', // For easily config the vpc on serverless
  ],
  functions: {
    authorizer: {
      name: [authorizerName, '${self:provider.stage}'].join('-'),
      handler: 'src/handler.auth',
      layers: [lambdaSecretManagerLayerArn],
    },
  },
  provider: {
    name: 'aws',
    runtime: 'nodejs18.x',
    region: 'ap-southeast-1',
    stage: '${opt:stage}',
    timeout: 30,
    logRetentionInDays: 30,
    iam: {
      role: {
        statements: isOfflineMode
          ? []
          : [
              {
                Effect: 'Allow',
                Action: ['secretsmanager:GetSecretValue'],
                Resource: ['${ssm:/nemo-mobile/${self:provider.stage}/sm_applications_env_arn}'],
              },
            ],
      },
    },
    environment: isOfflineMode
      ? {
          // Offline custom authorizer ENV
          STAGE: process.env.STAGE,
          POSTGRES_HOST: process.env.POSTGRES_HOST,
          POSTGRES_PORT: process.env.PORT,
          POSTGRES_USER: process.env.POSTGRES_USER,
          POSTGRES_PASSWORD: process.env.POSTGRES_PASSWORD,
          POSTGRES_DATABASE: process.env.POSTGRES_DATABASE,
          POSTGRES_SCHEMA: process.env.POSTGRES_SCHEMA,
          REDIS_HOST: process.env.REDIS_HOST,
          REDIS_PORT: process.env.REDIS_PORT,
          REDIS_PASSWORD: process.env.REDIS_PASSWORD,
          REDIS_DB: process.env.REDIS_DB,
          AES128_KEY: process.env.AES128_KEY,
          AES128_SALT: process.env.AES128_SALT,
        }
      : {
          STAGE: '${self:provider.stage}',
          SM_APPLICATION_ENV_ARN: '${ssm:/nemo-mobile/${self:provider.stage}/sm_applications_env_arn}',
        },
    tracing: {
      lambda: true,
    },
    stackTags: isOfflineMode
      ? {}
      : {
          Project: 'nemo',
          'workload-type': '${self:custom.env.${self:provider.stage}.workload_type}',
        },
  },
  resources: {
    Resources: isOfflineMode
      ? {}
      : {
          Authorizer: {
            Type: 'AWS::ApiGateway::Authorizer',
            Properties: {
              RestApiId: '${ssm:/nemo-mobile/${self:provider.stage}/rest_api_s3_proxy_id}',
              AuthorizerResultTtlInSeconds: 0,
              Type: 'REQUEST',
              AuthorizerUri: {
                'Fn::Join': [
                  '',
                  [
                    'arn:aws:apigateway:',
                    '${self:provider.region}',
                    ':lambda:path/2015-03-31/functions/',
                    { 'Fn::GetAtt': 'AuthorizerLambdaFunction.Arn' },
                    '/invocations',
                  ],
                ],
              },
              IdentitySource: 'method.request.header.Cookie',
              Name: authorizerName,
            },
          },
          AuthorizerLambdaPermission: {
            Type: 'AWS::Lambda::Permission',
            Properties: {
              FunctionName: {
                'Fn::GetAtt': 'AuthorizerLambdaFunction.Arn',
              },
              Action: 'lambda:InvokeFunction',
              Principal: {
                'Fn::Join': ['', ['apigateway.', { Ref: 'AWS::URLSuffix' }]],
              },
            },
          },
          ProxyResource: {
            Type: 'AWS::ApiGateway::Resource',
            Properties: {
              ParentId: '${ssm:/nemo-mobile/${self:provider.stage}/rest_api_s3_proxy_root_resource_id}',
              PathPart: '{proxy+}',
              RestApiId: '${ssm:/nemo-mobile/${self:provider.stage}/rest_api_s3_proxy_id}',
            },
          },
          S3ProxyMethod: {
            Type: 'AWS::ApiGateway::Method',
            Properties: {
              HttpMethod: 'ANY',
              // ResourceId: { Ref: 'ItemResource' },
              ResourceId: { Ref: 'ProxyResource' },
              RestApiId: '${ssm:/nemo-mobile/${self:provider.stage}/rest_api_s3_proxy_id}',
              ApiKeyRequired: false,
              AuthorizationType: 'CUSTOM',
              AuthorizerId: {
                Ref: 'Authorizer',
              },
              RequestParameters: {
                'method.request.path.proxy': false,
                'method.request.header.x-amz-acl': false,
              },
              Integration: {
                Credentials: '${ssm:/nemo-mobile/${self:provider.stage}/s3_proxy_role_arn}',
                IntegrationHttpMethod: 'ANY',
                Type: 'AWS',
                Uri: 'arn:aws:apigateway:${self:provider.region}:s3:path/${ssm:/nemo-mobile/${self:provider.stage}/frontend_s3_media_bucket_name}/{proxy}',
                RequestParameters: {
                  'integration.request.path.proxy': 'method.request.path.proxy',
                  'integration.request.header.x-amz-acl': 'method.request.header.x-amz-acl',
                },
                IntegrationResponses: [
                  {
                    StatusCode: 200,
                    ResponseParameters: {
                      'method.response.header.Content-Type': 'integration.response.header.Content-Type',
                      'method.response.header.Content-Disposition': 'integration.response.header.Content-Disposition',
                      'method.response.header.Content-Length': 'integration.response.header.Content-Length',
                      'method.response.header.Timestamp': 'integration.response.header.Date',
                      'method.response.header.Access-Control-Allow-Origin': "'*'",
                      'method.response.header.Access-Control-Allow-Methods': "'*'",
                      'method.response.header.Access-Control-Allow-Headers': "'*'",
                    },
                  },
                  {
                    StatusCode: 400,
                    SelectionPattern: '4d{2}',
                  },
                  {
                    StatusCode: 500,
                    SelectionPattern: '5d{2}',
                  },
                ],
              },
              MethodResponses: [
                {
                  StatusCode: 200,
                  ResponseParameters: {
                    'method.response.header.Content-Type': 'integration.response.header.Content-Type',
                    'method.response.header.Content-Length': 'integration.response.header.Content-Length',
                    'method.response.header.Content-Disposition': 'integration.response.header.Content-Disposition',
                    'method.response.header.Timestamp': 'integration.response.header.Timestamp',
                    'method.response.header.Access-Control-Allow-Origin': true,
                    'method.response.header.Access-Control-Allow-Methods': true,
                    'method.response.header.Access-Control-Allow-Headers': true,
                  },
                },
                {
                  StatusCode: 400,
                },
                {
                  StatusCode: 500,
                },
              ],
            },
          },
          S3ProxyOptionMethod: {
            Type: 'AWS::ApiGateway::Method',
            Properties: {
              HttpMethod: 'OPTIONS',
              ResourceId: { Ref: 'ProxyResource' },
              RestApiId: '${ssm:/nemo-mobile/${self:provider.stage}/rest_api_s3_proxy_id}',
              ApiKeyRequired: false,
              AuthorizationType: 'NONE',
              RequestParameters: {
                'method.request.path.proxy': false,
                'method.request.header.x-amz-acl': false,
              },
              Integration: {
                Credentials: '${ssm:/nemo-mobile/${self:provider.stage}/s3_proxy_role_arn}',
                IntegrationHttpMethod: 'ANY',
                Type: 'AWS',
                Uri: 'arn:aws:apigateway:${self:provider.region}:s3:path/${ssm:/nemo-mobile/${self:provider.stage}/frontend_s3_media_bucket_name}/{proxy}',
                RequestParameters: {
                  'integration.request.path.proxy': 'method.request.path.proxy',
                  'integration.request.header.x-amz-acl': 'method.request.header.x-amz-acl',
                },
                IntegrationResponses: [
                  {
                    StatusCode: 200,
                    ResponseParameters: {
                      'method.response.header.Content-Type': 'integration.response.header.Content-Type',
                      'method.response.header.Content-Disposition': 'integration.response.header.Content-Disposition',
                      'method.response.header.Content-Length': 'integration.response.header.Content-Length',
                      'method.response.header.Timestamp': 'integration.response.header.Date',
                      'method.response.header.Access-Control-Allow-Origin': "'*'",
                      'method.response.header.Access-Control-Allow-Methods': "'*'",
                      'method.response.header.Access-Control-Allow-Headers': "'*'",
                    },
                  },
                  {
                    StatusCode: 400,
                    SelectionPattern: '4d{2}',
                  },
                  {
                    StatusCode: 500,
                    SelectionPattern: '5d{2}',
                  },
                ],
              },
              MethodResponses: [
                {
                  StatusCode: 200,
                  ResponseParameters: {
                    'method.response.header.Content-Type': 'integration.response.header.Content-Type',
                    'method.response.header.Content-Length': 'integration.response.header.Content-Length',
                    'method.response.header.Content-Disposition': 'integration.response.header.Content-Disposition',
                    'method.response.header.Timestamp': 'integration.response.header.Timestamp',
                    'method.response.header.Access-Control-Allow-Origin': true,
                    'method.response.header.Access-Control-Allow-Methods': true,
                    'method.response.header.Access-Control-Allow-Headers': true,
                  },
                },
                {
                  StatusCode: 400,
                },
                {
                  StatusCode: 500,
                },
              ],
            },
          },
        },
  },
}

module.exports = serverlessConfiguration
