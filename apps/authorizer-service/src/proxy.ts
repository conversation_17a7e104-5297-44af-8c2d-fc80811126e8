import { APIGatewayProxyResultV2 } from 'aws-lambda'
import * as http from 'http'
import { isObject } from 'lodash'

export const handler = async (event: any): Promise<APIGatewayProxyResultV2> => {
  const { method, path } = event.requestContext.http
  const { axpPrincipalId = '', type = 'no_auth', lambda } = event.requestContext.authorizer
  const proxyEndpoint = new URL(process.env['PROXY_ENDPOINT'] ?? '')
  if (!proxyEndpoint) throw new Error('PROXY_ENDPOINT environment variable is not set')

  // Initial transform headers object
  const transformedHeaders: Record<string, any> = {}

  // Loop through headers and transform them to lowercase
  Object.entries(event.headers).forEach(([key, value]) => {
    // Check nested object
    if (isObject(value)) {
      // Initial transform sub headers object
      const transformSubHeaders: Record<string, any> = {}
      // Loop through sub headers and transform them to lowercase
      Object.entries(value).forEach(([subKey, subValue]) => {
        transformSubHeaders[subKey.toLowerCase()] = subValue
      })

      // Add sub headers to transformed headers
      transformedHeaders[key] = transformSubHeaders
    } else {
      // Add headers to transformed headers
      transformedHeaders[key.toLowerCase()] = value
    }
  })

  const options: http.RequestOptions = {
    hostname: proxyEndpoint.hostname,
    port: proxyEndpoint.port,
    path: `${path}?${event.rawQueryString}`,
    method: method,
    headers: {
      ...transformedHeaders,
      ...lambda,
      'authorizer-axp-principal-id': axpPrincipalId,
      'authorizer-type': type,
    },
  }

  return new Promise<APIGatewayProxyResultV2>((resolve, reject) => {
    const req = http.request(options, res => {
      let responseBody = ''

      res.on('data', chunk => {
        responseBody += chunk
      })

      res.on('end', () => {
        resolve({
          statusCode: res.statusCode ?? 200,
          headers: { ...res.headers } as any,
          body: responseBody,
        })
      })
    })

    req.on('error', error => {
      reject(error)
    })

    if (event.body) {
      req.write(event.body)
    }

    req.end()
  })
}

export default handler
