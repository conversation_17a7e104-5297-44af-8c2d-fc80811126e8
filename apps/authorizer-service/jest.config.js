module.exports = {
  displayName: "Auth Srv",
  testEnvironment: "node",
  // setupFiles: [
  //   "<rootDir>/../../jest-common.envs.js",
  // ],
  transform: {
    "^.+\\.tsx?$": "ts-jest",
  },
  collectCoverageFrom: [
    "src/*.(t|j)s",
  ],
  coveragePathIgnorePatterns: [
    '__tests__'
  ],
  coverageReporters: [
    "lcov",
    "text-summary",
    "text",
    "html",
    "cobertura"
  ],
  coverageDirectory: "./coverage",
  collectCoverage: true,
  moduleFileExtensions: [
    "ts",
    "tsx",
    "js",
    "json"
  ],
}
