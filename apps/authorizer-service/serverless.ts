import type { AWS } from '@serverless/typescript'
type DeepPartial<T> = T extends object
  ? {
      [P in keyof T]?: DeepPartial<T[P]>
    }
  : T
const isOfflineMode = process.env.STAGE === 'local'
const authorizerName = 'nemo-services-authorizer'
const lambdaSecretManagerLayerArn =
  'arn:aws:lambda:ap-southeast-1:044395824272:layer:AWS-Parameters-and-Secrets-Lambda-Extension:11'
const proxyConfig = isOfflineMode
  ? {
      private: {
        handler: 'src/proxy.handler',
        environment: {
          PROXY_ENDPOINT: 'http://127.0.0.1:3001',
        },
        events: [
          {
            httpApi: {
              path: '/{proxy+}',
              authorizer: {
                name: 'authorizer',
                type: 'request',
              },
              method: 'ANY',
            },
          },
        ],
      },
      public: {
        handler: 'src/proxy.handler',
        environment: {
          PROXY_ENDPOINT: 'http://127.0.0.1:3001',
        },
        events: [
          {
            httpApi: {
              path: '/api/core/v1/common/{proxy+}',
              method: 'ANY',
            },
          },
        ],
      },
    }
  : {}

const serverlessConfiguration: DeepPartial<AWS> = {
  service: authorizerName,
  custom: {
    'serverless-offline': {
      lambdaPort: 10000,
      httpPort: 10001,
      noPrependStageInUrl: true,
      ignoreJWTSignature: true,
      resourceRoutes: true,
    },
    esbuild: {
      bundle: true,
      minify: true,
      sourcemap: true,
      exclude: ['aws-sdk'],
      watch: {
        pattern: ['src/**/*.ts'],
        ignore: ['temp/**/*'],
      },
      platform: 'node',
      concurrency: 10,
    },
    env: {
      dev: {
        workload_type: 'SIT/DEV',
      },
      uat: {
        workload_type: 'UAT',
      },
      prd: {
        workload_type: 'PRD',
      },
    },
    vpcDiscovery: isOfflineMode
      ? {}
      : {
          vpcName: '${ssm:/nemo-mobile/${self:provider.stage}/vpc_name}',
          subnets: [
            {
              tagKey: 'subnet-type',
              tagValues: ['private'],
            },
          ],
          securityGroups: [
            {
              tagKey: 'Name',
              tagValues: ['${ssm:/nemo-mobile/${self:provider.stage}/lambda_security_group_name}'],
            },
          ],
        },
  },
  plugins: [
    'serverless-esbuild', // For build + packaging
    'serverless-offline', // For offline development
    'serverless-plugin-resource-tagging', // for resources tagging https://www.serverless.com/plugins/serverless-plugin-resource-tagging
    'serverless-vpc-discovery', // For easily config the vpc on serverless
  ],
  functions: {
    authorizer: {
      name: [authorizerName, '${self:provider.stage}'].join('-'),
      handler: 'src/handler.auth',
      layers: [lambdaSecretManagerLayerArn],
    },
    ...proxyConfig,
  },
  provider: {
    name: 'aws',
    runtime: 'nodejs18.x',
    region: 'ap-southeast-1',
    stage: '${opt:stage}',
    timeout: 30,
    logRetentionInDays: 30,
    iam: {
      role: {
        statements: isOfflineMode
          ? []
          : [
              {
                Effect: 'Allow',
                Action: ['secretsmanager:GetSecretValue'],
                Resource: ['${ssm:/nemo-mobile/${self:provider.stage}/sm_applications_env_arn}'],
              },
            ],
      },
    },
    environment: isOfflineMode
      ? {
          // Offline custom authorizer ENV
          STAGE: process.env.STAGE,
          POSTGRES_HOST: process.env.POSTGRES_HOST,
          POSTGRES_PORT: process.env.PORT,
          POSTGRES_USER: process.env.POSTGRES_USER,
          POSTGRES_PASSWORD: process.env.POSTGRES_PASSWORD,
          POSTGRES_DATABASE: process.env.POSTGRES_DATABASE,
          POSTGRES_SCHEMA: process.env.POSTGRES_SCHEMA,
          REDIS_HOST: process.env.REDIS_HOST,
          REDIS_PORT: process.env.REDIS_PORT,
          REDIS_PASSWORD: process.env.REDIS_PASSWORD,
          REDIS_DB: process.env.REDIS_DB,
          AES128_KEY: process.env.AES128_KEY,
          AES128_SALT: process.env.AES128_SALT,
        }
      : {
          STAGE: '${self:provider.stage}',
          SM_APPLICATION_ENV_ARN: '${ssm:/nemo-mobile/${self:provider.stage}/sm_applications_env_arn}',
        },
    tracing: {
      // Optional, can be true (true equals 'Active'), 'Active' or 'PassThrough'
      lambda: true,
    },
    stackTags: isOfflineMode
      ? {}
      : {
          Project: 'nemo',
          'workload-type': '${self:custom.env.${self:provider.stage}.workload_type}',
        },
    httpApi: {
      authorizers: {
        customAuthorizer: {
          type: 'request',
          functionName: 'authorizer',
          payloadVersion: '2.0',
          identitySource: ['$request.header.Authorization'],
        },
      },
    },
  },
  resources: {
    Resources: isOfflineMode
      ? {}
      : {
          Authorizer: {
            Type: 'AWS::ApiGatewayV2::Authorizer',
            Properties: {
              ApiId: '${ssm:/nemo-mobile/${self:provider.stage}/service_api_id}',
              // version = '1.0' and '2.0' are absolutely difference
              // https://docs.aws.amazon.com/apigateway/latest/developerguide/http-api-lambda-authorizer.html
              AuthorizerPayloadFormatVersion: '2.0',
              AuthorizerResultTtlInSeconds: 0,
              AuthorizerType: 'REQUEST',
              AuthorizerUri: {
                'Fn::Join': [
                  '',
                  [
                    'arn:aws:apigateway:',
                    '${self:provider.region}',
                    ':lambda:path/2015-03-31/functions/',
                    { 'Fn::GetAtt': 'AuthorizerLambdaFunction.Arn' },
                    '/invocations',
                  ],
                ],
              },
              IdentitySource: ['$request.header.Authorization'],
              Name: authorizerName,
            },
          },
          AuthorizerLambdaPermission: {
            Type: 'AWS::Lambda::Permission',
            Properties: {
              FunctionName: {
                'Fn::GetAtt': 'AuthorizerLambdaFunction.Arn',
              },
              Action: 'lambda:InvokeFunction',
              Principal: {
                'Fn::Join': ['', ['apigateway.', { Ref: 'AWS::URLSuffix' }]],
              },
            },
          },
          AuthRoute: {
            Type: 'AWS::ApiGatewayV2::Route',
            Properties: {
              ApiId: '${ssm:/nemo-mobile/${self:provider.stage}/service_api_id}',
              AuthorizationType: 'CUSTOM',
              AuthorizerId: {
                Ref: 'Authorizer',
              },
              RouteKey: 'ANY /core/v1/{proxy+}',
              Target: 'integrations/${ssm:/nemo-mobile/${self:provider.stage}/service_api_integration_id}',
            },
          },
          CorsProxyRoute: {
            Type: 'AWS::ApiGatewayV2::Route',
            Properties: {
              ApiId: '${ssm:/nemo-mobile/${self:provider.stage}/service_api_id}',
              RouteKey: 'OPTIONS /core/v1/{proxy+}',
              Target: 'integrations/${ssm:/nemo-mobile/${self:provider.stage}/service_api_integration_id}',
            },
          },
          PublicRoute: {
            Type: 'AWS::ApiGatewayV2::Route',
            Properties: {
              ApiId: '${ssm:/nemo-mobile/${self:provider.stage}/service_api_id}',
              RouteKey: 'ANY /core/v1/common/{proxy+}',
              Target: 'integrations/${ssm:/nemo-mobile/${self:provider.stage}/service_api_integration_id}',
            },
          },
          CorsPublicRoute: {
            Type: 'AWS::ApiGatewayV2::Route',
            Properties: {
              ApiId: '${ssm:/nemo-mobile/${self:provider.stage}/service_api_id}',
              RouteKey: 'OPTIONS /core/v1/common/{proxy+}',
              Target: 'integrations/${ssm:/nemo-mobile/${self:provider.stage}/service_api_integration_id}',
            },
          },
        },
  },
}

module.exports = serverlessConfiguration
