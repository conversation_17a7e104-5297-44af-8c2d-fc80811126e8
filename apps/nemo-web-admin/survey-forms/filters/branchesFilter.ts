import { BranchType } from '@/config/branches'

export const branchesFilter = () => {
  return {
    logoPosition: 'right',
    pages: [
      {
        name: 'page1',
        elements: [
          {
            type: 'text',
            name: 'label',
            title: { en: 'ชื่อ/รหัสสาขา', th: 'ชื่อ/รหัสสาขา' },
            startWithNewLine: false,
            hideNumber: true,
            placeholder: { en: 'กรุณากรอกชื่อ/รหัสสาขา', th: 'กรุณากรอกชื่อ/รหัสสาขา' },
          },
          {
            type: 'dropdown',
            name: 'branchType',
            hideNumber: true,
            startWithNewLine: false,
            title: { en: 'ประเภท', th: 'ประเภท' },
            choices: [
              { value: BranchType.SHOP, text: BranchType.SHOP },
              { value: BranchType.DEALER, text: BranchType.DEALER },
              { value: BranchType.WAREHOUSE, text: BranchType.WAREHOUSE },
              { value: BranchType.PARTNER, text: BranchType.PARTNER },
            ],
            defaultValue: 'none',
            showNoneItem: true,
            noneText: { en: 'All', th: 'ทั้งหมด' },
            placeholder: { en: 'All', th: 'ทั้งหมด' },
          },
        ],
      },
    ],
    questionDescriptionLocation: 'underInput',
    showQuestionNumbers: 'off',
    showCompletedPage: false,
    showNavigationButtons: false,
    textUpdateMode: 'onTyping',
  }
}
