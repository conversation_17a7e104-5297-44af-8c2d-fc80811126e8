import { GetModelMastersTableResponse } from 'contracts'

import { DataTableField } from 'ui'
import { Tooltip } from '@material-tailwind/react'

export const modelMastersListFields = (t: (text: string) => string): DataTableField[] => [
  {
    name: 'modelKey',
    label: t('model-master-table.id'),
    width: '280px',
    textAlignment: 'text-left justify-start',
    formatter: (item: GetModelMastersTableResponse) => {
      return (
        <Tooltip content={item?.modelKey} placement="top-start">
          {item?.modelKey}
        </Tooltip>
      )
    },
  },
  {
    name: 'matCode',
    label: t('model-master-table.mat'),
    width: '280px',
    textAlignment: 'text-left justify-start',
  },
  {
    name: 'brand',
    label: t('jobs-management.brand'),
    width: '150px',
    textAlignment: 'text-left justify-start',
  },
  {
    name: 'model',
    label: t('jobs-management.model'),
    width: '200px',
    textAlignment: 'text-left justify-start',
    formatter: (item: GetModelMastersTableResponse) => {
      return (
        <Tooltip content={item?.model} placement="top-start">
          {item?.model}
        </Tooltip>
      )
    },
  },
  {
    name: 'rom',
    label: t('jobs-management.rom'),
    width: '100px',
    disableSort: true,
    textAlignment: 'text-center justify-center',
    headerAlignment: 'text-left justify-start',
  },
  {
    name: 'referencePrice',
    label: t('model-master-table.reference-price'),
    width: '130px',
    disableSort: true,
    format: 'price',
    textAlignment: 'text-right justify-end',
    headerAlignment: 'text-left justify-start',
  },
  {
    name: 'modelYear',
    label: t('model-master-table.model-year'),
    width: '80px',
    disableSort: true,
    textAlignment: 'text-left justify-start',
  },
  {
    name: 'purchasedRatio',
    label: t('model-master-table.purchased-ratio'),
    width: '100px',
    disableSort: true,
    format: 'decimal',
    textAlignment: 'text-center justify-center',
  },
  {
    name: 'ownerName',
    label: t('model-master-table.owner-name'),
    width: '170px',
    sticky: true,
    stickyDirection: 'right',
    textAlignment: 'text-left justify-start',
    formatter: (item: GetModelMastersTableResponse) => {
      return (
        <Tooltip content={item?.ownerName} placement="top-start">
          {item?.ownerName}
        </Tooltip>
      )
    },
  },
  {
    name: 'gradeA',
    label: t('model-master-table.grade-a'),
    width: '130px',
    sticky: true,
    stickyDirection: 'right',
    disableSort: true,
    format: 'price',
    cls: 'text-d6-semi-bold text-primary-500',
    textAlignment: 'text-right justify-end',
    headerAlignment: 'text-center justify-center',
  },
  {
    name: 'gradeB',
    label: t('model-master-table.grade-b'),
    width: '130px',
    sticky: true,
    stickyDirection: 'right',
    disableSort: true,
    format: 'price',
    cls: 'text-d6-semi-bold text-primary-500',
    textAlignment: 'text-right justify-end',
    headerAlignment: 'text-center justify-center',
  },
  {
    name: 'gradeC',
    label: t('model-master-table.grade-c'),
    width: '130px',
    sticky: true,
    stickyDirection: 'right',
    disableSort: true,
    format: 'price',
    cls: 'text-d6-semi-bold text-primary-500',
    textAlignment: 'text-right justify-end',
    headerAlignment: 'text-center justify-center',
  },
  {
    name: 'gradeD',
    label: t('model-master-table.grade-d'),
    width: '130px',
    sticky: true,
    stickyDirection: 'right',
    disableSort: true,
    format: 'price',
    cls: 'text-d6-semi-bold text-primary-500',
    textAlignment: 'text-right justify-end',
    headerAlignment: 'text-center justify-center',
  },
]
