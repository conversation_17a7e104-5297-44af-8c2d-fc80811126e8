import { autoserializeAs } from 'cerialize'
import {
  ModelMasterGradeDetailResponse,
  ModelMasterResponse,
  ModelMastersResponse,
  GetModelMastersTableResponse,
} from 'contracts'

export class ModelMasterGrade implements ModelMasterGradeDetailResponse {
  @autoserializeAs('grade')
  grade: string = ''
  @autoserializeAs('purchasePrice')
  purchasePrice: string = ''
  @autoserializeAs('lastPurchasedPrice')
  lastPurchasedPrice: string = ''
  @autoserializeAs('lastPurchasedOn')
  lastPurchasedOn: string = ''
}

export class Model implements ModelMasterResponse {
  @autoserializeAs('companyId')
  companyId: string = ''
  @autoserializeAs('modelKey')
  modelKey: string = ''
  @autoserializeAs('modelIdentifiers')
  modelIdentifiers: any
  @autoserializeAs('templateId')
  templateId: string = ''
  @autoserializeAs(ModelMasterGrade, 'modelMasterGrades')
  modelMasterGrades: ModelMasterGrade[] = []
  @autoserializeAs('lastPurchasedPrice')
  lastPurchasedPrice?: number
  @autoserializeAs('createdBy')
  createdBy: string = ''
  @autoserializeAs('updatedBy')
  updatedBy?: string
}

export class ModelMaster implements ModelMastersResponse {
  @autoserializeAs('key')
  key: string = ''
  @autoserializeAs('label')
  label: string = ''
  @autoserializeAs(Model, 'model')
  model?: Model
}

export class ModelMastersTableResponse implements GetModelMastersTableResponse {
  @autoserializeAs('createdAt')
  createdAt!: Date
  @autoserializeAs('updatedAt')
  updatedAt!: Date
  @autoserializeAs('companyId')
  companyId!: string
  @autoserializeAs('modelKey')
  modelKey!: string
  //modelIdentifiers?: any
  //templateId?: string
  //modelMasterGrades?: any[]
  //modelExportDetails?: any
  @autoserializeAs('createdBy')
  createdBy?: string
  @autoserializeAs('updatedBy')
  updatedBy?: string
  @autoserializeAs('matCode')
  matCode?: string
  @autoserializeAs('referencePrice')
  referencePrice?: number
  @autoserializeAs('modelYear')
  modelYear?: string
  @autoserializeAs('purchasedRatio')
  purchasedRatio?: number
  @autoserializeAs('brand')
  brand?: string
  @autoserializeAs('model')
  model?: string
  @autoserializeAs('rom')
  rom?: string
  @autoserializeAs('gradeA')
  gradeA?: string
  @autoserializeAs('gradeB')
  gradeB?: string
  @autoserializeAs('gradeC')
  gradeC?: string
  @autoserializeAs('gradeD')
  gradeD?: string
  @autoserializeAs('matCodeSale')
  matCodeSale?: string
  @autoserializeAs('insuranceCost')
  insuranceCost?: number
  @autoserializeAs('averageRetailCost')
  averageRetailCost?: any
  @autoserializeAs('averageWholeSaleCost')
  averageWholeSaleCost?: any
  @autoserializeAs('ownerName')
  ownerName?: string
}
