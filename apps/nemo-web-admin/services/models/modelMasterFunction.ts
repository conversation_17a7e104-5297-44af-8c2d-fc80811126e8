import { autoserializeAs } from 'cerialize'
import { ColumnName, ModelIdentifiers, ModelMasterFunctionAdjustResult } from 'contracts'

export class ModelIdentifiersClass implements ModelIdentifiers {
  @autoserializeAs('brand')
  brand: string = ''
  @autoserializeAs('model')
  model: string = ''
  @autoserializeAs('rom')
  rom: string = ''
}

export class ModelMasterFunctionResult implements ModelMasterFunctionAdjustResult {
  @autoserializeAs('ownerName')
  ownerName: string = ''
  @autoserializeAs('id')
  id!: string
  @autoserializeAs(ModelIdentifiersClass, 'modelIdentifiers')
  modelIdentifiers!: ModelIdentifiersClass
  @autoserializeAs('penalty')
  penalty!: { [key: string]: string }
}

export class ColumnNameClass implements ColumnName {
  @autoserializeAs('column')
  column!: string[]
}
