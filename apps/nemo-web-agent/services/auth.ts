import { usePersistStore } from '@/stores/persistStore'
import { useSystemStore } from '@/stores/systemStore'
import { getAuth } from 'firebase/auth'
import { BranchRoleModel, PermissionGroupConfigModel, RoleConfigModel } from './models/me'
import { getCanAccessList, getPermissionConfig, getPermissionMenu, getPermissionsPath } from 'utils'

// config
export const SINGLE_SESSION_USER_TYPES = ['WW']

// function
export const getFirebaseToken = async () => {
  const idToken = (await getAuth().currentUser?.getIdToken()) ?? null
  return idToken
}

export const refreshFirebaseToken = async () => {
  const idToken = await getAuth().currentUser?.getIdToken(true)
  return idToken ?? ''
}

export const getInitialHeader = () => {
  const headers: HeadersInit = {}
  const { currentBranchId } = usePersistStore.getState()

  // Check current branch not null or empty
  if (currentBranchId) {
    headers['x-branch'] = currentBranchId
  }

  // Get domain name from url
  if (process.env.STAGE === 'local') {
    headers['x-company'] = 'WW'
  } else {
    headers['x-company'] = window.location.hostname
  }

  return headers
}

export const getPermissions = ({
  roleConfig,
  permissionGroupConfig,
  selectedBranchRoles,
}: {
  roleConfig: RoleConfigModel[]
  permissionGroupConfig: PermissionGroupConfigModel[]
  selectedBranchRoles: string[]
}) => {
  const { permissionConfig } = getPermissionConfig({
    roleConfig,
    selectedBranchRoles,
  })
  const { pathByPermissions } = getPermissionsPath(permissionConfig, 'SHOP')
  const canAccessClass = getCanAccessList(permissionConfig).join(' ')
  const { groupMenu: permissionMenu, permissionPaths } = getPermissionMenu({ permissionGroupConfig, pathByPermissions })
  return {
    selectedBranchRoles,
    permissionConfig,
    canAccessClass,
    permissionMenu,
    permissionPaths: [...permissionPaths, '/select-branches'],
  }
}

export const onBranchConfirm = ({
  currentBranchId,
  branchRoles,
  roleConfig,
  permissionGroupConfig,
}: {
  currentBranchId: string
  branchRoles: BranchRoleModel[]
  roleConfig: RoleConfigModel[]
  permissionGroupConfig: PermissionGroupConfigModel[]
}) => {
  const { getAccessMenu, getPermissionConfig } = useSystemStore.getState()
  const selectedBranchRoles = branchRoles.find(branch => branch.branchId === currentBranchId)?.roles || []
  const { permissionConfig, canAccessClass, permissionMenu, permissionPaths } = getPermissions({
    roleConfig,
    permissionGroupConfig,
    selectedBranchRoles,
  })
  getAccessMenu(permissionMenu, permissionPaths)
  getPermissionConfig({
    permissionConfig,
    canAccessClass,
  })
  return { permissionPaths }
}
