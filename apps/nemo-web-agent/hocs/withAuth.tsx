import { useRouter } from 'next/router'
import { useEffect, useState } from 'react'
import { User, getAuth, onAuthStateChanged, signOut } from 'firebase/auth'
import { useShallow } from 'zustand/react/shallow'
import { DefaultSpinner } from 'ui'
import { usePersistStore } from '@/stores/persistStore'
import { useUserStore } from '@/stores/userStore'
import { useTranslation } from '@/hooks'
import { useCompanyStore } from '@/stores/companyStore'
import { doc, getDoc, getFirestore, onSnapshot, setDoc } from 'firebase/firestore'
import { useSystemStore } from '@/stores/systemStore'
import { MeResponse } from '@/services/models/me'
import { onBranchConfirm, SINGLE_SESSION_USER_TYPES } from '@/services/auth'
import { NOT_FOUND_PATH } from 'utils'

export const withAuth = (Component: React.ComponentType) => {
  const Auth = (props: any) => {
    const { replace, pathname } = useRouter()
    const [isOverlay, setIsOverlay] = useState(true)
    const { t } = useTranslation('common')

    const { setCurrentBranch, clearCurrentBranch, currentBranchId } = usePersistStore(
      useShallow(state => {
        return {
          setCurrentBranch: state.setCurrentBranch,
          clearCurrentBranch: state.clearCurrentBranch,
          currentBranchId: state.currentBranchId,
        }
      })
    )

    const { hideCommonModal, canAccessClass, fetchUserPermission, setFetchUserPermission } = useSystemStore(
      useShallow(state => {
        return {
          hideCommonModal: state.hideCommonModal,
          canAccessClass: state.canAccessClass,
          fetchUserPermission: state.fetchUserPermission,
          setFetchUserPermission: state.setFetchUserPermission,
        }
      })
    )

    const { getCompanyId } = useCompanyStore(
      useShallow(state => {
        return {
          getCompanyId: state.getCompanyId,
        }
      })
    )

    const { fetchUserMe, currentUser } = useUserStore(
      useShallow(state => {
        return {
          currentUser: state.user,
          fetchUserMe: state.fetchUserMe,
        }
      })
    )

    useEffect(() => {
      const unsubscribe = onAuthStateChanged(getAuth(), user => {
        if (typeof window === 'undefined') return

        // get env from STAGE env
        const env = process.env.STAGE ?? ''

        if (user) {
          handleUserAuthentication(user, env)
        } else {
          // User is signed out
          // Remove cookie x-sv-auth
          document.cookie = `x-sv-auth-${env}=; expires=${new Date().toUTCString()}; path=/; domain=axonstech.com`
          replace('/login')
          return
        }

        setIsOverlay(false)
      })

      return () => unsubscribe()
    }, [])

    useEffect(() => {
      if (fetchUserPermission) {
        handleUserFetch()
        setFetchUserPermission(false)
      }
    }, [fetchUserPermission])

    useEffect(() => {
      // Prevent invalid current user
      if (currentUser && SINGLE_SESSION_USER_TYPES.includes(currentUser.userType)) {
        // Subscribe session collection
        const unsubscribe = subscribeSession(currentUser)

        // Unsubscrie when component unmount
        return () => {
          unsubscribe()
        }
      }
    }, [currentUser])

    const handleUserAuthentication = async (user: User, env: string) => {
      // if no user in store will fetch new user from db
      if (!currentUser?.userKey) {
        handleUserFetch()
      }

      // Set cookie x-sv-auth for 1 hour
      const expires = new Date()
      expires.setTime(expires.getTime() + 1 * 60 * 60 * 1000) // Set time + 1 hour
      user.getIdToken().then(token => {
        document.cookie = `x-sv-auth-${env}=${token}; expires=${expires.toUTCString()}; path=/; domain=axonstech.com`
      })
    }

    // Subscribe session firestore
    const subscribeSession = (currentUser: MeResponse) => {
      // Firestore query path
      const queryPath = `company/${getCompanyId()}/sessions/${currentUser.userKey}/`

      // Document reference
      const sessionRef = doc(getFirestore(), queryPath)

      // Get document data
      getDoc(sessionRef).then(docSnap => {
        // Check data not exists
        if (!docSnap.exists()) {
          // Get current timestamp
          const currentTimeStamp = Date.now()

          // Set firestore data
          setDoc(sessionRef, { lastLoginAt: currentTimeStamp })

          // Set localStorage
          localStorage.setItem('lastLoginAt', String(currentTimeStamp))
        }
      })

      // Declare execute sigout
      let isExecuteSigout = false

      return onSnapshot(doc(getFirestore(), queryPath), snapshot => {
        if (!snapshot.metadata.hasPendingWrites) {
          // Get lastLoginAt from local storage
          const lastLoginAt = localStorage.getItem('lastLoginAt')

          // Check lastLoginAt equal or not
          if (snapshot.data()?.lastLoginAt > Number(lastLoginAt) && !isExecuteSigout) {
            // Clear current branch
            clearCurrentBranch()

            // Hide modal
            hideCommonModal()

            // Prevent auth invalid
            if (getAuth()) {
              // Sign out

              signOut(getAuth())
                .then(() => {
                  // Change flag execute
                  isExecuteSigout = true
                })
                .catch(error => {
                  console.error('signOut error', error)
                })
            }
          }
        }
      })
    }

    const handleUserFetch = () => {
      fetchUserMe(t, () => {
        replace('/login')
      }).then(userMe => {
        if (userMe) {
          const { branchRoles, roleConfig, permissionGroupConfig } = userMe
          let branchId = currentBranchId

          /* 
          if no branch selected
             will redirect to select branch page to select branch
             but if have only one branch will set it as current branch and bypass select branch page 
          */
          if (!currentBranchId) {
            if (branchRoles.length > 1) {
              replace('/select-branches')
              return
            } else if (branchRoles.length === 1) {
              setCurrentBranch(branchRoles[0])
              branchId = branchRoles[0].branchId
            }
          }

          const { permissionPaths } = onBranchConfirm({
            currentBranchId: branchId,
            branchRoles,
            roleConfig,
            permissionGroupConfig,
          })

          const pathIndex = permissionPaths.findIndex(path => pathname === path)
          if (pathIndex === -1) {
            if (pathname === '/') {
              replace(permissionPaths[0])
            } else {
              replace(NOT_FOUND_PATH)
            }
          }
        }
      })
    }

    return isOverlay || (canAccessClass === null && pathname !== '/select-branches') ? (
      <div className="flex items-center justify-center w-screen h-screen bg-white">
        <DefaultSpinner />
      </div>
    ) : (
      <div className={canAccessClass ?? ''} style={{ height: '100%' }}>
        <Component {...props} />
      </div>
    )
  }
  return Auth
}
