import { NestFactory } from '@nestjs/core';
import { AppModule } from '../src/app.module';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { patchNestJsSwagger } from 'nestjs-zod';
import { writeFile } from 'fs/promises';
import * as yaml from 'js-yaml';

async function generateApi() {
  // Create NestJS application
  const app = await NestFactory.create(AppModule);

  // Set config swagger
  const config = new DocumentBuilder()
    .setTitle('Nemo api service')
    .setVersion('0.1')
    .build();

  // Create swagger document
  const document = SwaggerModule.createDocument(app, config);

  // Patch update swagger
  patchNestJsSwagger();

  try {
    await writeFile(process.cwd() + '/openapi.yaml', yaml.dump(document));
  } catch (_err) {
    console.error('Error writing openapi.yaml');
  }
}

generateApi();
