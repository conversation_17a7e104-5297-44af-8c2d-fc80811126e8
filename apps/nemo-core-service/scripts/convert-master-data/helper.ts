import { promises as fs } from 'fs';
import { DateTime } from 'luxon';
import * as path from 'path';
import { isObject } from 'lodash';

const header: Record<string, string[]> = {
  branch: ['created_at', 'updated_at', 'company_id', 'branch_id', 'title'],
  model_master: [
    'created_at',
    'updated_at',
    'company_id',
    'model_key',
    'model_identifiers',
    'template_id',
    'created_by',
    'updated_by',
    'model_master_grades',
  ],
  user: [
    'created_at',
    'updated_at',
    'company_id',
    'user_key',
    'name',
    'roles',
    'last_login',
  ],
};

const mappingKey: Record<string, Record<string, string>> = {
  branch: {
    branchId: 'Code',
    title: 'Shop TH1',
  },
  model_master: {
    brand: 'Brand',
    model: 'Model',
    rom: 'ความจุ',
  },
  user: {
    companyId: 'COMPANY',
    userKey: 'EMP_ID',
    name: 'EMP_NAME',
    surename: 'EMP_SNAME',
    empType: 'EMP_TYPE2',
    shopCode: 'SHOP_CODE',
  },
  role: {
    empType: 'EMP_TYPE2',
    role: 'Group Role 1',
    isApprover: 'Group Role 2',
  },
  user_muze: {
    name: 'Name',
    email: 'Email',
  },
};

export async function convertMasterData<T>(
  tableName: string,
  callback?: (result: any[]) => Promise<T[]>,
) {
  try {
    // File path resolution based on table name
    const result = await readFileTSV(tableName);

    // Callback execution and writing to CSV file
    const data = (await callback?.(result)) ?? (result as T[]);
    await writeEntitiesToCSV(
      data,
      `output/${tableName}.csv`,
      header[tableName.replace(/-/, '_')],
    );
  } catch (error) {
    console.error('Error occurred:', error);
  }
}

export async function readFileTSV(tableName: string) {
  const filePath = path.resolve(__dirname, `input/${tableName}.tsv`);
  const fileData = await fs.readFile(filePath, 'utf-8');
  const [headerRow, ...dataRows] = fileData
    .trim()
    .split('\n')
    .map((row) => row.split('\t').map((cell) => cell.trim()));

  // Mapping data based on configuration
  const mapping = mappingKey[tableName.replace(/-/, '_')];
  return dataRows.map((line) =>
    Object.fromEntries(
      Object.entries(mapping).map(([key, value]) => [
        key,
        line[headerRow.indexOf(value)],
      ]),
    ),
  );
}

export async function writeEntitiesToCSV<T>(
  result: T[],
  filePath: string,
  headers: string[],
) {
  try {
    const csvContent = prepareCSVContent(result, headers);
    const csvFilePath = path.resolve(__dirname, filePath);
    await fs.writeFile(csvFilePath, csvContent);
    console.log(`${filePath} has been created successfully.`);
  } catch (error) {
    console.error('Error occurred while writing the file:', error);
  }
}

function prepareCSVContent<T>(result: T[], headers: string[]): string {
  return (
    headers.join(',') +
    '\n' +
    result
      .map((item: T) =>
        headers
          .map((header) => {
            if (header === 'created_at' || header === 'updated_at') {
              return DateTime.now().toUTC();
            }
            const camelCaseHeader = header.replace(/_([a-z])/g, (_, match) =>
              match.toUpperCase(),
            );
            if (isObject(item)) {
              const value =
                (item as Record<string, any>)[camelCaseHeader] || 'null';
              return typeof value === 'object' ? jsonToString(value) : value;
            }
          })
          .join(','),
      )
      .join('\n')
  );
}

export function jsonToString(value: any): string {
  return `"${JSON.stringify(value).replace(/"/g, '""')}"`;
}
