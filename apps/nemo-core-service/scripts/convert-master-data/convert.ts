import { AutoIdField } from 'src/utils/autoId';
import {
  BranchEntity,
  ModelMasterEntity,
  UserEntity,
  UserRole,
} from '../../src/entities';
import { convertMasterData, readFileTSV, writeEntitiesToCSV } from './helper';
import { uniqBy } from 'lodash';

function formattedModelMasters(result: any[]) {
  return result.map((modelMaster) => {
    const { brand, model, rom } = modelMaster;

    // Formatting model identifiers
    const modelIdentifiers = { brand, model, rom };
    const modelKey = AutoIdField.basic.produce();

    // Creating model master grades
    const modelMasterGrades = [
      {
        grade: 'A',
        purchasePrice: '20000.00',
        lastPurchasedPrice: 'Grade Info',
        lastPurchasedOn: 'ISO8601',
      },
      {
        grade: 'B',
        purchasePrice: '15000.00',
        lastPurchasedPrice: 'Grade Info',
        lastPurchasedOn: 'ISO8601',
      },
      {
        grade: 'C',
        purchasePrice: '10000.00',
        lastPurchasedPrice: 'Grade Info',
        lastPurchasedOn: 'ISO8601',
      },
      {
        grade: 'D',
        purchasePrice: '8000.00',
        lastPurchasedPrice: 'Grade Info',
        lastPurchasedOn: 'ISO8601',
      },
    ];

    return {
      modelKey,
      modelIdentifiers,
      templateId: 'v1',
      companyId: 'WW',
      createdBy: 'system',
      updatedBy: 'system',
      modelMasterGrades,
      modelExportDetails: {},
    } as ModelMasterEntity;
  });
}

async function formattedUsers(result: any[]) {
  // filtering user data for company 'WW'
  const userWW = result.filter((item) => item.companyId === 'WW');
  // Reading branch and role data
  const [branchs, roles] = await Promise.all([
    readFileTSV('branch'),
    readFileTSV('role'),
  ]);

  // Mapping user roles based on branch and role data
  const mapUserRoles: Record<string, Record<string, string[]>> = {};

  userWW.forEach((item) => {
    const { shopCode, empType, userKey } = item;
    const branch = branchs.find((b) => b.branchId === shopCode);
    const role = roles.find((r) => r.empType === empType);

    if (branch && role) {
      const branchId = branch.branchId;
      mapUserRoles[userKey] = mapUserRoles[userKey] || {};
      mapUserRoles[userKey][branchId] = mapUserRoles[userKey][branchId] || [];

      // Adding manager role for approver
      if (role.isApprover === 'Approve') {
        mapUserRoles[userKey][branchId].push('Manager');
      }
      if (role.role) {
        mapUserRoles[userKey][branchId].push(role.role);
      }
    }
  });

  // Formatting user data with mapped roles and adjusted names
  return uniqBy(userWW, 'userKey').map((user) => {
    const name = `${user.name} ${user.surename}`.trim();
    const roles = Object.entries(mapUserRoles[user.userKey] || {}).map(
      ([branchId, role]) => ({ branchId, role }),
    ) as UserRole[];
    return { ...user, name, roles };
  });
}

export async function runConvertBranchData() {
  await convertMasterData<BranchEntity>('branch', async (result) =>
    // Modifying data: Setting companyId to 'WW' and ensuring uniqueness by branchId
    uniqBy(result, 'branchId').map((branch) => {
      return {
        ...branch,
        companyId: 'WW',
      };
    }),
  );
}

export async function runConvertModelMasterData() {
  await convertMasterData<ModelMasterEntity>('model-master', async (result) =>
    // Ensuring uniqueness based on companyId and modelKey combination
    uniqBy(
      formattedModelMasters(result),
      (modelMaster) => `${modelMaster.companyId}_${modelMaster.modelKey}`,
    ),
  );
}

export async function runConvertUserData() {
  await convertMasterData<UserEntity>('user', async (result) => {
    const data = await formattedUsers(result);

    // Add user muze
    const muzeUsers = await readFileTSV('user-muze');
    const formatMuzeUsers = muzeUsers.map((muzeUser) => ({
      name: muzeUser.name.replace(/Mr\.|Ms\./g, '').trim(),
      userKey: muzeUser.email,
      companyId: 'WW',
      roles: [
        { role: ['Manager', 'Sale'], branchId: '80000430' },
        { role: ['Sale'], branchId: '80000011' },
        { role: ['Sale'], branchId: '80000400' },
      ],
    }));

    return data.concat(formatMuzeUsers);
  });
}

export async function runGenerateModelMasterPrice() {
  const modelMasters = await readFileTSV('model-master');

  const modelMasterPrices = {
    'product_information.device_condition=minor_marks': '-1000.00',
    'product_information.device_condition=major_marks': '-2000.00',
    'product_information.country_of_purchase=etc': '-3000.00',
    'product_information.screen_display=abnormal': '-5000.00',
    'product_information.additional_accessories=incomplete': '-800.00',
    'product_information.battery_health=below_80_percent': '-1000.00',
    'product_information.icloud_or_google_account=cannot_sign_out': '-1000.00',
    'remobie_check_list.bluetooth=non_functional': '-1000.00',
    'remobie_check_list.wifi=non_functional': '-1000.00',
    'remobie_check_list.front_camera=non_functional': '-1000.00',
    'remobie_check_list.rear_camera=non_functional': '-1000.00',
    'remobie_check_list.biometric=non_functional': '-1000.00',
    'remobie_check_list.vibration=non_functional': '-1000.00',
    'remobie_check_list.speaker=non_functional': '-1000.00',
    'remobie_check_list.earpiece=non_functional': '-1000.00',
    'remobie_check_list.touch_screen=non_functional': '-1000.00',
    'remobie_check_list.power_charging=non_functional': '-1000.00',
    'remobie_check_list.proximity_sensor=non_functional': '-1000.00',
    'remobie_check_list.microphone=non_functional': '-1000.00',
    'remobie_check_list.volume_button=non_functional': '-1000.00',
  };

  const result = formattedModelMasters(modelMasters).flatMap((modelMaster) =>
    Object.entries(modelMasterPrices).map(([condition, penalty]) => ({
      companyId: 'WW',
      modelKey: modelMaster.modelKey,
      functionKeyCond: condition,
      penalties: penalty,
      createdBy: 'system',
      updatedBy: 'system',
    })),
  );

  await writeEntitiesToCSV(result, `output/model-master-function.csv`, [
    'created_at',
    'updated_at',
    'company_id',
    'model_key',
    'function_key_cond',
    'penalties',
    'created_by',
    'updated_by',
  ]);
}

runConvertUserData();
runConvertBranchData();
runConvertModelMasterData();
runGenerateModelMasterPrice();
