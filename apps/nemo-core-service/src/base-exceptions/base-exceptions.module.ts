import { Module, Global, DynamicModule } from '@nestjs/common';
import { BaseExceptionService } from '../exceptions';

@Global()
@Module({})
export class BaseExceptionModule {
  static register(): DynamicModule {
    return {
      module: BaseExceptionModule,
      providers: [
        {
          provide: BaseExceptionService,
          useFactory: () => {
            return new BaseExceptionService();
          },
        },
      ],
      exports: [BaseExceptionService],
    };
  }
}
