import { Injectable, NestMiddleware } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import type { WithUserContext } from '../interfaces/with-user.interface';
import { BaseExceptionService } from '../exceptions/base.exception';
import { FirebaseService } from '../firebase/firebase.service';
import { InjectRepository } from '@nestjs/typeorm';
import { CompanyEntity, UserEntity } from '../entities';
import { Repository } from 'typeorm';
import { DecodedIdToken } from 'firebase-admin/auth';
import { mappingUrlWithCompanyId } from '../config';

function mappingKeyNameFn(
  payload: DecodedIdToken,
  keyName?: string | null,
): string | undefined {
  if (!keyName) {
    return payload.email;
  }

  if (keyName === 'getWwUserKeyClaim') {
    if (process.env.NODE_ENV === 'production') {
      return payload.employeeId;
    } else {
      return payload.employeeId || payload.email;
    }
  }

  return payload.email;
}

@Injectable()
export class WithUserMiddleware implements NestMiddleware {
  constructor(
    private readonly firebaseService: FirebaseService,
    @InjectRepository(CompanyEntity)
    private readonly companyRepository: Repository<CompanyEntity>,
    @InjectRepository(UserEntity)
    private readonly userRepository: Repository<UserEntity>,
  ) {}

  async use(
    req: Request & { withUserContext: WithUserContext },
    _res: Response,
    next: NextFunction,
  ) {
    // Validate token
    const authorization = req.headers.authorization;

    // Prevent authorization invalid
    if (!authorization) {
      // Throw exception
      throw new BaseExceptionService().exception(
        'UNAUTHORIZED',
        'authorization in headers invalid',
      );
    }

    // Regex for validate bearer token
    const regex = /Bearer /;

    // Prevent authorization invalid
    if (typeof authorization === 'string' && !regex.test(authorization)) {
      // Throw exception
      throw new BaseExceptionService().exception(
        'UNAUTHORIZED',
        'authorization invalid match Bearer',
      );
    }

    // Extract bearer token
    const token = authorization.split('Bearer ')[1];

    // Get x company
    const xCompany = req.headers['x-company'];

    if (typeof xCompany !== 'string') {
      // Throw exception
      throw new BaseExceptionService().exception(
        'UNAUTHORIZED',
        'authorization in headers invalid',
      );
    }

    // Get company
    const company = mappingUrlWithCompanyId(xCompany);

    // Prevent company invalid
    if (!company || typeof company !== 'string') {
      // Throw exception
      throw new BaseExceptionService().exception('COMPANY_INVALID');
    }

    try {
      // Initialize firebase
      await this.firebaseService.InitializeFirebase(company);

      // Payload
      const payload = await this.firebaseService.verifyIdToken(token);

      // Find company
      const userKeyClaim = await this.companyRepository.findOne({
        select: ['userKeyClaimFnName'],
        where: { companyId: company },
      });

      // Mapping key name
      const keyClaim = mappingKeyNameFn(
        payload,
        userKeyClaim?.userKeyClaimFnName,
      );

      if (!keyClaim) {
        // Throw exception
        throw new BaseExceptionService().exception(
          'UNAUTHORIZED',
          'key claim failed',
        );
      }

      // Prevent user key invalid
      const user = await this.userRepository.findOne({
        relations: [
          'userRoleBranch',
          'userRoleBranch.role',
          'userRoleBranch.role.rolePermissions',
        ],
        where: { userKey: keyClaim, companyId: company },
      });

      const permissionArray = user?.userRoleBranch?.map((userRoleBranch) => {
        const arrayList: string[] = [];
        userRoleBranch.role.rolePermissions.forEach((rolePermission) => {
          const permissionId = rolePermission.permissionId;
          if (rolePermission.view) {
            arrayList.push(`${permissionId}_VIEW`);
          }
          if (rolePermission.create) {
            arrayList.push(`${permissionId}_CREATE`);
          }
          if (rolePermission.update) {
            arrayList.push(`${permissionId}_UPDATE`);
          }
          if (rolePermission.delete) {
            arrayList.push(`${permissionId}_DELETE`);
          }
          if (rolePermission.download) {
            arrayList.push(`${permissionId}_DOWNLOAD`);
          }
          if (rolePermission.upload) {
            arrayList.push(`${permissionId}_UPLOAD`);
          }
        });
        return {
          permission: arrayList,
          branchId: userRoleBranch.branchId,
        };
      });
      const permissionBranchMapCompound = permissionArray?.reduce<
        { branchId: string; role: string[] }[]
      >((acc, curr) => {
        if (acc.length === 0) {
          acc.push({ branchId: curr.branchId, role: curr.permission });
        } else {
          const index = acc.findIndex(
            (element) => element.branchId === curr.branchId,
          );
          if (index !== -1) {
            acc[index].role = [...acc[index].role, ...curr.permission];
          } else {
            acc.push({ branchId: curr.branchId, role: curr.permission });
          }
        }
        return acc;
      }, []);
      const permissionBranchCompleteArray = permissionBranchMapCompound
        ? Object.entries(permissionBranchMapCompound).map(([, value]) => {
            return {
              branchId: value.branchId,
              permission: [...new Set(value.role)],
            };
          })
        : [];
      // throw error if not found user
      if (!user) {
        throw new BaseExceptionService().exception(
          'NOT_FOUND_USER',
          'user not found',
        );
      }

      // Inject user context to request
      req.withUserContext = {
        idToken: token,
        company,
        userKey: keyClaim,
        name: user?.name,
        roles: user?.roles,
        permissions: permissionBranchCompleteArray,
      };

      // Execute next function
      next();
    } catch (error) {
      console.error(error);
      // Throw exception
      if (error instanceof BaseExceptionService && error.code === '2001') {
        throw error;
      }
      throw new BaseExceptionService().exception(
        'UNAUTHORIZED',
        'token invalid',
      );
    }
  }
}
