import { Injectable, NestMiddleware } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { get } from 'lodash';
import { BaseExceptionService } from '../exceptions';
import type { WithBranchContext, WithUserContext } from '../interfaces';
import { InjectRepository } from '@nestjs/typeorm';
import { UserEntity, UserRoleBranchEntity } from '../entities';
import { Repository } from 'typeorm';
import { CacheManagerService } from '../cache-manager/cache-manager.service';
import { RedisUtils } from '../utils/redis.util';
import { mappingUrlWithCompanyId } from 'src/config';

@Injectable()
export class WithBranchMiddleware implements NestMiddleware {
  constructor(
    @InjectRepository(UserEntity)
    private readonly userRepository: Repository<UserEntity>,
    @InjectRepository(UserRoleBranchEntity)
    private readonly userRoleBranchEntity: Repository<UserRoleBranchEntity>,
    private readonly cacheManager: CacheManagerService,
  ) {}
  async use(
    req: Request & {
      withUserContext: WithUserContext;
      withBranchContext: WithBranchContext;
    },
    _res: Response,
    next: NextFunction,
  ) {
    // Get branch from header
    const branchId = get(req.headers, 'x-branch');

    // Get branch from header
    const xCompany = get(req.headers, 'x-company');

    if (typeof xCompany !== 'string') {
      // Throw exception
      throw new BaseExceptionService().exception('COMPANY_INVALID');
    }

    // Mapping x company with company id
    const companyId = mappingUrlWithCompanyId(xCompany);

    // Prevent branch
    if (!branchId || !companyId) {
      // Throw exception
      throw new BaseExceptionService().exception('BRANCH_INVALID');
    }

    // Get user key
    const userKey = req.withUserContext.userKey;

    // Get redis key with prefix
    const userRolesKey = RedisUtils.getUserRoleKey(`${companyId}:${userKey}`);

    // Get redis value with company id + user key
    const redisResult = await this.cacheManager.getData(userRolesKey);

    // Declare roles
    let roles: { branchId: string; role: string[] }[] = [];

    // If cache valid should return result
    if (redisResult?.length) {
      // Assign redis value to roles variable
      roles = redisResult;
    } else {
      // Find user select only roles column
      const userRoleBranch = await this.userRoleBranchEntity.find({
        where: {
          companyId,
          userKey,
        },
      });

      const userRoles = Object.values(
        userRoleBranch.reduce(
          (data, { roleId, branchId }) => {
            if (!data[branchId]) {
              data[branchId] = { role: [], branchId };
            }
            data[branchId].role.push(roleId);
            return data;
          },
          {} as Record<string, { role: string[]; branchId: string }>,
        ),
      );

      // Prevent roles not exists
      if (userRoles.length === 0) {
        // Throw exception
        throw new BaseExceptionService().exception('ROLES_INVALID');
      }

      // Set cache
      await this.cacheManager.setData(
        userRolesKey,
        userRoles,
        RedisUtils.dayTTL,
      );

      // Assign user role to roles variable
      roles = userRoles;
    }

    // Get roles in branch
    const rolesInBranch = roles?.find((role) => role.branchId === branchId);

    // Prevent roles invalid in branch
    if (!rolesInBranch?.role?.length) {
      // Throw exception
      throw new BaseExceptionService().exception('ROLES_INVALID');
    }

    // Inject branch context to request
    req.withBranchContext = {
      branch: branchId,
    } as WithBranchContext;

    // Execute next function
    next();
  }
}
