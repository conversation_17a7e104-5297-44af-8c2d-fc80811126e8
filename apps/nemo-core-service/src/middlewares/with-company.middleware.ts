import { Injectable, NestMiddleware } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { BaseExceptionService } from '../exceptions/base.exception';
import { InjectRepository } from '@nestjs/typeorm';
import { CompanyEntity } from '../entities';
import { Repository } from 'typeorm';
import { mappingUrlWithCompanyId } from '../config';
import { WithCompanyContext } from '../interfaces/with-company.interface';
import { CommonExceptionService } from '../exceptions';
import { getLanguageFromHeader } from '../utils/general';

@Injectable()
export class WithCompanyMiddleware implements NestMiddleware {
  constructor(
    @InjectRepository(CompanyEntity)
    private readonly companyRepository: Repository<CompanyEntity>,
  ) {}

  async use(
    req: Request & { withCompanyContext: WithCompanyContext },
    _res: Response,
    next: NextFunction,
  ) {
    // Get x company
    const xCompany = req.headers['x-company'];
    const lang = getLanguageFromHeader(req.headers);

    if (typeof xCompany !== 'string') {
      // Throw exception
      throw new CommonExceptionService().exception('COMPANY_INVALID', lang);
    }

    // Get company
    const company = mappingUrlWithCompanyId(xCompany);

    // Prevent company invalid
    if (!company || typeof company !== 'string') {
      // Throw exception
      throw new CommonExceptionService().exception('COMPANY_INVALID', lang);
    }

    try {
      // Find company
      const companyEntity = await this.companyRepository.findOne({
        where: { companyId: company },
      });

      if (!companyEntity) {
        throw new CommonExceptionService().exception('NOT_FOUND_DATA', lang);
      }

      req.withCompanyContext = {
        companyId: companyEntity.companyId,
      } as WithCompanyContext;

      // Execute next function
      next();
    } catch (error) {
      console.error(error);
      // Throw exception
      if (error instanceof BaseExceptionService && error.code === '2001') {
        throw error;
      }

      throw new CommonExceptionService().exception('UNAUTHORIZED', lang);
    }
  }
}
