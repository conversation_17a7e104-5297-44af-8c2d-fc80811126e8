/*
This class decorator is use for removing a route on a crudController route only!
NOTE: Must use this decorator just above @controller('example')
Example: 
@CrudRemoveRoute(['findAll', 'removeOne'])
@Controller('example')
*/

enum CrudMethodOptions {
  findAll = 'findAll',
  findOne = 'findOne',
  create = 'create',
  update = 'update',
  removeOne = 'removeOne',
  softRemoveOne = 'softRemoveOne',
  restore = 'restore',
}

export function CrudRemoveRoute(methods: (keyof typeof CrudMethodOptions)[]) {
  return function <T extends new (...args: any[]) => object>(constructor: T) {
    const parentClass = Object.getPrototypeOf(constructor.prototype);
    methods.forEach((method) => {
      const descriptor = Object.getOwnPropertyDescriptor(parentClass, method);
      if (descriptor && typeof descriptor.value === 'function') {
        delete parentClass[method];
      }
    });
  };
}
