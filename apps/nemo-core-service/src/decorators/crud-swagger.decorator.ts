import { ApiTags } from '@nestjs/swagger';

export enum MethodOptions {
  findAll = 'findAll',
  findOne = 'findOne',
  create = 'create',
  update = 'update',
  removeOne = 'removeOne',
  all = 'all',
}

export type additionalDec = (
  target: any,
  propertyKey: string | symbol,
  descriptor: PropertyDescriptor,
) => void;

export interface CrudMethod {
  findAll?: additionalDec[];
  findOne?: additionalDec[];
  create?: additionalDec[];
  update?: additionalDec[];
  removeOne?: additionalDec[];
  all?: additionalDec[]; // all route
}

/*
This class decorator is use for adding nestjs/swagger on a crudController route only!
Example: 
@CrudSwagger(
  {
    findAll: [ApiOperation({ summary: 'Operation 1111' }), ApiResponse({ status: 200, description: 'Response 1' })],
    findOne: [ApiOperation({ summary: 'Operation 2' }), ApiResponse({ status: 200, description: 'Response 2' })],
    create: [ApiOperation({ summary: 'Operation 3' }), ApiResponse({ status: 200, description: 'Response 3' })],
    update: [ApiOperation({ summary: 'Operation 4' }), ApiResponse({ status: 200, description: 'Response 4' })],
    all: [ApiResponse({ status: 404, description: 'not found' })],
  },
  'test',
)
*/

export function CrudSwagger(swaggerDecorators: CrudMethod, tags?: string) {
  return function <T extends new (...args: any[]) => object>(constructor: T) {
    return class extends constructor {
      constructor(...args: any[]) {
        super(...args);
        this.applySwaggerDecorators(swaggerDecorators, tags);
      }

      applySwaggerDecorators(swaggerDecorators: CrudMethod, tags?: string) {
        let currentProto = this.constructor.prototype;
        const apiTags = tags ? ApiTags(tags) : () => {};

        while (currentProto !== Object.prototype) {
          this.applyDecoratorsToMethods(
            currentProto,
            swaggerDecorators,
            apiTags,
          );
          currentProto = Object.getPrototypeOf(currentProto);
        }
      }

      applyDecoratorsToMethods(
        currentProto: any,
        swaggerDecorators: CrudMethod,
        apiTags: MethodDecorator & ClassDecorator,
      ) {
        const methodNames = Object.getOwnPropertyNames(currentProto).filter(
          (name) => name !== 'constructor',
        );
        for (const methodName of methodNames) {
          this.applyDecoratorToMethod(
            methodName,
            currentProto,
            swaggerDecorators,
            apiTags,
          );
        }
      }

      applyDecoratorToMethod(
        methodName: string,
        currentProto: any,
        swaggerDecorators: CrudMethod,
        apiTags: MethodDecorator & ClassDecorator,
      ) {
        const method = this[methodName];
        if (
          typeof method === 'function' &&
          Object.values(MethodOptions).includes(methodName as MethodOptions)
        ) {
          const descriptor =
            Object.getOwnPropertyDescriptor(currentProto, methodName) ?? {};
          const target = constructor.prototype;
          const decorators = swaggerDecorators[methodName as keyof CrudMethod];
          const allDecorators = swaggerDecorators.all;
          apiTags(target, methodName, descriptor);
          if (decorators) {
            decorators.forEach((decorator) => {
              decorator(target, methodName, descriptor);
            });
          }
          if (allDecorators) {
            allDecorators.forEach((decorator) => {
              decorator(target, methodName, descriptor);
            });
          }
        }
      }
    };
  };
}
