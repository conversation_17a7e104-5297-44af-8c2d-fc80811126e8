import { AES128MessageService } from '../../encrypt-decrypt-message/encrypt-decrypt.service';
import { Column, ColumnOptions, ValueTransformer } from 'typeorm';

export function EncryptDecryptString(options?: ColumnOptions) {
  return function (target: any, propertyKey: string) {
    const aes128Message = new AES128MessageService(
      process.env.AES128_KEY!,
      process.env.AES128_SALT!,
    );

    const transformer: ValueTransformer = {
      to(data: string): string {
        if (!data) return data;
        return aes128Message.encrypt(data).toString('base64');
      },
      from(data: string): string {
        if (!data) return data;
        return aes128Message.decrypt(Buffer.from(data, 'base64'));
      },
    };

    Column({ ...options, transformer })(target, propertyKey);
  };
}

export function EncryptDecryptObject(options?: ColumnOptions) {
  return function (target: any, propertyKey: string) {
    const aes128Message = new AES128MessageService(
      process.env.AES128_KEY!,
      process.env.AES128_SALT!,
    );

    const transformer: ValueTransformer = {
      to(data: any): string {
        if (!data) return data;
        return aes128Message.encrypt(JSON.stringify(data)).toString('base64');
      },
      from(data: string): any {
        if (!data) return data;
        return JSON.parse(aes128Message.decrypt(Buffer.from(data, 'base64')));
      },
    };

    Column({ ...options, transformer })(target, propertyKey);
  };
}
