import {
  <PERSON>um<PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  ManyToOne,
  OneToMany,
  PrimaryColumn,
  Index,
} from 'typeorm';
import { BaseEntity } from './base.entity';
import { AutoIdField } from '../utils';
import { ModelMasterFunctionEntity } from './model-master-function.entity';
import { CompanyEntity } from './company.entity';

export enum ChecklistType {
  QUESTION = 'QUESTION',
  MODULE = 'MODULE',
}

export enum QuestionType {
  SELECTION = 'SELECTION',
  OPTION = 'OPTION',
  TEXT = 'FREETEXT',
}

@Entity({ name: 'model_checklist' })
@Index(['companyId', 'functionKey'], { unique: true })
export class ModelChecklistEntity extends BaseEntity {
  @PrimaryColumn({ type: 'varchar', length: 200 })
  id: string = AutoIdField.basic.produce();

  @PrimaryColumn({ type: 'varchar', length: 200 })
  companyId!: string;

  @Column({ type: 'varchar', length: 200 })
  functionKey!: string;

  @Column({ type: 'varchar', length: 200 })
  functionSection!: string;

  @Column({ type: 'bool', default: true })
  isRequired!: boolean;

  @Column({ type: 'varchar' })
  checklistType!: ChecklistType;

  @Column({ type: 'varchar', length: 1000 })
  checklistNameTh!: string;

  @Column({ type: 'varchar', length: 1000 })
  checklistNameEn!: string;

  @Column({ type: 'varchar', length: 1000, nullable: true })
  checklistDescriptionTh?: string | null;

  @Column({ type: 'varchar', length: 1000, nullable: true })
  checklistDescriptionEn?: string | null;

  @Column({ type: 'text', nullable: true })
  tooltipTh?: string | null;

  @Column({ type: 'text', nullable: true })
  tooltipEn?: string | null;

  @Column({ type: 'varchar', length: 200, nullable: true })
  placeholderTh?: string | null;

  @Column({ type: 'varchar', length: 200, nullable: true })
  placeholderEn?: string | null;

  @Column({ type: 'varchar', length: 200, nullable: true })
  errorTextTh?: string | null;

  @Column({ type: 'varchar', length: 200, nullable: true })
  errorTextEn?: string | null;

  @Column({ type: 'varchar', length: 1000, nullable: true })
  iconImageUrl?: string | null;

  @Column({ type: 'varchar', length: 100, nullable: true })
  moduleCode?: string | null;

  @Column({ type: 'varchar', nullable: true })
  questionType?: QuestionType | null;

  @Column({ type: 'jsonb', nullable: true })
  questionChoices?: any | null;

  @Column({ type: 'bool', default: false })
  isIncludeVideo!: boolean;

  @Column({ type: 'jsonb', nullable: true })
  tooltip?: any | null;

  @Column({ type: 'jsonb', nullable: true })
  popup?: any | null;

  @OneToMany(
    () => ModelMasterFunctionEntity,
    (modelMasterFunction) => modelMasterFunction.modelChecklist,
  )
  modelMasterFunction?: ModelMasterFunctionEntity;

  @JoinColumn({
    name: 'company_id',
    referencedColumnName: 'companyId',
  })
  @ManyToOne(() => CompanyEntity, (company) => company.modelMasterChecklist, {
    onDelete: 'CASCADE',
  })
  company!: CompanyEntity;
}
