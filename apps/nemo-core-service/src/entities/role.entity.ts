import {
  <PERSON>umn,
  <PERSON><PERSON><PERSON>,
  <PERSON>inColumn,
  ManyToOne,
  OneToMany,
  PrimaryColumn,
  Unique,
} from 'typeorm';
import { BaseEntity } from './base.entity';
import { UserEntity } from './user.entity';
import { CompanyEntity } from './company.entity';
import { AutoIdField } from '../utils';
import { RolePermissionEntity } from './role-permission.entity';
import { UserRoleBranchEntity } from './user-role-branch.entity';

export enum SiteType {
  CMS = 'CMS',
  FRONTSHOP = 'FRONTSHOP',
}
@Entity({ name: 'role' })
@Unique(['companyId', 'roleName', 'type'])
export class RoleEntity extends BaseEntity {
  @PrimaryColumn({ type: 'varchar', length: 200 })
  companyId!: string;

  @PrimaryColumn({ type: 'varchar', length: 200 })
  roleId: string = AutoIdField.basic.produce();

  @Column({ type: 'varchar', length: 200 })
  roleName!: string;

  @Column({ type: 'varchar', length: 20 })
  type!: SiteType;

  @Column({ type: 'varchar', length: 200, nullable: true })
  createdBy?: string | null;

  @Column({ type: 'varchar', length: 200, nullable: true })
  updatedBy?: string | null;

  @JoinColumn({
    name: 'company_id',
    referencedColumnName: 'companyId',
  })
  @ManyToOne(() => CompanyEntity, (company) => company.roles, {
    onDelete: 'CASCADE',
  })
  company!: CompanyEntity;

  @JoinColumn([
    {
      name: 'created_by',
      referencedColumnName: 'userKey',
    },
    {
      name: 'company_id',
      referencedColumnName: 'companyId',
    },
  ])
  @ManyToOne(() => UserEntity, (user) => user.roleCreatedBy)
  createdUser?: UserEntity;

  @JoinColumn([
    {
      name: 'updated_by',
      referencedColumnName: 'userKey',
    },
    {
      name: 'company_id',
      referencedColumnName: 'companyId',
    },
  ])
  @ManyToOne(() => UserEntity, (user) => user.roleUpdatedBy)
  updatedUser?: UserEntity;

  @OneToMany(
    () => RolePermissionEntity,
    (rolePermission) => rolePermission.role,
    {
      onDelete: 'CASCADE',
    },
  )
  rolePermissions!: RolePermissionEntity[];

  @OneToMany(
    () => UserRoleBranchEntity,
    (userRoleBranch) => userRoleBranch.role,
    {
      onDelete: 'CASCADE',
    },
  )
  userRoleBranch?: UserRoleBranchEntity[];
}
