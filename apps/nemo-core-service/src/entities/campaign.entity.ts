import {
  <PERSON>umn,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  ManyToOne,
  OneToMany,
  PrimaryColumn,
  ManyToMany,
  JoinTable,
} from 'typeorm';
import { BaseEntity } from './base.entity';
import { CompanyEntity } from './company.entity';
import { UserEntity } from './user.entity';
import { ModelMasterEntity } from './model-master.entity';
import { CampaignRedemptionCodeEntity } from './campaign-redemption-code.entity';

export enum CampaignStatus {
  ALL = 'ALL',
  IN_PROCESS = 'IN_PROCESS',
  NOT_START = 'NOT_START',
  EXPIRED = 'EXPIRED',
  CANCELLED = 'CANCELLED',
}
@Entity({ name: 'campaign' })
export class CampaignEntity extends BaseEntity {
  @PrimaryColumn({ name: 'campaign_code', type: 'varchar', length: 200 })
  campaignCode!: string;

  @PrimaryColumn({ type: 'varchar', length: 200 })
  companyId!: string;

  @Column({ type: 'varchar', length: 100 })
  campaignName!: string;

  @Column({ type: 'varchar', length: 300 })
  description!: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  remark?: string;

  @Column({ type: 'timestamptz' })
  startDate!: Date;

  @Column({ type: 'timestamptz' })
  endDate!: Date;

  @Column({ type: 'int' })
  maxRedemptionCode!: number;

  @Column({ type: 'varchar', length: 200 })
  createdBy!: string;

  @Column({ type: 'varchar', length: 200 })
  updatedBy!: string;

  @Column({ type: 'bool', default: true })
  isActive!: boolean;

  @Column({ type: 'varchar', length: 200, nullable: true })
  canceledBy?: string;

  @Column({ type: 'timestamptz', nullable: true })
  canceledAt?: Date;

  @JoinColumn([
    {
      name: 'company_id',
      referencedColumnName: 'companyId',
    },
  ])
  @ManyToOne(() => CompanyEntity, (company) => company.campaigns, {
    onDelete: 'CASCADE',
  })
  company!: CompanyEntity;

  @JoinColumn([
    {
      name: 'created_by',
      referencedColumnName: 'userKey',
    },
    {
      name: 'company_id',
      referencedColumnName: 'companyId',
    },
  ])
  @ManyToOne(() => UserEntity, (user) => user.campaignCreatedBy)
  createdUser?: UserEntity;

  @JoinColumn([
    {
      name: 'updated_by',
      referencedColumnName: 'userKey',
    },
    {
      name: 'company_id',
      referencedColumnName: 'companyId',
    },
  ])
  @ManyToOne(() => UserEntity, (user) => user.campaignUpdatedBy)
  updatedUser?: UserEntity;

  @JoinColumn([
    {
      name: 'canceled_by',
      referencedColumnName: 'userKey',
    },
    {
      name: 'company_id',
      referencedColumnName: 'companyId',
    },
  ])
  @ManyToOne(() => UserEntity, (user) => user.campaignCanceledBy)
  canceledUser?: UserEntity;

  @ManyToMany(() => ModelMasterEntity, { nullable: true, cascade: true })
  @JoinTable()
  modelMasters?: ModelMasterEntity[];

  @OneToMany(
    () => CampaignRedemptionCodeEntity,
    (campaignRedemtion) => campaignRedemtion.campaign,
  )
  campaignRedemptionCode?: CampaignRedemptionCodeEntity[];
}
