import {
  <PERSON>um<PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>o<PERSON><PERSON>,
  OneToMany,
  PrimaryColumn,
} from 'typeorm';
import { BaseEntity } from './base.entity';
import { JobEntity } from './job.entity';
import { BranchEntity } from './branch.entity';
import { UserEntity } from './user.entity';
import { CompanyEntity } from './company.entity';
import { AllocationOrderActivitiesEntity } from './allocation-order-activities.entity';

export enum AllocationOrderStatus {
  DRAFT = '00_DRAFT',
  APPOINTMENT_PENDING = '05_APPOINTMENT_PENDING',
  APPOINTMENT_CONFIRMED = '10_APPOINTMENT_CONFIRMED',
  IN_TRANSIT = '20_IN_TRANSIT',
  PARTIAL_RECEIVED = '25_PARTIAL_RECEIVED',
  DELIVERY_SUCCESSFUL = '30_DELIVERY_SUCCESSFUL',
  REJECT_BY_SHOP = '98_REJECT_BY_SHOP',
  DELETED = '99_DELETED',
}

export enum AllocationOrderType {
  RETAIL = 'retail',
  WHOLESALE = 'wholesale',
}
export interface ModelIdentifiers {
  brand: string;
  model: string;
  rom: string;
}
export interface JobSnapshot {
  jobId: string;
  modelKey: string;
  deviceKey: string | null;
  modelIdentifiers: ModelIdentifiers;
  currentGrade: string;
  qcStatus: string | null;
  costPrice: number | null;
  wholeSalePrice: number | null;
  retailPrice: number | null;
  wholeSaleMargin: number | null;
  retailMargin: number | null;
  marginWholeSaleBaht: number | null;
  marginRetailBaht: number | null;
  aoShippingStatus: string | null;
  remark: string | null;
  videoPath: string | null;
  aoIncompleteInspectedUserKey: string | null;
  aoIncompleteInspectedUserName: string | null;
  aoIncompleteInspectedAt: Date | null;
}

@Entity({ name: 'allocation_order' })
export class AllocationOrderEntity extends BaseEntity {
  @PrimaryColumn({ name: 'allocation_order_id', type: 'varchar', length: 200 })
  allocationOrderId!: string;

  @Column({ name: 'company_id', type: 'varchar', length: 200 })
  companyId!: string;

  @Column({
    name: 'from_branch_id',
    type: 'varchar',
    length: 200,
    nullable: true,
  })
  fromBranchId?: string;

  @Column({
    name: 'to_branch_id',
    type: 'varchar',
    length: 200,
    nullable: true,
  })
  toBranchId?: string;

  @Column({ name: 'quantity', type: 'smallint' })
  quantity!: number;

  @Column({
    name: 'status',
    type: 'enum',
    enum: AllocationOrderStatus,
    default: AllocationOrderStatus.DRAFT,
  })
  status = AllocationOrderStatus.DRAFT;

  @Column({
    name: 'allocation_order_type',
    type: 'enum',
    enum: AllocationOrderType,
    default: AllocationOrderType.RETAIL,
  })
  allocationOrderType = AllocationOrderType.RETAIL;

  @Column({
    name: 'transporter_name',
    type: 'varchar',
    length: 200,
    nullable: true,
  })
  transporterName?: string;

  @Column({
    name: 'transporter_mobile_number',
    type: 'varchar',
    length: 30,
    nullable: true,
  })
  transporterMobileNumber?: string;

  @Column({ name: 'created_by', type: 'varchar', length: 200, nullable: true })
  createdBy?: string | null;

  @Column({ name: 'awb_number', type: 'varchar', length: 100, nullable: true })
  awbNumber?: string;

  @Column({ name: 'appointment_date', type: 'timestamptz', nullable: true })
  appointmentDate?: Date;

  @Column({ name: 'pickup_date', type: 'timestamptz', nullable: true })
  pickupDate?: Date;

  @Column({ name: 'received_date', type: 'timestamptz', nullable: true })
  receivedDate?: Date;

  @Column({ type: 'varchar', nullable: true, length: 200 })
  receiverUserKey?: string;

  @Column({
    name: 'confirm_appointment_at',
    type: 'timestamptz',
    nullable: true,
  })
  confirmAppointmentAt?: Date;

  @Column({
    name: 'confirm_appointment_user_key',
    type: 'varchar',
    nullable: true,
    length: 200,
  })
  confirmAppointmentUserKey?: string;

  @Column({
    name: 'confirm_pickup_at',
    type: 'timestamptz',
    nullable: true,
  })
  confirmPickupAt?: Date;

  @Column({
    name: 'confirm_pickup_user_key',
    type: 'varchar',
    nullable: true,
    length: 200,
  })
  confirmPickupUserKey?: string;

  @Column({ type: 'jsonb', nullable: true })
  jobsSnapshot?: JobSnapshot[] | null;

  @Column({ type: 'varchar', length: 1000, nullable: true })
  videoPath?: string;

  @Column({ type: 'varchar', length: 200, nullable: true })
  remark?: string;

  @Column({ type: 'bool', default: false })
  isInspectedByAdmin!: boolean;

  @OneToMany(() => JobEntity, (job) => job.allocationOrder)
  jobs!: JobEntity[];

  @JoinColumn([
    {
      name: 'company_id',
      referencedColumnName: 'companyId',
    },
    {
      name: 'from_branch_id',
      referencedColumnName: 'branchId',
    },
  ])
  @ManyToOne(() => BranchEntity, (branch) => branch.fromAllocationOrders, {
    onDelete: 'CASCADE',
  })
  fromBranch?: BranchEntity;

  @JoinColumn([
    {
      name: 'company_id',
      referencedColumnName: 'companyId',
    },
    {
      name: 'to_branch_id',
      referencedColumnName: 'branchId',
    },
  ])
  @ManyToOne(() => BranchEntity, (branch) => branch.toAllocationOrders, {
    onDelete: 'CASCADE',
  })
  toBranch?: BranchEntity;

  @JoinColumn([
    {
      name: 'company_id',
      referencedColumnName: 'companyId',
    },
    {
      name: 'created_by',
      referencedColumnName: 'userKey',
    },
  ])
  @ManyToOne(() => UserEntity, (user) => user.allocationOrderShop, {
    onDelete: 'CASCADE',
    nullable: true,
  })
  createdUser?: UserEntity;

  @JoinColumn([
    {
      name: 'receiver_user_key',
      referencedColumnName: 'userKey',
    },
    {
      name: 'company_id',
      referencedColumnName: 'companyId',
    },
  ])
  @ManyToOne(() => UserEntity, (user) => user.aoReceiver)
  receiverUser?: UserEntity;

  @JoinColumn([
    {
      name: 'company_id',
      referencedColumnName: 'companyId',
    },
    {
      name: 'confirm_appointment_user_key',
      referencedColumnName: 'userKey',
    },
  ])
  @ManyToOne(
    () => UserEntity,
    (user) => user.allocationOrderConfirmAppointmentUser,
    {
      onDelete: 'CASCADE',
    },
  )
  confirmAppointmentUser?: UserEntity;

  @JoinColumn([
    {
      name: 'company_id',
      referencedColumnName: 'companyId',
    },
    {
      name: 'confirm_pickup_user_key',
      referencedColumnName: 'userKey',
    },
  ])
  @ManyToOne(
    () => UserEntity,
    (user) => user.allocationOrderConfirmPickupUser,
    {
      onDelete: 'CASCADE',
    },
  )
  confirmPickupUser?: UserEntity;

  @JoinColumn({
    name: 'company_id',
    referencedColumnName: 'companyId',
  })
  @ManyToOne(() => CompanyEntity, (company) => company.allocationOrders, {
    onDelete: 'CASCADE',
  })
  company!: CompanyEntity;

  @OneToMany(
    () => AllocationOrderActivitiesEntity,
    (allocationOrderActivity) => allocationOrderActivity.allocationOrder,
    {
      nullable: true,
    },
  )
  allocationOrderActivities?: AllocationOrderActivitiesEntity[];
}
