import { Enti<PERSON>, Join<PERSON><PERSON>um<PERSON>, ManyTo<PERSON>ne, PrimaryColumn } from 'typeorm';
import { BaseEntity } from './base.entity';
import { UserEntity } from './user.entity';
import { CompanyEntity } from './company.entity';
import { RoleEntity } from './role.entity';
import { BranchEntity } from './branch.entity';

@Entity({ name: 'user_role_branch' })
export class UserRoleBranchEntity extends BaseEntity {
  @PrimaryColumn({ type: 'varchar', length: 200 })
  companyId!: string;

  @PrimaryColumn({ type: 'varchar', length: 200 })
  userKey!: string;

  @PrimaryColumn({ type: 'varchar', length: 200 })
  roleId!: string;

  @PrimaryColumn({ type: 'varchar', length: 200 })
  branchId!: string;

  @JoinColumn({
    name: 'company_id',
    referencedColumnName: 'companyId',
  })
  @ManyToOne(() => CompanyEntity, (company) => company.userRoleBranch, {
    onDelete: 'CASCADE',
  })
  company!: CompanyEntity;

  @JoinColumn([
    {
      name: 'company_id',
      referencedColumnName: 'companyId',
    },
    {
      name: 'user_key',
      referencedColumnName: 'userKey',
    },
  ])
  @ManyToOne(() => UserEntity, (user) => user.userRoleBranch, {
    onDelete: 'CASCADE',
  })
  user!: UserEntity;

  @JoinColumn([
    {
      name: 'company_id',
      referencedColumnName: 'companyId',
    },
    {
      name: 'role_id',
      referencedColumnName: 'roleId',
    },
  ])
  @ManyToOne(() => RoleEntity, (role) => role.userRoleBranch, {
    onDelete: 'CASCADE',
  })
  role!: RoleEntity;

  @JoinColumn([
    {
      name: 'company_id',
      referencedColumnName: 'companyId',
    },
    {
      name: 'branch_id',
      referencedColumnName: 'branchId',
    },
  ])
  @ManyToOne(() => BranchEntity, (branch) => branch.userRoleBranch, {
    onDelete: 'CASCADE',
  })
  branch!: BranchEntity;
}
