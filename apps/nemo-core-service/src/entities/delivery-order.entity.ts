import {
  <PERSON>umn,
  <PERSON><PERSON><PERSON>,
  <PERSON>in<PERSON><PERSON><PERSON><PERSON>,
  ManyToOne,
  OneToMany,
  PrimaryColumn,
} from 'typeorm';
import { BaseEntity } from './base.entity';
import { JobEntity } from './job.entity';
import { BranchEntity } from './branch.entity';
import { UserEntity } from './user.entity';
import { CompanyEntity } from './company.entity';
import { DeliveryOrderActivitiesEntity } from './delivery-order-activities.entity';

export enum DeliveryOrderStatus {
  APPOINTMENT_PENDING = '00_APPOINTMENT_PENDING',
  APPOINTMENT_CONFIRMED = '10_APPOINTMENT_CONFIRMED',
  IN_TRANSIT = '20_IN_TRANSIT',
  PARTIAL_RECEIVED = '25_PARTIAL_RECEIVED',
  DELIVERY_SUCCESSFUL = '30_DELIVERY_SUCCESSFUL',
}

@Entity({ name: 'delivery_order' })
export class DeliveryOrderEntity extends BaseEntity {
  @PrimaryColumn({ name: 'delivery_order_id', type: 'varchar', length: 200 })
  deliveryOrderId!: string;

  @Column({ name: 'company_id', type: 'varchar', length: 200 })
  companyId!: string;

  @Column({ name: 'branch_id', type: 'varchar', length: 200 })
  branchId!: string;

  @Column({ name: 'quantity', type: 'smallint' })
  quantity!: number;

  @Column({
    name: 'status',
    type: 'enum',
    enum: DeliveryOrderStatus,
    default: DeliveryOrderStatus.APPOINTMENT_PENDING,
  })
  status = DeliveryOrderStatus.APPOINTMENT_PENDING;

  @Column({ name: 'sender_user_company_id', type: 'varchar', length: 200 })
  senderUserCompanyId!: string;

  @Column({ name: 'sender_user_key', type: 'varchar', length: 200 })
  senderUserKey!: string;

  @Column({ name: 'sender_mobile_number', type: 'varchar', length: 30 })
  senderMobileNumber!: string;

  @Column({
    name: 'transporter_name',
    type: 'varchar',
    length: 200,
    nullable: true,
  })
  transporterName?: string;

  @Column({
    name: 'transporter_mobile_number',
    type: 'varchar',
    length: 30,
    nullable: true,
  })
  transporterMobileNumber?: string;

  @Column({ name: 'shop_user_key', type: 'varchar', length: 200 })
  shopUserKey!: string;

  @Column({ name: 'awb_number', type: 'varchar', length: 100, nullable: true })
  awbNumber?: string;

  @Column({ name: 'appointment_date', type: 'timestamptz', nullable: true })
  appointmentDate?: Date;

  @Column({ name: 'pickup_date', type: 'timestamptz', nullable: true })
  pickupDate?: Date;

  @Column({ name: 'received_date', type: 'timestamptz', nullable: true })
  receivedDate?: Date;

  @Column({
    name: 'confirm_appointment_at',
    type: 'timestamptz',
    nullable: true,
  })
  confirmAppointmentAt?: Date;

  @Column({
    name: 'edit_confirm_appointment_at',
    type: 'timestamptz',
    nullable: true,
  })
  editConfirmAppointmentAt?: Date;

  @Column({
    name: 'confirm_appointment_user_key',
    type: 'varchar',
    nullable: true,
    length: 200,
  })
  confirmAppointmentUserKey?: string;

  @Column({
    name: 'edit_confirm_appointment_user_key',
    type: 'varchar',
    nullable: true,
    length: 200,
  })
  editConfirmAppointmentUserKey?: string;

  @Column({
    name: 'shop_updated_at',
    type: 'timestamptz',
    nullable: true,
  })
  shopUpdatedAt?: Date;

  @OneToMany(() => JobEntity, (job) => job.deliveryOrder)
  jobs!: JobEntity[];

  @JoinColumn([
    {
      name: 'company_id',
      referencedColumnName: 'companyId',
    },
    {
      name: 'branch_id',
      referencedColumnName: 'branchId',
    },
  ])
  @ManyToOne(() => BranchEntity, (branch) => branch.deliveryOrders, {
    onDelete: 'CASCADE',
  })
  branch!: BranchEntity;

  @JoinColumn([
    {
      name: 'company_id',
      referencedColumnName: 'companyId',
    },
    {
      name: 'shop_user_key',
      referencedColumnName: 'userKey',
    },
  ])
  @ManyToOne(() => UserEntity, (user) => user.deliveryOrderShop, {
    onDelete: 'CASCADE',
  })
  shopUser!: UserEntity;

  @JoinColumn([
    {
      name: 'sender_user_company_id',
      referencedColumnName: 'companyId',
    },
    {
      name: 'sender_user_key',
      referencedColumnName: 'userKey',
    },
  ])
  @ManyToOne(() => UserEntity, (user) => user.deliveryOrderSender, {
    onDelete: 'CASCADE',
  })
  senderUser!: UserEntity;

  @JoinColumn([
    {
      name: 'company_id',
      referencedColumnName: 'companyId',
    },
    {
      name: 'confirm_appointment_user_key',
      referencedColumnName: 'userKey',
    },
  ])
  @ManyToOne(
    () => UserEntity,
    (user) => user.deliveryOrderConfirmAppointmentUser,
    {
      onDelete: 'CASCADE',
    },
  )
  confirmAppointmentUser?: UserEntity;

  @JoinColumn([
    {
      name: 'company_id',
      referencedColumnName: 'companyId',
    },
    {
      name: 'edit_confirm_appointment_user_key',
      referencedColumnName: 'userKey',
    },
  ])
  @ManyToOne(
    () => UserEntity,
    (user) => user.deliveryOrderEditConfirmAppointmentUser,
    {
      onDelete: 'CASCADE',
    },
  )
  editConfirmAppointmentUser?: UserEntity;

  @JoinColumn({
    name: 'company_id',
    referencedColumnName: 'companyId',
  })
  @ManyToOne(() => CompanyEntity, (company) => company.deliveryOrders, {
    onDelete: 'CASCADE',
  })
  company!: CompanyEntity;

  @OneToMany(
    () => DeliveryOrderActivitiesEntity,
    (deliveryOrderActivity) => deliveryOrderActivity.deliveryOrder,
    {
      nullable: true,
    },
  )
  deliveryOrderActivities?: DeliveryOrderActivitiesEntity[];
}
