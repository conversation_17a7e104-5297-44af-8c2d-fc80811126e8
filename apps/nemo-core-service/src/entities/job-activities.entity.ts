import { <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>um<PERSON>, <PERSON><PERSON>o<PERSON>ne, PrimaryColumn } from 'typeorm';
import { BaseEntity } from './base.entity';
import { JobEntity } from './job.entity';
import { AutoIdField } from '../utils';

export interface DataChangeInfo {
  from: any;
  to: any;
}

export interface JobActivityDetail {
  summary: string;
  branchId: string;
  status: string;
  attachments?: string[];
  displayName?: string;
  message?: string;
  dataChange?: Record<string, DataChangeInfo>;
  dataChangeSlug?: string | null;
  audience?: string;
  shopUserKey?: string;
  adminUserKey?: string;
  shopUserName?: string;
  adminUserName?: string;
}

export enum JobActivitiesType {
  UPDATE = 'update',
  COMMENT = 'comment',
}

@Entity({ name: 'job_activities' })
export class JobActivitiesEntity extends BaseEntity {
  @PrimaryColumn({ type: 'varchar', length: 200 })
  activityId: string = AutoIdField.basic.produce();

  @Column({ type: 'varchar', length: 200 })
  jobId!: string;

  @Column({ type: 'varchar', length: 200 })
  companyId!: string;

  @Column({ type: 'varchar', length: 200 })
  type!: string;

  @Column({ type: 'jsonb' })
  detail!: JobActivityDetail;

  @Column({ type: 'varchar', length: 100 })
  createdBy!: string;

  @JoinColumn([
    {
      name: 'company_id',
      referencedColumnName: 'companyId',
    },
    {
      name: 'job_id',
      referencedColumnName: 'jobId',
    },
  ])
  @ManyToOne(() => JobEntity, (job) => job.jobActivities, {
    onDelete: 'CASCADE',
  })
  job!: JobEntity;

  get isAdminAction() {
    return this.detail.shopUserKey !== this.createdBy;
  }
}
