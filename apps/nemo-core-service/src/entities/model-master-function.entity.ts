import { Column, <PERSON>tity, Join<PERSON><PERSON>umn, ManyToOne, PrimaryColumn } from 'typeorm';
import { BaseEntity } from './base.entity';
import { CompanyEntity } from './company.entity';
import { ModelMasterEntity } from './model-master.entity';
import { ModelChecklistEntity } from './model-checklist.entity';

@Entity({ name: 'model_master_function' })
export class ModelMasterFunctionEntity extends BaseEntity {
  @PrimaryColumn({ type: 'varchar', length: 200 })
  companyId!: string;

  @PrimaryColumn({ type: 'varchar', length: 200 })
  modelKey!: string;

  @PrimaryColumn({ type: 'varchar', length: 200 })
  functionKeyCond!: string;

  @Column({ type: 'varchar', length: 10 })
  penalties!: string;

  @Column({ type: 'varchar', length: 200, nullable: true })
  checkListId?: string;

  @Column({ type: 'varchar', length: 100 })
  createdBy!: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  updatedBy?: string;

  @JoinColumn({
    name: 'company_id',
    referencedColumnName: 'companyId',
  })
  @ManyToOne(() => CompanyEntity, (company) => company.modelMasters, {
    onDelete: 'CASCADE',
  })
  company!: CompanyEntity;

  @JoinColumn([
    {
      name: 'company_id',
      referencedColumnName: 'companyId',
    },
    {
      name: 'model_key',
      referencedColumnName: 'modelKey',
    },
  ])
  @ManyToOne(
    () => ModelMasterEntity,
    (modelMaster) => modelMaster.modelMasterFunction,
    {
      onDelete: 'CASCADE',
    },
  )
  modelMaster!: ModelMasterEntity;

  @JoinColumn([
    {
      name: 'check_list_id',
      referencedColumnName: 'id',
    },
    {
      name: 'company_id',
      referencedColumnName: 'companyId',
    },
  ])
  @ManyToOne(
    () => ModelChecklistEntity,
    (modelCheckList) => modelCheckList.modelMasterFunction,
    {
      onDelete: 'CASCADE',
    },
  )
  modelChecklist?: ModelChecklistEntity;
}
