import {
  Column,
  Entity,
  Index,
  JoinColumn,
  JoinTable,
  ManyToMany,
  ManyToOne,
  OneToMany,
  PrimaryColumn,
} from 'typeorm';
import { BaseEntity } from './base.entity';
import { CompanyEntity } from './company.entity';
import { JobTemplateEntity } from './job-template.entity';
import { JobEntity } from './job.entity';
import { ModelMasterFunctionEntity } from './model-master-function.entity';
import { ColumnDecimalTransformer } from './transformer/column-decimal.transformer';
import { EstimationActivitiesEntity } from './estimation-activities.entity';
import { ModelMasterColorEntity } from './model-master-color.entity';

export interface ModelMasterGradeDetail {
  grade: string; // Must enforce capital to ensure sorting.
  purchasePrice: string; // maximum price to purchase on this grade.
  lastPurchasedPrice: string; // Grade Info.
  lastPurchasedOn: string; // ISO8601 - Date Format
}

@Entity({ name: 'model_master' })
@Index(['companyId', 'systemCode'])
export class ModelMasterEntity extends BaseEntity {
  @PrimaryColumn({ type: 'varchar', length: 200 })
  companyId!: string;

  @PrimaryColumn({ type: 'varchar', length: 200 })
  modelKey!: string;

  @Column({ type: 'jsonb', array: false })
  modelIdentifiers!: any;

  @Column({ type: 'varchar', length: 200 })
  templateId!: string;

  @Column({ type: 'jsonb' })
  modelMasterGrades!: ModelMasterGradeDetail[];

  @Column({ type: 'jsonb', nullable: true })
  modelExportDetails?: any;

  @Column({ type: 'varchar', length: 100 })
  createdBy!: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  updatedBy?: string;

  @Column({ type: 'varchar', length: 200, nullable: true })
  matCode?: string;

  @Column({ type: 'varchar', length: 200, nullable: true })
  systemCode?: string;

  @Column({ type: 'varchar', length: 1000, nullable: true })
  modelImageUrl?: string;

  @Column({
    type: 'decimal',
    precision: 8,
    scale: 2,
    default: 0,
    transformer: new ColumnDecimalTransformer(),
    nullable: true,
  })
  referencePrice?: number;

  @Column({ type: 'varchar', length: 16, nullable: true })
  modelYear?: string;

  @Column({
    type: 'decimal',
    precision: 8,
    scale: 0,
    default: 0,
    transformer: new ColumnDecimalTransformer(),
    nullable: true,
  })
  purchasedRatio?: number;

  @Column({
    type: 'decimal',
    precision: 8,
    scale: 2,
    default: 0,
    transformer: new ColumnDecimalTransformer(),
    nullable: true,
  })
  insuranceCost?: number;

  @Column({ type: 'varchar', length: 200, nullable: true })
  matCodeSale?: string;

  @Column({ type: 'jsonb', nullable: true })
  averageRetailCost?: any;

  @Column({ type: 'jsonb', nullable: true })
  averageWholeSaleCost?: any;

  @Column('text', { array: true, nullable: true })
  systemCodeList?: string[];

  @Column({ type: 'varchar', length: 30, default: 'WW' })
  ownerName!: string;

  @JoinColumn({
    name: 'company_id',
    referencedColumnName: 'companyId',
  })
  @ManyToOne(() => CompanyEntity, (company) => company.modelMasters, {
    onDelete: 'CASCADE',
  })
  company!: CompanyEntity;

  @JoinColumn([
    {
      name: 'company_id',
      referencedColumnName: 'companyId',
    },
    {
      name: 'template_id',
      referencedColumnName: 'templateId',
    },
  ])
  @ManyToOne(
    () => JobTemplateEntity,
    (jobTemplate) => jobTemplate.modelMasters,
    {
      onDelete: 'CASCADE',
    },
  )
  jobTemplate!: JobTemplateEntity;

  @OneToMany(() => JobEntity, (job) => job.modelMaster, {
    nullable: true,
  })
  jobs?: JobEntity[];

  @OneToMany(
    () => ModelMasterFunctionEntity,
    (modelFunc) => modelFunc.modelMaster,
    {
      nullable: true,
    },
  )
  modelMasterFunction?: ModelMasterFunctionEntity[];

  @OneToMany(
    () => EstimationActivitiesEntity,
    (estimationActivity) => estimationActivity.modelMaster,
    {
      nullable: true,
    },
  )
  estimationActivities?: EstimationActivitiesEntity[];

  @ManyToMany(() => ModelMasterColorEntity, { nullable: true })
  @JoinTable()
  modelMasterColors?: ModelMasterColorEntity[];
}
