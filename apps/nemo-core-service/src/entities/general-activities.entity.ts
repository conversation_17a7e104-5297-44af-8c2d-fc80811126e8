import { Column, Entity, Join<PERSON><PERSON>umn, ManyToOne, PrimaryColumn } from 'typeorm';
import { BaseEntity } from './base.entity';
import { AutoIdField } from '../utils';
import { UserEntity } from './user.entity';
import { CompanyEntity } from './company.entity';

@Entity({ name: 'general_activities' })
export class GeneralActivitiesEntity extends BaseEntity {
  @PrimaryColumn({ type: 'varchar', length: 200 })
  generalActivityId: string = AutoIdField.basic.produce();

  @Column({ type: 'varchar', length: 200 })
  type!: string;

  @Column({ type: 'jsonb' })
  detail!: any;

  @Column({ type: 'varchar', length: 200 })
  createdBy!: string;

  @Column({ type: 'varchar', length: 200 })
  companyId!: string;

  @JoinColumn([
    {
      name: 'created_by',
      referencedColumnName: 'userKey',
    },
    {
      name: 'company_id',
      referencedColumnName: 'companyId',
    },
  ])
  @ManyToOne(() => UserEntity, (user) => user.generalActivityCreatedBy, {
    onDelete: 'CASCADE',
  })
  createdUser!: UserEntity;

  @JoinColumn([
    {
      name: 'company_id',
      referencedColumnName: 'companyId',
    },
  ])
  @ManyToOne(() => CompanyEntity, (company) => company.generalActivities, {
    onDelete: 'CASCADE',
  })
  company!: CompanyEntity;
}
