import { ViewColumn, ViewEntity } from 'typeorm';

/**
 * Readonly Table!
 * master_addresses are already optimized for search only.
 */
@ViewEntity({
  name: 'master_address_entity',
  expression: `
SELECT
  master_address_subdistrict.code AS subdistrictCode,
  zip_code AS zipcode,
  master_address_district.code AS districtCode,
  master_address_provinces.code AS provinceCode,
  json_build_object('en', master_address_subdistrict.name_en, 'th', master_address_subdistrict.name_th) AS subdistrict,
  json_build_object('en', master_address_district.name_en, 'th', master_address_district.name_th) AS district,
  json_build_object('en', master_address_provinces.name_en, 'th', master_address_provinces.name_th) AS province
FROM
  public.master_address_subdistrict
  LEFT JOIN master_address_district ON master_address_district.code = master_address_subdistrict.district_code
  LEFT JOIN master_address_provinces ON master_address_provinces.code = master_address_district.province_code;
`,
})
export class MasterAddressEntity {
  @ViewColumn({
    name: 'zipcode',
  })
  zipcode!: string;

  @ViewColumn({
    name: 'subdistrictcode',
  })
  subdistrictCode!: string;

  @ViewColumn({
    name: 'districtcode',
  })
  districtCode!: string;

  @ViewColumn({
    name: 'provincecode',
  })
  provinceCode!: string;

  // labels

  @ViewColumn({
    name: 'subdistrict',
  })
  subdistrict!: {
    en: string;
    th: string;
  };

  @ViewColumn({
    name: 'district',
  })
  district!: string;

  @ViewColumn({
    name: 'province',
  })
  province!: string;
}
