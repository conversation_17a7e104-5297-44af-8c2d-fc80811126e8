import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>To<PERSON>ne, PrimaryColumn } from 'typeorm';
import { BaseEntity } from './base.entity';
import { AutoIdField } from '../utils';
import { UserEntity } from './user.entity';
import { CompanyEntity } from './company.entity';
import { ModelMasterGradeDetail } from './model-master.entity';
import { ModelIdentifiers } from './allocation-order.entity';

export enum ModelPriceActivityType {
  FILE = 'file',
  WEB = 'web',
}

export enum ModelPriceActivityTable {
  MODEL_MASTER = 'modelMaster',
  MODEL_MASTER_FUNCTION = 'modelMasterFunction',
}

export interface modelMasterFunctionLog {
  keyCond: string;
  penalties: string;
  action?: string;
}

export interface ModelPriceActivityDetail {
  companyId: string;
  modelKey: string;
  modelMaster: {
    modelIdentifiers?: ModelIdentifiers;
    matCode?: string;
    modelImageUrl?: string;
    modelPrice?: number;
    releaseYear?: string;
    percentPurchase?: string;
    systemCodeList: string[];
    modelMasterGrades: ModelMasterGradeDetail[];
    ownerName?: string;
  };
  modelMasterFunction: modelMasterFunctionLog[];
}
@Entity({ name: 'model_price_activities' })
export class ModelPriceActivitiesEntity extends BaseEntity {
  @PrimaryColumn({ type: 'varchar', length: 200 })
  modelPriceActivityId: string = AutoIdField.basic.produce();

  @Column({ type: 'varchar', length: 200 })
  type!: ModelPriceActivityType;

  @Column({ type: 'varchar', length: 200 })
  table!: ModelPriceActivityTable;

  @Column({ type: 'jsonb' })
  detail!: any;

  @Column({ type: 'varchar', length: 200 })
  createdBy!: string;

  @Column({ type: 'varchar', length: 200 })
  companyId!: string;

  @JoinColumn([
    {
      name: 'created_by',
      referencedColumnName: 'userKey',
    },
    {
      name: 'company_id',
      referencedColumnName: 'companyId',
    },
  ])
  @ManyToOne(() => UserEntity, (user) => user.modelPriceActivityCreatedBy, {
    onDelete: 'CASCADE',
  })
  createdUser!: UserEntity;

  @JoinColumn([
    {
      name: 'company_id',
      referencedColumnName: 'companyId',
    },
  ])
  @ManyToOne(() => CompanyEntity, (company) => company.modelPriceActivities, {
    onDelete: 'CASCADE',
  })
  company!: CompanyEntity;
}
