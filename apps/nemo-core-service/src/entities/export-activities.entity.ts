import { Column, <PERSON>tity, Join<PERSON><PERSON>umn, ManyToOne, PrimaryColumn } from 'typeorm';
import { BaseEntity } from './base.entity';
import { AutoIdField } from '../utils';
import { UserEntity } from './user.entity';
import { CompanyEntity } from './company.entity';

export interface ExportActivityCondition {
  filter?: any;
  columnSlug?: string[];
}

@Entity({ name: 'export_activities' })
export class ExportActivitiesEntity extends BaseEntity {
  @PrimaryColumn({ type: 'varchar', length: 200 })
  exportActivityId: string = AutoIdField.basic.produce();

  @Column({ type: 'jsonb' })
  condition!: ExportActivityCondition;

  @Column({ type: 'varchar', length: 200 })
  downloadedBy!: string;

  @Column({ type: 'varchar', length: 200 })
  companyId!: string;

  @JoinColumn([
    {
      name: 'downloaded_by',
      referencedColumnName: 'userKey',
    },
    {
      name: 'company_id',
      referencedColumnName: 'companyId',
    },
  ])
  @ManyToOne(() => UserEntity, (user) => user.downloadedBy)
  downloadedUser?: UserEntity;

  @JoinColumn([
    {
      name: 'company_id',
      referencedColumnName: 'companyId',
    },
  ])
  @ManyToOne(() => CompanyEntity, (company) => company.exportActivities, {
    onDelete: 'CASCADE',
  })
  company!: CompanyEntity;
}
