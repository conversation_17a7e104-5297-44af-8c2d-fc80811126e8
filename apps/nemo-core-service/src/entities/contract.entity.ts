import {
  Column,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  OneToOne,
  PrimaryColumn,
} from 'typeorm';
import { BaseEntity } from './base.entity';
import { JobEntity } from './job.entity';
import { AutoIdField } from '../utils';
import { VoucherEntity } from './voucher.entity';
import { ImportedVoucherEntity } from './imported-voucher.entity';
import { EncryptDecryptObject, EncryptDecryptString } from './decorators';
import { CompanyEntity } from './company.entity';

export interface NameInfo {
  firstName: string;
  lastName: string;
  middleName: string;
  title: string;
}

export interface AddressValue {
  code: string;
  name: string;
}

export interface AddressInfo {
  subdistrict: AddressValue;
  district: AddressValue;
  province: AddressValue;
  moo: string;
  houseNumber: string;
  trok: string;
  soi: string;
  road: string;
}

export enum CustomerInfoType {
  DIP_CHIP = 'DIP_CHIP',
  IDENTITY_VERIFICATION = 'IDENTITY_VERIFICATION',
}

export interface CustomerInfo {
  type: CustomerInfoType;
  photo: string;
  identificationNumber: string;
  thaiName: NameInfo;
  engName: NameInfo;
  address: AddressInfo;
  birthDate: string;
  issueDate: string;
  expireDate: string;
  mobileNumber?: string;
  email?: string;
  rejectReason?: string;
  isOcr?: boolean;
}

export enum ContractStatus {
  DRAFT = 'draft',
  COMPLETED = 'completed',
}

export interface IPositionStampByPage {
  page: number;
  position: { x: number; y: number };
}

export interface IStampPosition {
  campaign?: { barcode: IPositionStampByPage[]; box: { width: number } };
}

@Entity({ name: 'contract' })
export class ContractEntity extends BaseEntity {
  @PrimaryColumn({ type: 'varchar', length: 200 })
  contractId: string = AutoIdField.basic.produce();

  @Column({ type: 'varchar', length: 200 })
  companyId!: string;

  @Column({ type: 'varchar', length: 200 })
  jobId!: string;

  @Column({ type: 'varchar', length: 200 })
  contractLink!: string;

  @Column({ type: 'varchar', length: 200, nullable: true })
  transactionLink?: string;

  @EncryptDecryptObject({ type: 'text' })
  customerInfo!: CustomerInfo;

  @Column({
    type: 'enum',
    enum: ContractStatus,
    default: ContractStatus.DRAFT,
  })
  status!: ContractStatus;

  @Index('idx_contract_customer_key')
  @EncryptDecryptString({ type: 'text' })
  customerKey!: string;

  @Index('idx_contract_customer_ref')
  @EncryptDecryptString({ type: 'text' })
  customerRef!: string;

  @Column({ type: 'jsonb' })
  consentList!: any[];

  @Column({ type: 'jsonb' })
  consentListValue!: any[];

  @Column({ type: 'text', nullable: true })
  version?: string | null;

  @Column({ type: 'jsonb', nullable: true })
  stampPosition?: IStampPosition | null;

  @Column({ type: 'varchar', length: 100 })
  createdBy!: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  updatedBy?: string;

  @JoinColumn([
    {
      name: 'job_id',
      referencedColumnName: 'jobId',
    },
  ])
  @OneToOne(() => JobEntity, (job) => job.jobActivities, {
    onDelete: 'CASCADE',
  })
  job!: JobEntity;

  @OneToOne(() => VoucherEntity, (voucher) => voucher.contract, {
    nullable: true,
    onDelete: 'CASCADE',
  })
  voucher?: VoucherEntity;

  @OneToOne(
    () => ImportedVoucherEntity,
    (importedVoucher) => importedVoucher.contract,
    {
      nullable: true,
      onDelete: 'CASCADE',
    },
  )
  importedVouchers?: ImportedVoucherEntity;

  @JoinColumn({
    name: 'company_id',
    referencedColumnName: 'companyId',
  })
  @ManyToOne(() => CompanyEntity, (company) => company.contracts, {
    onDelete: 'CASCADE',
  })
  company!: CompanyEntity;

  @Column({ type: 'timestamptz', nullable: true })
  UpdatingVoucherAt?: Date | null;
}
