import { Column, <PERSON>ti<PERSON>, Join<PERSON><PERSON>umn, ManyToOne, PrimaryColumn } from 'typeorm';
import { BaseEntity } from './base.entity';
import { AutoIdField } from '../utils';
import { CompanyEntity } from './company.entity';

export interface SendEmailInfo {
  from: any;
  to: any;
}

export enum EmailActivitiesType {
  CONTRACT = 'CONTRACT',
  ISSUE_REPORT = 'ISSUE_REPORT',
  QUOTE_REQUESTED = 'QUOTE_REQUESTED',
  IDENTITY_REQUESTED = 'IDENTITY_REQUESTED',
  IMPORTED_VOUCHER_OUT_OF_STOCK = 'IMPORTED_VOUCHER_OUT_OF_STOCK',
}

export interface EmailActivityDetail {
  contractLink?: string;
  transactionLink?: string;
  sendEmailInfo: SendEmailInfo;
}

export interface EmailActivitiesInfo {
  refId: string;
  companyId: string;
  detail: EmailActivityDetail;
  type: EmailActivitiesType;
  errorMsg?: string;
}

@Entity({ name: 'email_activities' })
export class EmailActivitiesEntity extends BaseEntity {
  @PrimaryColumn({ type: 'varchar', length: 200 })
  activityId: string = AutoIdField.basic.produce();

  @Column({ type: 'varchar', length: 200 })
  refId!: string;

  @Column({ type: 'varchar', length: 200 })
  companyId!: string;

  @Column({ type: 'jsonb' })
  detail!: EmailActivityDetail;

  @Column({ type: 'varchar', length: 100 })
  type!: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  errorMsg?: string;

  @JoinColumn([
    {
      name: 'company_id',
      referencedColumnName: 'companyId',
    },
  ])
  @ManyToOne(() => CompanyEntity, (company) => company.emailActivities, {
    onDelete: 'CASCADE',
  })
  company!: CompanyEntity;
}
