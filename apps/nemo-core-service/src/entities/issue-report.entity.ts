import {
  <PERSON>umn,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>To<PERSON>ne,
  PrimaryColumn,
  Index,
} from 'typeorm';
import { BaseEntity } from './base.entity';
import { CompanyEntity } from './company.entity';
import { UserEntity } from './user.entity';
import { BranchEntity } from './branch.entity';
import { JobEntity } from './job.entity';

export interface EmailChange {
  firstName: string;
  lastName: string;
  phoneNumber: string;
  currentEmail: string;
  newEmail: string;
  imageIdCardUrlPath: string;
  imageEmailCaptureUrlPath: string;
}

export enum IssueReportStatus {
  PENDING = '00_PENDING',
  APPROVED = '10_APPROVED',
  REJECTED = '20_REJECTED',
}

export enum IssueReportType {
  EMAIL_CHANGE = 'EMAIL_CHANGE',
  OTHER = 'OTHER',
}
@Entity({ name: 'issue_report' })
export class IssueReportEntity extends BaseEntity {
  @PrimaryColumn({ type: 'varchar', length: 200 })
  issueReportId!: string;

  @PrimaryColumn({ type: 'varchar', length: 200 })
  companyId!: string;

  @Column({ type: 'varchar', length: 200 })
  issueReportType!: IssueReportType;

  @Column({ type: 'jsonb' })
  data!: EmailChange;

  @Index()
  @Column({ type: 'varchar' })
  status!: IssueReportStatus;

  @Column({ type: 'varchar', length: 200, nullable: true })
  remark?: string;

  @Column({ type: 'varchar', length: 200 })
  createdBy!: string;

  @Column({ type: 'varchar', length: 200, nullable: true })
  reviewedBy?: string;

  @Column({ name: 'reviewed_at', type: 'timestamptz', nullable: true })
  reviewedAt?: Date;

  @Column({ type: 'varchar', length: 100 })
  creatorPhoneNumber!: string;

  @Column({ name: 'job_id', type: 'varchar', length: 200, nullable: true })
  jobId?: string;

  @Column({ type: 'varchar', length: 200, nullable: true })
  branchId?: string;

  @JoinColumn([
    {
      name: 'company_id',
      referencedColumnName: 'companyId',
    },
  ])
  @ManyToOne(() => CompanyEntity, (company) => company.issueReport, {
    onDelete: 'CASCADE',
  })
  company!: CompanyEntity;

  @JoinColumn([
    {
      name: 'company_id',
      referencedColumnName: 'companyId',
    },
    {
      name: 'branch_id',
      referencedColumnName: 'branchId',
    },
  ])
  @ManyToOne(() => BranchEntity, (branch) => branch.issueReport, {
    onDelete: 'CASCADE',
  })
  branch?: BranchEntity;

  @JoinColumn([
    {
      name: 'created_by',
      referencedColumnName: 'userKey',
    },
    {
      name: 'company_id',
      referencedColumnName: 'companyId',
    },
  ])
  @ManyToOne(() => UserEntity, (user) => user.issueReportCreatedBy)
  createdUser?: UserEntity;

  @JoinColumn([
    {
      name: 'reviewed_by',
      referencedColumnName: 'userKey',
    },
    {
      name: 'company_id',
      referencedColumnName: 'companyId',
    },
  ])
  @ManyToOne(() => UserEntity, (user) => user.issueReportReviewedBy)
  reviewedUser?: UserEntity;

  @JoinColumn([
    {
      name: 'job_id',
      referencedColumnName: 'jobId',
    },
  ])
  @ManyToOne(() => JobEntity, (job) => job.issueReport)
  job?: JobEntity;
}
