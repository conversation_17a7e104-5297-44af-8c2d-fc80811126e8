import {
  <PERSON>umn,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>um<PERSON>,
  ManyToOne,
  OneToMany,
  PrimaryColumn,
} from 'typeorm';
import { ColumnDecimalTransformer } from './transformer/column-decimal.transformer';
import { BaseEntity } from './base.entity';
import { AutoIdField } from '../utils';
import { CompanyEntity } from './company.entity';
import { EstimationActivitiesEntity } from './estimation-activities.entity';

export enum ILegalDocumentType {
  PDPA = 'PDPA',
  TERM = 'TermAndCondition',
}

@Entity('legal_document')
export class LegalDocumentEntity extends BaseEntity {
  // --- primary
  @PrimaryColumn({ type: 'varchar', length: 200 })
  id: string = AutoIdField.basic.produce();

  @PrimaryColumn({ type: 'varchar', length: 200 })
  companyId!: string;

  // --- require field
  @Column({ type: 'enum', enum: ILegalDocumentType })
  type!: ILegalDocumentType;

  @Column({
    type: 'decimal',
    precision: 7,
    scale: 2,
    transformer: new ColumnDecimalTransformer(),
  })
  version!: number;

  @Column({ type: 'varchar', length: 1000 })
  contentUrlTh!: string;

  @Column({ type: 'varchar', length: 1000 })
  contentUrlEn!: string;

  // --- join require
  @JoinColumn({
    name: 'company_id',
    referencedColumnName: 'companyId',
  })
  @ManyToOne(() => CompanyEntity, (company) => company.legalDocuments, {
    onDelete: 'CASCADE',
  })
  company!: CompanyEntity;

  // --- join
  @OneToMany(
    () => EstimationActivitiesEntity,
    (estimationActivity) => estimationActivity.legalDocument,
    {
      nullable: true,
    },
  )
  estimationActivities?: EstimationActivitiesEntity[];
}
