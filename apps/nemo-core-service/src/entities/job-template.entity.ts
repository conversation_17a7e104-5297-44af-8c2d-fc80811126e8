import {
  <PERSON>umn,
  <PERSON><PERSON>ty,
  <PERSON>in<PERSON><PERSON><PERSON>n,
  ManyToOne,
  OneToMany,
  PrimaryColumn,
} from 'typeorm';
import { BaseEntity } from './base.entity';
import { CompanyEntity } from './company.entity';
import { ModelMasterEntity } from './model-master.entity';

@Entity({ name: 'job_template' })
export class JobTemplateEntity extends BaseEntity {
  @PrimaryColumn({ type: 'varchar', length: 200 })
  companyId!: string;

  @PrimaryColumn({ type: 'varchar', length: 200, default: 'v1' })
  templateId!: string;

  @Column({ type: 'jsonb', nullable: true })
  template?: any;

  @Column({ type: 'varchar', length: 100 })
  createdBy!: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  updatedBy?: string;

  @JoinColumn({
    name: 'company_id',
    referencedColumnName: 'companyId',
  })
  @ManyToOne(() => CompanyEntity, (company) => company.users, {
    onDelete: 'CASCADE',
  })
  company!: CompanyEntity;

  @OneToMany(() => ModelMasterEntity, (model) => model.jobTemplate, {
    nullable: true,
  })
  modelMasters?: ModelMasterEntity[];
}
