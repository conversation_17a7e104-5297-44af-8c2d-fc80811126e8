import {
  Column,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  OneToOne,
  PrimaryColumn,
} from 'typeorm';
import { BaseEntity } from './base.entity';
import { AutoIdField } from '../utils';
import { ContractEntity, CustomerInfo } from './contract.entity';
import { ColumnDecimalTransformer } from './transformer';
import { EncryptDecryptObject } from './decorators';
import { CompanyEntity } from './company.entity';
import { BranchEntity } from './branch.entity';
import { UserEntity } from './user.entity';

export enum RemoteActivationStatus {
  ACTIVATE = 'activate',
  DEACTIVATE = 'deactivate',
  USED = 'used',
}

export enum VerificationStatus {
  REQUESTED = 'requested',
  REJECTED = 'rejected',
  VERIFIED = 'verified',
  IDLE = 'idle',
}

@Entity({ name: 'voucher' })
export class VoucherEntity extends BaseEntity {
  @Index({
    unique: true,
  })
  @PrimaryColumn({ type: 'varchar', length: 200 })
  voucherId: string = AutoIdField.basic.produce();

  @Column({ type: 'varchar', length: 200 })
  contractId!: string;

  @Column({ type: 'varchar', length: 200 })
  companyId!: string;

  @Index('idx_contract_redemption_code', {
    unique: true,
  })
  @Column({ type: 'varchar', length: 200 })
  redemptionCode!: string;

  @Column({ type: 'varchar', length: 100 })
  remoteServiceType!: string;

  @Column({
    type: 'enum',
    enum: RemoteActivationStatus,
    default: RemoteActivationStatus.DEACTIVATE,
  })
  remoteActivationStatus!: RemoteActivationStatus;

  @Column({
    type: 'enum',
    enum: VerificationStatus,
    default: VerificationStatus.REQUESTED,
  })
  verificationStatus!: VerificationStatus;

  @Column({
    type: 'decimal',
    precision: 8,
    scale: 2,
    default: 0,
    transformer: new ColumnDecimalTransformer(),
  })
  voucherValue!: number;

  @EncryptDecryptObject({ type: 'text' })
  customerInfo!: CustomerInfo;

  @JoinColumn([
    {
      name: 'contract_id',
      referencedColumnName: 'contractId',
    },
  ])
  @OneToOne(() => ContractEntity, (contract) => contract.voucher, {
    onDelete: 'CASCADE',
  })
  contract!: ContractEntity;

  @JoinColumn({
    name: 'company_id',
    referencedColumnName: 'companyId',
  })
  @ManyToOne(() => CompanyEntity, (company) => company.vouchers, {
    onDelete: 'CASCADE',
  })
  company!: CompanyEntity;

  @ManyToOne(() => BranchEntity, (branch) => branch.vouchers, {
    onDelete: 'CASCADE',
    nullable: true,
  })
  verificationBranch?: BranchEntity;

  @ManyToOne(() => UserEntity, (user) => user.vouchersRequester, {
    onDelete: 'CASCADE',
    nullable: true,
  })
  requesterUser?: UserEntity;
}
