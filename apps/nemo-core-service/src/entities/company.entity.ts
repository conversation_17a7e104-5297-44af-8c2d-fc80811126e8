import { Column, Entity, OneToMany, PrimaryColumn } from 'typeorm';
import { BaseEntity } from './base.entity';
import { UserEntity } from './user.entity';
import { BranchEntity } from './branch.entity';
import { JobTemplateEntity } from './job-template.entity';
import { ModelMasterEntity } from './model-master.entity';
import { IS_DEV, defaultValue } from '../utils';
import { VoucherEntity } from './voucher.entity';
import { ImportedVoucherEntity } from './imported-voucher.entity';
import { ContractEntity } from './contract.entity';
import { DeliveryOrderEntity } from './delivery-order.entity';
import { JobEntity } from './job.entity';
import { EmailActivitiesEntity } from './email-activities.entity';
import { MasterLovEntity } from './master-lov.entity';
import { ExportActivitiesEntity } from './export-activities.entity';
import { ConfigActivitiesEntity } from './config-activities.entity';
import { AllocationOrderEntity } from './allocation-order.entity';
import { EstimationActivitiesEntity } from './estimation-activities.entity';
import { ModelMasterColorEntity } from './model-master-color.entity';
import { LegalDocumentEntity } from './legal-document.entity';
import { ModelChecklistEntity } from './model-checklist.entity';
import { GeneralActivitiesEntity } from './general-activities.entity';
import { ModelPriceActivitiesEntity } from './model-price-activities.entity';
import { CampaignEntity } from './campaign.entity';
import { CampaignRedemptionCodeEntity } from './campaign-redemption-code.entity';
import { IssueReportEntity } from './issue-report.entity';
import { RoleEntity } from './role.entity';
import { PermissionEntity } from './permission.entity';
import { RolePermissionEntity } from './role-permission.entity';
import { UserRoleBranchEntity } from './user-role-branch.entity';
import { PermissionGroupEntity } from './permission-group.entity';

@Entity({ name: 'company' })
export class CompanyEntity extends BaseEntity {
  @PrimaryColumn({ type: 'varchar', length: 200 })
  companyId!: string;

  @Column({ type: 'varchar', length: 200 })
  title!: string;

  @Column({ type: 'varchar', length: 200, nullable: true })
  logoUrl?: string;

  @Column({ type: 'varchar', length: 200, nullable: true })
  companyEmail?: string;

  @Column({ type: 'varchar', length: 200, nullable: true })
  contractEmail?: string;

  @Column({ type: 'varchar', length: 200 })
  createdBy!: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  userKeyClaimFnName?: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  empUploadMapperFnName?: string;

  @OneToMany(() => UserEntity, (user) => user.company, {
    nullable: true,
  })
  users?: UserEntity[];

  @OneToMany(() => VoucherEntity, (voucher) => voucher.company, {
    nullable: true,
  })
  vouchers?: VoucherEntity[];

  @OneToMany(
    () => ImportedVoucherEntity,
    (importedVoucher) => importedVoucher.company,
    {
      nullable: true,
    },
  )
  importedVouchers?: ImportedVoucherEntity[];

  @OneToMany(() => ContractEntity, (contract) => contract.company, {
    nullable: true,
  })
  contracts?: ContractEntity[];

  @OneToMany(() => BranchEntity, (branch) => branch.company, {
    nullable: true,
  })
  branches?: BranchEntity[];

  @OneToMany(() => JobTemplateEntity, (jobTemplate) => jobTemplate.company, {
    nullable: true,
  })
  jobTemplates?: JobTemplateEntity[];

  @OneToMany(() => JobEntity, (job) => job.company, {
    nullable: true,
  })
  jobs?: JobEntity[];

  @OneToMany(() => ModelMasterEntity, (model) => model.company, {
    nullable: true,
  })
  modelMasters?: ModelMasterEntity[];

  @OneToMany(
    () => DeliveryOrderEntity,
    (deliveryOrder) => deliveryOrder.company,
    {
      nullable: true,
    },
  )
  deliveryOrders?: DeliveryOrderEntity[];

  @OneToMany(
    () => AllocationOrderEntity,
    (allocationOrder) => allocationOrder.company,
    {
      nullable: true,
    },
  )
  allocationOrders?: AllocationOrderEntity[];

  @OneToMany(
    () => EstimationActivitiesEntity,
    (estimationActivity) => estimationActivity.company,
    {
      nullable: true,
    },
  )
  estimationActivities?: EstimationActivitiesEntity[];

  @OneToMany(() => ModelMasterColorEntity, (color) => color.company, {
    nullable: true,
  })
  modelMasterColors?: ModelMasterColorEntity[];

  @OneToMany(() => LegalDocumentEntity, (doc) => doc.company, {
    nullable: true,
  })
  legalDocuments?: LegalDocumentEntity[];

  @OneToMany(() => ModelChecklistEntity, (checklist) => checklist.company, {
    nullable: true,
  })
  modelMasterChecklist?: ModelChecklistEntity[];

  @OneToMany(
    () => EmailActivitiesEntity,
    (emailActivities) => emailActivities.company,
    {
      nullable: true,
    },
  )
  emailActivities?: EmailActivitiesEntity[];

  @OneToMany(() => MasterLovEntity, (masterLov) => masterLov.company, {
    nullable: true,
  })
  masterLov?: MasterLovEntity[];

  @OneToMany(
    () => ExportActivitiesEntity,
    (exportActivities) => exportActivities.company,
    {
      nullable: true,
    },
  )
  exportActivities?: ExportActivitiesEntity[];

  @OneToMany(
    () => ConfigActivitiesEntity,
    (configActivities) => configActivities.company,
    {
      nullable: true,
    },
  )
  configActivities?: ExportActivitiesEntity[];

  @OneToMany(
    () => GeneralActivitiesEntity,
    (generalActivities) => generalActivities.company,
    {
      nullable: true,
    },
  )
  generalActivities?: GeneralActivitiesEntity[];

  @OneToMany(
    () => ModelPriceActivitiesEntity,
    (modelPriceActivities) => modelPriceActivities.company,
    {
      nullable: true,
    },
  )
  modelPriceActivities?: ModelPriceActivitiesEntity[];

  @OneToMany(() => CampaignEntity, (campaign) => campaign.company, {
    nullable: true,
  })
  campaigns?: CampaignEntity[];

  @OneToMany(
    () => CampaignRedemptionCodeEntity,
    (campaignRedemptionCode) => campaignRedemptionCode.company,
    {
      nullable: true,
    },
  )
  campaignRedemptionCodes?: CampaignRedemptionCodeEntity[];

  @OneToMany(() => IssueReportEntity, (issueReport) => issueReport.company, {
    nullable: true,
  })
  issueReport?: IssueReportEntity[];

  @OneToMany(() => RoleEntity, (role) => role.company, {
    nullable: true,
  })
  roles?: RoleEntity[];

  @OneToMany(() => PermissionEntity, (permission) => permission.company, {
    nullable: true,
  })
  permissions?: PermissionEntity[];

  @OneToMany(
    () => PermissionGroupEntity,
    (permissionGroup) => permissionGroup.company,
    {
      nullable: true,
    },
  )
  permissionGroups?: PermissionEntity[];

  @OneToMany(
    () => RolePermissionEntity,
    (rolePermission) => rolePermission.company,
    {
      nullable: true,
    },
  )
  rolePermissions?: RolePermissionEntity[];

  @OneToMany(
    () => UserRoleBranchEntity,
    (userRoleBranch) => userRoleBranch.company,
    {
      nullable: true,
    },
  )
  userRoleBranch?: UserRoleBranchEntity[];

  get logoPath() {
    const pathLogo = IS_DEV
      ? /\/nemo-assets\/asset\/(.*)/.exec(defaultValue(this.logoUrl, ''))?.[1]
      : /\/asset\/(.*)/.exec(defaultValue(this.logoUrl, ''))?.[1];

    if (pathLogo) {
      return 'asset/' + pathLogo;
    }
    return '';
  }
}
