import {
  Column,
  <PERSON><PERSON><PERSON>,
  <PERSON>in<PERSON><PERSON>umn,
  ManyToOne,
  OneToMany,
  PrimaryColumn,
} from 'typeorm';
import { ColumnDecimalTransformer } from './transformer/column-decimal.transformer';
import { BaseEntity } from './base.entity';
import { CompanyEntity } from './company.entity';
import { JobEntity } from './job.entity';
import { DeliveryOrderEntity } from './delivery-order.entity';
import { VoucherEntity } from './voucher.entity';
import { AllocationOrderEntity } from './allocation-order.entity';
import { EstimationActivitiesEntity } from './estimation-activities.entity';
import { IssueReportEntity } from './issue-report.entity';
import { UserRoleBranchEntity } from './user-role-branch.entity';

export enum branchType {
  SHOP = 'Shop',
  WAREHOUSE = 'Warehouse',
  DEALER = 'Dealer',
  ADMIN = 'Admin',
  PARTNER = 'Partner',
}
@Entity({ name: 'branch' })
export class BranchEntity extends BaseEntity {
  @PrimaryColumn({ type: 'varchar', length: 200 })
  companyId!: string;

  @PrimaryColumn({ type: 'varchar', length: 200 })
  branchId!: string;

  @Column({ type: 'varchar', length: 200 })
  title!: string;

  @Column({ type: 'varchar', length: 200, default: '' })
  titleEn!: string;

  @Column({ type: 'varchar' })
  branchType!: branchType;

  @Column({ type: 'varchar', length: 1000, nullable: true })
  addressTh?: string;

  @Column({ type: 'varchar', length: 1000, nullable: true })
  addressEn?: string;

  @Column({ type: 'varchar', length: 120, nullable: true })
  subDistrictTh?: string;

  @Column({ type: 'varchar', length: 120, nullable: true })
  subDistrictEn?: string;

  @Column({ type: 'varchar', length: 120, nullable: true })
  districtTh?: string;

  @Column({ type: 'varchar', length: 120, nullable: true })
  districtEn?: string;

  @Column({ type: 'varchar', length: 120, nullable: true })
  provinceTh?: string;

  @Column({ type: 'varchar', length: 120, nullable: true })
  provinceEn?: string;

  @Column({ type: 'varchar', length: 20, nullable: true })
  zipCode?: string;

  @Column({ type: 'varchar', length: 10, nullable: true })
  costCenter?: string;

  @Column({
    type: 'decimal',
    precision: 10,
    scale: 7,
    default: null,
    transformer: new ColumnDecimalTransformer(),
    nullable: true,
  })
  latitude?: number | null;

  @Column({
    type: 'decimal',
    precision: 10,
    scale: 7,
    default: null,
    transformer: new ColumnDecimalTransformer(),
    nullable: true,
  })
  longitude?: number | null;

  @Column({ type: 'varchar', length: 1000, nullable: true })
  imageUrl?: string;

  @Column({ type: 'varchar', length: 30, nullable: true })
  partnerName?: string;

  @JoinColumn({
    name: 'company_id',
    referencedColumnName: 'companyId',
  })
  @ManyToOne(() => CompanyEntity, (company) => company.users, {
    onDelete: 'CASCADE',
  })
  company!: CompanyEntity;

  @OneToMany(() => JobEntity, (job) => job.branch, {
    nullable: true,
  })
  jobs?: JobEntity[];

  @OneToMany(
    () => DeliveryOrderEntity,
    (deliveryOrder) => deliveryOrder.branch,
    {
      nullable: true,
    },
  )
  deliveryOrders?: DeliveryOrderEntity[];

  @OneToMany(() => VoucherEntity, (voucher) => voucher.verificationBranch, {
    nullable: true,
  })
  vouchers?: VoucherEntity[];

  @OneToMany(
    () => AllocationOrderEntity,
    (allocationOrder) => allocationOrder.fromBranch,
    {
      nullable: true,
    },
  )
  fromAllocationOrders?: AllocationOrderEntity[];

  @OneToMany(
    () => AllocationOrderEntity,
    (allocationOrder) => allocationOrder.toBranch,
    {
      nullable: true,
    },
  )
  toAllocationOrders?: AllocationOrderEntity[];

  @OneToMany(
    () => EstimationActivitiesEntity,
    (estimationActivity) => estimationActivity.branch,
    {
      nullable: true,
    },
  )
  estimationActivities?: EstimationActivitiesEntity[];

  @OneToMany(() => IssueReportEntity, (issueReport) => issueReport.branch, {
    nullable: true,
  })
  issueReport?: IssueReportEntity[];

  @OneToMany(
    () => UserRoleBranchEntity,
    (userRoleBranch) => userRoleBranch.branch,
    {
      nullable: true,
    },
  )
  userRoleBranch?: UserRoleBranchEntity[];
}
