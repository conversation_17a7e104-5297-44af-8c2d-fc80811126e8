import {
  <PERSON>um<PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>umn,
  ManyToOne,
  OneToMany,
  PrimaryColumn,
  OneToOne,
} from 'typeorm';
import { BaseEntity } from './base.entity';
import { CompanyEntity } from './company.entity';
import { JobEntity } from './job.entity';
import { EncryptDecryptString } from './decorators';
import { CampaignEntity } from './campaign.entity';
import { UserEntity } from './user.entity';

@Entity({ name: 'campaign_redemption_code' })
export class CampaignRedemptionCodeEntity extends BaseEntity {
  @PrimaryColumn({ type: 'varchar', length: 200 })
  companyId!: string;

  @Column({ name: 'campaign_code', type: 'varchar', length: 200 })
  campaignCode!: string;

  @EncryptDecryptString({ type: 'text', primary: true })
  redemptionCode!: string;

  @Column({ type: 'varchar', length: 50, nullable: true })
  supporter?: string;

  @Column('text', { array: true })
  grade!: string[];

  @Column({ type: 'decimal' })
  value!: number;

  @Column({ type: 'int' })
  order!: number;

  @Column({ type: 'varchar', length: 200, nullable: true })
  jobId?: string | null;

  @Column({ type: 'varchar', length: 200 })
  createdBy!: string;

  @JoinColumn([
    {
      name: 'company_id',
      referencedColumnName: 'companyId',
    },
  ])
  @ManyToOne(
    () => CompanyEntity,
    (company) => company.campaignRedemptionCodes,
    {
      onDelete: 'CASCADE',
    },
  )
  company!: CompanyEntity;

  @JoinColumn([
    {
      name: 'campaign_code',
      referencedColumnName: 'campaignCode',
    },
    {
      name: 'company_id',
      referencedColumnName: 'companyId',
    },
  ])
  @ManyToOne(
    () => CampaignEntity,
    (campaign) => campaign.campaignRedemptionCode,
  )
  campaign!: CampaignEntity;

  @JoinColumn([
    {
      name: 'created_by',
      referencedColumnName: 'userKey',
    },
    {
      name: 'company_id',
      referencedColumnName: 'companyId',
    },
  ])
  @ManyToOne(() => UserEntity, (user) => user.campaignRedemptionCodeCreatedBy)
  createdUser?: UserEntity;

  @JoinColumn([
    {
      name: 'job_id',
      referencedColumnName: 'jobId',
    },
    {
      name: 'company_id',
      referencedColumnName: 'companyId',
    },
  ])
  @ManyToOne(() => JobEntity, (job) => job.campaignRedemptionCode)
  job?: JobEntity;
}
