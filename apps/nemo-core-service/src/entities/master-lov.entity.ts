import { Column, Entity, Join<PERSON><PERSON>umn, ManyToOne, PrimaryColumn } from 'typeorm';
import { BaseEntity } from './base.entity';
import { CompanyEntity } from './company.entity';
import { MasterLovAdditionalValue } from 'contracts';

export enum Locale {
  TH = 'th',
  EN = 'en',
}

@Entity({ name: 'master_lov' })
export class MasterLovEntity extends BaseEntity {
  @PrimaryColumn({ type: 'varchar', length: 200 })
  companyId!: string;

  @PrimaryColumn({ type: 'varchar', length: 200 })
  type!: string;

  @PrimaryColumn({ type: 'varchar', length: 2 })
  locale!: Locale;

  @PrimaryColumn({ type: 'varchar', length: 200 })
  value!: string;

  @Column({ type: 'varchar', length: 200 })
  label!: string;

  @Column({ type: 'numeric' })
  sortIndex!: number;

  @Column({ type: 'jsonb', nullable: true })
  additionalValue?: MasterLovAdditionalValue;

  @JoinColumn({
    name: 'company_id',
    referencedColumnName: 'companyId',
  })
  @ManyToOne(() => CompanyEntity, (company) => company.masterLov, {
    onDelete: 'CASCADE',
  })
  company!: CompanyEntity;
}
