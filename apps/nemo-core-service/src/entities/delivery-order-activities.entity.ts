import { Column, <PERSON>tity, Join<PERSON><PERSON>umn, ManyToOne, PrimaryColumn } from 'typeorm';
import { BaseEntity } from './base.entity';
import { AutoIdField, DataChangeInfo } from '../utils';
import { DeliveryOrderEntity } from './delivery-order.entity';

export enum DeliveryOrderActivitiesType {
  CREATE = 'create',
  UPDATE = 'update',
}

export interface DeliveryOrderActivityDetail {
  summary: string;
  branchId: string;
  status: string;
  dataChange?: Record<string, DataChangeInfo>;
  dataChangeSlug?: string | null;
}

@Entity({ name: 'delivery_order_activities' })
export class DeliveryOrderActivitiesEntity extends BaseEntity {
  @PrimaryColumn({ type: 'varchar', length: 200 })
  activityId: string = AutoIdField.basic.produce();

  @Column({ type: 'varchar', length: 200 })
  deliveryOrderId!: string;

  @Column({ type: 'varchar', length: 200 })
  companyId!: string;

  @Column({ type: 'varchar', length: 200 })
  type!: string;

  @Column({ type: 'jsonb' })
  detail!: DeliveryOrderActivityDetail;

  @Column({ type: 'varchar', length: 100 })
  createdBy!: string;

  @JoinColumn([
    {
      name: 'delivery_order_id',
      referencedColumnName: 'deliveryOrderId',
    },
  ])
  @ManyToOne(
    () => DeliveryOrderEntity,
    (deliveryOrder) => deliveryOrder.deliveryOrderActivities,
    {
      onDelete: 'CASCADE',
    },
  )
  deliveryOrder!: DeliveryOrderEntity;
}
