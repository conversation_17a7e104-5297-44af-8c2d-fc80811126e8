import {
  Column,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  OneToOne,
  PrimaryColumn,
} from 'typeorm';
import { BaseEntity } from './base.entity';
import { AutoIdField } from '../utils';
import { ContractEntity } from './contract.entity';
import { ColumnDecimalTransformer } from './transformer';
import { EncryptDecryptString } from './decorators';
import { CompanyEntity } from './company.entity';
import { UserEntity } from './user.entity';

@Entity({ name: 'imported_voucher' })
export class ImportedVoucherEntity extends BaseEntity {
  @Index({
    unique: true,
  })
  @PrimaryColumn({ type: 'varchar', length: 200 })
  voucherId: string = AutoIdField.basic.produce();

  @Column({ type: 'varchar', length: 200, nullable: true })
  contractId?: string;

  @Column({ type: 'varchar', length: 200 })
  companyId!: string;

  @Index('idx_contract_redemption_code_imported_voucher', {
    unique: true,
  })
  @EncryptDecryptString({ type: 'text' })
  redemptionCode!: string;

  @Column({
    type: 'decimal',
    precision: 8,
    scale: 2,
    default: 0,
    transformer: new ColumnDecimalTransformer(),
  })
  voucherValue!: number;

  @Column({ type: 'varchar', length: 200, default: '' })
  otherPaymentCode!: string;

  @Column({ type: 'varchar', length: 200, nullable: true })
  uploadedBy?: string;

  @JoinColumn([
    {
      name: 'contract_id',
      referencedColumnName: 'contractId',
    },
  ])
  @OneToOne(() => ContractEntity, (contract) => contract.importedVouchers, {
    onDelete: 'CASCADE',
    nullable: true,
  })
  contract?: ContractEntity;

  @JoinColumn({
    name: 'company_id',
    referencedColumnName: 'companyId',
  })
  @ManyToOne(() => CompanyEntity, (company) => company.importedVouchers, {
    onDelete: 'CASCADE',
  })
  company!: CompanyEntity;

  @JoinColumn([
    {
      name: 'uploaded_by',
      referencedColumnName: 'userKey',
    },
    {
      name: 'company_id',
      referencedColumnName: 'companyId',
    },
  ])
  @ManyToOne(() => UserEntity, (user) => user.vouchersUploadUser, {
    onDelete: 'CASCADE',
    nullable: true,
  })
  uploaderUser?: UserEntity;
}
