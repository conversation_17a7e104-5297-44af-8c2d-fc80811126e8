import {
  Column,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  PrimaryColumn,
} from 'typeorm';
import { BaseEntity } from './base.entity';
import { AutoIdField } from '../utils';
import { UserEntity } from './user.entity';
import { CompanyEntity } from './company.entity';

export enum ConfigType {
  OPERATION_COST = 'operation_cost',
  MARKETING = 'marketing',
}

@Entity({ name: 'config_activities' })
export class ConfigActivitiesEntity extends BaseEntity {
  @Index({
    unique: true,
  })
  @PrimaryColumn({ type: 'varchar', length: 200 })
  configActivityId: string = AutoIdField.basic.produce();

  @Column({ type: 'varchar', length: 200 })
  companyId!: string;

  @Column({ type: 'varchar' })
  configType!: ConfigType;

  @Column({ type: 'jsonb' })
  configValue!: any;

  @Column({ type: 'varchar', length: 200 })
  configBy!: string;

  @Column({ type: 'timestamptz' })
  configAt!: Date;

  @JoinColumn([
    {
      name: 'config_by',
      referencedColumnName: 'userKey',
    },
    {
      name: 'company_id',
      referencedColumnName: 'companyId',
    },
  ])
  @ManyToOne(() => UserEntity, (user) => user.configBy)
  configUser?: UserEntity;

  @JoinColumn([
    {
      name: 'company_id',
      referencedColumnName: 'companyId',
    },
  ])
  @ManyToOne(() => CompanyEntity, (company) => company.configActivities, {
    onDelete: 'CASCADE',
  })
  company!: CompanyEntity;
}
