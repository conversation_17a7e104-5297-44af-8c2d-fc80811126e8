import {
  <PERSON>umn,
  <PERSON>ti<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>umn,
  ManyToOne,
  OneToMany,
  PrimaryColumn,
} from 'typeorm';
import { BaseEntity } from './base.entity';
import { AutoIdField } from '../utils';
import { CompanyEntity } from './company.entity';
import { EstimationActivitiesEntity } from './estimation-activities.entity';
import { JobEntity } from './job.entity';

@Entity('model_master_color')
export class ModelMasterColorEntity extends BaseEntity {
  // --- primary
  @PrimaryColumn({ type: 'varchar', length: 200 })
  id: string = AutoIdField.basic.produce();

  @PrimaryColumn({ type: 'varchar', length: 200 })
  companyId!: string;

  // --- require field
  @Column({ type: 'varchar', length: 200 })
  nameTh!: string;

  @Column({ type: 'varchar', length: 200 })
  nameEn!: string;

  // --- join require
  @JoinColumn({
    name: 'company_id',
    referencedColumnName: 'companyId',
  })
  @ManyToOne(() => CompanyEntity, (company) => company.modelMasterColors, {
    onDelete: 'CASCADE',
  })
  company!: CompanyEntity;

  // --- join
  @OneToMany(
    () => EstimationActivitiesEntity,
    (estimationActivity) => estimationActivity.modelMasterColor,
    {
      nullable: true,
    },
  )
  estimationActivities?: EstimationActivitiesEntity[];

  @OneToMany(() => JobEntity, (job) => job.color, {
    nullable: true,
  })
  jobs?: JobEntity[];
}
