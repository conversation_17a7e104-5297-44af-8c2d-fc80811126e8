import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  ManyToOne,
  OneToMany,
  PrimaryColumn,
} from 'typeorm';
import { BaseEntity } from './base.entity';
import { CompanyEntity } from './company.entity';
import { JobEntity } from './job.entity';
import { DeliveryOrderEntity } from './delivery-order.entity';
import { VoucherEntity } from './voucher.entity';
import { ImportedVoucherEntity } from './imported-voucher.entity';
import { ExportActivitiesEntity } from './export-activities.entity';
import { ConfigActivitiesEntity } from './config-activities.entity';
import { AllocationOrderEntity } from './allocation-order.entity';
import { GeneralActivitiesEntity } from './general-activities.entity';
import { ModelPriceActivitiesEntity } from './model-price-activities.entity';
import { CampaignEntity } from './campaign.entity';
import { CampaignRedemptionCodeEntity } from './campaign-redemption-code.entity';
import { IssueReportEntity } from './issue-report.entity';
import { RoleEntity } from './role.entity';
import { UserRoleBranchEntity } from './user-role-branch.entity';
export interface UserRole {
  branchId?: string;
  role: string[];
}

export interface PermissionBranch {
  branchId: string;
  permission: string[];
}

export interface UserPosition {
  branchId?: string;
  position: string[];
}

@Entity({ name: 'user' })
export class UserEntity extends BaseEntity {
  @PrimaryColumn({ type: 'varchar', length: 200 })
  companyId!: string;

  @PrimaryColumn({ type: 'varchar', length: 200, comment: 'identifier user' })
  userKey!: string;

  @Column({ type: 'varchar', length: 200, nullable: true })
  name?: string;

  @Column({ type: 'varchar', length: 200, nullable: true })
  nameEng?: string;

  @Column({ type: 'jsonb', nullable: true })
  position?: UserPosition[];

  @Column({ type: 'varchar', length: 200, nullable: true })
  email?: string;

  @Column({ type: 'jsonb', nullable: true })
  roles?: UserRole[];

  @Column({ type: 'timestamptz', nullable: true })
  lastLogin?: Date;

  @Column({ type: 'bool', default: true })
  isActive!: boolean;

  @Column({ type: 'varchar', length: 30, default: 'WW' })
  userType!: string;

  @JoinColumn({
    name: 'company_id',
    referencedColumnName: 'companyId',
  })
  @ManyToOne(() => CompanyEntity, (company) => company.users, {
    onDelete: 'CASCADE',
  })
  company!: CompanyEntity;

  @OneToMany(() => JobEntity, (job) => job.shopUser)
  jobsShop?: JobEntity[];

  @OneToMany(() => JobEntity, (job) => job.adminUser)
  jobsAdmin?: JobEntity[];

  @OneToMany(() => JobEntity, (job) => job.receiverUser)
  jobsReceiver?: JobEntity[];

  @OneToMany(() => JobEntity, (job) => job.aoReceiverUser)
  jobsAOReceiver?: JobEntity[];

  @OneToMany(
    () => AllocationOrderEntity,
    (allocation) => allocation.receiverUser,
  )
  aoReceiver?: AllocationOrderEntity[];

  @OneToMany(() => JobEntity, (job) => job.qcUser)
  qcBy?: JobEntity[];

  @OneToMany(() => JobEntity, (job) => job.repairedUser)
  repairedBy?: JobEntity[];

  @OneToMany(() => JobEntity, (job) => job.inspectedUser)
  inspectedBy?: JobEntity[];

  @OneToMany(() => JobEntity, (job) => job.updatedUser)
  jobUpdatedBy?: JobEntity[];

  @OneToMany(() => JobEntity, (job) => job.createdUser)
  jobCreatedBy?: JobEntity[];

  @OneToMany(() => JobEntity, (job) => job.confirmedPriceUser)
  confirmedPriceBy?: JobEntity[];

  @OneToMany(
    () => DeliveryOrderEntity,
    (deliveryOrder) => deliveryOrder.shopUser,
  )
  deliveryOrderShop?: DeliveryOrderEntity[];

  @OneToMany(
    () => DeliveryOrderEntity,
    (deliveryOrder) => deliveryOrder.senderUser,
  )
  deliveryOrderSender?: DeliveryOrderEntity[];

  @OneToMany(() => VoucherEntity, (voucher) => voucher.requesterUser)
  vouchersRequester?: VoucherEntity[];

  @OneToMany(
    () => DeliveryOrderEntity,
    (deliveryOrder) => deliveryOrder.confirmAppointmentUser,
  )
  deliveryOrderConfirmAppointmentUser?: DeliveryOrderEntity[];

  @OneToMany(
    () => DeliveryOrderEntity,
    (deliveryOrder) => deliveryOrder.editConfirmAppointmentUser,
  )
  deliveryOrderEditConfirmAppointmentUser?: DeliveryOrderEntity[];

  @OneToMany(
    () => AllocationOrderEntity,
    (allocationOrder) => allocationOrder.createdUser,
  )
  allocationOrderShop?: AllocationOrderEntity[];

  @OneToMany(
    () => AllocationOrderEntity,
    (allocationOrder) => allocationOrder.confirmAppointmentUser,
  )
  allocationOrderConfirmAppointmentUser?: AllocationOrderEntity[];

  @OneToMany(
    () => AllocationOrderEntity,
    (allocationOrder) => allocationOrder.confirmPickupUser,
  )
  allocationOrderConfirmPickupUser?: AllocationOrderEntity[];

  @OneToMany(() => ImportedVoucherEntity, (voucher) => voucher.uploaderUser)
  vouchersUploadUser?: ImportedVoucherEntity[];

  @OneToMany(
    () => ExportActivitiesEntity,
    (exportActivitiy) => exportActivitiy.downloadedUser,
  )
  downloadedBy?: ExportActivitiesEntity[];

  @OneToMany(
    () => ConfigActivitiesEntity,
    (configActivity) => configActivity.configUser,
  )
  configBy?: ConfigActivitiesEntity[];

  @OneToMany(
    () => GeneralActivitiesEntity,
    (generalActivitiy) => generalActivitiy.createdUser,
  )
  generalActivityCreatedBy?: GeneralActivitiesEntity[];

  @OneToMany(
    () => ModelPriceActivitiesEntity,
    (modelPriceActivitiy) => modelPriceActivitiy.createdUser,
  )
  modelPriceActivityCreatedBy?: ModelPriceActivitiesEntity[];

  @OneToMany(() => CampaignEntity, (campaign) => campaign.createdUser)
  campaignCreatedBy?: CampaignEntity[];

  @OneToMany(() => CampaignEntity, (campaign) => campaign.updatedUser)
  campaignUpdatedBy?: CampaignEntity[];

  @OneToMany(() => CampaignEntity, (campaign) => campaign.canceledUser)
  campaignCanceledBy?: CampaignEntity[];

  @OneToMany(
    () => CampaignRedemptionCodeEntity,
    (campaignRedemptionCode) => campaignRedemptionCode.createdUser,
  )
  campaignRedemptionCodeCreatedBy?: CampaignRedemptionCodeEntity[];

  @OneToMany(() => IssueReportEntity, (issueReport) => issueReport.createdUser)
  issueReportCreatedBy?: IssueReportEntity[];

  @OneToMany(() => IssueReportEntity, (issueReport) => issueReport.reviewedUser)
  issueReportReviewedBy?: IssueReportEntity[];

  @OneToMany(() => RoleEntity, (role) => role.createdUser)
  roleCreatedBy?: RoleEntity[];

  @OneToMany(() => RoleEntity, (role) => role.updatedUser)
  roleUpdatedBy?: RoleEntity[];

  @OneToMany(
    () => UserRoleBranchEntity,
    (userRoleBranch) => userRoleBranch.user,
  )
  userRoleBranch?: UserRoleBranchEntity[];

  @OneToMany(() => UserEntity, (user) => user.createdUser)
  createdBy?: UserEntity[];

  @JoinColumn([
    {
      name: 'created_by',
      referencedColumnName: 'userKey',
    },
    {
      name: 'company_id',
      referencedColumnName: 'companyId',
    },
  ])
  @ManyToOne(() => UserEntity, (user) => user.createdBy)
  createdUser?: UserEntity;

  @OneToMany(() => UserEntity, (user) => user.updatedUser)
  updatedBy?: UserEntity[];

  @JoinColumn([
    {
      name: 'updated_by',
      referencedColumnName: 'userKey',
    },
    {
      name: 'company_id',
      referencedColumnName: 'companyId',
    },
  ])
  @ManyToOne(() => UserEntity, (user) => user.updatedBy)
  updatedUser?: UserEntity;
}
