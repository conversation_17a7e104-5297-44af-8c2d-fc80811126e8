import {
  <PERSON>umn,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  ManyToOne,
  OneToMany,
  PrimaryColumn,
} from 'typeorm';
import { BaseEntity } from './base.entity';
import { PermissionEntity } from './permission.entity';
import { CompanyEntity } from './company.entity';
import { AutoIdField } from '../utils/autoId';
import { SiteType } from './role.entity';

@Entity({ name: 'permission_group' })
export class PermissionGroupEntity extends BaseEntity {
  @PrimaryColumn({ type: 'varchar', length: 200 })
  companyId!: string;

  @PrimaryColumn({ name: 'permission_group_id', type: 'varchar', length: 200 })
  permissionGroupId: string = AutoIdField.basic.produce();

  @Column({ type: 'varchar', length: 200 })
  label!: string;

  @Column({ type: 'int' })
  sortIndex!: number;

  @Column({ type: 'bool', default: false })
  isInMenu!: boolean;

  @Column({ type: 'varchar', length: 20 })
  type!: SiteType;

  @JoinColumn({
    name: 'company_id',
    referencedColumnName: 'companyId',
  })
  @ManyToOne(() => CompanyEntity, (company) => company.permissionGroups, {
    onDelete: 'CASCADE',
  })
  company!: CompanyEntity;

  @OneToMany(
    () => PermissionEntity,
    (permission) => permission.permissionGroup,
    {
      onDelete: 'CASCADE',
    },
  )
  permissions!: PermissionEntity[];
}
