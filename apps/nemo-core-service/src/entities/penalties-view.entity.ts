import { DataSource, ViewColumn, ViewEntity } from 'typeorm';
import { ModelMasterFunctionEntity } from './model-master-function.entity';

@ViewEntity({
  expression: (dataSource: DataSource) =>
    dataSource
      .createQueryBuilder()
      .select('modelFunc.companyId', 'company_id')
      .addSelect('modelFunc.modelKey', 'model_key')
      .addSelect(
        'jsonb_object_agg(modelFunc.functionKeyCond, modelFunc.penalties)',
        'penalties',
      )
      .from(ModelMasterFunctionEntity, 'modelFunc')
      .groupBy('modelFunc.company_id')
      .addGroupBy('modelFunc.model_key'),
})
export class PenaltiesView {
  @ViewColumn({
    name: 'company_id',
  })
  companyId!: string;

  @ViewColumn({
    name: 'model_key',
  })
  modelKey!: string;

  @ViewColumn({
    name: 'penalties',
  })
  penalties!: Record<string, string>;
}
