import {
  <PERSON>um<PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  ManyToOne,
  OneToMany,
  PrimaryColumn,
} from 'typeorm';
import { BaseEntity } from './base.entity';
import { CompanyEntity } from './company.entity';
import { SiteType } from './role.entity';
import { PermissionGroupEntity } from './permission-group.entity';
import { RolePermissionEntity } from './role-permission.entity';
import { AutoIdField } from '../utils';

@Entity({ name: 'permission' })
export class PermissionEntity extends BaseEntity {
  @PrimaryColumn({ type: 'varchar', length: 200 })
  companyId!: string;

  @PrimaryColumn({ type: 'varchar', length: 200 })
  permissionId: string = AutoIdField.basic.produce();

  @Column({ type: 'varchar', length: 200 })
  label!: string;

  @Column({ name: 'permission_group_id', type: 'varchar', length: 200 })
  permissionGroupId!: string;

  @Column({ type: 'int' })
  sortIndex!: number;

  @Column({ type: 'varchar', length: 200, nullable: true })
  iconName?: string;

  @Column({ type: 'varchar', length: 200, nullable: true })
  iconNameActive?: string;

  @Column({ type: 'bool', default: false })
  view!: boolean;

  @Column({ type: 'bool', default: false })
  create!: boolean;

  @Column({ type: 'bool', default: false })
  update!: boolean;

  @Column({ type: 'bool', default: false })
  delete!: boolean;

  @Column({ type: 'bool', default: false })
  download!: boolean;

  @Column({ type: 'bool', default: false })
  upload!: boolean;

  @JoinColumn({
    name: 'company_id',
    referencedColumnName: 'companyId',
  })
  @ManyToOne(() => CompanyEntity, (company) => company.permissions, {
    onDelete: 'CASCADE',
  })
  company!: CompanyEntity;

  @JoinColumn([
    {
      name: 'permission_group_id',
      referencedColumnName: 'permissionGroupId',
    },
    {
      name: 'company_id',
      referencedColumnName: 'companyId',
    },
  ])
  @ManyToOne(
    () => PermissionGroupEntity,
    (permissionGroup) => permissionGroup.permissions,
    {
      onDelete: 'CASCADE',
    },
  )
  permissionGroup!: PermissionGroupEntity;

  @OneToMany(
    () => RolePermissionEntity,
    (rolePermission) => rolePermission.permission,
    {
      onDelete: 'CASCADE',
    },
  )
  rolePermissions!: PermissionGroupEntity[];
}
