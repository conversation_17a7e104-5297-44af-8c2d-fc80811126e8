import { Column, <PERSON>ti<PERSON>, Jo<PERSON><PERSON><PERSON>umn, ManyToOne, PrimaryColumn } from 'typeorm';
import { BaseEntity } from './base.entity';
import { CompanyEntity } from './company.entity';
import { PermissionEntity } from './permission.entity';
import { RoleEntity } from './role.entity';

@Entity({ name: 'role_permission' })
export class RolePermissionEntity extends BaseEntity {
  @PrimaryColumn({ type: 'varchar', length: 200 })
  companyId!: string;

  @PrimaryColumn({ type: 'varchar', length: 200 })
  roleId!: string;

  @PrimaryColumn({ type: 'varchar', length: 200 })
  permissionId!: string;

  @Column({ type: 'bool', default: false })
  view!: boolean;

  @Column({ type: 'bool', default: false })
  create!: boolean;

  @Column({ type: 'bool', default: false })
  update!: boolean;

  @Column({ type: 'bool', default: false })
  delete!: boolean;

  @Column({ type: 'bool', default: false })
  download!: boolean;

  @Column({ type: 'bool', default: false })
  upload!: boolean;

  @JoinColumn({
    name: 'company_id',
    referencedColumnName: 'companyId',
  })
  @ManyToOne(() => CompanyEntity, (company) => company.rolePermissions, {
    onDelete: 'CASCADE',
  })
  company!: CompanyEntity;

  @JoinColumn([
    {
      name: 'company_id',
      referencedColumnName: 'companyId',
    },
    {
      name: 'permission_id',
      referencedColumnName: 'permissionId',
    },
  ])
  @ManyToOne(
    () => PermissionEntity,
    (permission) => permission.rolePermissions,
    {
      onDelete: 'CASCADE',
    },
  )
  permission!: PermissionEntity;

  @JoinColumn([
    {
      name: 'company_id',
      referencedColumnName: 'companyId',
    },
    {
      name: 'role_id',
      referencedColumnName: 'roleId',
    },
  ])
  @ManyToOne(() => RoleEntity, (role) => role.rolePermissions, {
    onDelete: 'CASCADE',
  })
  role!: RoleEntity;
}
