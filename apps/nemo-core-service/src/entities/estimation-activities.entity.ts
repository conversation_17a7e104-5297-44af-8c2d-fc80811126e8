import {
  <PERSON>um<PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  ManyToOne,
  OneToOne,
  PrimaryColumn,
} from 'typeorm';
import { ColumnDecimalTransformer } from './transformer/column-decimal.transformer';
import { BaseEntity } from './base.entity';
import { JobEntity } from './job.entity';
import { AutoIdField } from '../utils';
import { ChecklistType, QuestionType } from './model-checklist.entity';
import { CompanyEntity } from './company.entity';
import {
  ModelMasterEntity,
  ModelMasterGradeDetail,
} from './model-master.entity';
import { BranchEntity } from './branch.entity';
import { ModelMasterColorEntity } from './model-master-color.entity';
import { LegalDocumentEntity } from './legal-document.entity';

export enum IModuleStatus {
  PASS = 'pass',
  FAIL = 'fail',
  SKIP = 'skip',
}

export interface IQuestionChoice {
  id: string;
  choiceNameTh: string;
  choiceNameEn: string;
  iconImageUrl: string;
  isSelected: boolean;
}

export interface IModelChecklistResultItemDefault {
  functionKeyCond: string;
  modelChecklistId: string;
  isRequired: boolean;
  penalties: string;
  isSkip: boolean;
  modelChecklistNameTh: string;
  modelChecklistNameEn: string;
  modelCheckListDescriptionTh?: string;
  modelCheckListDescriptionEn?: string;
  iconImageUrl?: string;
}
export interface IModelChecklistResultItemModule
  extends IModelChecklistResultItemDefault {
  modelChecklistModuleCode: string;
  moduleStatus: IModuleStatus;
}

export interface IModelChecklistResultItemQuestion
  extends IModelChecklistResultItemDefault {
  answerType: QuestionType;
  questionChoices: IQuestionChoice[];
  questionAnswerText: string;
}

export interface IModelChecklistResult {
  [ChecklistType.MODULE]: IModelChecklistResultItemModule[];
  [ChecklistType.QUESTION]: IModelChecklistResultItemQuestion[];
}

@Entity({ name: 'estimation_activities' })
export class EstimationActivitiesEntity extends BaseEntity {
  // --- primary
  @PrimaryColumn({ type: 'varchar', length: 200 })
  id: string = AutoIdField.basic.produce();

  @PrimaryColumn({ type: 'varchar', length: 200 })
  companyId!: string;

  // --- require id
  @Column({ type: 'varchar', length: 200 })
  modelKey!: string;

  // --- not require id
  @Column({ type: 'varchar', length: 200, nullable: true })
  colorId?: string | null;

  @Column({ type: 'varchar', length: 200, nullable: true })
  branchId?: string | null;

  @Column({ type: 'varchar', length: 200, nullable: true })
  jobId?: string | null;

  @Column({ type: 'varchar', length: 200, nullable: true })
  legalDocumentId?: string | null;

  // --- require field
  @Column({ type: 'varchar', length: 200 })
  deviceId!: string;

  @Column({
    type: 'decimal',
    precision: 12,
    scale: 2,
    transformer: new ColumnDecimalTransformer(),
  })
  estimatedPrice!: number;

  // --- not require field phone
  @Column({ type: 'varchar', length: 100, nullable: true })
  imei1?: string | null;

  @Column({ type: 'varchar', length: 100, nullable: true })
  imei2?: string | null;

  // --- not require field customer
  @Column({ type: 'varchar', length: 200, nullable: true })
  firstName?: string | null;

  @Column({ type: 'varchar', length: 200, nullable: true })
  lastName?: string | null;

  @Column({ type: 'varchar', length: 100, nullable: true })
  phoneNumber?: string | null;

  @Column({ type: 'varchar', length: 200, nullable: true })
  email?: string | null;

  // --- not require field pdpa
  @Column({ type: 'bool', nullable: true })
  isAcceptPDPA?: boolean | null;

  @Column({
    type: 'decimal',
    precision: 7,
    scale: 2,
    default: null,
    transformer: new ColumnDecimalTransformer(),
    nullable: true,
  })
  PDPAVersion?: number | null;

  // --- json obj
  @Column({ type: 'jsonb' })
  modelMasterGradeSnapshot!: ModelMasterGradeDetail[];

  @Column({ type: 'jsonb' })
  modelChecklistResult!: IModelChecklistResult;

  // --- join require
  @JoinColumn({
    name: 'company_id',
    referencedColumnName: 'companyId',
  })
  @ManyToOne(() => CompanyEntity, (company) => company.estimationActivities, {
    onDelete: 'CASCADE',
  })
  company!: CompanyEntity;

  @JoinColumn([
    {
      name: 'company_id',
      referencedColumnName: 'companyId',
    },
    {
      name: 'model_key',
      referencedColumnName: 'modelKey',
    },
  ])
  @ManyToOne(() => ModelMasterEntity, (model) => model.estimationActivities, {
    onDelete: 'CASCADE',
  })
  modelMaster!: ModelMasterEntity;

  // --- join not require
  @JoinColumn([
    {
      name: 'company_id',
      referencedColumnName: 'companyId',
    },
    {
      name: 'color_id',
      referencedColumnName: 'id',
    },
  ])
  @ManyToOne(
    () => ModelMasterColorEntity,
    (modelMasterColor) => modelMasterColor.estimationActivities,
    {
      nullable: true,
    },
  )
  modelMasterColor?: ModelMasterColorEntity;

  @JoinColumn([
    {
      name: 'company_id',
      referencedColumnName: 'companyId',
    },
    {
      name: 'branch_id',
      referencedColumnName: 'branchId',
    },
  ])
  @ManyToOne(() => BranchEntity, (branch) => branch.estimationActivities, {
    nullable: true,
  })
  branch?: BranchEntity;

  @JoinColumn([
    {
      name: 'company_id',
      referencedColumnName: 'companyId',
    },
    {
      name: 'legal_document_id',
      referencedColumnName: 'id',
    },
  ])
  @ManyToOne(() => LegalDocumentEntity, (doc) => doc.estimationActivities, {
    nullable: true,
  })
  legalDocument?: LegalDocumentEntity;

  @JoinColumn([
    {
      name: 'company_id',
      referencedColumnName: 'companyId',
    },
    {
      name: 'job_id',
      referencedColumnName: 'jobId',
    },
  ])
  @OneToOne(() => JobEntity, (job) => job.estimationActivities, {
    nullable: true,
  })
  job?: JobEntity;
}
