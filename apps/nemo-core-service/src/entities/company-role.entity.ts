import { Column, Entity, PrimaryColumn } from 'typeorm';
import { BaseEntity } from './base.entity';

@Entity({ name: 'company_role' })
export class CompanyRoleEntity extends BaseEntity {
  @PrimaryColumn({ type: 'varchar', length: 200 })
  companyId!: string;

  @PrimaryColumn({ type: 'varchar', length: 200 })
  companyRole!: string;

  @Column('simple-array', { array: true, nullable: true })
  role: string[] = [];
}
