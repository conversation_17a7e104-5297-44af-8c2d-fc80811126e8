import { Column, <PERSON>ti<PERSON>, Join<PERSON><PERSON>umn, ManyToOne, PrimaryColumn } from 'typeorm';
import { BaseEntity } from './base.entity';
import { AutoIdField, DataChangeInfo } from '../utils';
import { AllocationOrderEntity } from './allocation-order.entity';

export enum AllocationOrderActivitiesType {
  CREATE = 'create',
  UPDATE = 'update',
}

export interface AllocationOrderActivityDetail {
  summary: string;
  status: string;
  dataChange?: Record<string, DataChangeInfo>;
  dataChangeSlug?: string | null;
}

@Entity({ name: 'allocation_order_activities' })
export class AllocationOrderActivitiesEntity extends BaseEntity {
  @PrimaryColumn({ type: 'varchar', length: 200 })
  activityId: string = AutoIdField.basic.produce();

  @Column({ type: 'varchar', length: 200 })
  allocationOrderId!: string;

  @Column({ type: 'varchar', length: 200 })
  companyId!: string;

  @Column({ type: 'varchar', length: 200 })
  type!: string;

  @Column({ type: 'jsonb' })
  detail!: AllocationOrderActivityDetail;

  @Column({ type: 'varchar', length: 100 })
  createdBy!: string;

  @JoinColumn([
    {
      name: 'allocation_order_id',
      referencedColumnName: 'allocationOrderId',
    },
  ])
  @ManyToOne(
    () => AllocationOrderEntity,
    (allocationOrder) => allocationOrder.allocationOrderActivities,
    {
      onDelete: 'CASCADE',
    },
  )
  allocationOrder!: AllocationOrderEntity;
}
