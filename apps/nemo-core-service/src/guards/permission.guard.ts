import { CanActivate, ExecutionContext, Injectable } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { Observable } from 'rxjs';
import { Permissions } from '../decorators';

@Injectable()
export class PermissionGuard implements CanActivate {
  constructor(private reflector: Reflector) {}

  canActivate(
    context: ExecutionContext,
  ): boolean | Promise<boolean> | Observable<boolean> {
    // Get roles from decorator
    const permissions = this.reflector.get(Permissions, context.getHandler());

    // If no roles are defined, allow access
    if (!permissions?.length) return true;

    // Get request from context
    const request = context.switchToHttp().getRequest();

    // Get user context from request
    const withUserContext = request.withUserContext;
    const userPermisssions =
      withUserContext.permissions?.reduce((acc, curr) => {
        return [...acc, ...curr.permission];
      }, []) ?? [];

    // Check if user has required role
    const hasRoles = permissions.filter((permission) =>
      userPermisssions.includes(permission),
    );

    if (!hasRoles.length) return false;

    return true;
  }
}
