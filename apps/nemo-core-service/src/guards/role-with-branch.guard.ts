import { CanActivate, ExecutionContext, Injectable } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { Observable } from 'rxjs';
import { RolesWithBranch } from '../decorators';
import { WithBranchContext, WithUserContext } from '../interfaces';
import { BaseExceptionService } from '../exceptions';

@Injectable()
export class RolesWithBranchGuard implements CanActivate {
  constructor(private reflector: Reflector) {}

  canActivate(
    context: ExecutionContext,
  ): boolean | Promise<boolean> | Observable<boolean> {
    // Get roles from decorator
    const roles = this.reflector.get(RolesWithBranch, context.getHandler());

    // If no roles are defined, allow access
    if (!roles?.length) return true;

    // Get request from context
    const request = context.switchToHttp().getRequest<{
      withUserContext: WithUserContext;
      withBranchContext: WithBranchContext;
    }>();

    // Get user context from request
    const withUserContext = request.withUserContext;

    // Get branch context from request
    const withBranchContext = request.withBranchContext;

    // Get branch
    const branchId = withBranchContext.branch;

    // Find role in current branch
    const rolesInBranch = withUserContext.roles?.find(
      (role) => role.branchId === branchId,
    );

    if (!rolesInBranch?.role.length) {
      // Throw exception
      throw new BaseExceptionService().exception('ROLES_INVALID');
    }

    // Check role match some user roles
    const roleValid = roles.some((role) => rolesInBranch?.role.includes(role));

    if (!roleValid) {
      // Throw exception
      throw new BaseExceptionService().exception('ROLES_INVALID');
    }

    // Return boolean
    return true;
  }
}
