import { CanActivate, ExecutionContext, Injectable } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { Observable } from 'rxjs';
import { PermissionsWithBranch } from '../decorators';
import { WithBranchContext, WithUserContext } from '../interfaces';
import { BaseExceptionService } from '../exceptions';

@Injectable()
export class PermissionsWithBranchGuard implements CanActivate {
  constructor(private reflector: Reflector) {}

  canActivate(
    context: ExecutionContext,
  ): boolean | Promise<boolean> | Observable<boolean> {
    // Get roles from decorator
    const permissions = this.reflector.get(
      PermissionsWithBranch,
      context.getHandler(),
    );

    // If no roles are defined, allow access
    if (!permissions?.length) return true;

    // Get request from context
    const request = context.switchToHttp().getRequest<{
      withUserContext: WithUserContext;
      withBranchContext: WithBranchContext;
    }>();

    // Get user context from request
    const withUserContext = request.withUserContext;

    // Get branch context from request
    const withBranchContext = request.withBranchContext;

    // Get branch
    const branchId = withBranchContext.branch;

    // Find role in current branch
    const permissionsInBranch = withUserContext.permissions?.find(
      (permission) => permission.branchId === branchId,
    );

    if (!permissionsInBranch?.permission.length) {
      // Throw exception
      throw new BaseExceptionService().exception('ROLES_INVALID');
    }

    // Check role match some user roles
    const permissionValid = permissions.some(
      (permission) => permissionsInBranch?.permission.includes(permission),
    );

    if (!permissionValid) {
      // Throw exception
      throw new BaseExceptionService().exception('ROLES_INVALID');
    }

    // Return boolean
    return true;
  }
}
