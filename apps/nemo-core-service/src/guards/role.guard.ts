import { CanActivate, ExecutionContext, Injectable } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { Observable } from 'rxjs';
import { Roles } from '../decorators';

@Injectable()
export class RolesGuard implements CanActivate {
  constructor(private reflector: Reflector) {}

  canActivate(
    context: ExecutionContext,
  ): boolean | Promise<boolean> | Observable<boolean> {
    // Get roles from decorator
    const roles = this.reflector.get(Roles, context.getHandler());

    // If no roles are defined, allow access
    if (!roles?.length) return true;

    // TODO:: Logic validate role user with roles from params decorator

    // Get request from context
    const request = context.switchToHttp().getRequest();

    // Get user context from request
    const withUserContext = request.withUserContext;
    const userRoles =
      withUserContext.roles?.reduce((acc, curr) => {
        return [...acc, ...curr.role];
      }, []) ?? [];

    // Check if user has required role
    const hasRoles = roles.filter((role) => userRoles.includes(role));

    if (!hasRoles.length) return false;

    return true;
  }
}
