import { ArgumentsHost, Catch, ExceptionFilter } from '@nestjs/common';
import { HttpAdapterHost } from '@nestjs/core';
import { BaseExceptionService } from '../exceptions';

@Catch(BaseExceptionService)
export class BaseExceptionFilter implements ExceptionFilter {
  constructor(private readonly httpAdapterHost: HttpAdapterHost) {}

  catch(exception: BaseExceptionService, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const { httpAdapter } = this.httpAdapterHost;

    const responseBody: Partial<BaseExceptionService> = {
      code: exception.code,
      success: false,
      message: exception.message,
      data: exception.data || null,
    };

    httpAdapter.reply(ctx.getResponse(), responseBody, exception.status);
  }
}
