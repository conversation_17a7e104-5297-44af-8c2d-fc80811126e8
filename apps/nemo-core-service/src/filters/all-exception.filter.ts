import {
  ArgumentsHost,
  Catch,
  ExceptionFilter,
  HttpException,
  HttpStatus,
} from '@nestjs/common';
import { HttpAdapterHost } from '@nestjs/core';
import { BaseResponse } from 'contracts';
import { BASE_EXCEPTIONS } from '../config';
import { formatErrorLog } from '../utils/general';

@Catch()
export class AllExceptionsFilter implements ExceptionFilter {
  constructor(private readonly httpAdapterHost: HttpAdapterHost) {}

  catch(exception: unknown, host: ArgumentsHost): void {
    // In certain situations `httpAdapter` might not be available in the
    // constructor method, thus we should resolve it here.
    const { httpAdapter } = this.httpAdapterHost;

    // Get context
    const ctx = host.switchToHttp();

    // Get status code
    const status =
      exception instanceof HttpException
        ? exception.getStatus()
        : HttpStatus.INTERNAL_SERVER_ERROR;

    // Initial error response
    let errorResponse: any = null;

    if (exception instanceof Error) {
      errorResponse = exception.message;
    }

    if (exception instanceof HttpException) {
      // Get error response
      errorResponse = exception.getResponse();
    }

    console.log(
      formatErrorLog(
        'all',
        status,
        errorResponse,
        BASE_EXCEPTIONS.INTERNAL_SERVER_ERROR.code,
      ),
    );

    // Make response
    const result: BaseResponse = {
      code: BASE_EXCEPTIONS.INTERNAL_SERVER_ERROR.code,
      success: false,
      data: errorResponse,
    };

    httpAdapter.reply(ctx.getResponse(), result, status);
  }
}
