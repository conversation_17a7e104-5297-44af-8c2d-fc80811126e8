import { ArgumentsHost, Catch, ExceptionFilter } from '@nestjs/common';
import { BaseResponse } from 'contracts';
import { ZodValidationException } from 'nestjs-zod';
import {
  BASE_EXCEPTIONS,
  COMMON_EXCEPTIONS_TITLE_DESCRIPTION,
} from '../config';
import { formatErrorLog, getLanguageFromHeader } from '../utils/general';

@Catch(ZodValidationException)
export class ZodExceptionFilter implements ExceptionFilter {
  catch(exception: ZodValidationException, host: ArgumentsHost) {
    // Get zod error
    const zodError = exception.getZodError();

    const ctx = host.switchToHttp();
    // Get response object
    const response = ctx.getResponse();

    // Get status code
    const status = exception.getStatus();

    const header = ctx.getRequest().headers;
    const language = getLanguageFromHeader(header);

    const issues = zodError.issues;
    const firstIssue = issues[0];
    let commonException = COMMON_EXCEPTIONS_TITLE_DESCRIPTION.COMMON_ZOD_ERROR;
    let field = '';
    if (firstIssue) {
      const { message, path } = firstIssue;
      field = path.join('.');
      const foundCommonException = COMMON_EXCEPTIONS_TITLE_DESCRIPTION[message];
      if (foundCommonException) {
        commonException = foundCommonException;
      }
    }

    console.log(
      formatErrorLog(
        'zod',
        status,
        firstIssue?.message,
        BASE_EXCEPTIONS.BODY_PAYLOAD_INVALID.code,
      ),
    );

    // Make response
    const result: BaseResponse = {
      code: BASE_EXCEPTIONS.BODY_PAYLOAD_INVALID.code,
      success: false,
      message: `Body payload invalid: ${field}`,
      data: null,
      error: {
        // ...zodError,
        title: commonException[language]?.title,
        description: commonException[language]?.description,
      },
    };

    // Send response
    response.status(status).json(result);
  }
}
