import { ArgumentsHost, Catch, ExceptionFilter } from '@nestjs/common';
import { HttpAdapterHost } from '@nestjs/core';
import { CommonExceptionService } from '../exceptions';

@Catch(CommonExceptionService)
export class CommonExceptionFilter implements ExceptionFilter {
  constructor(private readonly httpAdapterHost: HttpAdapterHost) {}

  catch(exception: CommonExceptionService, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const { httpAdapter } = this.httpAdapterHost;

    const responseBody: Partial<CommonExceptionService> = {
      code: exception.code,
      success: false,
      message: exception.message,
      data: exception.data || null,
      error: exception.error,
    };

    httpAdapter.reply(ctx.getResponse(), responseBody, exception.status);
  }
}
