import {
  Body,
  Controller,
  Delete,
  Get,
  HttpException,
  HttpStatus,
  Param,
  Patch,
  Post,
  Query,
  Req,
} from '@nestjs/common';
import {
  Between,
  EntityManager,
  Equal,
  FindManyOptions,
  FindOptionsOrder,
  FindOptionsWhere,
  ILike,
  In,
  <PERSON>Null,
  <PERSON><PERSON><PERSON>,
  Like,
  <PERSON><PERSON>han,
  Not,
  ObjectLiteral,
  Repository,
  SelectQueryBuilder,
} from 'typeorm';
import { pick, toPairs, isEmpty, assign, keyBy, Dictionary } from 'lodash';
import { Request } from 'express';
import { ColumnMetadata } from 'typeorm/metadata/ColumnMetadata';
import { RelationMetadata } from 'typeorm/metadata/RelationMetadata';
import { BASE_COLLATE, selectExportDBCols } from '../config';

export interface ICrudController<T> {
  findAll(
    context: Request,
    query: any,
  ): Promise<{ items: T[]; paginationResult: Pagination }>;
  findOne(
    context: Request,
    id: string,
    manager?: EntityManager,
    withDeleted?: boolean,
  ): Promise<T>;
  create(context: Request, body: T): Promise<T>;
  update(context: Request, id: string, body: Partial<T>): Promise<T>;
  removeOne(context: Request, id: string): Promise<T>;
  softRemoveOne(context: Request, id: string): Promise<T>;
  restore(context: Request, id: string): Promise<T>;
}

export interface QueriesParam {
  orderBy: string;
  pageSize: string;
  page: string;
  search: string;
  searchField: string;
  populate: string;
  pagination: string;
}

export interface Pagination {
  page: number;
  pageSize: number;
  totalRecords: number;
}

export declare enum QueryOperator {
  $eq = '=',
  $in = 'in',
  $nin = 'not in',
  $gt = '>',
  $gte = '>=',
  $lt = '<',
  $lte = '<=',
  $ne = '!=',
  $not = 'not',
  $like = 'like',
  $re = 'regexp',
  $fulltext = 'fulltext',
  $exists = 'not null',
  $ilike = 'ilike',
  $overlap = '&&',
  $contains = '@>',
  $contained = '<@',
}
export const DEFAULT_PAGE_SIZE = '20';

export const whereClauseByQuery = <T extends ObjectLiteral>(
  query: Partial<QueriesParam>,
  options: Partial<Options<T>>,
  resourceName: string,
): Partial<{
  [key in keyof T]: Partial<{ [key in QueryOperator]: any }>;
}>[] => {
  const q = {
    ...pick(query, options.searchableFields ?? []),
  };
  const evalValue = (val: string): string => {
    if (/\$dt\(1\d+\)/.test(val)) {
      const m = /\$dt\((.+)\)/.exec(val);
      if (!m)
        throw new HttpException(
          `Fail to evalQuery on ${resourceName}`,
          HttpStatus.BAD_REQUEST,
        );
      return new Date(+m[1]).toISOString();
    }
    return val;
  };

  const handleNullValue = (
    m: RegExpExecArray | null,
    operation: string,
  ): RegExpExecArray => {
    if (!m) {
      throw new HttpException(
        `Fail to evalQuery ${operation} on ${resourceName}`,
        HttpStatus.BAD_REQUEST,
      );
    }

    return m;
  };

  /**
   * Supported format
   *
   * - Between Operator: `$between(v1, v2)`
   * - Date Operator: `$dt(milliseconds)`
   * - In Operator: `$in(value split by comma)`
   *
   * @param v
   */
  const evalQuery = (v: string): Partial<{ [key: string]: any }> | 'void' => {
    if (/\$between\((\d+),(\d+)\)/i.test(v)) {
      let m = /\$between\(([^,]+),(.+)\)/i.exec(v);
      m = handleNullValue(m, '$between');
      return Between(+m[1], +m[2]);
    } else if (/\$like\(([^)]*)\)/i.test(v)) {
      let m = /\$like\(([^)]*)\)/i.exec(v);
      m = handleNullValue(m, '$like');
      return Like(`%${m[1]}%`);
    } else if (/\$ilike\(([^)]*)\)/i.test(v)) {
      let m = /\$ilike\(([^)]*)\)/i.exec(v);
      m = handleNullValue(m, '$ilike');
      return ILike(`%${m[1]}%`);
    } else if (/\$between\(([^,]+),(.+)\)/i.test(v)) {
      let m = /\$between\(([^,]+),(.+)\)/i.exec(v);
      m = handleNullValue(m, '$between');
      return Between(evalValue(m[1]), evalValue(m[2]));
    } else if (/\$in\([^)]+\)/i.test(v)) {
      let m = /\$in\(([^)]+)\)/i.exec(v);
      m = handleNullValue(m, '$in');
      const splitted = m[1].split(',').filter((o) => !!o);
      if (splitted.length > 0) {
        return In(splitted);
      }
      return 'void';
    } else if (/\$gt\([^)]+\)/i.test(v)) {
      let m = /\$gt\(([^)]+)\)/i.exec(v);
      m = handleNullValue(m, '$gt');
      return MoreThan(evalValue(m[1]));
    } else if (/\$lt\([^)]+\)/i.test(v)) {
      let m = /\$lt\(([^)]+)\)/i.exec(v);
      m = handleNullValue(m, '$lt');
      return LessThan(evalValue(m[1]));
    } else if (/\$null/i.test(v)) {
      return IsNull();
    } else if (/\$notNull/i.test(v)) {
      return Not(IsNull());
    } else if (/\$eq\(([^)]*)\)/i.test(v)) {
      let m = /\$eq\(([^)]*)\)/i.exec(v);
      m = handleNullValue(m, '$eq');
      return Equal(m[1]);
    }
    // apply equal operation as default
    return Equal(v);
  };
  /**
   * {
   *  [fieldName]: {
   *    [key in QueryOperator]: any,
   *    ...
   *  }
   * }
   */
  const res = toPairs(q).map(
    ([key, v]): Partial<{
      [key in keyof T]: Partial<{ [key in QueryOperator]: any }>;
    }> => {
      if (typeof v === 'function')
        throw new HttpException(
          'cannot evaluate value as function.',
          HttpStatus.BAD_REQUEST,
        );

      const _v = (options.searchableFieldValueConverter?.[key] ?? ((v) => v))(
        v,
      );

      const val = evalQuery(_v);

      return val === 'void' ? {} : ({ [key]: val } as any);
    },
  );

  return res;
};

const handleRelationOrder = <T>(
  relM: RegExpMatchArray,
  relMetadataKeyByPropertyName: Dictionary<RelationMetadata>,
  c: Partial<FindOptionsOrder<T>>,
): Partial<FindOptionsOrder<T>> => {
  const relMetadata = relMetadataKeyByPropertyName[relM[1]];
  if (!relMetadata)
    throw new HttpException(
      `order by relation ${relM[1]} not found`,
      HttpStatus.BAD_REQUEST,
    );
  const relColumnsMetadata = relMetadata.inverseEntityMetadata.columns;
  const relColumnMetadata = relColumnsMetadata.find(
    (c) => c.propertyAliasName === relM[2],
  );
  if (!relColumnMetadata)
    throw new HttpException(
      `order by relation ${relM[1]} column ${relM[2]} not found`,
      HttpStatus.BAD_REQUEST,
    );
  return {
    ...c,
    [`${relMetadata.propertyName}.${relColumnMetadata.propertyAliasName}`]: `${BASE_COLLATE} ${
      (relM[4]?.toLowerCase() ?? 'desc') as any
    }`,
  };
};

const handleJsonOrder = <T>(
  element: string,
  columnsMetadataKeyByPropertyAliasName: Dictionary<ColumnMetadata>,
  c: any,
): Partial<FindOptionsOrder<T>> => {
  const jsonM = /^([^ ]+) ([^ ]+)(\s+(asc|desc))?$/.exec(element);
  if (!jsonM)
    throw new HttpException(
      'order with JSONB MUST has following format `db_field_name_1 key asc,db_field_name2 key asc,db_field_name_3 key desc`',
      HttpStatus.BAD_REQUEST,
    );
  return {
    ...c,
    [`"r".${
      columnsMetadataKeyByPropertyAliasName[element.split(' ')?.[0]]
        .databaseName
    }->>'${jsonM[2]}'${BASE_COLLATE}`]: (jsonM[4]?.toLowerCase() ??
      'desc') as any,
  };
};

export const orderBy = <T>(
  query: Partial<QueriesParam>,
  columnsMetadata: ColumnMetadata[],
  relationsMetadata: RelationMetadata[],
): FindOptionsOrder<T> => {
  if (!query.orderBy) {
    return {};
  }
  const order = query.orderBy;
  const orders = order.split(',');
  return orders.reduce((c, element): FindOptionsOrder<T> => {
    const columnsMetadataKeyByPropertyAliasName = keyBy(
      columnsMetadata,
      'propertyAliasName',
    );
    const relMetadataKeyByPropertyName = keyBy(
      relationsMetadata,
      'propertyName',
    );
    // For relation look for the query value with 'rel:' prefix.
    const relM = /^rel:([^ ]+) ([^ ]+)(\s+(asc|desc))?$/.exec(element);
    if (relM) {
      return handleRelationOrder(relM, relMetadataKeyByPropertyName, c);
    }
    // For JSON Value
    if (
      columnsMetadataKeyByPropertyAliasName[element.split(' ')?.[0]]?.type ===
      'jsonb'
    ) {
      return handleJsonOrder(element, columnsMetadataKeyByPropertyAliasName, c);
    }
    // For Non JSON Value
    const m = /^([^ ]+)(\s+(asc|desc))?$/.exec(element);
    if (!m)
      throw new HttpException(
        'order MUST has following format `db_field_name_1 asc,db_field_name2 asc,db_field_name_3 desc`',
        HttpStatus.BAD_REQUEST,
      );
    if (
      columnsMetadataKeyByPropertyAliasName[element.split(' ')?.[0]]
        ?.entityMetadata.schema === 'time' &&
      columnsMetadataKeyByPropertyAliasName[element.split(' ')?.[0]]?.type ===
        String
    ) {
      return {
        ...c,
        [`r.${m[1]} ${BASE_COLLATE}`]: (m[3]?.toLowerCase() ?? 'desc') as any,
      };
    }

    // Get data info column metadata
    const columnMetadataInfo = columnsMetadataKeyByPropertyAliasName[m[1]];

    // Blacklist type for order with collate
    const blacklistTypes = [
      'timestamptz',
      'timestamp',
      'numeric',
      'decimal',
      'smallint',
      'bool',
    ];

    return {
      ...c,
      [`r.${m[1]} ${
        !blacklistTypes.includes(columnMetadataInfo.type.toString())
          ? BASE_COLLATE
          : ''
      }`]: (m[3]?.toLowerCase() ?? 'desc') as any,
    };
  }, {});
};

export interface Options<T extends ObjectLiteral> {
  resourceKeyPath: keyof T;
  order: FindOptionsOrder<T>;
  searchableFields: (keyof T)[];
  searchableFieldValueConverter: Partial<{
    [key in keyof T]: (raw: any) => string;
  }>;
  afterLoad: ((ctx: Request, objects: T[], isMany: boolean) => Promise<T[]>)[];
  preDelete: ((ctx: Request, em: EntityManager, object: T) => Promise<T>)[];
  preSave: ((
    ctx: Request,
    em: EntityManager,
    object: T,
    isCreated: boolean,
  ) => Promise<T>)[];
  postDelete: ((ctx: Request, em: EntityManager, object: T) => Promise<T>)[];
  postSave: ((
    ctx: Request,
    em: EntityManager,
    object: T,
    isCreated: boolean,
  ) => Promise<T>)[];
  preRestore: ((ctx: Request, em: EntityManager, object: T) => Promise<T>)[];
  postRestore: ((ctx: Request, em: EntityManager, object: T) => Promise<T>)[];
  defaultFilter: (
    request: Request,
    query: SelectQueryBuilder<T>,
    em?: EntityManager,
  ) => Promise<SelectQueryBuilder<T>>;
  searchFilter: (
    request: Request,
    em: EntityManager,
    query: SelectQueryBuilder<T>,
  ) => Promise<SelectQueryBuilder<T>>;
  sanitizeInputBody: (
    ctx: Request,
    em: EntityManager,
    body: any,
    isCreating: boolean,
  ) => Promise<any>;
  loadResourceToCreate: (
    ctx: Request,
    em: EntityManager,
  ) => Promise<T | undefined>;
  forAllResources: (ctx: Request) => Partial<{ [key in keyof T]: any }>;
  computeUpdatePayload: (
    ctx: Request,
    em: EntityManager,
    loadedFromDb: T,
    inputPayload: any,
  ) => Promise<any>;
  defaultPopulate: (ctx: Request, isMany: boolean) => (keyof T)[];
}

@Controller()
export class CrudController<T extends ObjectLiteral>
  implements ICrudController<T>
{
  protected options: Options<T>;

  constructor(
    protected cnstr: new () => T,
    protected readonly resourceName: string,
    protected repository: Repository<T>,
    options: Partial<Options<T>>,
  ) {
    const mergedOptions: Partial<Options<T>> = {};
    for (const key in options) {
      if (options[key] !== undefined) {
        mergedOptions[key] = options[key];
      }
    }

    this.options = {
      resourceKeyPath: 'id' as keyof T,
      order: { [options?.resourceKeyPath ?? '']: 'ASC' } as FindOptionsOrder<T>,
      searchableFields: [],
      searchableFieldValueConverter: {} as Partial<{
        [key in keyof T]: (raw: any) => string;
      }>,
      afterLoad: [],
      preSave: [],
      postSave: [],
      preDelete: [],
      postDelete: [],
      preRestore: [],
      postRestore: [],
      defaultFilter: async (_ctx, query) => query,
      searchFilter: async (_ctx, _em, query) => query,
      sanitizeInputBody: async (_ctx, _em, body) => body,
      computeUpdatePayload: async (_ctx, _em, _fromDb, body) => body,
      loadResourceToCreate: async () => undefined,
      forAllResources: () => ({}),
      defaultPopulate: () => [],
      ...mergedOptions,
    };
  }

  @Get()
  async findAll(
    @Req() context: Request,
    @Query() req: Partial<QueriesParam>,
    isUsingCustomQuery?: boolean,
  ): Promise<{ items: T[]; paginationResult: Pagination }> {
    return await this.repository.manager.transaction(
      async (
        man: EntityManager,
      ): Promise<{ items: T[]; paginationResult: Pagination }> => {
        const metadata = man.connection.getMetadata(this.cnstr);
        const order = orderBy(req, metadata.columns, metadata.relations);
        const limit = +(req.pageSize ?? DEFAULT_PAGE_SIZE);
        const offset = (+(req.page ?? '1') - 1) * limit;
        const __alias = 'r';
        const disablePagination = /false/i.test(req.pagination ?? '');
        const tableName = metadata.tableName;
        const isProductExport = context.query.productExport === 'true';
        const findOptions: FindManyOptions<T> = {
          where: {
            ...this.options.forAllResources(context),
            ...whereClauseByQuery(req, this.options, this.resourceName).reduce(
              (acc, cur) => ({ ...acc, ...cur }),
              {},
            ),
          },
        };

        let query = man.createQueryBuilder(this.cnstr, __alias);

        if (isUsingCustomQuery) {
          if (tableName === 'job') {
            // selectExportDBCols
            if (isProductExport) {
              selectExportDBCols.columns.push(
                `"r"."check_list_values"->'product_information'->>'device_color' AS "r_device_color"`,
              );
            }
            query.select(selectExportDBCols.columns);
          }
        }

        let population = this.options.defaultPopulate(
          context,
          true,
        ) as string[];
        if (req.populate && typeof req.populate === 'string') {
          population = req.populate.split(',');
        }

        if (!isUsingCustomQuery) {
          for (const relation of population) {
            query = query.leftJoinAndSelect(`${__alias}.${relation}`, relation);
          }
        }

        // apply additional search clause.
        if (findOptions.where) {
          query = query.where(findOptions.where);
        }

        query = await this.options.defaultFilter(context, query, man);
        query = await this.options.searchFilter(context, man, query);
        query = query.orderBy(order);

        if (!disablePagination) {
          query = query.offset(offset);
          query = query.limit(limit);
        }

        // Query count

        let items, count;
        if (!isUsingCustomQuery) {
          [items, count] = await query.getManyAndCount();
        } else {
          items = await query.getRawMany();
          count = items.length;
        }

        const paginationResult: Pagination = {
          page: disablePagination ? 1 : +(req.page ?? '1'),
          pageSize: disablePagination ? count : limit,
          totalRecords: count,
        };
        let result = items;
        // apply afterLoad hook
        for (const h of this.options.afterLoad) {
          result = await h(context, result, true);
        }

        return { items: result, paginationResult };
      },
    );
  }

  @Get()
  async findAllWithStream(
    @Req() context: Request,
    @Query() req: Partial<QueriesParam>,
    isUsingCustomQuery?: boolean,
  ): Promise<{ queryStream: SelectQueryBuilder<T> }> {
    const man = this.repository.manager;
    const metadata = man.connection.getMetadata(this.cnstr);
    const order = orderBy(req, metadata.columns, metadata.relations);
    const limit = +(req.pageSize ?? DEFAULT_PAGE_SIZE);
    const offset = (+(req.page ?? '1') - 1) * limit;
    const __alias = 'r';
    const disablePagination = /false/i.test(req.pagination ?? '');
    const tableName = metadata.tableName;
    const isProductExport = context.query.productExport === 'true';
    const isSapExport = context.query.sapExport === 'true';

    const findOptions: FindManyOptions<T> = {
      where: {
        ...this.options.forAllResources(context),
        ...whereClauseByQuery(req, this.options, this.resourceName).reduce(
          (acc, cur) => ({ ...acc, ...cur }),
          {},
        ),
      },
    };

    let query = man.createQueryBuilder(this.cnstr, __alias);

    if (isUsingCustomQuery) {
      if (tableName === 'job') {
        // selectExportDBCols
        if (isProductExport) {
          selectExportDBCols.columns.push(
            `"r"."check_list_values"->'product_information'->>'device_color' AS "r_device_color"`,
          );
        } else if (isSapExport) {
          selectExportDBCols.columns.push(
            `"r"."model_template"->'modelKey' AS "r_model_key"`,
            `"r"."model_template"->'matCode' AS "r_material_code"`,
          );
        }

        query.select(selectExportDBCols.columns);
      }
    }

    let population = this.options.defaultPopulate(context, true) as string[];
    if (req.populate && typeof req.populate === 'string') {
      population = req.populate.split(',');
    }

    if (!isUsingCustomQuery) {
      for (const relation of population) {
        query = query.leftJoinAndSelect(`${__alias}.${relation}`, relation);
      }
    }

    if (isSapExport) {
      query = query.leftJoin(
        'branch',
        'branch',
        'r.branch_id = branch.branch_id',
      );

      query = query.leftJoin(
        'contract',
        'contract',
        'r.job_id = contract.job_id',
      );

      query = query.leftJoin(
        'imported_voucher',
        'imported_voucher',
        'contract.contract_id = imported_voucher.contract_id',
      );

      query = query.addSelect([
        'imported_voucher.redemption_code',
        'branch.cost_center',
      ]);
    }

    // apply additional search clause.
    if (findOptions.where) {
      query = query.where(findOptions.where);
    }

    query = await this.options.defaultFilter(context, query, man);
    query = await this.options.searchFilter(context, man, query);
    query = query.orderBy(order);

    if (!disablePagination) {
      query = query.offset(offset);
      query = query.limit(limit);
    }

    return { queryStream: query };
  }

  @Get('/:id')
  async findOne(
    @Req() context: Request,
    @Param('id') id: string,
    manager?: EntityManager,
    withDeleted?: boolean,
  ): Promise<T> {
    const req = context.query;

    const where: FindOptionsWhere<T> = {
      [this.options.resourceKeyPath]: id,
    } as FindOptionsWhere<T>;

    const __alias = 'r';

    let query: SelectQueryBuilder<T>;
    if (manager) {
      query = manager.createQueryBuilder(this.cnstr, __alias);
    } else {
      query = this.repository.createQueryBuilder(__alias);
    }

    let population = this.options.defaultPopulate(context, false) as string[];
    if (req.populate && typeof req.populate === 'string') {
      population = req.populate.split(',');
    }
    for (const relation of population) {
      query = query.leftJoinAndSelect(`${__alias}.${relation}`, relation);
    }
    //
    // apply additional search clause.
    query = await this.options.defaultFilter(context, query, manager);

    query = query.andWhere(where); // caution, can use query.andWhere when query.where has not defined on defaultFilter.

    if (withDeleted) {
      query = query.withDeleted();
    }

    const data = await query.getOne();

    if (!data) {
      throw new HttpException('Resource not found', HttpStatus.NOT_FOUND);
    }

    let results = [data];
    // apply afterLoad hook
    for (const h of this.options.afterLoad) {
      results = await h(context, results, false);
    }

    if (results.length !== 1) {
      throw new HttpException(
        `Internal resource hooks (${this.resourceName}) might not returned promised objects. Please check afterLoad hooks.`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }

    return results[0];
  }

  @Post()
  async create(@Req() context: Request, @Body() body: T): Promise<T> {
    return await this.repository.manager.transaction(
      async (man: EntityManager): Promise<T> => {
        const allReq = this.options.forAllResources(context);

        const sanitizedBody = await this.options.sanitizeInputBody(
          context,
          man,
          body,
          true,
        );
        const preloadInstance = await this.options.loadResourceToCreate(
          context,
          man,
        );
        let raw =
          preloadInstance ??
          man.create(this.cnstr, {
            ...sanitizedBody,
            ...allReq,
          });

        // apply beforeSave hook, and replace to raw save
        for (const h of this.options.preSave) {
          raw = await h(context, man, raw, true);
        }

        let created = await man.save(raw, { reload: true });
        // apply afterSave hook, and replace to raw result
        for (const h of this.options.postSave) {
          created = await h(context, man, created, true);
        }
        return created;
      },
    );
  }

  @Patch('/:id')
  async update(
    @Req() context: Request,
    @Param('id') id: string,
    @Body() body: Partial<T>,
  ) {
    if (isEmpty(body)) {
      throw new HttpException(
        `Invalid input for ${this.resourceName} - empty update body.`,
        HttpStatus.BAD_REQUEST,
      );
    }

    return await this.repository.manager.transaction(
      async (man: EntityManager): Promise<T> => {
        const raw: T = await this.findOne(context, id, man);

        let sanitizedBody = await this.options.sanitizeInputBody(
          context,
          man,
          body,
          false,
        );
        sanitizedBody = await this.options.computeUpdatePayload(
          context,
          man,
          raw,
          sanitizedBody,
        );
        assign(raw, sanitizedBody);

        let entity = this.repository.create(raw);

        // apply before hook options
        for (const h of this.options.preSave) {
          entity = await h(context, man, entity, false);
        }
        let result = await man.save(entity, { reload: true });
        // apply after hook options
        for (const h of this.options.postSave) {
          result = await h(context, man, result, false);
        }
        return result;
      },
    );
  }

  @Delete('/:id')
  async removeOne(
    @Req() context: Request,
    @Param('id') id: string,
  ): Promise<T> {
    return this.repository.manager.transaction(
      async (man: EntityManager): Promise<T> => {
        const raw: T = await this.findOne(context, id, man);
        // Grab pk data from entity before remove
        // typeorm automatically mutate entity pk to undefined after removed
        const metadata = man.connection.getMetadata(this.cnstr);
        const pksName = metadata.primaryColumns.map((c) => c.propertyAliasName);
        const pk = pksName.reduce(
          (acc, cur) => ({ ...acc, [cur]: raw[cur] }),
          {},
        );

        let deleteEntry: T = raw;
        // apply before hook options
        for (const h of this.options.preDelete) {
          deleteEntry = await h(context, man, deleteEntry);
        }
        let result = await man.remove(this.cnstr, deleteEntry);

        // Reassign pk back to the entity.
        // This can ensure that the data is available in case it is needed for a subsequent response or other operations.
        result = { ...result, ...pk };
        // apply after hook options
        for (const h of this.options.postDelete) {
          result = await h(context, man, result);
        }
        return result;
      },
    );
  }

  @Delete('/soft-delete/:id')
  async softRemoveOne(
    @Req() context: Request,
    @Param('id') id: string,
  ): Promise<T> {
    return this.repository.manager.transaction(
      async (man: EntityManager): Promise<T> => {
        const raw: T = await this.findOne(context, id, man);
        const metadata = man.connection.getMetadata(this.cnstr);
        if (!metadata.deleteDateColumn) {
          throw new HttpException(
            `${this.resourceName} - not supported soft delete`,
            HttpStatus.BAD_REQUEST,
          );
        }
        let deleteEntry: T = raw;
        for (const h of this.options.preDelete) {
          deleteEntry = await h(context, man, deleteEntry);
        }
        await man.softRemove(this.cnstr, deleteEntry);
        for (const h of this.options.postDelete) {
          deleteEntry = await h(context, man, deleteEntry);
        }
        return deleteEntry;
      },
    );
  }

  @Patch('/restore/:id')
  async restore(@Req() context: Request, @Param('id') id: string): Promise<T> {
    return this.repository.manager.transaction(
      async (man: EntityManager): Promise<T> => {
        const metadata = man.connection.getMetadata(this.cnstr);
        if (!metadata.deleteDateColumn) {
          throw new HttpException(
            `${this.resourceName} - not supported soft delete`,
            HttpStatus.BAD_REQUEST,
          );
        }
        let restoreEntity: T = await this.findOne(context, id, man, true);
        for (const h of this.options.preRestore) {
          restoreEntity = await h(context, man, restoreEntity);
        }
        await man.recover(this.cnstr, restoreEntity);
        for (const h of this.options.postRestore) {
          restoreEntity = await h(context, man, restoreEntity);
        }
        return restoreEntity;
      },
    );
  }
}
