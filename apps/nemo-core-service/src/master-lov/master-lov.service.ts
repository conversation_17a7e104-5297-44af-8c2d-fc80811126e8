import { Injectable } from '@nestjs/common';
import { MasterLovEntity, Locale } from '../entities';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { BaseExceptionService } from '../exceptions';
import { MasterLovItem } from 'contracts';

@Injectable()
export class MasterLovService {
  constructor(
    @InjectRepository(MasterLovEntity)
    private readonly masterLovRepository: Repository<MasterLovEntity>,
    private readonly baseExceptionService: BaseExceptionService,
  ) {}

  async getMasterLov({
    type,
    locale,
    companyId,
  }: {
    type: string;
    locale: Locale;
    companyId: string;
  }): Promise<MasterLovItem[]> {
    const targetList = await this.masterLovRepository.find({
      where: { type, locale, companyId },
      order: { sortIndex: 'ASC' },
    });

    if (!targetList.length) {
      throw this.baseExceptionService.exception(
        'NOT_FOUND_DATA',
        'MasterLov not found',
      );
    }

    return targetList.map((record) => {
      const returnValue: MasterLovItem = {
        label: record.label,
        value: record.value,
      };
      if (record.additionalValue) {
        returnValue.additionalValue = record.additionalValue;
      }
      return returnValue;
    });
  }
}
