import { MiddlewareConsumer, Module, NestModule } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { MasterLovEntity } from '../entities';
import { WithUserMiddleware } from '../middlewares';
import { MasterLovController } from './master-lov.controller';
import { MasterLovService } from './master-lov.service';

@Module({
  imports: [TypeOrmModule.forFeature([MasterLovEntity])],
  controllers: [MasterLovController],
  providers: [MasterLovService],
})
export class MasterLovModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(WithUserMiddleware).forRoutes(MasterLovController);
  }
}
