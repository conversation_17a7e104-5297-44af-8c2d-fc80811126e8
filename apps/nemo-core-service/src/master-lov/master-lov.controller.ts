import { Controller, Get, Param, Req } from '@nestjs/common';
import { Request } from 'express';
import { InjectRepository } from '@nestjs/typeorm';
import { Locale, MasterLovEntity } from '../entities';
import { Repository } from 'typeorm';
import { MasterLovService } from './master-lov.service';
import { WithUser } from '../decorators';
import { WithUserContext } from '../interfaces';

@Controller('v1/master-lov')
export class MasterLovController {
  constructor(
    @InjectRepository(MasterLovEntity)
    repo: Repository<MasterLovEntity>,
    private readonly masterLovService: MasterLovService,
  ) {}

  @Get('/:type/:locale')
  async getMasterLov(
    @Req() context: Request,
    @Param('type') type: string,
    @Param('locale') locale: Locale,
    @WithUser() user: WithUserContext,
  ) {
    return await this.masterLovService.getMasterLov({
      type,
      locale,
      companyId: user.company,
    });
  }
}
