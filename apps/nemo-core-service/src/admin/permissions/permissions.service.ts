import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { PermissionGroupEntity, SiteType } from '../../entities';
import { Repository } from 'typeorm';

@Injectable()
export class AdminPermissionsService {
  constructor(
    @InjectRepository(PermissionGroupEntity)
    private readonly permissionGroupRepo: Repository<PermissionGroupEntity>,
  ) {}

  async getPermissions(companyId: string) {
    const permissionGroupData = await this.permissionGroupRepo.find({
      where: { companyId: companyId },
      relations: ['permissions'],
    });

    const responseData = {
      frontshop: [],
      cms: [],
    };

    permissionGroupData.forEach((data) => {
      if (data.type !== SiteType.FRONTSHOP && data.type !== SiteType.CMS) {
        return;
      }

      const groupType = data.type.toLowerCase();
      const filteredPermissionsData = data.permissions.map((permission) => {
        return {
          permissionId: permission.permissionId,
          label: permission.label,
          sortIndex: permission.sortIndex,
          iconName: permission.iconName,
          view: permission.view,
          create: permission.create,
          update: permission.update,
          delete: permission.delete,
          upload: permission.upload,
          download: permission.download,
        };
      });

      const resultData = {
        permissionGroupId: data.permissionGroupId,
        label: data.label,
        isInMenu: data.isInMenu,
        sortIndex: data.sortIndex,
        permissions: filteredPermissionsData,
      };

      responseData[groupType].push(resultData);
    });

    return responseData;
  }
}
