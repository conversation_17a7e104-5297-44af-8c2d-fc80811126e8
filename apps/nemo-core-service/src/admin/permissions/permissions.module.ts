import { MiddlewareConsumer, Module, NestModule } from '@nestjs/common';
import { WithUserMiddleware } from '../../middlewares';
import { AdminPermissionsController } from './permissions.controller';
import { AdminPermissionsService } from './permissions.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import {
  BranchEntity,
  CompanyEntity,
  CompanyRoleEntity,
  PermissionGroupEntity,
  UserEntity,
} from '../../entities';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      UserEntity,
      BranchEntity,
      CompanyEntity,
      CompanyRoleEntity,
      PermissionGroupEntity,
    ]),
  ],
  controllers: [AdminPermissionsController],
  providers: [AdminPermissionsService],
})
export class AdminPermissionsModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(WithUserMiddleware).forRoutes(AdminPermissionsController);
  }
}
