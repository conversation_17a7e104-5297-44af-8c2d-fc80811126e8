import { Controller, Get, Req } from '@nestjs/common';
import { Request } from 'express';

import { Permission, PermissionAction } from '../../config';
import { AdminPermissionsService } from './permissions.service';
import { WithUser, Permissions } from '../../decorators';
import { WithUserContext } from '../../interfaces';

@Controller('/v1/admin/permissions')
export class AdminPermissionsController {
  constructor(readonly AdminPermissionsService: AdminPermissionsService) {}

  @Get()
  @Permissions([Permission.CMS_ROLE_MANAGE + PermissionAction.VIEW])
  async getUsers(@Req() context: Request, @WithUser() user: WithUserContext) {
    return await this.AdminPermissionsService.getPermissions(user.company);
  }
}
