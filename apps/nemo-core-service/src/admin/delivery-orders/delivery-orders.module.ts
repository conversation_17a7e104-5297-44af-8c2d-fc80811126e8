import { MiddlewareConsumer, Module, NestModule } from '@nestjs/common';
import { DeliveryOrderEntity } from '../../entities';
import { TypeOrmModule } from '@nestjs/typeorm';
import { DeliveryOrdersController } from './delivery-orders.controller';

import { WithUserMiddleware } from '../../middlewares';
import { AdminDeliveryOrdersService } from './delivery-orders.service';

@Module({
  imports: [TypeOrmModule.forFeature([DeliveryOrderEntity])],
  controllers: [DeliveryOrdersController],
  providers: [AdminDeliveryOrdersService],
})
export class DeliveryOrdersModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(WithUserMiddleware).forRoutes(DeliveryOrdersController);
  }
}
