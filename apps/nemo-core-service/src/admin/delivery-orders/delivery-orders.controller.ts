import {
  Body,
  Controller,
  Get,
  Param,
  Patch,
  Query,
  Req,
} from '@nestjs/common';
import { Request } from 'express';
import { InjectRepository } from '@nestjs/typeorm';
import { CrudController } from '../../crud';
import { DeliveryOrderEntity } from '../../entities';
import { EntityManager, Repository, SelectQueryBuilder } from 'typeorm';
import { AdminDeliveryOrdersService } from './delivery-orders.service';
import {
  mappingUrlWithCompanyId,
  Permission,
  PermissionAction,
  Role,
} from 'src/config';
import { GetDeliveryOrdersDto } from './dto';
import { Roles, WithUser, Permissions } from '../../decorators';
import { WithUserContext } from 'src/interfaces';
import { UpdateDeliveryOrdersDto } from './dto/update-delivery-orders.dto';

@Controller('v1/admin/delivery-orders')
export class DeliveryOrdersController extends CrudController<DeliveryOrderEntity> {
  constructor(
    @InjectRepository(DeliveryOrderEntity)
    repo: Repository<DeliveryOrderEntity>,
    private readonly deliveryOrdersService: AdminDeliveryOrdersService,
  ) {
    super(DeliveryOrderEntity, 'deliveryOrder', repo, {
      resourceKeyPath: 'deliveryOrderId',
      order: { updatedAt: 'asc' },
      defaultPopulate: (ctx: Request) => {
        return ctx.params?.id
          ? ['branch', 'shopUser', 'senderUser']
          : ['branch', 'shopUser'];
      },
      defaultFilter: async (
        request: Request,
        listQuery: SelectQueryBuilder<DeliveryOrderEntity>,
      ) => {
        const xCompany = request.headers['x-company'] as string;

        const company = mappingUrlWithCompanyId(xCompany);

        return listQuery.andWhere(`r.companyId = :company`, {
          company: company,
        });
      },
      searchFilter: async (
        request: Request,
        _em: EntityManager,
        listQuery: SelectQueryBuilder<DeliveryOrderEntity>,
      ) => this.deliveryOrdersService.buildSearchQuery(request, listQuery),
      computeUpdatePayload: async (
        _ctx,
        _man,
        raw: DeliveryOrderEntity,
        data,
      ): Promise<Partial<DeliveryOrderEntity>> =>
        this.deliveryOrdersService.computeUpdatePayload(raw, data),
    });
  }

  @Get()
  @Permissions([Permission.CMS_DELIVERY_ORDER_BY_DO + PermissionAction.VIEW])
  async getDeliveryOrderList(
    @Req() context: Request,
    @Query() query: GetDeliveryOrdersDto,
  ) {
    return super.findAll(context, query);
  }

  @Get('/count')
  @Permissions([Permission.CMS_DELIVERY_ORDER_BY_DO + PermissionAction.VIEW])
  async getDeliveryOrderCount() {
    return await this.deliveryOrdersService.getDeliveryOrderCount();
  }

  @Get('/:id')
  @Permissions([Permission.CMS_DELIVERY_ORDER_BY_DO + PermissionAction.VIEW])
  async getDeliveryOrder(@Req() context: Request, @Param('id') id: string) {
    return await super.findOne(context, id);
  }

  @Patch('/:id')
  @Permissions([
    Permission.CMS_DELIVERY_ORDER_BY_DO + PermissionAction.UPDATE,
    Permission.CMS_DELIVERY_ORDER_BY_JOB + PermissionAction.UPDATE,
  ])
  async updateDeliveryOrder(
    @Req() req: Request,
    @Param('id') id: string,
    @Body() body: UpdateDeliveryOrdersDto,
    @WithUser() user: WithUserContext,
  ) {
    const deliveryOrderEntity =
      await this.deliveryOrdersService.prepareBeforeUpdate(
        user.userKey,
        body,
        id,
      );

    return await super.update(req, id, deliveryOrderEntity);
  }
}
