import { Injectable } from '@nestjs/common';
import { Request } from 'express';
import { DeliveryOrderEntity, DeliveryOrderStatus } from '../../entities';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, SelectQueryBuilder } from 'typeorm';
import { UpdateDeliveryOrdersDto } from './dto/update-delivery-orders.dto';
import { BaseExceptionService } from '../../../src/exceptions';
import { isValidISODate } from '../../../src/utils';

@Injectable()
export class AdminDeliveryOrdersService {
  constructor(
    @InjectRepository(DeliveryOrderEntity)
    private readonly deliveryOrderRepository: Repository<DeliveryOrderEntity>,
    private readonly baseExceptionService: BaseExceptionService,
  ) {}

  buildSearchQuery(
    context: Request,
    listQuery: SelectQueryBuilder<DeliveryOrderEntity>,
  ): SelectQueryBuilder<DeliveryOrderEntity> {
    const deliveryOrderId = context.query.deliveryOrderId as string;
    const status = context.query.status as string[];
    // Construct filter conditions & apply conditions
    const conditions = [
      status && `r.status IN ('${status.map((s) => s).join("','")}')`,
      deliveryOrderId && `r.deliveryOrderId ILIKE '%${deliveryOrderId}%'`,
    ].filter(Boolean);

    if (conditions.length) listQuery.andWhere(conditions.join(' AND '));

    return listQuery;
  }

  async getDeliveryOrderCount() {
    const deliveryOrderCount = await this.deliveryOrderRepository.findAndCount({
      where: { status: DeliveryOrderStatus.APPOINTMENT_PENDING },
    });

    return {
      appointmentPending: deliveryOrderCount[1],
    };
  }

  async validateInputData(
    body: UpdateDeliveryOrdersDto,
    doId: string,
  ): Promise<void> {
    const oldDeliveryOrder = await this.deliveryOrderRepository.findOne({
      where: { deliveryOrderId: doId },
    });

    if (!oldDeliveryOrder) {
      throw this.baseExceptionService.exception(
        'NOT_FOUND_DATA',
        'Delivery order not found',
      );
    }

    if (
      ![
        DeliveryOrderStatus.APPOINTMENT_CONFIRMED,
        DeliveryOrderStatus.APPOINTMENT_PENDING,
      ].includes(oldDeliveryOrder.status as DeliveryOrderStatus)
    ) {
      throw this.baseExceptionService.exception(
        'INVALID_DO_STATUS_TO_UPDATE',
        `Invalid status for update delivery order. Status must be ${DeliveryOrderStatus.APPOINTMENT_CONFIRMED} or ${DeliveryOrderStatus.APPOINTMENT_PENDING}`,
      );
    }

    if (
      !isValidISODate(body.lastUpdate) ||
      !isValidISODate(body.appointmentDate)
    ) {
      throw this.baseExceptionService.exception(
        'INVALID_INPUT_FOR_UPDATE_DO',
        `Date incorrect format. Date format should be YYYY-MM-DDTHH:MM:TT.TTTZ`,
      );
    }

    const today = new Date(new Date().toISOString().slice(0, 10));
    const appointmentDate = new Date(body.appointmentDate);
    if (appointmentDate < today) {
      throw this.baseExceptionService.exception(
        'INVALID_INPUT_FOR_UPDATE_DO',
        `Appoinment date incorrect. The appointment date cannot be in the past`,
      );
    }

    if (body.awbNumber.length !== 9) {
      throw this.baseExceptionService.exception(
        'INVALID_INPUT_FOR_UPDATE_DO',
        `Air way bill incorrect. The air way bill must have 9 character`,
      );
    }

    if (/[^\d]+/g.test(body.awbNumber)) {
      throw this.baseExceptionService.exception(
        'INVALID_INPUT_FOR_UPDATE_DO',
        `Air way bill incorrect. The air way bill must be number`,
      );
    }
  }

  async prepareBeforeUpdate(
    userKey: string,
    body: UpdateDeliveryOrdersDto,
    doId: string,
  ): Promise<DeliveryOrderEntity> {
    await this.validateInputData(body, doId);
    const deliveryOrderEntity = new DeliveryOrderEntity();
    deliveryOrderEntity.awbNumber = body.awbNumber;
    deliveryOrderEntity.updatedAt = new Date(body.lastUpdate);
    deliveryOrderEntity.appointmentDate = new Date(body.appointmentDate);
    deliveryOrderEntity.editConfirmAppointmentAt = new Date();
    deliveryOrderEntity.editConfirmAppointmentUserKey = userKey;
    deliveryOrderEntity.status = DeliveryOrderStatus.APPOINTMENT_CONFIRMED;

    return deliveryOrderEntity;
  }

  computeUpdatePayload(
    old: DeliveryOrderEntity,
    comingData: Partial<DeliveryOrderEntity>,
  ) {
    if (old.status === DeliveryOrderStatus.APPOINTMENT_PENDING) {
      if (old.updatedAt?.getTime() !== comingData.updatedAt?.getTime()) {
        throw this.baseExceptionService.exception(
          'DO_ALREADY_CHANGED',
          `Delivery orders already changed please recheck last updated data`,
        );
      }
      comingData.confirmAppointmentAt = comingData.editConfirmAppointmentAt;
      comingData.confirmAppointmentUserKey =
        comingData.editConfirmAppointmentUserKey;
    }

    comingData.updatedAt = comingData.editConfirmAppointmentAt;
    return comingData;
  }
}
