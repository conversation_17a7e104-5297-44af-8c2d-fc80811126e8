import { SchemaObject } from '@nestjs/swagger/dist/interfaces/open-api-spec.interface';
import { GetDeliveryOrdersRequest } from 'contracts';

import { createZodDto, zodToOpenAPI } from 'nestjs-zod';
import { z } from 'nestjs-zod/z';
import { DeliveryOrderStatus } from '../../../entities';

const GetDeliveryOrdersSchema: z.ZodType<GetDeliveryOrdersRequest> = z.object({
  status: z.nativeEnum(DeliveryOrderStatus).array().optional(),
  deliveryOrderId: z.string().optional(),
  orderBy: z.string().optional(),
  page: z.string().optional(),
  pageSize: z.string().optional(),
});

export const GetDeliveryOrdersOpenApi = zodToOpenAPI(
  GetDeliveryOrdersSchema,
) as SchemaObject;

export class GetDeliveryOrdersDto extends createZodDto(
  GetDeliveryOrdersSchema,
) {}
