import { SchemaObject } from '@nestjs/swagger/dist/interfaces/open-api-spec.interface';
import { UpdateDeliveryOrdersRequest } from 'contracts';

import { createZodDto, zodToOpenAPI } from 'nestjs-zod';
import { z } from 'nestjs-zod/z';

const UpdateDeliveryOrdersSchema: z.ZodType<UpdateDeliveryOrdersRequest> =
  z.object({
    awbNumber: z.string(),
    appointmentDate: z.string(),
    lastUpdate: z.string(),
  });

export const UpdateDeliveryOrdersOpenApi = zodToOpenAPI(
  UpdateDeliveryOrdersSchema,
) as SchemaObject;

export class UpdateDeliveryOrdersDto extends createZodDto(
  UpdateDeliveryOrdersSchema,
) {}
