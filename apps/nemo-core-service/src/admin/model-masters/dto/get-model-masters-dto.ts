import { toZod } from 'tozod';
import { GetModelMastersRequestAdmin } from 'contracts';
import { z } from 'nestjs-zod/z';
import { createZodDto, zodToOpenAPI } from 'nestjs-zod';
import { SchemaObject } from '@nestjs/swagger/dist/interfaces/open-api-spec.interface';

const GetModelMastersSchema: toZod<GetModelMastersRequestAdmin> = z.object({
  orderBy: z.string().optional(),
  page: z.string().optional(),
  pageSize: z.string().optional(),
});

export const GetModelMastersOpenApi = zodToOpenAPI(
  GetModelMastersSchema,
) as SchemaObject;

export class GetModelMastersDto extends createZodDto(GetModelMastersSchema) {}
