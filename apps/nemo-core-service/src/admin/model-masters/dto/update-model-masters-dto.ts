import { z } from 'nestjs-zod/z';
import { createZodDto, zodToOpenAPI } from 'nestjs-zod';
import { SchemaObject } from '@nestjs/swagger/dist/interfaces/open-api-spec.interface';

export enum TypeUpdateDataModelMasterFunctionAction {
  CREATE = 'CREATE',
  UPDATE = 'UPDATE',
  DELETE = 'DELETE',
}
export enum TypeUpdateDataModelMasterGradeGrade {
  A = 'A',
  B = 'B',
  C = 'C',
  D = 'D',
}

const updateDataModelMasterSchema = z.object({
  systemCodeList: z.array(z.string()).nonempty(),
  modelMasterGrades: z
    .array(
      z.object({
        grade: z.string(),
        purchasePrice: z.string(),
        lastPurchasedOn: z.string(),
        lastPurchasedPrice: z.string(),
      }),
    )
    .nonempty(),
});

const updateDataModelMasterFunctionSchema = z.object({
  keyCond: z.string(),
  penalties: z.string(),
  action: z.string(),
});

export const updateModelMasterSchema = z
  .object({
    companyId: z.string(),
    modelKey: z.string(),
    modelMaster: updateDataModelMasterSchema,
    modelMasterFunction: z.array(updateDataModelMasterFunctionSchema),
  })
  .strict();

export const UpdateModelMasterOpenApi = zodToOpenAPI(
  updateModelMasterSchema,
) as SchemaObject;

export class UpdateModelMasterDto extends createZodDto(
  updateModelMasterSchema,
) {}
