import { MiddlewareConsumer, Module, NestModule } from '@nestjs/common';
import {
  JobEntity,
  ModelChecklistEntity,
  ModelMasterEntity,
  SystemConfigEntity,
  ModelPriceActivitiesEntity,
} from '../../entities';
import { TypeOrmModule } from '@nestjs/typeorm';
import { WithUserMiddleware } from '../../middlewares';
import { ModelMastersController } from './model-masters.controller';
import {
  ModelMastersService,
  excelManagerOption,
} from './model-masters.service';
import { ExcelManagerModule } from '../../excel/excel-manager.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      ModelMasterEntity,
      SystemConfigEntity,
      JobEntity,
      ModelChecklistEntity,
      ModelPriceActivitiesEntity,
    ]),
    ExcelManagerModule.register(excelManagerOption),
  ],
  controllers: [ModelMastersController],
  providers: [ModelMastersService],
})
export class ModelMastersModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(WithUserMiddleware).forRoutes(ModelMastersController);
  }
}
