import { SchemaObject } from '@nestjs/swagger/dist/interfaces/open-api-spec.interface';

import { createZodDto, zodToOpenAPI } from 'nestjs-zod';
import { z } from 'nestjs-zod/z';

const setOperationCostSchema = z.object({
  operationCost: z.object({
    logistic: z.string(),
    warehouseRental: z.string(),
    productPackaging: z.string(),
  }),
  marketing: z.string(),
});

export const setOperationCostOpenApi = zodToOpenAPI(
  setOperationCostSchema,
) as SchemaObject;

export class SetOperationCostDto extends createZodDto(setOperationCostSchema) {}
