import { SchemaObject } from '@nestjs/swagger/dist/interfaces/open-api-spec.interface';
import { createZodDto, zodToOpenAPI } from 'nestjs-zod';
import { z } from 'nestjs-zod/z';

const ExportJobsSchema = z.object({
  filter: z
    .object({
      brand: z.string().optional(),
      currentGrade: z.string().optional(),
      branch: z.string().optional(),
      jobId: z.string().optional(),
      deviceKey: z.string().optional(),
      model: z.string().optional(),
      status: z.array(z.string().optional()).optional(),
      minUpdatedDate: z.string().optional(),
      maxUpdatedDate: z.string().optional(),
      minPrice: z.string().optional(),
      maxPrice: z.string().optional(),
    })
    .optional(),
  headerSlug: z.array(z.string()).optional(),
});

export const ExportJobsOpenApi = zodToOpenAPI(ExportJobsSchema) as SchemaObject;

export class ExportJobsDto extends createZodDto(ExportJobsSchema) {}
