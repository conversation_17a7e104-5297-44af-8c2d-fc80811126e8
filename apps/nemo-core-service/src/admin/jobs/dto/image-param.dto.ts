import { SchemaObject } from '@nestjs/swagger/dist/interfaces/open-api-spec.interface';
import { createZodDto, zodToOpenAPI } from 'nestjs-zod';
import { z } from 'nestjs-zod/z';

// Define a Zod schema for the ImageJobRequest interface
const ImageJobRequestSchema = z.object({
  id: z.string(),
  key: z.string(),
  slug: z.enum(['device-image', 'customer-identification', 'additional-image']),
});

export const ImageJobParamOpenApi = zodToOpenAPI(
  ImageJobRequestSchema,
) as SchemaObject;

export class ImageJobParamDto extends createZodDto(ImageJobRequestSchema) {}
