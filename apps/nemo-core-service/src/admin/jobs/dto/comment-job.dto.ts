import { SchemaObject } from '@nestjs/swagger/dist/interfaces/open-api-spec.interface';
import { CommentJobBody } from 'contracts';

import { createZodDto, zodToOpenAPI } from 'nestjs-zod';
import { z } from 'nestjs-zod/z';
import { toZod } from 'tozod';

const CommentJobSchema: toZod<CommentJobBody> = z.object({
  message: z.string().max(3000),
});

export const CommentJobOpenApi = zodToOpenAPI(CommentJobSchema) as SchemaObject;

export class CommentJobDto extends createZodDto(CommentJobSchema) {}
