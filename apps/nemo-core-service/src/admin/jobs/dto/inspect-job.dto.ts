import { SchemaObject } from '@nestjs/swagger/dist/interfaces/open-api-spec.interface';
import { createZodDto, zodToOpenAPI } from 'nestjs-zod';
import { z } from 'nestjs-zod/z';

const InspectJobSchema = z.object({
  inspectionDetail: z.string().max(200).optional(),
  inspectionResult: z.enum(['pass', 'fail']),
});

export const InspectJobOpenApi = zodToOpenAPI(InspectJobSchema) as SchemaObject;

export class InspectJobDto extends createZodDto(InspectJobSchema) {}
