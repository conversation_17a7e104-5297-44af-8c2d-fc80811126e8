import { SchemaObject } from '@nestjs/swagger/dist/interfaces/open-api-spec.interface';
import { RejectAOBody } from 'contracts';

import { createZodDto, zodToOpenAPI } from 'nestjs-zod';
import { z } from 'nestjs-zod/z';
import { toZod } from 'tozod';

const RejectAOSchema: toZod<RejectAOBody> = z.object({
  remark: z.string().max(200).optional(),
});

export const RejectAOOpenApi = zodToOpenAPI(RejectAOSchema) as SchemaObject;

export class RejectAODto extends createZodDto(RejectAOSchema) {}
