import { SchemaObject } from '@nestjs/swagger/dist/interfaces/open-api-spec.interface';
import { ReceiveJobs } from 'contracts';
import { createZodDto, zodToOpenAPI } from 'nestjs-zod';
import { z } from 'nestjs-zod/z';
import { toZod } from 'tozod';

const GetReceiveJobsSchema: toZod<ReceiveJobs> = z.object({
    jobs: z.array(z.object({
        id: z.string(),
        shippingStatus: z.string(),
        remark: z.string().nullable()
    }))
});

export const GetReceiveJobsOpenApi = zodToOpenAPI(GetReceiveJobsSchema) as SchemaObject;

export class GetReceiveJobsDto extends createZodDto(GetReceiveJobsSchema) { }