import { SchemaObject } from '@nestjs/swagger/dist/interfaces/open-api-spec.interface';
import { QCStatusBody } from 'contracts';
import { createZodDto, zodToOpenAPI } from 'nestjs-zod';
import { z } from 'nestjs-zod/z';
import { toZod } from 'tozod';

const QCStatusSchema: toZod<QCStatusBody> = z.object({
    status: z.string()
});

export const QCStatusOpenApi = zodToOpenAPI(QCStatusSchema) as SchemaObject;

export class QCStatusDto extends createZodDto(QCStatusSchema) { }