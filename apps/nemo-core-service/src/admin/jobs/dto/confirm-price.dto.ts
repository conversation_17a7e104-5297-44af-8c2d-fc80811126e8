import { SchemaObject } from '@nestjs/swagger/dist/interfaces/open-api-spec.interface';
import { SubmitConfirmPriceBodyDto } from 'contracts';
import { createZodDto, zodToOpenAPI } from 'nestjs-zod';
import { z } from 'nestjs-zod/z';
import { toZod } from 'tozod';

const ConfirmPriceSchema: toZod<SubmitConfirmPriceBodyDto> = z.object({
  jobs: z.array(
    z.object({
      jobId: z.string(),
      updatedAt: z.string(),
    }),
  ),
});

export const ConfirmPriceOpenApi = zodToOpenAPI(
  ConfirmPriceSchema,
) as SchemaObject;

export class ConfirmPriceDto extends createZodDto(ConfirmPriceSchema) {}
