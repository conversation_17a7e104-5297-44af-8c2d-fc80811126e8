import { SchemaObject } from '@nestjs/swagger/dist/interfaces/open-api-spec.interface';
import { createZodDto, zodToOpenAPI } from 'nestjs-zod';
import { z } from 'nestjs-zod/z';

const AssignRepairSchema = z.object({
  qcStatus: z.string().max(200).optional(),
});

export const AssignRepairOpenApi = zodToOpenAPI(
  AssignRepairSchema,
) as SchemaObject;

export class AssignRepairDto extends createZodDto(AssignRepairSchema) {}
