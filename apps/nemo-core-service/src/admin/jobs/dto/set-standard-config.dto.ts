import { SchemaObject } from '@nestjs/swagger/dist/interfaces/open-api-spec.interface';

import { createZodDto, zodToOpenAPI } from 'nestjs-zod';
import { z } from 'nestjs-zod/z';

const setStandardConfigSchema = z.object({
  configType: z.string(),
  configValue: z.record(z.any()),
});

export const setStandardConfigOpenApi = zodToOpenAPI(
  setStandardConfigSchema,
) as SchemaObject;

export class SetStandardConfigDto extends createZodDto(
  setStandardConfigSchema,
) {}
