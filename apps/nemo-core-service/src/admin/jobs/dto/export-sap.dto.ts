import { SchemaObject } from '@nestjs/swagger/dist/interfaces/open-api-spec.interface';
import { createZodDto, zodToOpenAPI } from 'nestjs-zod';
import { z } from 'nestjs-zod/z';

const ExportSapSchema = z.object({
  filter: z.object({
    startDate: z.dateString(),
    endDate: z.dateString(),
    dateType: z.enum(['purchased', 'received']),
  }),
  headerSlug: z.array(z.string()).optional(),
});

export const ExportSapOpenApi = zodToOpenAPI(ExportSapSchema) as SchemaObject;

export class ExportSapDto extends createZodDto(ExportSapSchema) {}
