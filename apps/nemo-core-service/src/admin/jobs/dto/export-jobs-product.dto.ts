import { SchemaObject } from '@nestjs/swagger/dist/interfaces/open-api-spec.interface';
import { createZodDto, zodToOpenAPI } from 'nestjs-zod';
import { z } from 'nestjs-zod/z';

const ExportJobsProductSchema = z.object({
  filter: z.object({
    jobId: z.string().optional(),
    deviceKey: z.string().optional(),
    brand: z.string().optional(),
    model: z.string().optional(),
    currentGrade: z.string().optional(),
    status: z.array(z.string().optional()).optional(),
    qcStatusFromTab: z.string().optional(),
  }),
  headerSlug: z.array(z.string()).optional(),
});

export const ExportJobsProductOpenApi = zodToOpenAPI(
  ExportJobsProductSchema,
) as SchemaObject;

export class ExportJobsProductDto extends createZodDto(
  ExportJobsProductSchema,
) {}
