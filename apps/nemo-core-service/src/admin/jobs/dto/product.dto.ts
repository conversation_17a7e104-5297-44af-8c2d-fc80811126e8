import { SchemaObject } from '@nestjs/swagger/dist/interfaces/open-api-spec.interface';
import { createZodDto, zodToOpenAPI } from 'nestjs-zod';
import { z } from 'nestjs-zod/z';

const updateJobProductSchema = z.object({
  retailPrice: z.number(),
  wholeSalePrice: z.number(),
});

export const UpdateJobProductOpenApi = zodToOpenAPI(
  updateJobProductSchema,
) as SchemaObject;

export class UpdateJobProductDto extends createZodDto(updateJobProductSchema) {}
