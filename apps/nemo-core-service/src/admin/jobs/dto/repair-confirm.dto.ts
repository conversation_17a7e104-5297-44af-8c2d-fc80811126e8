import { SchemaObject } from '@nestjs/swagger/dist/interfaces/open-api-spec.interface';
import { createZodDto, zodToOpenAPI } from 'nestjs-zod';
import { z } from 'nestjs-zod/z';

const RepairConfirmSchema = z.object({
  detail: z.string().max(200),
  cost: z.number().min(0).optional(),
  grade: z.string().optional(),
  type: z.enum(['confirm', 'refurbish', 'scrap']),
});

export const RepairConfirmOpenApi = zodToOpenAPI(
  RepairConfirmSchema,
) as SchemaObject;

export class RepairConfirmDto extends createZodDto(RepairConfirmSchema) {}
