import { SchemaObject } from '@nestjs/swagger/dist/interfaces/open-api-spec.interface';
import { LostAOBody } from 'contracts';

import { createZodDto, zodToOpenAPI } from 'nestjs-zod';
import { z } from 'nestjs-zod/z';
import { toZod } from 'tozod';

const LostAOSchema: toZod<LostAOBody> = z.object({
  remark: z.string().max(200).optional(),
  videoPath: z.string(),
});

export const LostAOOpenApi = zodToOpenAPI(LostAOSchema) as SchemaObject;

export class LostAODto extends createZodDto(LostAOSchema) {}
