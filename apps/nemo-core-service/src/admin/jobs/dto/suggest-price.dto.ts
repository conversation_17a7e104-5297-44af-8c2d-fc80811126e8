import { SchemaObject } from '@nestjs/swagger/dist/interfaces/open-api-spec.interface';

import { createZodDto, zodToOpenAPI } from 'nestjs-zod';
import { z } from 'nestjs-zod/z';

const SuggestPriceSchema = z.object({
  suggestedPrice: z.number().optional(),
  grade: z.string().max(10),
  adminCheckListValues: z.any().optional(),
});

export const SuggestPriceOpenApi = zodToOpenAPI(
  SuggestPriceSchema,
) as SchemaObject;

export class SuggestPriceDto extends createZodDto(SuggestPriceSchema) {}
