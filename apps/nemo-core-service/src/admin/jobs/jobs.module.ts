import { MiddlewareConsumer, Module, NestModule } from '@nestjs/common';
import {
  BranchEntity,
  ContractEntity,
  DeliveryOrderEntity,
  JobActivitiesEntity,
  JobEntity,
  JobTemplateEntity,
  ModelMasterEntity,
  PenaltiesView,
  VoucherEntity,
  EmailActivitiesEntity,
  ImportedVoucherEntity,
  ExportActivitiesEntity,
  CompanyRoleEntity,
  ConfigActivitiesEntity,
  EstimationActivitiesEntity,
  ModelMasterColorEntity,
  AllocationOrderEntity,
  GeneralActivitiesEntity,
  CampaignEntity,
  CampaignRedemptionCodeEntity,
  RoleEntity,
  UserRoleBranchEntity,
} from '../../entities';
import { MasterAddressEntity } from '../../entities/address-master.entity';
import { TypeOrmModule } from '@nestjs/typeorm';
import { WithUserMiddleware } from '../../middlewares';
import { JobsController } from './jobs.controller';
import { AdminJobsService } from './jobs.service';
import { AdminUsersService } from '../users/users.service';
import { JobsService } from 'src/shop/jobs/jobs.service';
import { S3Service } from '../../storage/s3.service';
import { ContractsService } from 'src/shop/contracts/contracts.service';
import { SmtpService } from '../../smtp/smtp.service';
import { OcrService } from '../../ocr/ocr.service';
import { HttpClient } from '../../http-client/http-client.service';

import { excelManagerOption } from '../model-masters/model-masters.service';
import { ExcelManagerModule } from '../../excel/excel-manager.module';
import { BranchesService } from '../branches/branches.service';
import { SystemConfigService } from 'src/system-config/system-config.service';
import { AdminAllocationOrdersService } from '../allocation-orders/allocation-orders.service';
import { JobRequestQueueModule } from '../../shop/jobs/job-request-queue.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      JobEntity,
      JobActivitiesEntity,
      ModelMasterEntity,
      PenaltiesView,
      JobTemplateEntity,
      ContractEntity,
      BranchEntity,
      VoucherEntity,
      DeliveryOrderEntity,
      SmtpService,
      EmailActivitiesEntity,
      ImportedVoucherEntity,
      ExportActivitiesEntity,
      CompanyRoleEntity,
      ConfigActivitiesEntity,
      EstimationActivitiesEntity,
      ModelMasterColorEntity,
      AllocationOrderEntity,
      MasterAddressEntity,
      GeneralActivitiesEntity,
      CampaignEntity,
      CampaignRedemptionCodeEntity,
      RoleEntity,
      UserRoleBranchEntity,
    ]),
    ExcelManagerModule.register(excelManagerOption),
    JobRequestQueueModule,
  ],
  controllers: [JobsController],
  providers: [
    JobsService,
    AdminJobsService,
    S3Service,
    ContractsService,
    SmtpService,
    AdminUsersService,
    BranchesService,
    SystemConfigService,
    AdminAllocationOrdersService,
    OcrService,
    HttpClient,
  ],
})
export class JobsModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(WithUserMiddleware).forRoutes(JobsController);
  }
}
