import { Injectable } from '@nestjs/common';
import { Process, Processor } from '@nestjs/bull';
import { Job } from 'bull';
import { InjectRepository } from '@nestjs/typeorm';
import { In, Repository } from 'typeorm';
import { JobEntity, JobStatus } from '../../entities';
import {
  calculateProductPrices,
  getMargin,
  isNumber,
} from '../../utils/job/calculateProduct';

@Processor('re-calculate-product-queue')
@Injectable()
class JobsConsumer {
  constructor(
    @InjectRepository(JobEntity)
    private readonly jobsRepo: Repository<JobEntity>,
  ) {}
  @Process('re-calculate-product')
  async reCalculateProductQueue(job: Job<any>) {
    if (job.data.jobId) {
      const jobEntity = await this.jobsRepo.findOne({
        where: {
          jobId: job.data.jobId,
          status: In([
            JobStatus.INSPECTION_COMPLETED,
            JobStatus.INSPECTION_AUTO_COMPLETED,
          ]),
        },
        relations: ['modelMaster'],
      });
      if (jobEntity) {
        const {
          wholeSaleMargin,
          wholeSalePrice,
          marginWholeSaleBaht,
          retailMargin,
          retailPrice,
          marginRetailBaht,
          costPrice,
        } = calculateProductPrices(jobEntity, job.data.costConfig);
        if (
          jobEntity.adminUpdateCostListValue &&
          jobEntity.adminUpdateCostListValue.length > 0
        ) {
          const wholeSaleMargin = isNumber(jobEntity.wholeSalePrice)
            ? getMargin(costPrice, jobEntity.wholeSalePrice as number)
            : { baht: null, percent: null };
          const retailMargin = isNumber(jobEntity.retailPrice)
            ? getMargin(costPrice, jobEntity.retailPrice as number)
            : { baht: null, percent: null };
          jobEntity.costPrice = costPrice;
          jobEntity.wholeSaleMargin = wholeSaleMargin.percent;
          jobEntity.marginWholeSaleBaht = wholeSaleMargin.baht;
          jobEntity.retailMargin = retailMargin.percent;
          jobEntity.marginRetailBaht = retailMargin.baht;
        } else {
          jobEntity.wholeSaleMargin = wholeSaleMargin;
          jobEntity.marginWholeSaleBaht = marginWholeSaleBaht;
          jobEntity.wholeSalePrice = wholeSalePrice;
          jobEntity.retailMargin = retailMargin;
          jobEntity.marginRetailBaht = marginRetailBaht;
          jobEntity.retailPrice = retailPrice;
          jobEntity.costPrice = costPrice;
        }

        jobEntity.isConfirmPrice = false;
        delete (jobEntity as any).modelMaster;
        await this.jobsRepo.save(jobEntity);
      }
    }
  }
}
export { JobsConsumer };
