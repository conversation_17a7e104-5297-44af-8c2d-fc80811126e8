import { MiddlewareConsumer, Module, NestModule } from '@nestjs/common';
import {
  AllocationOrderEntity,
  JobEntity,
  ContractEntity,
  VoucherEntity,
  BranchEntity,
  CompanyEntity,
  SystemConfigEntity,
  EmailActivitiesEntity,
  ConfigActivitiesEntity,
} from '../../entities';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AllocationOrdersController } from './allocation-orders.controller';

import { WithUserMiddleware } from '../../middlewares';
import { AdminAllocationOrdersService } from './allocation-orders.service';
import { ContractsService } from '../../../src/shop/contracts/contracts.service';
import { CacheManagerService } from '../../../src/cache-manager/cache-manager.service';
import { S3Service } from '../../../src/storage/s3.service';
import { SmtpService } from '../../../src/smtp/smtp.service';
import { SystemConfigService } from 'src/system-config/system-config.service';
import { AllocationOrderEntitySubscriber } from '../../../src/subscriber/allocation-order.subscriber';
@Module({
  imports: [
    TypeOrmModule.forFeature([
      AllocationOrderEntity,
      JobEntity,
      ConfigActivitiesEntity,
      // contract call these
      ContractEntity,
      VoucherEntity,
      BranchEntity,
      CompanyEntity,
      EmailActivitiesEntity,
      SystemConfigEntity,
    ]),
  ],
  controllers: [AllocationOrdersController],
  providers: [
    AdminAllocationOrdersService,
    SystemConfigService,
    // contract call these
    ContractsService,
    CacheManagerService,
    S3Service,
    SmtpService,
    AllocationOrderEntitySubscriber,
  ],
})
export class AllocationOrdersModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(WithUserMiddleware).forRoutes(AllocationOrdersController);
  }
}
