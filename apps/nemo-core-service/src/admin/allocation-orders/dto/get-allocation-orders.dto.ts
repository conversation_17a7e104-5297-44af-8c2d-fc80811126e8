import { SchemaObject } from '@nestjs/swagger/dist/interfaces/open-api-spec.interface';
import { GetAllocationOrderRequest } from 'contracts';

import { createZodDto, zodToOpenAPI } from 'nestjs-zod';
import { z } from 'nestjs-zod/z';
import { AllocationOrderStatus, AllocationOrderType } from '../../../entities';

const GetAllocationOrdersSchema: z.ZodType<GetAllocationOrderRequest> =
  z.object({
    status: z.nativeEnum(AllocationOrderStatus).array().optional(),
    orderBy: z.string().optional(),
    page: z.string().optional(),
    pageSize: z.string().optional(),
    allocationOrderId: z.string().optional(),
    type: z.nativeEnum(AllocationOrderType).optional(),
  });

export const GetAllcationOrdersOpenApi = zodToOpenAPI(
  GetAllocationOrdersSchema,
) as SchemaObject;

export class GetAllcationOrdersDto extends createZodDto(
  GetAllocationOrdersSchema,
) {}
