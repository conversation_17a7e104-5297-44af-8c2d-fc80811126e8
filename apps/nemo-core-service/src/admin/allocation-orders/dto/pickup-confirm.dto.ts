import { SchemaObject } from '@nestjs/swagger/dist/interfaces/open-api-spec.interface';
import { createZodDto, zodToOpenAPI } from 'nestjs-zod';
import { z } from 'nestjs-zod/z';

const pickupConfirmSchema = z.object({
  transporterName: z.string(),
  transporterMobileNumber: z.string(),
});

export const AOPickupConfirmOpenApi = zodToOpenAPI(
  pickupConfirmSchema,
) as SchemaObject;

export class AOPickupConfirmDto extends createZodDto(pickupConfirmSchema) {}
