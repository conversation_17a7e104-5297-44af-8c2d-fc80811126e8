import { SchemaObject } from '@nestjs/swagger/dist/interfaces/open-api-spec.interface';
import { createZodDto, zodToOpenAPI } from 'nestjs-zod';
import { z } from 'nestjs-zod/z';
import { AllocationOrderType } from '../../../entities';

const createAOSchema = z.object({
  fromBranchId: z.string().max(30),
  toBranchId: z.string().max(30),
  aoType: z.nativeEnum(AllocationOrderType),
  jobList: z.array(z.string().min(1)).min(1),
});

export const CreateAOOpenApi = zodToOpenAPI(createAOSchema) as SchemaObject;

export class CreateAODto extends createZodDto(createAOSchema) {}
