import { SchemaObject } from '@nestjs/swagger/dist/interfaces/open-api-spec.interface';
import { createZodDto, zodToOpenAPI } from 'nestjs-zod';
import { z } from 'nestjs-zod/z';

const awbConfirmSchema = z.object({
  awbNumber: z.string(),
  appointmentDate: z.dateString().optional(),
  pickupDate: z.dateString().optional(),
});

export const AOAwbConfirmOpenApi = zodToOpenAPI(
  awbConfirmSchema,
) as SchemaObject;

export class AOAwbConfirmDto extends createZodDto(awbConfirmSchema) {}
