import { SchemaObject } from '@nestjs/swagger/dist/interfaces/open-api-spec.interface';
import { createZodDto, zodToOpenAPI } from 'nestjs-zod';
import { z } from 'nestjs-zod/z';
import { AllocationOrderType } from '../../../entities';

const createDraftSchema = z.object({
  fromBranchId: z.string().max(30),
  toBranchId: z.string().max(30),
  aoType: z.nativeEnum(AllocationOrderType),
});

export const CreateDraftOpenApi = zodToOpenAPI(
  createDraftSchema,
) as SchemaObject;

export class CreateDraftDto extends createZodDto(createDraftSchema) {}
