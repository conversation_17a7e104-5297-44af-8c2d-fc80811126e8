import { Injectable } from '@nestjs/common';
import { Request } from 'express';
import {
  AllocationOrderEntity,
  AllocationOrderStatus,
  AllocationOrderType,
  JobEntity,
  JobStatus,
  CompanyEntity,
  BranchEntity,
  branchType,
  AOShippingStatus,
} from '../../entities';
import { ContractsService } from '../../../src/shop/contracts/contracts.service';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, SelectQueryBuilder, In } from 'typeorm';
import { BaseExceptionService } from '../../exceptions';
import { WithUserContext } from '../../interfaces';
import { InjectQueue } from '@nestjs/bull';
import { Queue } from 'bull';
import { SystemConfigService } from '../../system-config/system-config.service';
import { AOPickupConfirmDto } from './dto/pickup-confirm.dto';
import { AOAwbConfirmDto } from './dto/awb-confirm.dto';
import { getDateFromToday } from '../../../src/utils/general';
import { S3Service } from '../../storage/s3.service';

@Injectable()
export class AdminAllocationOrdersService {
  constructor(
    @InjectRepository(AllocationOrderEntity)
    private readonly allocationOrderRepository: Repository<AllocationOrderEntity>,
    @InjectRepository(JobEntity)
    private readonly jobRepository: Repository<JobEntity>,
    @InjectRepository(CompanyEntity)
    private readonly companyRepo: Repository<CompanyEntity>,
    @InjectRepository(BranchEntity)
    private readonly branchRepository: Repository<BranchEntity>,
    private readonly contractsService: ContractsService,
    private readonly baseExceptionService: BaseExceptionService,
    @InjectQueue('re-calculate-product-queue')
    private readonly queue: Queue,
    private readonly systemConfigService: SystemConfigService<string>,
    private readonly s3Service: S3Service,
  ) {}

  buildSearchQuery(
    context: Request,
    listQuery: SelectQueryBuilder<AllocationOrderEntity>,
  ): SelectQueryBuilder<AllocationOrderEntity> {
    const allocationOrderId = context.query.allocationOrderId as string;
    const status = context.query.status as string[];
    const type = context.query.type as string;
    // Construct filter conditions & apply conditions
    const conditions = [
      status && `r.status IN ('${status.map((s) => s).join("','")}')`,
      allocationOrderId && `r.allocationOrderId ILIKE '%${allocationOrderId}%'`,
      type && `r.allocationOrderType = '${type}'`,
    ].filter(Boolean);

    if (conditions.length) listQuery.andWhere(conditions.join(' AND '));

    return listQuery;
  }

  async createDraft(user: WithUserContext): Promise<string> {
    const companyId = user.company;
    const currentDate = new Date();

    const monthYearAO = `AO-${currentDate.getFullYear()}${(
      currentDate.getMonth() + 1
    )
      .toString()
      .padStart(2, '0')}`;

    const latestAllocationOrder = await this.allocationOrderRepository.findOne({
      where: { companyId: companyId },
      order: {
        allocationOrderId: 'DESC',
      },
    });
    const latestId = latestAllocationOrder?.allocationOrderId;
    let newId = monthYearAO;

    // check existing record
    if (latestId) {
      //check running id in month year
      if (latestId.substring(0, 9) === monthYearAO) {
        const runningId = parseInt(latestId.replace(monthYearAO, ''));
        newId = `${newId}${(runningId + 1).toString().padStart(4, '0')}`;
      } else {
        newId = `${newId}0001`;
      }
    } else {
      newId = `${newId}0001`;
    }

    const allocationOrderEntity = new AllocationOrderEntity();
    allocationOrderEntity.allocationOrderId = newId;
    allocationOrderEntity.createdBy = user.userKey;
    allocationOrderEntity.companyId = companyId;
    allocationOrderEntity.quantity = 0;

    try {
      await this.allocationOrderRepository.insert(allocationOrderEntity);
    } catch (error) {
      throw this.baseExceptionService.exception(
        'INVALID_ALLOCATION_DRAFT',
        `Insert error during create draft`,
      );
    }

    return allocationOrderEntity.allocationOrderId;
  }

  async createAllocationOrder(
    aoId: string,
    user: WithUserContext,
    jobList: string[],
    fromBranchId: string,
    toBranchId: string,
    aoType: AllocationOrderType,
  ) {
    const jobResultList: JobEntity[] = [];
    const jobUpdateFailList: string[] = [];
    const jobStatusFailList: string[] = [];
    const aoEntity = await this.allocationOrderRepository.findOne({
      where: { allocationOrderId: aoId },
    });

    if (!aoEntity) {
      throw this.baseExceptionService.exception(
        'NOT_FOUND_DATA',
        'Allocation order not found',
      );
    }

    if (aoEntity.status !== AllocationOrderStatus.DRAFT) {
      throw this.baseExceptionService.exception(
        'INVALID_AO_STATUS_TO_UPDATE',
        `Allocation order status must be ${AllocationOrderStatus.DRAFT}`,
      );
    }

    // validate existing branch
    await this.validateBranch(fromBranchId, true);
    await this.validateBranch(toBranchId);

    const isDuplicatedJob =
      jobList.filter((item, index) => jobList.indexOf(item) !== index).length >
      0;

    if (isDuplicatedJob) {
      throw this.baseExceptionService.exception(
        'DUPLICATED_JOB',
        `Input jobList has duplicated data`,
      );
    }
    const jobListEntity = await this.jobRepository.find({
      where: {
        jobId: In(jobList),
      },
    });

    if (jobListEntity.length !== jobList.length) {
      throw this.baseExceptionService.exception(
        'INVALID_JOB_ID_INPUT',
        `Invalid jobId`,
      );
    }

    const statusWhiteList = [
      JobStatus.INSPECTION_COMPLETED,
      JobStatus.INSPECTION_AUTO_COMPLETED,
    ];

    jobList.forEach((jobId) => {
      const job = jobListEntity.find((job) => job.jobId === jobId);

      if (job) {
        if (!statusWhiteList.includes(job.status)) {
          jobStatusFailList.push(job.jobId);
        } else if (job.updatedAt && aoEntity.updatedAt) {
          if (aoEntity.updatedAt < job.updatedAt || !job.isConfirmPrice) {
            jobUpdateFailList.push(job.jobId);
          } else {
            job.allocationOrderId = aoEntity.allocationOrderId;
            jobResultList.push(job);
          }
        } else {
          jobUpdateFailList.push(job.jobId);
        }
      }
    });

    if (jobStatusFailList.length > 0 || jobUpdateFailList.length > 0) {
      // update datetime
      aoEntity.updatedAt = new Date();
      await this.allocationOrderRepository.save(aoEntity);

      throw this.baseExceptionService.exception('JOB_ALREADY_CHANGED', {
        jobList: jobResultList,
        updatedJobList: jobUpdateFailList,
        unavailableJobList: jobStatusFailList,
      });
    }

    const queryRunner =
      this.allocationOrderRepository.manager.connection.createQueryRunner();

    try {
      // Connection query runner
      await queryRunner.connect();

      // Start transaction
      await queryRunner.startTransaction();

      // Assign allocatioin order properties
      aoEntity.status = AllocationOrderStatus.APPOINTMENT_PENDING;
      aoEntity.fromBranchId = fromBranchId;
      aoEntity.toBranchId = toBranchId;
      aoEntity.allocationOrderType = aoType;
      aoEntity.quantity = jobResultList.length;
      aoEntity.jobs = jobResultList;
      aoEntity.createdAt = new Date();

      aoEntity.jobs.forEach((job) => {
        job.status = JobStatus.AO_CREATED;
        job.updatedBy = user.userKey;
      });

      // Update job status
      await queryRunner.manager.save(aoEntity.jobs);

      // Update allocation order
      await queryRunner.manager.save(aoEntity);

      // Commit transaction
      await queryRunner.commitTransaction();
    } catch (error) {
      // Rollback transaction when error query
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      // Release query runner
      await queryRunner.release();
    }

    return {
      jobList: jobResultList,
      updatedJobList: jobUpdateFailList,
      unavailableJobList: jobStatusFailList,
    };
  }

  async confirmAWB({
    allocationOrderId,
    user,
    body,
  }: {
    allocationOrderId: string;
    user: WithUserContext;
    body: AOAwbConfirmDto;
  }) {
    // --- find AO --- //
    const allocationOrder = await this.allocationOrderRepository.findOne({
      where: { allocationOrderId },
      relations: ['createdUser'],
    });

    // --- validate --- //
    if (!allocationOrder) {
      throw this.baseExceptionService.exception(
        'NOT_FOUND_DATA',
        'Allocation order ID not found',
      );
    }

    const { allocationOrderType, status, createdBy } = allocationOrder;
    const { userKey } = user;
    const { awbNumber, appointmentDate, pickupDate } = body;
    // validate permission
    if (createdBy !== userKey) {
      throw this.baseExceptionService.exception(
        'INVALID_AO_OWNER',
        'Need permission for update AO',
      );
    }
    // validate payload
    // awbNumber validate in DTO -> not reach this step
    if (allocationOrderType === AllocationOrderType.RETAIL) {
      if (!appointmentDate) {
        throw this.baseExceptionService.exception(
          'BODY_PAYLOAD_INVALID',
          'AppointmentDate need for update AO',
        );
      }
    } else if (allocationOrderType === AllocationOrderType.WHOLESALE) {
      if (!pickupDate) {
        throw this.baseExceptionService.exception(
          'BODY_PAYLOAD_INVALID',
          'PickupDate need for update AO',
        );
      }
    }
    // validate previous field
    if (status !== AllocationOrderStatus.APPOINTMENT_PENDING) {
      throw this.baseExceptionService.exception(
        'INVALID_AO_STATUS_TO_UPDATE',
        `Previous AO Status must be ${AllocationOrderStatus.APPOINTMENT_PENDING}`,
      );
    }
    // validate input field
    const today = new Date(getDateFromToday()[3]);

    if (
      allocationOrderType === AllocationOrderType.RETAIL &&
      new Date(appointmentDate as string) < today
    ) {
      throw this.baseExceptionService.exception(
        'INVALID_INPUT_FOR_UPDATE_AO',
        `Appoinment date incorrect. The appointment date cannot be in the past`,
      );
    }
    if (
      allocationOrderType === AllocationOrderType.WHOLESALE &&
      new Date(pickupDate as string) < today
    ) {
      throw this.baseExceptionService.exception(
        'INVALID_INPUT_FOR_UPDATE_AO',
        `Pickup date incorrect. The pickup date cannot be in the past`,
      );
    }

    if (body.awbNumber.length !== 9) {
      throw this.baseExceptionService.exception(
        'INVALID_INPUT_FOR_UPDATE_AO',
        `Air way bill incorrect. The air way bill must have 9 character`,
      );
    }

    if (/[^\d]+/g.test(body.awbNumber)) {
      throw this.baseExceptionService.exception(
        'INVALID_INPUT_FOR_UPDATE_AO',
        `Air way bill incorrect. The air way bill must be number`,
      );
    }

    // --- update --- //
    const now = new Date();

    // both type ao entity
    allocationOrder.awbNumber = awbNumber;
    allocationOrder.updatedAt = now;
    // by type
    if (allocationOrderType === AllocationOrderType.RETAIL) {
      allocationOrder.status = AllocationOrderStatus.APPOINTMENT_CONFIRMED;
      allocationOrder.appointmentDate = new Date(appointmentDate as string);
      allocationOrder.confirmAppointmentAt = now;
      allocationOrder.confirmAppointmentUserKey = userKey;
    } else if (allocationOrderType === AllocationOrderType.WHOLESALE) {
      allocationOrder.status = AllocationOrderStatus.DELIVERY_SUCCESSFUL;
      allocationOrder.pickupDate = new Date(pickupDate as string);
      allocationOrder.confirmPickupAt = now;
      allocationOrder.confirmPickupUserKey = userKey;
      if (allocationOrderType === AllocationOrderType.WHOLESALE) {
        const jobs = await this.jobRepository.find({
          where: {
            allocationOrderId,
          },
        });

        for (let job of jobs) {
          job.aoShippingStatus = AOShippingStatus.RECEIVED;
          job.status = JobStatus.AO_RECEIVED;
        }

        allocationOrder.jobs = jobs;
      }
    }

    const queryRunner =
      this.jobRepository.manager.connection.createQueryRunner();

    try {
      await queryRunner.connect();
      await queryRunner.startTransaction();
      if (allocationOrderType === AllocationOrderType.WHOLESALE) {
        await queryRunner.manager.save(allocationOrder.jobs);
      }
      await queryRunner.manager.save(allocationOrder);
      await queryRunner.commitTransaction();
    } catch (error) {
      // Rollback transaction when error query
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      // Release query runner
      await queryRunner.release();
    }

    return true;
  }

  async confirmPickup({
    allocationOrderId,
    user,
    body,
  }: {
    allocationOrderId: string;
    user: WithUserContext;
    body: AOPickupConfirmDto;
  }) {
    // --- find AO --- //
    const allocationOrder = await this.allocationOrderRepository.findOne({
      where: { allocationOrderId },
      relations: ['createdUser'],
    });

    // --- validate --- //
    if (!allocationOrder) {
      throw this.baseExceptionService.exception(
        'NOT_FOUND_DATA',
        'Allocation order ID not found',
      );
    }

    const { allocationOrderType, status, createdBy } = allocationOrder;
    const { userKey } = user;
    const { transporterName, transporterMobileNumber } = body;
    // validate permission
    if (createdBy !== userKey) {
      throw this.baseExceptionService.exception(
        'INVALID_AO_OWNER',
        'Need permission for update AO',
      );
    }
    // validate previous field
    if (status !== AllocationOrderStatus.APPOINTMENT_CONFIRMED) {
      throw this.baseExceptionService.exception(
        'INVALID_AO_STATUS_TO_UPDATE',
        `Previous AO Status must be ${AllocationOrderStatus.APPOINTMENT_CONFIRMED}`,
      );
    }
    if (allocationOrderType !== AllocationOrderType.RETAIL) {
      throw this.baseExceptionService.exception(
        'INVALID_AO_STATUS_TO_UPDATE',
        `allocationOrderType must be ${AllocationOrderType.RETAIL}`,
      );
    }
    // validate input field
    if (transporterName.length > 100) {
      throw this.baseExceptionService.exception(
        'INVALID_INPUT_FOR_UPDATE_AO',
        'TransporterName too long',
      );
    }

    const validateTransporterMobileNumber = /^0\d{9}$/.test(
      transporterMobileNumber,
    );
    if (!validateTransporterMobileNumber) {
      throw this.baseExceptionService.exception(
        'INVALID_INPUT_FOR_UPDATE_AO',
        'TransporterMobileNumber invalid format',
      );
    }

    // --- update --- //
    const now = new Date();

    // ao entity
    allocationOrder.transporterName = transporterName;
    allocationOrder.transporterMobileNumber = transporterMobileNumber;
    allocationOrder.status = AllocationOrderStatus.IN_TRANSIT;
    allocationOrder.pickupDate = now;
    allocationOrder.confirmPickupAt = now;
    allocationOrder.confirmPickupUserKey = userKey;
    allocationOrder.updatedAt = now;

    const jobs = await this.jobRepository.find({
      where: {
        allocationOrderId,
      },
    });

    for (let job of jobs) {
      job.aoShippingStatus = AOShippingStatus.SHIPPED;
    }

    const queryRunner =
      this.jobRepository.manager.connection.createQueryRunner();

    try {
      await queryRunner.connect();
      await queryRunner.startTransaction();
      await queryRunner.manager.save(jobs);
      await queryRunner.manager.save(allocationOrder);
      await queryRunner.commitTransaction();
    } catch (error) {
      // Rollback transaction when error query
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      // Release query runner
      await queryRunner.release();
    }

    return true;
  }

  async validateBranch(branchId: string, isSourceBranch?: boolean) {
    const branchEntity = await this.branchRepository.findOne({
      where: {
        branchId: branchId,
      },
    });

    if (!branchEntity) {
      throw this.baseExceptionService.exception(
        'NOT_FOUND_DATA',
        `Branch not found`,
      );
    }

    if (
      isSourceBranch &&
      branchEntity.branchType !== branchType.SHOP &&
      branchEntity.branchType !== branchType.WAREHOUSE
    ) {
      throw this.baseExceptionService.exception(
        'INVALID_BRANCH_TYPE',
        `fromBranchType is not ${branchType.SHOP}/${branchType.WAREHOUSE}`,
      );
    }
  }

  async getTransaction(allocationOrderId: string) {
    const allocationOrder = await this.allocationOrderRepository.findOne({
      where: { allocationOrderId },
      relations: ['createdUser', 'fromBranch', 'toBranch'],
    });

    if (!allocationOrder) {
      throw this.baseExceptionService.exception(
        'NOT_FOUND_DATA',
        'Allocation order ID not found',
      );
    }

    const {
      companyId,
      status,
      awbNumber,
      appointmentDate,
      pickupDate,
      allocationOrderType,
      fromBranchId,
      toBranchId,
    } = allocationOrder;

    if (
      ![
        AllocationOrderStatus.APPOINTMENT_CONFIRMED,
        AllocationOrderStatus.IN_TRANSIT,
        AllocationOrderStatus.PARTIAL_RECEIVED,
        AllocationOrderStatus.DELIVERY_SUCCESSFUL,
      ].includes(status)
    ) {
      throw this.baseExceptionService.exception(
        'INVALID_AO_FOR_ACTION',
        `Allocation order status not match.`,
      );
    }

    if (
      !awbNumber ||
      !allocationOrderType ||
      (allocationOrderType === AllocationOrderType.RETAIL &&
        !appointmentDate) ||
      (allocationOrderType === AllocationOrderType.WHOLESALE && !pickupDate) ||
      !fromBranchId ||
      !toBranchId
    ) {
      throw this.baseExceptionService.exception('INVALID_AO_FOR_ACTION');
    }

    const company = await this.companyRepo.findOne({
      where: { companyId },
    });

    if (!company?.logoPath) {
      throw this.baseExceptionService.exception(
        'NOT_FOUND_DATA',
        'Company image not found - invalid url',
      );
    }
    const jobs = await this.jobRepository.find({
      where: {
        allocationOrderId,
      },
      order: {
        jobId: 'ASC',
      },
    });

    return await this.contractsService.getTransactionAo({
      allocationOrder,
      jobs,
      companyLogoS3Path: company.logoPath,
    });
  }

  async deleteAllocationOrder(aoId: string, user: WithUserContext) {
    const aoEntity = await this.allocationOrderRepository.findOne({
      where: { allocationOrderId: aoId },
      relations: { jobs: true },
    });

    if (!aoEntity) {
      throw this.baseExceptionService.exception(
        'NOT_FOUND_DATA',
        'Allocation order not found',
      );
    }

    if (aoEntity.createdBy !== user.userKey) {
      throw this.baseExceptionService.exception(
        'INVALID_AO_OWNER',
        `user (${user.name}) does not have permission to update this AllocationOrder`,
      );
    }

    if (aoEntity.status !== AllocationOrderStatus.APPOINTMENT_PENDING) {
      throw this.baseExceptionService.exception(
        'INVALID_AO_STATUS_TO_UPDATE',
        `Invalid AllocationOrder status. AllocationOrder status must be ${AllocationOrderStatus.APPOINTMENT_PENDING}`,
      );
    }

    const queryRunner =
      this.allocationOrderRepository.manager.connection.createQueryRunner();

    let jobs: JobEntity[] = [];

    try {
      // Connection query runner
      await queryRunner.connect();

      // Start transaction
      await queryRunner.startTransaction();

      // rollback job status before delete ao
      aoEntity.jobs.forEach((job) => {
        if (job.inspectedBy === null) {
          job.status = JobStatus.INSPECTION_AUTO_COMPLETED;
        } else {
          job.status = JobStatus.INSPECTION_COMPLETED;
        }

        job.updatedBy = user.userKey;
      });

      // Update job status
      await queryRunner.manager.save(aoEntity.jobs);

      jobs = aoEntity.jobs;

      // Assign allocatioin order properties
      aoEntity.status = AllocationOrderStatus.DELETED;
      aoEntity.quantity = 0;
      aoEntity.jobs = [] as JobEntity[];
      // Update allocation order
      await queryRunner.manager.save(aoEntity);

      // Commit transaction
      await queryRunner.commitTransaction();
    } catch (error) {
      // Rollback transaction when error query
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      // sent job to recal queue
      const costConfig = await this.systemConfigService.getSystemConfig(
        user.company,
        'operation_cost',
      );
      jobs.forEach((job) => {
        this.queue.add('re-calculate-product', {
          jobId: job.jobId,
          costConfig,
        });
      });

      // Release query runner
      await queryRunner.release();
    }

    return null;
  }

  async getPresignDownload(aoId: string) {
    const allocationOrder = await this.allocationOrderRepository.findOne({
      where: { allocationOrderId: aoId },
    });

    if (!allocationOrder) {
      throw this.baseExceptionService.exception(
        'NOT_FOUND_DATA',
        'Allocation order ID not found',
      );
    }

    const keyPath = allocationOrder.videoPath;

    if (!keyPath) {
      throw this.baseExceptionService.exception(
        'NOT_FOUND_DATA',
        'Video path not found',
      );
    }

    try {
      const url = await this.s3Service.getFileWithSignedUrl(keyPath);
      return { url: url };
    } catch (error) {
      console.log('s3 getFile error', keyPath, error);
      throw error;
    }
  }
}
