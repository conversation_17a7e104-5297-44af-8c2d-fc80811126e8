import {
  Body,
  Controller,
  Get,
  Param,
  Post,
  Query,
  Req,
  Delete,
  Patch,
} from '@nestjs/common';
import { Request } from 'express';
import { InjectRepository } from '@nestjs/typeorm';
import { CrudController } from '../../crud';
import { AllocationOrderEntity } from '../../entities';
import { Repository, SelectQueryBuilder, EntityManager } from 'typeorm';
import { AdminAllocationOrdersService } from './allocation-orders.service';
import {
  mappingUrlWithCompanyId,
  Permission,
  PermissionAction,
} from 'src/config';
import { WithUser, Permissions } from '../../decorators';
import { WithUserContext } from 'src/interfaces';
import { CreateAODto, GetAllcationOrdersDto } from './dto';
import { AOAwbConfirmDto } from './dto/awb-confirm.dto';
import { AOPickupConfirmDto } from './dto/pickup-confirm.dto';

@Controller('v1/admin/allocation-orders')
export class AllocationOrdersController extends CrudController<AllocationOrderEntity> {
  constructor(
    @InjectRepository(AllocationOrderEntity)
    repo: Repository<AllocationOrderEntity>,
    private readonly allocationOrdersService: AdminAllocationOrdersService,
  ) {
    super(AllocationOrderEntity, 'allocationOrder', repo, {
      resourceKeyPath: 'allocationOrderId',
      order: { updatedAt: 'asc' },
      defaultPopulate: (ctx: Request) => {
        return ['fromBranch', 'toBranch', 'createdUser', 'receiverUser'];
      },
      defaultFilter: async (
        request: Request,
        listQuery: SelectQueryBuilder<AllocationOrderEntity>,
      ) => {
        const xCompany = request.headers['x-company'] as string;

        const company = mappingUrlWithCompanyId(xCompany);

        return listQuery.andWhere(`r.companyId = :company`, {
          company: company,
        });
      },
      searchFilter: async (
        request: Request,
        _em: EntityManager,
        listQuery: SelectQueryBuilder<AllocationOrderEntity>,
      ) => this.allocationOrdersService.buildSearchQuery(request, listQuery),
    });
  }

  @Get()
  @Permissions([Permission.CMS_ALLOCATION_ORDER_ALL + PermissionAction.VIEW])
  async getAllocationOrderList(
    @Req() context: Request,
    @Query() query: GetAllcationOrdersDto,
  ) {
    return super.findAll(context, query);
  }

  @Post('/draft')
  @Permissions([Permission.CMS_ALLOCATION_ORDER_ALL + PermissionAction.CREATE])
  async createAODraft(@WithUser() user: WithUserContext) {
    const allocationOrderId =
      await this.allocationOrdersService.createDraft(user);

    return { allocationOrderId: allocationOrderId };
  }

  @Post('/:id/create')
  @Permissions([Permission.CMS_ALLOCATION_ORDER_ALL + PermissionAction.CREATE])
  async createAllocationOrder(
    @WithUser() user: WithUserContext,
    @Param('id') id: string,
    @Body() body: CreateAODto,
  ) {
    const result = await this.allocationOrdersService.createAllocationOrder(
      id,
      user,
      body.jobList,
      body.fromBranchId,
      body.toBranchId,
      body.aoType,
    );

    return result;
  }

  @Delete('/:id')
  @Permissions([Permission.CMS_ALLOCATION_ORDER_ALL + PermissionAction.UPDATE])
  async deleteAllocationOrder(
    @WithUser() user: WithUserContext,
    @Param('id') id: string,
  ) {
    return await this.allocationOrdersService.deleteAllocationOrder(id, user);
  }

  @Get('/:id')
  @Permissions([Permission.CMS_ALLOCATION_ORDER_ALL + PermissionAction.VIEW])
  async getAllocationOrder(@Req() context: Request, @Param('id') id: string) {
    return await super.findOne(context, id);
  }

  @Patch('/:id/awb-confirm')
  @Permissions([Permission.CMS_ALLOCATION_ORDER_ALL + PermissionAction.UPDATE])
  async awbConfirm(
    @Body() body: AOAwbConfirmDto,
    @Param('id') allocationOrderId: string,
    @WithUser() user: WithUserContext,
  ) {
    return await this.allocationOrdersService.confirmAWB({
      allocationOrderId,
      user,
      body,
    });
  }

  @Patch('/:id/pickup-confirm')
  @Permissions([Permission.CMS_ALLOCATION_ORDER_ALL + PermissionAction.UPDATE])
  async pickupConfirm(
    @Body() body: AOPickupConfirmDto,
    @Param('id') allocationOrderId: string,
    @WithUser() user: WithUserContext,
  ) {
    return await this.allocationOrdersService.confirmPickup({
      allocationOrderId,
      user,
      body,
    });
  }

  @Get('/:id/transaction')
  @Permissions([
    Permission.CMS_ALLOCATION_ORDER_ALL + PermissionAction.DOWNLOAD,
  ])
  async getTransactionBase64(@Param('id') id: string) {
    const base64 = await this.allocationOrdersService.getTransaction(id);
    return { base64 };
  }

  @Get('/:id/presign/download')
  @Permissions([Permission.CMS_ALLOCATION_ORDER_ALL + PermissionAction.VIEW])
  async getPresignDownload(@Param('id') id: string) {
    return await this.allocationOrdersService.getPresignDownload(id);
  }
}
