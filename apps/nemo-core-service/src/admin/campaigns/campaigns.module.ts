import { MiddlewareConsumer, Module, NestModule } from '@nestjs/common';
import {
  CampaignEntity,
  CampaignRedemptionCodeEntity,
  CompanyEntity,
  GeneralActivitiesEntity,
  JobEntity,
  ModelMasterEntity,
  UserEntity,
} from '../../entities';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CampaignsController } from './campaigns.controller';

import { WithUserMiddleware } from '../../middlewares';
import {
  AdminCampaignsService,
  campaignSummaryHeaderOption,
} from './campaigns.service';
import { ExcelManagerModule } from 'src/excel/excel-manager.module';
import { CampaignRedemptionCodeQueueModule } from './campaign-redemption-code-queue.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      CampaignEntity,
      CampaignRedemptionCodeEntity,
      JobEntity,
      UserEntity,
      ModelMasterEntity,
      CompanyEntity,
      GeneralActivitiesEntity,
    ]),
    ExcelManagerModule.register(campaignSummaryHeaderOption),
    CampaignRedemptionCodeQueueModule,
  ],
  controllers: [CampaignsController],
  providers: [AdminCampaignsService],
})
export class CampaignsModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(WithUserMiddleware).forRoutes(CampaignsController);
  }
}
