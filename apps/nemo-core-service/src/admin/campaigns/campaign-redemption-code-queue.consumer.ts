import { Injectable } from '@nestjs/common';
import { Process, Processor } from '@nestjs/bull';
import { Job } from 'bull';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, EntityManager, In, LessThan } from 'typeorm';
import {
  CampaignEntity,
  CampaignRedemptionCodeEntity,
  GeneralActivitiesEntity,
} from '../../entities';
import { BaseExceptionService } from '../../exceptions';
import { AES128MessageService } from '../../encrypt-decrypt-message/encrypt-decrypt.service';
import { FirebaseService } from '../../firebase/firebase.service';
import { makeCollectionPath } from '../../subscriber/helper';
import { ActivitiesType } from '../../subscriber';
import { BASE_EXCEPTIONS } from '../../../src/config';

@Processor('campaign-redemption-code-queue')
@Injectable()
class CampaignRedemptionCodeQueueConsumer {
  constructor(
    @InjectRepository(CampaignRedemptionCodeEntity)
    private readonly campaignRedemptionCodeRepo: Repository<CampaignRedemptionCodeEntity>,
    @InjectRepository(CampaignEntity)
    private readonly campaignRepo: Repository<CampaignEntity>,
    @InjectRepository(GeneralActivitiesEntity)
    private readonly generalActivitiesRepo: Repository<GeneralActivitiesEntity>,
    private readonly baseExceptionService: BaseExceptionService,
    private readonly aes128MessageService: AES128MessageService,
    private readonly firebaseService: FirebaseService,
  ) {}
  @Process('campaign-redemption-code-upload')
  async campaignRedemptionCodeUpload(job: Job<any>) {
    const startTime = new Date();
    const { fileData, company, user, id } = job.data;

    const activity = await this.generalActivitiesRepo.findOne({
      where: { generalActivityId: id },
    });

    if (!activity) {
      throw this.baseExceptionService.exception('NOT_FOUND_DATA');
    }

    const firebaseData: {
      collectionRef: string;
      data: {
        id: string;
        result: 'pending' | 'success' | 'error';
        at: Date;
        error: null | { code: string; campaignCode?: string };
      };
    } = {
      collectionRef: makeCollectionPath(
        activity,
        ActivitiesType.CAMPAIGN_REDEMPTION_CODE_CONVERSATION,
        true,
      ),
      data: {
        id: id,
        result: 'pending',
        at: new Date(),
        error: null,
      },
    };

    try {
      const saveList: Partial<CampaignRedemptionCodeEntity>[] = [];
      const uniqueRedemptionCodeSet = new Set();

      for (const value of fileData) {
        const saveData = new CampaignRedemptionCodeEntity();

        if (value.redemptionCode.length > 20) {
          throw this.baseExceptionService.exception(
            'BODY_PAYLOAD_INVALID',
            `redemptionCode exceed 20 characters`,
          );
        }

        if (value.supporter.length > 50) {
          throw this.baseExceptionService.exception(
            'BODY_PAYLOAD_INVALID',
            `${value.supporter}: supporter exceed 50 characters`,
          );
        }

        saveData.companyId = company;
        saveData.campaignCode = value.campaignCode;
        saveData.redemptionCode = value.redemptionCode;
        saveData.supporter = value.supporter;
        saveData.grade = this.filterGrade(value.grade);
        saveData.value = value.value;
        saveData.order = value.order;
        saveData.createdBy = user;

        // --- check duplicate redemption code (inside file)
        if (uniqueRedemptionCodeSet.has(value.redemptionCode)) {
          throw this.baseExceptionService.exception(
            'BODY_PAYLOAD_INVALID',
            'Duplicate redemption code in file',
          );
        } else {
          uniqueRedemptionCodeSet.add(value.redemptionCode);
        }

        saveList.push(saveData);
      }

      const count = saveList.length;

      // check active campaign
      await this.checkActiveCampaign(saveList, company);

      // --- check duplicate redemption code (file and db)
      // await this.checkDuplicateRedemptionCode(saveList, company);

      try {
        // --- save to database
        await this.campaignRedemptionCodeRepo.manager.transaction(
          async (man: EntityManager): Promise<void> => {
            for (let i = 0; i < count; i += 1000) {
              const _ = await man
                .createQueryBuilder()
                .insert()
                .into(CampaignRedemptionCodeEntity)
                .values(saveList.slice(i, i + 1000))
                .execute();
            }
          },
        );
      } catch (err: any) {
        if (err?.driverError?.code === '23505') {
          throw this.baseExceptionService.exception(
            'REDEMPTION_CODE_ALREADY_UPLOADED',
          );
        } else {
          throw err;
        }
      }

      activity.detail = {
        start: startTime,
        end: new Date(),
        status: 'success',
        count,
      };

      await this.generalActivitiesRepo.save(activity);
      firebaseData.data.result = 'success';
    } catch (err: any) {
      activity.detail = {
        start: startTime,
        end: new Date(),
        status: 'error',
        error: err,
      };
      await this.generalActivitiesRepo.save(activity);
      const { code, data } = err ?? {};
      const error: typeof firebaseData.data.error = { code };
      if (code === BASE_EXCEPTIONS.CAMPAIGN_NOT_ACTIVE.code) {
        error.campaignCode = data;
      }
      firebaseData.data.result = 'error';
      firebaseData.data.error = error;
      throw err;
    } finally {
      this.firebaseService.addData(
        firebaseData.collectionRef,
        firebaseData.data,
      );
    }
  }

  async checkActiveCampaign(
    data: Partial<CampaignRedemptionCodeEntity>[],
    company: string,
  ) {
    const campaignFromPayload = data.map((campaign) => campaign.campaignCode);
    const uniqueCampaign = [...new Set(campaignFromPayload)];

    const prevDate = new Date();
    prevDate.setDate(prevDate.getDate() - 1);

    const campaignData = await this.campaignRepo.find({
      where: [
        {
          campaignCode: In(uniqueCampaign),
          companyId: company,
          isActive: false,
        },
        {
          campaignCode: In(uniqueCampaign),
          companyId: company,
          endDate: LessThan(prevDate),
        },
      ],
    });

    const inActiveCampaign = campaignData.map(
      (campaign) => campaign.campaignCode,
    );

    if (inActiveCampaign.length > 0) {
      throw this.baseExceptionService.exception(
        'CAMPAIGN_NOT_ACTIVE',
        inActiveCampaign[0],
      );
    }
  }

  async checkDuplicateRedemptionCode(
    data: Partial<CampaignRedemptionCodeEntity>[],
    company: string,
  ) {
    const redemptionCodeFromPayload = data.map(
      (redemption) => redemption.redemptionCode ?? '',
    );

    for (let i = 0; i < redemptionCodeFromPayload.length; i += 10000) {
      const queryData = await this.campaignRedemptionCodeRepo.find({
        select: ['redemptionCode'],
        where: {
          companyId: company,
          redemptionCode: In(redemptionCodeFromPayload.slice(i, i + 10000)),
        },
      });

      if (queryData?.length > 0) {
        throw this.baseExceptionService.exception(
          'REDEMPTION_CODE_ALREADY_UPLOADED',
        );
      }
    }
  }

  filterGrade(grade: string) {
    const gradeTemplate = ['A', 'B', 'C', 'D'];
    const gradeArray = grade.toUpperCase().split(',');
    const gradeValue = [...new Set(gradeArray)]
      .filter((value) => gradeTemplate.includes(value))
      .sort((a, b) => a.localeCompare(b));

    if (gradeValue.length === 0) {
      throw this.baseExceptionService.exception(
        'BODY_PAYLOAD_INVALID',
        'Grade required at least one of A, B, C, D',
      );
    }

    return gradeValue;
  }
}
export { CampaignRedemptionCodeQueueConsumer };
