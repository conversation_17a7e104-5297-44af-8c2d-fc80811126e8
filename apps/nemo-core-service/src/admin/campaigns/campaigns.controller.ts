import {
  Controller,
  Put,
  Req,
  UploadedFile,
  UseInterceptors,
  Get,
  Param,
  Query,
  Delete,
  Patch,
  Body,
} from '@nestjs/common';
import { Request } from 'express';
import { InjectRepository } from '@nestjs/typeorm';
import { CrudController } from '../../crud';
import { CampaignEntity } from '../../entities';
import { Repository, SelectQueryBuilder } from 'typeorm';
import { AdminCampaignsService } from './campaigns.service';
import {
  mappingUrlWithCompanyId,
  Permission,
  PermissionAction,
} from 'src/config';
import { WithUser, Permissions } from '../../decorators';
import { WithUserContext } from 'src/interfaces';
import { FileInterceptor } from '@nestjs/platform-express';
import { GetCampaignListDto } from './dto/get-camapaign-list.dto';
import { CampaignTransferDto } from './dto/campaign-transfer.dto';

@Controller('v1/admin/campaigns')
export class CampaignsController extends CrudController<CampaignEntity> {
  constructor(
    @InjectRepository(CampaignEntity)
    repo: Repository<CampaignEntity>,
    private readonly campaignService: AdminCampaignsService,
  ) {
    super(CampaignEntity, 'campaign', repo, {
      resourceKeyPath: 'campaignCode',
      order: { updatedAt: 'asc' },
      defaultFilter: async (
        request: Request,
        listQuery: SelectQueryBuilder<CampaignEntity>,
      ) => {
        const xCompany = request.headers['x-company'] as string;

        const company = mappingUrlWithCompanyId(xCompany);

        return listQuery.andWhere(`r.companyId = :company`, {
          company: company,
        });
      },
      // searchFilter: async (
      //   request: Request,
      //   _em: EntityManager,
      //   listQuery: SelectQueryBuilder<CampaignEntity>,
      // ) => this.campaignService.buildSearchQuery(request, listQuery),
    });
  }

  @Get()
  @Permissions([Permission.CMS_CAMPAIGN_MANAGE + PermissionAction.VIEW])
  async getCampaigns(
    @WithUser() user: WithUserContext,
    @Query() query: GetCampaignListDto,
  ) {
    const campaigns = this.campaignService.getCampaignList({
      companyId: user.company,
      query,
    });

    return campaigns;
  }

  @Get('/remaining-vouchers')
  @Permissions([Permission.CMS_TRANSFER_VOUCHER + PermissionAction.VIEW])
  async getRemainingVouchers(@WithUser() user: WithUserContext) {
    const transferCampaigns = this.campaignService.getRemainingVouchers({
      companyId: user.company,
    });
    return transferCampaigns;
  }

  @Get('/active-campaign')
  @Permissions([Permission.CMS_TRANSFER_VOUCHER + PermissionAction.UPDATE])
  async getActiveCampaignDetail(@WithUser() user: WithUserContext) {
    const campaign = this.campaignService.getActiveCampaign(user);

    return campaign;
  }

  @Get('/:id')
  @Permissions([Permission.CMS_CAMPAIGN_MANAGE + PermissionAction.VIEW])
  async getCampaignDetail(
    @Param('id') campaignCode: string,
    @WithUser() user: WithUserContext,
  ) {
    const campaign = this.campaignService.getCampaignDetail({
      campaignCode,
      companyId: user.company,
    });

    return campaign;
  }

  @Get('/:id/redemption-codes/summary')
  @Permissions([Permission.CMS_CAMPAIGN_MANAGE + PermissionAction.VIEW])
  async getCampaignRedemptionCodeSummary(
    @Param('id') campaignCode: string,
    @WithUser() user: WithUserContext,
  ) {
    console.log('campaignCode');
    const redemptionCodes =
      this.campaignService.getCampaignRedemptionCodeSummary({
        campaignCode,
        companyId: user.company,
      });

    return redemptionCodes;
  }

  @Delete('/:id/cancel')
  @Permissions([Permission.CMS_CAMPAIGN_MANAGE + PermissionAction.UPDATE])
  async cancelCampaign(
    @Param('id') campaignCode: string,
    @WithUser() user: WithUserContext,
  ) {
    return this.campaignService.cancelCampaign({
      campaignCode,
      companyId: user.company,
      userKey: user.userKey,
    });
  }

  @Put()
  @Permissions([Permission.CMS_CAMPAIGN_MANAGE + PermissionAction.UPLOAD])
  @UseInterceptors(FileInterceptor('file'))
  async uploadCampaignSummaryFile(
    @Req() context: Request,
    @UploadedFile() file: Express.Multer.File,
    @WithUser() user: WithUserContext,
  ) {
    const xCompany = context.headers['x-company'] as string;
    const company = mappingUrlWithCompanyId(xCompany);

    return this.campaignService.saveCampaignSummaryFromFile(
      company as string,
      file,
      user.userKey,
    );
  }

  @Put('/model')
  @Permissions([Permission.CMS_CAMPAIGN_MANAGE + PermissionAction.UPLOAD])
  @UseInterceptors(FileInterceptor('file'))
  async uploadCampaignModelFile(
    @Req() context: Request,
    @UploadedFile() file: Express.Multer.File,
    @WithUser() user: WithUserContext,
  ) {
    const xCompany = context.headers['x-company'] as string;
    const company = mappingUrlWithCompanyId(xCompany);

    return this.campaignService.saveCampaignModelFromFile(
      company as string,
      file,
      user.userKey,
    );
  }

  @Put('/redemption-code')
  @Permissions([Permission.CMS_CAMPAIGN_MANAGE + PermissionAction.UPLOAD])
  @UseInterceptors(FileInterceptor('file'))
  async uploadCampaignRedemptionCodeFile(
    @Req() context: Request,
    @UploadedFile() file: Express.Multer.File,
    @WithUser() user: WithUserContext,
  ) {
    const xCompany = context.headers['x-company'] as string;
    const company = mappingUrlWithCompanyId(xCompany);

    return this.campaignService.saveCampaignRedemptionCodeFromFile(
      company as string,
      file,
      user.userKey,
    );
  }

  @Get('/template/:type')
  @Permissions([Permission.CMS_CAMPAIGN_MANAGE + PermissionAction.DOWNLOAD])
  async getVoucherTemplate(@Param('type') templateType: string) {
    const excelFileBuffer =
      await this.campaignService.exportCampaignTemplate(templateType);
    return {
      base64String: excelFileBuffer.toString('base64'),
    };
  }

  @Patch('/transfer')
  @Permissions([Permission.CMS_TRANSFER_VOUCHER + PermissionAction.UPDATE])
  async transferCampaign(
    @Body() body: CampaignTransferDto,
    @WithUser() user: WithUserContext,
  ) {
    return this.campaignService.transferCampaign(body, user);
  }
}
