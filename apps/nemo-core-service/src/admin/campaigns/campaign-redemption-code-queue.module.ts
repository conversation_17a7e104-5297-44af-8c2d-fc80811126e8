import { Module } from '@nestjs/common';
import { BullModule } from '@nestjs/bull';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CampaignRedemptionCodeQueueConsumer } from './campaign-redemption-code-queue.consumer';
import {
  CampaignEntity,
  CampaignRedemptionCodeEntity,
  GeneralActivitiesEntity,
} from '../../entities';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      CampaignRedemptionCodeEntity,
      GeneralActivitiesEntity,
      CampaignEntity,
    ]),
    BullModule.registerQueue({
      name: 'campaign-redemption-code-queue',
      defaultJobOptions: {
        attempts: 1,
        removeOnComplete: true,
        removeOnFail: true,
      },
    }),
  ],
  providers: [BullModule, CampaignRedemptionCodeQueueConsumer],
  exports: [BullModule],
})
export class CampaignRedemptionCodeQueueModule {}
