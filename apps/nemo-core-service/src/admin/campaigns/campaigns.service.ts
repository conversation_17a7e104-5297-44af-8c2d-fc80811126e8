import { Injectable } from '@nestjs/common';
import { InjectQueue } from '@nestjs/bull';
import {
  CampaignEntity,
  ModelMasterEntity,
  CompanyEntity,
  GeneralActivitiesEntity,
  CampaignStatus,
  CampaignRedemptionCodeEntity,
} from '../../entities';
import { InjectRepository } from '@nestjs/typeorm';
import {
  Repository,
  EntityManager,
  In,
  MoreThan,
  IsNull,
  LessThanOrEqual,
} from 'typeorm';
import { BaseExceptionService } from '../../exceptions';
import {
  ExcelManagerService,
  Options,
  IConvertToType,
} from '../../excel/excel-manager.service';
import { uniqBy } from 'lodash';
import { DateTime } from 'luxon';
import { Queue } from 'bull';
import { GetCampaignListDto } from './dto/get-camapaign-list.dto';
import { WithUserContext } from 'src/interfaces';
import { CampaignTransferDto } from './dto/campaign-transfer.dto';

enum exportTemplateType {
  CAMAPAIGN = 'campaign',
  MODEL = 'model',
  REDEMPTION_CODE = 'redemption-code',
}
@Injectable()
export class AdminCampaignsService {
  constructor(
    @InjectRepository(CampaignEntity)
    private readonly campaignRepository: Repository<CampaignEntity>,
    @InjectRepository(ModelMasterEntity)
    private readonly modelMasterRepository: Repository<ModelMasterEntity>,
    @InjectRepository(CompanyEntity)
    private readonly companyRepository: Repository<CompanyEntity>,
    @InjectRepository(GeneralActivitiesEntity)
    private readonly generalActivitiesRepository: Repository<GeneralActivitiesEntity>,
    @InjectRepository(CampaignRedemptionCodeEntity)
    private readonly campaignRedemptionCodeRepository: Repository<CampaignRedemptionCodeEntity>,
    private readonly excelManagerService: ExcelManagerService,
    private readonly baseExceptionService: BaseExceptionService,

    @InjectQueue('campaign-redemption-code-queue')
    private readonly queue: Queue,
  ) {}
  async convertCampaignStatusToQuery(status: string) {
    const nowDate = new Date().toISOString();
    const yesterday = new Date(nowDate);
    yesterday.setDate(yesterday.getDate() - 1);
    const yesterdayStr = yesterday.toISOString();

    if (status === CampaignStatus.CANCELLED) {
      return {
        query: 'campaign.isActive = false',
        value: {},
      };
    } else if (status === CampaignStatus.EXPIRED) {
      return {
        query: 'campaign.isActive = true and campaign.endDate <= :beforeDate',
        value: { beforeDate: `${yesterdayStr}` },
      };
    } else if (status === CampaignStatus.IN_PROCESS) {
      return {
        query:
          'campaign.isActive = true and campaign.startDate <= :currentDate AND campaign.endDate > :beforeDate',
        value: { currentDate: `${nowDate}`, beforeDate: `${yesterdayStr}` },
      };
    } else if (status === CampaignStatus.NOT_START) {
      return {
        query:
          'campaign.isActive = true and campaign.startDate > :currentDate AND campaign.endDate > :beforeDate',
        value: { currentDate: `${nowDate}`, beforeDate: `${yesterdayStr}` },
      };
    } else {
      return {
        query: '1=1',
        value: {},
      };
    }
  }

  async getCampaignList({
    companyId,
    query,
  }: {
    companyId: string;
    query: GetCampaignListDto;
  }) {
    const page = query.page?.trim() ? query.page : '1';
    const pageSize = query.pageSize?.trim() ? query.pageSize : '10';
    const limit = Number(pageSize);
    const offset = (Number(page) - 1) * Number(limit);
    const orderBy =
      query.orderBy && query.orderBy.trim() !== ''
        ? query.orderBy
        : 'startDate ASC';
    const [sort, direction] = orderBy.split(' ');
    const queryStatus = await this.convertCampaignStatusToQuery(
      query.status ?? '',
    );
    const queryBuilder = this.campaignRepository
      .createQueryBuilder('campaign')
      .select([
        'campaign.campaignCode as "campaignCode"',
        'campaign.campaignName as "campaignName"',
        'campaign.startDate as "startDate"',
        'campaign.endDate as "endDate"',
        'campaign.maxRedemptionCode as "maxRedemptionCode"',
        'campaign.isActive as "isActive"',
        'b.count as "countModelKey"',
        'c.count as "countRedemptionCode"',
      ])
      .leftJoin(
        (subQuery) => {
          return subQuery
            .select([
              'campaign.campaignCode as "campaignCode"',
              'count(campaign.campaignCode)',
            ])
            .from(CampaignEntity, 'campaign')
            .innerJoin('campaign.modelMasters', 'modelMasters')
            .groupBy('campaign.campaignCode, campaign.companyId');
        },
        'b',
        'b."campaignCode" = campaign.campaignCode',
      )
      .leftJoin(
        (subQuery) => {
          return subQuery
            .select([
              'campaign.campaignCode as "campaignCode"',
              'count(campaign.campaignCode)',
            ])
            .from(CampaignEntity, 'campaign')
            .innerJoin(
              'campaign.campaignRedemptionCode',
              'campaignRedemptionCode',
            )
            .groupBy('campaign.campaignCode, campaign.companyId');
        },
        'c',
        'c."campaignCode" = campaign.campaignCode',
      )
      .where(`campaign.companyId = :companyId`, { companyId })
      .andWhere(queryStatus.query, queryStatus.value)
      .orderBy(`campaign.${sort}`, direction.toUpperCase() as 'ASC' | 'DESC')
      .offset(offset)
      .limit(limit);

    if (sort !== 'campaignCode') {
      queryBuilder.addOrderBy(`campaign.campaignCode`, 'ASC');
    }
    const campaigns = await queryBuilder.getRawMany();

    const resultCount = await this.campaignRepository
      .createQueryBuilder('campaign')
      .select('COUNT(DISTINCT campaign.campaignCode)', 'count')
      .where('campaign.companyId = :companyId', { companyId })
      .andWhere(queryStatus.query, queryStatus.value)
      .getRawOne();
    return {
      items: campaigns,
      paginationResult: {
        page: Number(page),
        pageSize: Number(pageSize),
        totalRecords: Number(resultCount.count ?? 0),
      },
    };
  }

  async getRemainingVouchers({ companyId }: { companyId: string }) {
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    const yesterdayStr = yesterday.toISOString();
    const queryBuilder = this.campaignRedemptionCodeRepository
      .createQueryBuilder('campaignRedemptionCode')
      .select([
        'campaignRedemptionCode.campaignCode as "campaignCode"',
        'campaignRedemptionCode.supporter as "supporter"',
        'campaignRedemptionCode.value as "value"',
        'count(campaignRedemptionCode.campaignCode) as "remain"',
      ])
      .leftJoin('campaignRedemptionCode.campaign', 'campaign')
      .where(
        `campaignRedemptionCode.companyId = :companyId and campaignRedemptionCode.jobId is null and (campaign.isActive = false or campaign.endDate <= :beforeDate)`,
        { companyId, beforeDate: yesterdayStr },
      )
      .groupBy(
        'campaignRedemptionCode.campaignCode, campaignRedemptionCode.supporter, campaignRedemptionCode.value',
      )
      .orderBy('campaignRedemptionCode.value', 'DESC')
      .addOrderBy('campaignRedemptionCode.supporter', 'ASC');

    const result = await queryBuilder.getRawMany();
    return result;
  }

  async getCampaignDetail({
    campaignCode,
    companyId,
  }: {
    campaignCode: string;
    companyId: string;
  }) {
    const campaign = await this.campaignRepository.findOne({
      select: {
        modelMasters: { modelKey: true },
        campaignRedemptionCode: {
          redemptionCode: true,
        },
      },
      where: { campaignCode, companyId },
      relations: ['modelMasters', 'campaignRedemptionCode'],
    });

    if (!campaign) {
      throw this.baseExceptionService.exception(
        'CAMPAIGN_NOT_FOUND',
        'Campaign not found.',
      );
    }

    const {
      modelMasters,
      campaignRedemptionCode,
      createdBy,
      updatedBy,
      createdAt,
      updatedAt,
      companyId: _,
      ...campaignDetail
    } = campaign;
    const modelMasterIds = modelMasters?.map((model) => model.modelKey) ?? [];
    const isCompleteUpload =
      campaignRedemptionCode?.length && modelMasterIds.length ? true : false;

    return { ...campaignDetail, modelMasterIds, isCompleteUpload };
  }

  async getActiveCampaign(user: WithUserContext) {
    const nowDate = new Date().toISOString();
    const yesterday = new Date(nowDate);
    yesterday.setDate(yesterday.getDate() - 1);

    const campaign = await this.campaignRepository.find({
      where: {
        companyId: user.company,
        isActive: true,
        endDate: MoreThan(yesterday),
      },
      order: {
        campaignCode: 'ASC',
      },
    });

    return campaign;
  }

  async getCampaignRedemptionCodeSummary({
    campaignCode,
    companyId,
  }: {
    campaignCode: string;
    companyId: string;
  }) {
    // this function will not throw an error if campaign not found and will return empty array if campaign redemption code not found
    const query = `
      SELECT grade, campaign_redemption_code.order, campaign_redemption_code.value, supporter AS description,
      COUNT(*) AS total,
      COUNT (CASE WHEN job_id IS NOT NULL THEN 1 END) AS used
      FROM core.campaign_redemption_code
      WHERE campaign_code = '${campaignCode}' AND company_id = '${companyId}'
      GROUP BY grade, campaign_redemption_code.order, campaign_redemption_code.value, supporter
      ORDER BY campaign_redemption_code.order, grade`;

    const queryResult = await this.campaignRepository.query(query);

    const result = queryResult.map((item: any) => {
      const { grade, order, total, used, description, value } = item;
      return {
        order,
        description,
        value: Number(value),
        total: Number(total),
        used: Number(used),
        grades: grade,
      };
    });

    return result;
  }

  async saveCampaignSummaryFromFile(
    company: string,
    file: Express.Multer.File,
    user: string,
  ) {
    this.excelManagerService.options = campaignSummaryHeaderOption;
    const fileData = await this.excelManagerService.readExcelFile(
      file.buffer,
      file.mimetype,
    );
    const saveList: Partial<CampaignEntity>[] = [];

    const companyData = await this.companyRepository.findOne({
      where: { companyId: company },
    });

    if (companyData) {
      // extract campaign code from file
      const campaignCodes = uniqBy(fileData, 'campaignCode').map(
        (campaign) => campaign.campaignCode,
      );

      // get existing campaign data from db by using campaign code
      const campaignDataList = await this.campaignRepository.find({
        where: { companyId: company, campaignCode: In(campaignCodes) },
      });

      // filter campaign that already started
      const campaignData = campaignDataList.filter(
        (campaign) =>
          DateTime.fromJSDate(campaign.startDate, {
            zone: 'Asia/Bangkok',
          }) < DateTime.now().setZone('Asia/Bangkok'),
      );

      if (campaignData.length > 0) {
        throw this.baseExceptionService.exception(
          'BODY_PAYLOAD_INVALID',
          'Body payload invalid, campaign already started.',
        );
      }

      for (const value of uniqBy(fileData, 'campaignCode')) {
        // validate length of data
        this.validateCampaignData(value);
        const saveData = new CampaignEntity();

        saveData.companyId = companyData.companyId;
        saveData.campaignCode = value.campaignCode;
        saveData.campaignName = value.campaignName;
        saveData.description = value.description;
        saveData.remark = value.remark;

        saveData.startDate = new Date(
          this.getDateTimeFromBkkTimezone(value.startDate),
        );
        saveData.endDate = new Date(
          this.getDateTimeFromBkkTimezone(value.endDate),
        );

        saveData.maxRedemptionCode = value.maxRedemtionCode;
        saveData.createdBy = user;
        saveData.updatedBy = user;

        saveList.push(saveData);
      }
    }

    await this.campaignRepository.manager.transaction(
      async (man: EntityManager): Promise<void> => {
        for (let i = 0; i < saveList.length; i += 1000) {
          await man
            .createQueryBuilder()
            .insert()
            .into(CampaignEntity)
            .values(saveList.slice(i, i + 1000))
            .orUpdate(
              [
                'campaign_name',
                'description',
                'remark',
                'start_date',
                'end_date',
                'updated_at',
                'updated_by',
                'max_redemption_code',
              ],
              ['company_id', 'campaign_code'],
            )
            .execute();
        }
      },
    );

    return null;
  }

  validateCampaignData(data: Record<string, any>) {
    if (data.campaignCode.length > 200) {
      throw this.baseExceptionService.exception(
        'BODY_PAYLOAD_INVALID',
        `${data.campaignCode}: campaignCode exceed 200 characters`,
      );
    } else if (data.campaignName.length > 100) {
      throw this.baseExceptionService.exception(
        'BODY_PAYLOAD_INVALID',
        `${data.campaignName}: campaignName exceed 100 characters`,
      );
    } else if (data.description.length > 300) {
      throw this.baseExceptionService.exception(
        'BODY_PAYLOAD_INVALID',
        `${data.description}: description exceed 300 characters`,
      );
    } else if (data.remark?.length > 100) {
      throw this.baseExceptionService.exception(
        'BODY_PAYLOAD_INVALID',
        `${data.remark}: remark exceed 100 characters`,
      );
    }
  }

  getDateTimeFromBkkTimezone(date: string) {
    try {
      const result = DateTime.fromISO(
        new Date(date).toISOString().substring(0, 10),
        {
          zone: 'Asia/Bangkok',
        },
      ).toISO();

      if (result) {
        return result;
      } else {
        throw this.baseExceptionService.exception(
          'BODY_PAYLOAD_INVALID',
          'Body payload invalid, invalid date format.',
        );
      }
    } catch (err: any) {
      throw this.baseExceptionService.exception(
        'BODY_PAYLOAD_INVALID',
        'Body payload invalid, invalid date format.',
      );
    }
  }

  async saveCampaignModelFromFile(
    company: string,
    file: Express.Multer.File,
    user: string,
  ) {
    this.excelManagerService.options = campaignModelHeaderOption;
    const fileData = await this.excelManagerService.readExcelFile(
      file.buffer,
      file.mimetype,
    );

    // map data from file to format --> campaignCode: ['modelKey']
    const campaignModel: Record<string, string[]> = {};
    fileData.forEach((item) => {
      if (!campaignModel[item.campaignCode]) {
        campaignModel[item.campaignCode] = [];
      }
      campaignModel[item.campaignCode].push(item.modelKey);
    });

    // extract campaign code from file
    const campaignCodes = uniqBy(fileData, 'campaignCode').map(
      (campaign) => campaign.campaignCode,
    );

    // extract model key from file
    const modelKeys = uniqBy(fileData, 'modelKey').map(
      (campaign) => campaign.modelKey,
    );

    const campaignDataList = await this.campaignRepository.find({
      where: { companyId: company, campaignCode: In(campaignCodes) },
      relations: ['modelMasters'],
    });

    const countCampaignCodeFromDB = campaignDataList.map(
      (campaign) => campaign.campaignCode,
    ).length;
    const countCampaignCodeFromExcel = campaignCodes.length;

    // check if campaign code from db and excel is not match
    if (countCampaignCodeFromDB !== countCampaignCodeFromExcel) {
      throw this.baseExceptionService.exception('CAMPAIGN_NOT_EXISTED');
    }

    const modelMasterList = await this.modelMasterRepository.find({
      where: { companyId: company, modelKey: In(modelKeys) },
    });

    const countModelKeyFromDB = modelMasterList.map(
      (model) => model.modelKey,
    ).length;
    const countModelKeyFromExcel = modelKeys.length;

    // check if model key from db and excel is not match
    if (countModelKeyFromDB !== countModelKeyFromExcel) {
      throw this.baseExceptionService.exception('MODEL_KEY_NOT_EXISTED');
    }

    const saveData: CampaignEntity[] = [];
    for (const campaign of campaignDataList) {
      const mapModelMaster = modelMasterList.filter((model) =>
        campaignModel[campaign.campaignCode].includes(model.modelKey),
      );

      const saveCampaign = {
        ...campaign,
        modelMasters: [...(campaign.modelMasters ?? []), ...mapModelMaster],
      } as CampaignEntity;

      saveData.push(saveCampaign);
    }

    await this.campaignRepository.save(saveData);

    return null;
  }

  async saveCampaignRedemptionCodeFromFile(
    company: string,
    file: Express.Multer.File,
    user: string,
  ) {
    this.excelManagerService.options = campaignRedemptionCodeHeaderOption;
    const fileData = await this.excelManagerService.readExcelFile(
      file.buffer,
      file.mimetype,
    );

    const count = fileData.length;
    const activity = new GeneralActivitiesEntity();
    activity.companyId = company;
    activity.type = 'campaign_redemption_code';
    activity.createdBy = user;
    activity.detail = {};
    const { generalActivityId: id } =
      await this.generalActivitiesRepository.save(activity);

    this.queue.add('campaign-redemption-code-upload', {
      fileData,
      company,
      user,
      id,
    });

    return { count, id };
  }

  async cancelCampaign({
    campaignCode,
    companyId,
    userKey,
  }: {
    campaignCode: string;
    companyId: string;
    userKey: string;
  }) {
    const nowDate = new Date();
    nowDate.setDate(nowDate.getDate() - 1);

    const campaign = await this.campaignRepository.findOne({
      where: { campaignCode, companyId },
    });

    if (!campaign) {
      throw this.baseExceptionService.exception('CAMPAIGN_NOT_FOUND');
    }
    if (!campaign.isActive || campaign.endDate < nowDate) {
      throw this.baseExceptionService.exception('CAMPAIGN_NOT_ACTIVE');
    }

    campaign.isActive = false;
    campaign.canceledBy = userKey;
    campaign.canceledAt = nowDate;

    await this.campaignRepository.save(campaign);

    return null;
  }

  async exportCampaignTemplate(type: string) {
    if (type === exportTemplateType.CAMAPAIGN) {
      this.excelManagerService.options = campaignSummaryHeaderOption;
    } else if (type === exportTemplateType.MODEL) {
      this.excelManagerService.options = campaignModelHeaderOption;
    } else if (type === exportTemplateType.REDEMPTION_CODE) {
      this.excelManagerService.options = campaignRedemptionCodeHeaderOption;
    } else {
      throw this.baseExceptionService.exception(
        'BODY_PAYLOAD_INVALID',
        'export template type not found.',
      );
    }

    return await this.excelManagerService.generateExcelFile([], type);
  }

  async transferCampaign(body: CampaignTransferDto, user: WithUserContext) {
    const fromCampaignCode = body.fromCampaignCode;
    const toCampaignCode = body.toCampaignCode;
    const campaignSupporter = body.supporter;
    const codeValue = body.value;
    const companyId = user.company;

    const nowDate = new Date().toISOString();
    const yesterday = new Date(nowDate);
    yesterday.setDate(yesterday.getDate() - 1);

    const fromCampaignData = await this.campaignRepository.findOne({
      where: { campaignCode: fromCampaignCode, companyId: companyId },
    });

    const toCampaignData = await this.campaignRepository.findOne({
      where: { campaignCode: toCampaignCode, companyId: companyId },
    });

    if (!fromCampaignData) {
      throw this.baseExceptionService.exception(
        'NOT_FOUND_DATA',
        'fromCampaignCode not found',
      );
    } else if (
      fromCampaignData.isActive &&
      fromCampaignData.endDate >= yesterday
    ) {
      throw this.baseExceptionService.exception(
        'BODY_PAYLOAD_INVALID',
        'fromCampaignCode not match condition',
      );
    }

    if (!toCampaignData) {
      throw this.baseExceptionService.exception(
        'NOT_FOUND_DATA',
        'toCampaignCode not found',
      );
    } else if (!toCampaignData.isActive || toCampaignData.endDate < yesterday) {
      throw this.baseExceptionService.exception(
        'BODY_PAYLOAD_INVALID',
        'toCampaignCode not match condition',
      );
    }

    const fromRedemptionCode = await this.campaignRedemptionCodeRepository.find(
      {
        where: {
          companyId: companyId,
          campaignCode: fromCampaignCode,
          supporter: campaignSupporter,
          value: codeValue,
          jobId: IsNull(),
        },
      },
    );

    if (fromRedemptionCode.length === 0) {
      throw this.baseExceptionService.exception(
        'NOT_FOUND_DATA',
        'voucher in fromCampaignCode is unavailable',
      );
    }

    const toRedemptionCode = await this.campaignRedemptionCodeRepository.find({
      where: {
        companyId: companyId,
        campaignCode: toCampaignCode,
        order: LessThanOrEqual(toCampaignData.maxRedemptionCode),
      },
    });

    let orderIndex = -1;
    let voucherGrade = ['A', 'B', 'C', 'D'];
    //finding order index to update
    const uniqueOrder = Array.from(
      new Set(toRedemptionCode.map((data) => data.order)),
    ).sort((a, b) => a - b);

    // finding order index to update
    const hasTarget = toRedemptionCode.some((data) => {
      if (
        data.supporter === campaignSupporter &&
        Number(data.value) === Number(codeValue)
      ) {
        orderIndex = data.order;
        voucherGrade = data.grade;
        return true;
      }
    });

    if (uniqueOrder.length >= toCampaignData.maxRedemptionCode && !hasTarget) {
      throw this.baseExceptionService.exception(
        'VOUCHER_ORDER_IS_OCCUPIED',
        'Voucher order is occupied',
      );
    }

    //finding order index to update if orderIndex is not found
    if (orderIndex === -1) {
      for (let i = 1; i <= toCampaignData.maxRedemptionCode; i++) {
        if (!uniqueOrder.includes(i)) {
          orderIndex = i;
          break;
        }
      }
    }

    //update data
    fromRedemptionCode.forEach((data) => {
      data.campaignCode = toCampaignCode;
      data.order = orderIndex;
      data.grade = voucherGrade;
    });
    await this.campaignRedemptionCodeRepository.save(fromRedemptionCode);

    const activity = new GeneralActivitiesEntity();
    activity.type = 'transfer-voucher';
    activity.detail = {
      error: null,
      at: new Date(),
      result: 'success',
      detail: {
        campaignCodeFrom: fromCampaignCode,
        campaignCodeTo: toCampaignCode,
        supporter: campaignSupporter,
        order: orderIndex,
        value: codeValue,
        grade: voucherGrade,
      },
    };

    activity.createdBy = user.userKey;
    activity.companyId = user.company;
    await this.generalActivitiesRepository.save(activity);

    return null;
  }
}

export const campaignSummaryHeaderOption: Options = {
  headers: {
    CAMPAIGN_CODE: {
      keyName: 'campaignCode',
      type: IConvertToType.string,
      isRequired: true,
      subHeader: 'Campaign Code',
    },
    CAMPAIGN_NAME: {
      keyName: 'campaignName',
      type: IConvertToType.string,
      isRequired: true,
      subHeader: 'Campaign Name max 100 digit',
    },
    DESCRIPTION: {
      keyName: 'description',
      type: IConvertToType.string,
      isRequired: true,
      subHeader: 'Description max 300 digit',
    },
    REMARK: {
      keyName: 'remark',
      type: IConvertToType.string,
      isRequired: false,
      subHeader: 'Remark (สำหรับใส่) max 100 digit',
    },
    START_DATE: {
      keyName: 'startDate',
      type: IConvertToType.string,
      isRequired: true,
      subHeader: 'Start date',
    },
    END_DATE: {
      keyName: 'endDate',
      type: IConvertToType.string,
      isRequired: true,
      subHeader: 'End date',
    },
    MAX_REDEMPTION_CODE: {
      keyName: 'maxRedemtionCode',
      type: IConvertToType.number,
      isRequired: true,
      subHeader: 'No of display',
      options: {
        min: { value: 0 },
      },
    },
  },
};

export const campaignModelHeaderOption: Options = {
  headers: {
    CAMPAIGN_CODE: {
      keyName: 'campaignCode',
      type: IConvertToType.string,
      isRequired: true,
      subHeader: 'Campaign Code',
    },
    MODEL_KEY: {
      keyName: 'modelKey',
      type: IConvertToType.string,
      isRequired: true,
      subHeader: 'Model_Key',
    },
  },
};

export const campaignRedemptionCodeHeaderOption: Options = {
  headers: {
    CAMPAIGN_CODE: {
      keyName: 'campaignCode',
      type: IConvertToType.string,
      isRequired: true,
      subHeader: 'Campaign Code',
    },
    REDEMPTION_CODE: {
      keyName: 'redemptionCode',
      type: IConvertToType.string,
      isRequired: true,
      subHeader: 'Redemption Code max 20 digit',
    },
    SUPPORTER: {
      keyName: 'supporter',
      type: IConvertToType.string,
      isRequired: true,
      subHeader: 'Description max 50 digit',
    },
    GRADE: {
      keyName: 'grade',
      type: IConvertToType.string,
      isRequired: true,
      subHeader: 'Grade',
    },
    CODE_VALUE: {
      keyName: 'value',
      type: IConvertToType.number,
      isRequired: true,
      subHeader: 'Value',
      options: {
        decimal: 0,
        min: { value: 0 },
      },
    },
    CODE_ORDER: {
      keyName: 'order',
      type: IConvertToType.number,
      isRequired: true,
      subHeader: 'Order of display',
      options: {
        decimal: 0,
        min: { value: 0 },
      },
    },
  },
};
