import { SchemaObject } from '@nestjs/swagger/dist/interfaces/open-api-spec.interface';

import { createZodDto, zodToOpenAPI } from 'nestjs-zod';
import { z } from 'nestjs-zod/z';

const CampaignTransferSchema = z.object({
  fromCampaignCode: z.string(),
  toCampaignCode: z.string(),
  supporter: z.string(),
  value: z.number(),
});

export const CampaignTransferOpenApi = zodToOpenAPI(
  CampaignTransferSchema,
) as SchemaObject;

export class CampaignTransferDto extends createZodDto(CampaignTransferSchema) {}
