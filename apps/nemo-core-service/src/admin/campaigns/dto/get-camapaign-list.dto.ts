import { SchemaObject } from '@nestjs/swagger/dist/interfaces/open-api-spec.interface';
import { GetCampaignTableRequest } from 'contracts';

import { createZodDto, zodToOpenAPI } from 'nestjs-zod';
import { z } from 'nestjs-zod/z';
import { CampaignStatus } from '../../../entities';

const GetCampaignListSchema: z.ZodType<GetCampaignTableRequest> = z.object({
  orderBy: z.string().optional(),
  page: z.string().optional(),
  pageSize: z.string().optional(),
  status: z.nativeEnum(CampaignStatus).optional(),
});

export const GetCountVocuherOpenApi = zodToOpenAPI(
  GetCampaignListSchema,
) as SchemaObject;

export class GetCampaignListDto extends createZodDto(GetCampaignListSchema) {}
