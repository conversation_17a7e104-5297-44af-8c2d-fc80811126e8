import { Module } from '@nestjs/common';
import { BullModule } from '@nestjs/bull';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ImportedVoucherQueueConsumer } from './imported-voucher-queue.consumer';
import { ImportedVoucherEntity, GeneralActivitiesEntity } from '../../entities';

@Module({
  imports: [
    TypeOrmModule.forFeature([ImportedVoucherEntity, GeneralActivitiesEntity]),
    BullModule.registerQueue({
      name: 'imported-voucher-queue',
      defaultJobOptions: {
        attempts: 1,
        removeOnComplete: true,
        removeOnFail: true,
      },
    }),
  ],
  providers: [BullModule, ImportedVoucherQueueConsumer],
  exports: [BullModule],
})
export class ImportedVoucherQueueModule {}
