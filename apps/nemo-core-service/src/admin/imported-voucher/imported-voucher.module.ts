import { MiddlewareConsumer, Module, NestModule } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
// Import ImportedVoucher module
import { GeneralActivitiesEntity, ImportedVoucherEntity } from '../../entities';
import {
  ImportedVoucherService,
  excelManagerOption,
} from './imported-voucher.service';
import { ImportedVoucherController } from './imported-voucher.controller';
// Import other service
import { WithUserMiddleware } from '../../middlewares';
import { ExcelManagerModule } from '../../excel/excel-manager.module';
import { ImportedVoucherQueueModule } from './imported-voucher-queue.module';
import { ImportedVoucherEntitySubscriber } from '../../subscriber/imported-voucher.subscriber';

@Module({
  imports: [
    TypeOrmModule.forFeature([ImportedVoucherEntity, GeneralActivitiesEntity]),
    ExcelManagerModule.register(excelManagerOption),
    ImportedVoucherQueueModule,
  ],
  controllers: [ImportedVoucherController],
  providers: [ImportedVoucherService, ImportedVoucherEntitySubscriber],
})
export class ImportedVoucherModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(WithUserMiddleware).forRoutes(ImportedVoucherController);
  }
}
