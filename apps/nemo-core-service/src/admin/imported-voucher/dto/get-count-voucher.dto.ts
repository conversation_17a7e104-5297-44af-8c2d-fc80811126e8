import { SchemaObject } from '@nestjs/swagger/dist/interfaces/open-api-spec.interface';
import { GetCountVocuherRequest } from 'contracts';

import { createZodDto, zodToOpenAPI } from 'nestjs-zod';
import { z } from 'nestjs-zod/z';
import { toZod } from 'tozod';

const GetCountVocuherSchema: toZod<GetCountVocuherRequest> = z.object({
  orderBy: z.string().optional(),
  page: z.string().optional(),
  pageSize: z.string().optional(),
});

export const GetCountVocuherOpenApi = zodToOpenAPI(
  GetCountVocuherSchema,
) as SchemaObject;

export class GetCountVocuherDto extends createZodDto(GetCountVocuherSchema) {}
