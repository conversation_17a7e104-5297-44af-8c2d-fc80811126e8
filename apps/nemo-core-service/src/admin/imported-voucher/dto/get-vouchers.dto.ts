import { SchemaObject } from '@nestjs/swagger/dist/interfaces/open-api-spec.interface';
import { GetImportedVouchersRequest } from 'contracts';

import { createZodDto, zodToOpenAPI } from 'nestjs-zod';
import { z } from 'nestjs-zod/z';
import { toZod } from 'tozod';

const GetImportedVouchersSchema: toZod<GetImportedVouchersRequest> = z.object({
  pagination: z.string().optional(),
  orderBy: z.string().optional(),
  page: z.string().optional(),
  pageSize: z.string().optional(),
});

export const GetImportedVouchersOpenApi = zodToOpenAPI(
  GetImportedVouchersSchema,
) as SchemaObject;

export class GetImportedVouchersDto extends createZodDto(
  GetImportedVouchersSchema,
) {}
