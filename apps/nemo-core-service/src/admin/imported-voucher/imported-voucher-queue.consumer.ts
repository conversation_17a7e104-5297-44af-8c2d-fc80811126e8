import { Injectable } from '@nestjs/common';
import { Process, Processor } from '@nestjs/bull';
import { Job } from 'bull';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, EntityManager } from 'typeorm';
import { ImportedVoucherEntity, GeneralActivitiesEntity } from '../../entities';
import { BaseExceptionService } from '../../exceptions';
import { FirebaseService } from '../../firebase/firebase.service';
import { makeCollectionPath } from '../../subscriber/helper';
import { ActivitiesType } from '../../subscriber';
import { extractStringInCharOpenClose } from '../../utils/general';
import { AES128MessageService } from '../../encrypt-decrypt-message/encrypt-decrypt.service';

@Processor('imported-voucher-queue')
@Injectable()
class ImportedVoucherQueueConsumer {
  constructor(
    @InjectRepository(ImportedVoucherEntity)
    private readonly importedVoucherRepo: Repository<ImportedVoucherEntity>,
    @InjectRepository(GeneralActivitiesEntity)
    private readonly generalActivitiesRepo: Repository<GeneralActivitiesEntity>,
    private readonly baseExceptionService: BaseExceptionService,
    private readonly firebaseService: FirebaseService,
    private readonly aes128MessageService: AES128MessageService,
  ) {}
  @Process('imported-voucher-upload')
  async importedVoucherUpload(job: Job<any>) {
    const startTime = new Date();
    const { vouchers, user, company, id } = job.data;

    const activity = await this.generalActivitiesRepo.findOne({
      where: { generalActivityId: id },
    });

    if (!activity) {
      throw this.baseExceptionService.exception('NOT_FOUND_DATA');
    }

    const firebaseData: {
      collectionRef: string;
      data: {
        id: string;
        result: 'pending' | 'success' | 'error';
        at: Date;
        error: null | { code: string; refNo: string };
      };
    } = {
      collectionRef: makeCollectionPath(
        activity,
        ActivitiesType.IMPORTED_VOUCHER_CONVERSATION,
        true,
      ),
      data: {
        id: id,
        result: 'pending',
        at: new Date(),
        error: null,
      },
    };

    try {
      const toUploadRecord: Partial<ImportedVoucherEntity>[] = [];
      const checkDupSet = new Set();

      for (const record of vouchers) {
        // --- duplicate in file case
        if (checkDupSet.has(record.redemptionCode)) {
          throw this.baseExceptionService.exception('DUPLICATE_DATA_RECORD', {
            refNo: record.refNo,
            value: record.voucherValue,
          });
        } else {
          checkDupSet.add(record.redemptionCode);
          const saveData = new ImportedVoucherEntity();
          saveData.redemptionCode = record.redemptionCode;
          saveData.otherPaymentCode = record.otherPaymentCode;
          saveData.voucherValue = Number(
            Number(record.voucherValue).toFixed(2),
          );
          saveData.companyId = company;
          saveData.uploadedBy = user;
          toUploadRecord.push(saveData);
        }
      }

      const count = toUploadRecord.length;

      // --- save to database
      await this.importedVoucherRepo.manager.transaction(
        async (man: EntityManager): Promise<void> => {
          for (let i = 0; i < count; i += 1000) {
            try {
              const _ = await man
                .createQueryBuilder()
                .insert()
                .into(ImportedVoucherEntity)
                .values(toUploadRecord.slice(i, i + 1000))
                .execute();
            } catch (err: any) {
              const error = this.uploadVoucherErrorDuplicateDatabase({
                error: err,
                fileData: vouchers,
              });

              throw this.baseExceptionService.exception(
                'VOUCHER_ALREADY_UPLOADED',
                error,
              );
            }
          }
        },
      );

      activity.detail = {
        start: startTime,
        end: new Date(),
        status: 'success',
        count,
      };

      await this.generalActivitiesRepo.save(activity);
      firebaseData.data.result = 'success';
    } catch (err: any) {
      activity.detail = {
        start: startTime,
        end: new Date(),
        status: 'error',
        error: err,
      };
      await this.generalActivitiesRepo.save(activity);
      firebaseData.data.result = 'error';
      firebaseData.data.error = {
        code: err?.code,
        refNo: err?.data?.refNo || '',
      };
      throw err;
    } finally {
      this.firebaseService.addData(
        firebaseData.collectionRef,
        firebaseData.data,
      );
    }
  }

  uploadVoucherErrorDuplicateDatabase({
    error,
    fileData,
  }: {
    error: any;
    fileData: Record<string, any>[];
  }) {
    const { detail } = error;
    const detailExtract = extractStringInCharOpenClose(detail, '(', ')');
    if (detailExtract[0] === 'redemption_code') {
      const decryptTarget = this.aes128MessageService.decrypt(
        Buffer.from(detailExtract[1], 'base64'),
      );
      const targetIndex = fileData.findIndex(
        (item) => item.redemptionCode === decryptTarget,
      );
      const target = fileData[targetIndex];
      return {
        refNo: target.refNo,
        value: target.voucherValue,
      };
    }
  }
}
export { ImportedVoucherQueueConsumer };
