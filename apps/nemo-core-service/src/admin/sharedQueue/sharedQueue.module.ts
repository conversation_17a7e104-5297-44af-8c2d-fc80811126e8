import { Global, Module } from '@nestjs/common';
import { BullModule } from '@nestjs/bull';
import { TypeOrmModule } from '@nestjs/typeorm';
import { JobsConsumer } from '../jobs/jobs.consumer';
import { JobEntity } from '../../entities';

@Global()
@Module({
  imports: [
    TypeOrmModule.forFeature([JobEntity]),
    BullModule.registerQueue({
      name: 're-calculate-product-queue',
      defaultJobOptions: {
        attempts: 2,
        removeOnComplete: true,
        removeOnFail: true,
      },
    }),
  ],
  providers: [BullModule, JobsConsumer],
  exports: [BullModule],
})
export class SharedQueueModule {}
