import { SchemaObject } from '@nestjs/swagger/dist/interfaces/open-api-spec.interface';

import { createZodDto, zodToOpenAPI } from 'nestjs-zod';
import { z } from 'nestjs-zod/z';

const CreateRolesSchema = z.object({
  name: z
    .string()
    .min(1)
    .max(30)
    .regex(/^[A-Z0-9_]+$/),
  type: z.enum(['FRONTSHOP', 'CMS']).optional(),
  permissions: z
    .array(
      z.object({
        permissionId: z.string(),
        view: z.boolean(),
        create: z.boolean(),
        update: z.boolean(),
        delete: z.boolean(),
        download: z.boolean(),
        upload: z.boolean(),
      }),
    )
    .min(1),
});

export const CreateRolesOpenApi = zodToOpenAPI(
  CreateRolesSchema,
) as SchemaObject;

export class CreateRolesDto extends createZodDto(CreateRolesSchema) {}
