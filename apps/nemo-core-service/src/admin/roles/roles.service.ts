import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import {
  GeneralActivitiesEntity,
  PermissionEntity,
  RoleEntity,
  RolePermissionEntity,
  SiteType,
  UserRoleBranchEntity,
} from '../../entities';
import { In, Repository } from 'typeorm';
import { BaseExceptionService } from '../../exceptions';
import { GetRolesDto } from './dto/get-roles.dto';
import { CreateRolesDto } from './dto/create-roles.dto';
import { WithUserContext } from '../../interfaces';
export enum sortDirection {
  ASC = 'ASC',
  DESC = 'DESC',
}

export enum sortColumn {
  CREATED_AT = 'createdAt',
  UPDATED_AT = 'updatedAt',
  ROLE_NAME = 'roleName',
  ROLE_ID = 'roleId',
}
@Injectable()
export class AdminRolesService {
  constructor(
    @InjectRepository(RoleEntity)
    private readonly roleRepo: Repository<RoleEntity>,
    @InjectRepository(UserRoleBranchEntity)
    private readonly userRoleBranchRepo: Repository<UserRoleBranchEntity>,
    @InjectRepository(PermissionEntity)
    private readonly permissionRepo: Repository<PermissionEntity>,
    @InjectRepository(RolePermissionEntity)
    private readonly rolePermissionRepo: Repository<RolePermissionEntity>,
    @InjectRepository(GeneralActivitiesEntity)
    private readonly generalActivitiesRepository: Repository<GeneralActivitiesEntity>,
    private readonly baseExceptionService: BaseExceptionService,
  ) {}

  async getRoles({
    companyId,
    query,
  }: {
    companyId: string;
    query: GetRolesDto;
  }) {
    const sortColumn = [
      'createdAt',
      'updatedAt',
      'roleName',
      'roleId',
      'createdBy',
      'updatedBy',
    ];
    const orderBy =
      query.orderBy && query.orderBy.trim() !== ''
        ? query.orderBy
        : 'roleId ASC';
    let [sort, direction] = orderBy.split(' ');

    if (!sortColumn.includes(sort)) {
      sort = 'roleId';
    }

    if (
      direction.toUpperCase() !== 'ASC' &&
      direction.toUpperCase() !== 'DESC'
    ) {
      direction = 'ASC';
    }

    const data = await this.roleRepo
      .createQueryBuilder('roles')
      .select('COUNT(DISTINCT(userRoleBranch.userKey))', 'count')
      .addSelect('roles.roleName', 'roleName')
      .addSelect('roles.companyId', 'companyId')
      .addSelect('roles.roleId', 'roleId')
      .addSelect('roles.createdAt', 'createdAt')
      .addSelect('roles.updatedAt', 'updatedAt')
      .addSelect('createdUser.name', 'createdBy')
      .addSelect('updatedUser.name', 'updatedBy')
      .leftJoin('roles.userRoleBranch', 'userRoleBranch')
      .leftJoin('roles.createdUser', 'createdUser')
      .leftJoin('roles.updatedUser', 'updatedUser')
      .where('roles.companyId = :companyId', { companyId: companyId })
      .orderBy(`roles.${sort}`, direction.toUpperCase() as 'ASC' | 'DESC')
      .groupBy(
        'roles.companyId, roles.roleId, createdUser.name, updatedUser.name',
      )
      .getRawMany();

    return data;
  }

  async getRolesConfig(companyId: string) {
    const queryData = await this.roleRepo.find({
      where: { companyId },
    });

    const roleData = queryData.map((role) => ({
      roleId: role.roleId,
      roleName: role.roleName,
      type: role.type,
    }));

    return roleData;
  }
  async getRoleById({ companyId, id }: { companyId: string; id: string }) {
    const role = await this.roleRepo.findOne({
      where: { companyId, roleId: id },
      relations: [
        'rolePermissions',
        'rolePermissions.permission',
        'createdUser',
        'updatedUser',
      ],
    });

    if (!role) {
      throw this.baseExceptionService.exception(
        'NOT_FOUND_DATA',
        'Role ID NOT FOUND',
      );
    }

    const countUser = await this.userRoleBranchRepo
      .createQueryBuilder('userRoleBranch')
      .select('COUNT(DISTINCT(userRoleBranch.userKey))', 'count')
      .where(
        'userRoleBranch.companyId = :companyId and userRoleBranch.roleId = :roleId',
        { companyId: companyId, roleId: id },
      )
      .groupBy('userRoleBranch.companyId, userRoleBranch.roleId')
      .getRawOne();
    const permission = role?.rolePermissions.map((rp) => {
      return {
        permissionId: rp.permissionId,
        view: rp.view,
        create: rp.create,
        update: rp.update,
        delete: rp.delete,
        download: rp.download,
        upload: rp.upload,
      };
    });

    return {
      roleId: role.roleId,
      roleName: role.roleName,
      type: role.type,
      createdBy: role.createdBy,
      updatedBy: role.updatedBy,
      createdAt: role.createdAt,
      updatedAt: role.updatedAt,
      createdUser: role.createdUser,
      updatedUser: role.updatedUser,
      count: countUser ? countUser.count : '0',
      permission,
    };
  }

  async createRoles({
    user,
    body,
  }: {
    user: WithUserContext;
    body: CreateRolesDto;
  }) {
    const companyId = user.company;
    await this.validateRolePermission(companyId, body);

    let roleId: string;
    const queryRunner = this.roleRepo.manager.connection.createQueryRunner();

    const activity = new GeneralActivitiesEntity();
    activity.type = 'role-create';

    let roleLog: RoleEntity;
    let rolePermissionLog: RolePermissionEntity[] = [];

    try {
      // Connection query runner
      await queryRunner.connect();

      // Start transaction
      await queryRunner.startTransaction();

      const role = new RoleEntity();
      role.roleName = body.name;
      role.type = body.type as SiteType;
      role.companyId = companyId;
      role.createdBy = user.userKey;
      role.updatedBy = user.userKey;

      roleLog = role;

      const roleData = await queryRunner.manager.save(RoleEntity, role);
      roleId = roleData.roleId;

      const rolePermission = body.permissions.map((obj) => ({
        ...obj,
        companyId: companyId,
        roleId: roleData.roleId,
      })) as RolePermissionEntity[];

      rolePermissionLog = rolePermission;
      await queryRunner.manager.save(RolePermissionEntity, rolePermission);
      await queryRunner.commitTransaction();
    } catch (error: any) {
      activity.detail = {
        error: error.message,
        at: new Date(),
        result: 'error',
      };
      activity.createdBy = user.userKey;
      activity.companyId = user.company;

      await this.generalActivitiesRepository.save(activity);
      // Rollback transaction when error query
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      // Release query runner
      await queryRunner.release();
    }

    activity.detail = {
      error: null,
      at: new Date(),
      result: 'success',
      detail: {
        role: roleLog,
        rolePermission: rolePermissionLog,
      },
    };

    activity.createdBy = user.userKey;
    activity.companyId = user.company;
    await this.generalActivitiesRepository.save(activity);

    return { roleId: roleId };
  }

  async validateRolePermission(
    companyId: string,
    body: CreateRolesDto,
    roleId?: string,
    roleTypeEdit?: SiteType,
  ) {
    const payLoadRoleName = body.name;
    const payLoadPermissions = body.permissions;
    const payLoadPermissionIds = body.permissions.map((p) => p.permissionId);
    let payLoadPermissionType = body.type;

    if (roleTypeEdit && !body.type) {
      payLoadPermissionType = roleTypeEdit;
    }

    const role = await this.roleRepo.findOne({
      where: {
        roleName: payLoadRoleName,
        companyId,
      },
    });

    if ((role && !roleId) || (role && roleId && role.roleId !== roleId)) {
      throw this.baseExceptionService.exception('ROLE_NAME_ALREADY_EXISTS');
    }

    const permissionsData = await this.permissionRepo.find({
      where: { permissionId: In(payLoadPermissionIds), companyId },
      relations: ['permissionGroup'],
    });

    if (
      permissionsData.map((p) => p.permissionId).length !==
      payLoadPermissionIds.length
    ) {
      throw this.baseExceptionService.exception(
        'BODY_PAYLOAD_INVALID',
        `Invalid permissionId`,
      );
    }

    const permissionTypeList = permissionsData.map(
      (p) => p.permissionGroup.type,
    );

    if (!permissionTypeList.every((type) => type === payLoadPermissionType)) {
      throw this.baseExceptionService.exception(
        'BODY_PAYLOAD_INVALID',
        `Invalid permissionId`,
      );
    }

    payLoadPermissions.forEach((p) => {
      const permission = permissionsData.find(
        (permission) => permission.permissionId === p.permissionId,
      );

      const actions = [
        'view',
        'create',
        'update',
        'delete',
        'download',
        'upload',
      ];

      const isActionCorrect = actions.every(
        (action) => !(p[action] && !permission?.[action]),
      );

      if (!isActionCorrect) {
        throw this.baseExceptionService.exception(
          'BODY_PAYLOAD_INVALID',
          `Invalid permission action`,
        );
      }
    });
  }

  async updateRole({
    user,
    id,
    body,
  }: {
    user: WithUserContext;
    id: string;
    body: CreateRolesDto;
  }) {
    const bodyRoleName = body.name;
    const bodyPermisssionList = body.permissions;
    const companyId = user.company;
    //find role where id
    const role = await this.roleRepo.findOne({
      where: { companyId, roleId: id },
      //relations: ['rolePermissions', 'rolePermissions.permission'],
    });
    if (!role) {
      throw this.baseExceptionService.exception(
        'NOT_FOUND_DATA',
        'Role ID NOT FOUND',
      );
    }
    const rolePermissionBody: RolePermissionEntity[] = [];

    if (bodyPermisssionList && bodyPermisssionList.length > 0) {
      for (const p of bodyPermisssionList) {
        rolePermissionBody.push({
          companyId: companyId,
          roleId: id,
          permissionId: p.permissionId,
          view: p.view,
          create: p.create,
          update: p.update,
          delete: p.delete,
          download: p.download,
          upload: p.upload,
        } as RolePermissionEntity);
      }
    }
    await this.validateRolePermission(companyId, body, id, role?.type);

    const activity = new GeneralActivitiesEntity();
    activity.type = 'role-update';
    activity.createdBy = user.userKey;
    activity.companyId = user.company;

    let roleLog = new RoleEntity();
    let rolePermissionLog: RolePermissionEntity[] = [];

    const queryRunner = this.roleRepo.manager.connection.createQueryRunner();
    try {
      await queryRunner.connect();
      await queryRunner.startTransaction();

      if (role) {
        //delete role permission by role id
        await queryRunner.manager.delete(RolePermissionEntity, {
          companyId: companyId,
          roleId: id,
        });
        //insert new role permission by role id
        await queryRunner.manager.save(
          RolePermissionEntity,
          rolePermissionBody,
        );

        role.roleName = bodyRoleName;
        role.updatedBy = user.userKey;
        role.updatedAt = new Date();

        roleLog = role;
        rolePermissionLog = rolePermissionBody;
        await queryRunner.manager.save(role);
        await queryRunner.commitTransaction();
      }
    } catch (error: any) {
      activity.detail = {
        error: error.message,
        at: new Date(),
        result: 'error',
      };

      await this.generalActivitiesRepository.save(activity);
      // Rollback transaction when error query
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      // Release query runner
      await queryRunner.release();
    }

    activity.detail = {
      error: null,
      at: new Date(),
      result: 'success',
      detail: {
        role: roleLog,
        rolePermission: rolePermissionLog,
      },
    };

    await this.generalActivitiesRepository.save(activity);

    return { roleId: id };
  }
}
