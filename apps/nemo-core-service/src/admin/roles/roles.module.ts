import { MiddlewareConsumer, Module, NestModule } from '@nestjs/common';
import { WithUserMiddleware } from '../../middlewares';
import { AdminRolesController } from './roles.controller';
import { AdminRolesService } from './roles.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import {
  BranchEntity,
  CompanyEntity,
  CompanyRoleEntity,
  GeneralActivitiesEntity,
  PermissionEntity,
  PermissionGroupEntity,
  RoleEntity,
  RolePermissionEntity,
  UserEntity,
  UserRoleBranchEntity,
} from '../../entities';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      UserEntity,
      BranchEntity,
      CompanyEntity,
      CompanyRoleEntity,
      RoleEntity,
      PermissionEntity,
      PermissionGroupEntity,
      UserRoleBranchEntity,
      RolePermissionEntity,
      GeneralActivitiesEntity,
    ]),
  ],
  controllers: [AdminRolesController],
  providers: [AdminRolesService],
})
export class AdminRolesModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(WithUserMiddleware).forRoutes(AdminRolesController);
  }
}
