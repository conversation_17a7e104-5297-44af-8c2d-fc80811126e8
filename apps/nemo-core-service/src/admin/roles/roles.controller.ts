import {
  Body,
  Controller,
  Get,
  Param,
  Post,
  Patch,
  Query,
} from '@nestjs/common';

import { Permission, PermissionAction } from '../../config';
import { AdminRolesService } from './roles.service';
import { WithUser, Permissions } from '../../decorators';
import { WithUserContext } from '../../interfaces';
import { GetRolesDto } from './dto/get-roles.dto';
import { CreateRolesDto } from './dto/create-roles.dto';

@Controller('/v1/admin/roles')
export class AdminRolesController {
  constructor(readonly AdminRolesService: AdminRolesService) {}

  @Get()
  @Permissions([Permission.CMS_ROLE_MANAGE + PermissionAction.VIEW])
  async getRoles(
    @WithUser() user: WithUserContext,
    @Query() query: GetRolesDto,
  ) {
    return await this.AdminRolesService.getRoles({
      companyId: user.company,
      query,
    });
  }

  @Get('config')
  @Permissions([Permission.CMS_STAFF_MANAGE + PermissionAction.VIEW])
  async getRolesConfig(@WithUser() user: WithUserContext) {
    return await this.AdminRolesService.getRolesConfig(user.company);
  }

  @Get('/:id')
  @Permissions([Permission.CMS_ROLE_MANAGE + PermissionAction.VIEW])
  async getRoleById(
    @Param('id') id: string,
    @WithUser() user: WithUserContext,
  ) {
    return await this.AdminRolesService.getRoleById({
      companyId: user.company,
      id,
    });
  }

  @Post()
  @Permissions([Permission.CMS_ROLE_MANAGE + PermissionAction.CREATE])
  async createRoles(
    @WithUser() user: WithUserContext,
    @Body() body: CreateRolesDto,
  ) {
    return await this.AdminRolesService.createRoles({
      user,
      body,
    });
  }

  @Patch('/:id')
  @Permissions([Permission.CMS_ROLE_MANAGE + PermissionAction.UPDATE])
  async updateRole(
    @Param('id') id: string,
    @WithUser() user: WithUserContext,
    @Body() body: CreateRolesDto,
  ) {
    return await this.AdminRolesService.updateRole({
      user,
      id,
      body,
    });
  }
}
