import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import {
  ModelMasterFunctionEntity,
  ModelChecklistEntity,
  ChecklistType,
  ModelMasterEntity,
  ModelPriceActivitiesEntity,
  ModelPriceActivityDetail,
  ModelPriceActivityType,
  ModelPriceActivityTable,
} from '../../entities';
import { Repository, EntityManager, ILike, In } from 'typeorm';
import { filter, get, isEmpty, isNumber, keyBy, map, uniqBy } from 'lodash';
import { GetModelMaserFunctionDto } from './dto';
import { DEFAULT_PAGE_SIZE, Pagination } from '../../crud';
import {
  ExcelManagerService,
  Options,
  IConvertToType,
  RemoveRowData,
} from '../../excel/excel-manager.service';
import {
  GetModelMasterFunctionResponse,
  ModelMasterFunctionAdjustResult,
  ModelMasterFunctionQueryResult,
  ModelMasterFunctionByModelKeyResult,
  ModelMasterQuestionByModelKeyResult,
  QuestionChoices,
} from 'contracts';
import { BASE_COLLATE } from '../../../src/config';
import { BaseExceptionService } from '../../exceptions';

const orderByChoice = [
  'brand asc',
  'brand desc',
  'model asc',
  'model desc',
  'modelKey asc',
  'modelKey desc',
  'ownerName asc',
  'ownerName desc',
] as const;
type OrderByChoice = (typeof orderByChoice)[number];
const isOrderByChoice = (x: any): x is OrderByChoice =>
  orderByChoice.includes(x);

const MODEL_MASTER_FUNCTION = 'modelMasterFunction';
const MODEL_MASTER = 'modelMaster';
export const MODEL_MASTER_KEY = 'modelMaster';

@Injectable()
export class ModelMasterFunctionService {
  constructor(
    @InjectRepository(ModelMasterFunctionEntity)
    private readonly modelMasterFunctionRepo: Repository<ModelMasterFunctionEntity>,
    @InjectRepository(ModelChecklistEntity)
    private readonly modelChecklistRepo: Repository<ModelChecklistEntity>,
    @InjectRepository(ModelMasterEntity)
    private readonly modelMastersRepo: Repository<ModelMasterEntity>,
    private readonly excelManagerService: ExcelManagerService,
    private readonly baseExceptionService: BaseExceptionService,
    @InjectRepository(ModelPriceActivitiesEntity)
    private readonly modelPriceActivitiesRepo: Repository<ModelPriceActivitiesEntity>,
  ) {}

  async customQueryModelMasterFunction(
    company: string | null,
    query: GetModelMaserFunctionDto,
  ): Promise<
    GetModelMasterFunctionResponse & { paginationResult: Pagination }
  > {
    const queryOrderBy = query.orderBy?.trim();
    const limit = +(query.pageSize ?? DEFAULT_PAGE_SIZE);
    const offset = (+(query.page ?? '1') - 1) * limit;
    const disablePagination = /false/i.test(query.pagination ?? '');
    const masterFunctionByModel =
      this.getQueryMasterFunctionGroupByModelKey(company);

    if (isOrderByChoice(queryOrderBy)) {
      let [col, order] = queryOrderBy.split(' ');
      if (['model', 'brand'].includes(col)) {
        col = `"${MODEL_MASTER_KEY}".model_identifiers->>'${col}'`;
      } else if (col === 'ownerName') {
        col = `"${MODEL_MASTER_KEY}".owner_name`;
      }
      masterFunctionByModel.orderBy(
        `${
          col === 'modelKey' ? `${MODEL_MASTER_KEY}.model_key` : col
        } ${BASE_COLLATE}`,
        order === 'asc' ? 'ASC' : 'DESC',
      );
    }

    if (!disablePagination) {
      masterFunctionByModel.offset(offset);
      masterFunctionByModel.limit(limit);
    }

    const result = await masterFunctionByModel.getRawMany();

    const count = await masterFunctionByModel
      .select(`COUNT(DISTINCT ${MODEL_MASTER_FUNCTION}.model_key)`, 'count')
      .orderBy('')
      .groupBy('')
      .offset(0)
      .limit(0)
      .getRawOne();

    const paginationResult: Pagination = {
      page: disablePagination ? 1 : +(query.page ?? '1'),
      pageSize: disablePagination ? count : limit,
      totalRecords: +(count.count ?? '1'),
    };

    return {
      items: this.afterLoad(result),
      paginationResult,
      column: this.getColumnHeader(),
    };
  }

  getQueryMasterFunctionGroupByModelKey(company: string | null) {
    return this.modelMasterFunctionRepo
      .createQueryBuilder(`${MODEL_MASTER_FUNCTION}`)
      .leftJoin(
        `${MODEL_MASTER_FUNCTION}.${MODEL_MASTER}`,
        `${MODEL_MASTER_KEY}`,
      )
      .select(`${MODEL_MASTER_KEY}.model_key`, 'modelKey')
      .addSelect(
        `string_agg("${MODEL_MASTER_FUNCTION}".function_key_cond,',')`,
        'aggFunctionKeyCond',
      )
      .addSelect(
        `string_agg("${MODEL_MASTER_FUNCTION}".penalties,',')`,
        'aggPenalties',
      )
      .addSelect(`"${MODEL_MASTER_KEY}".model_identifiers`, 'modelIdentifiers')
      .addSelect(`"${MODEL_MASTER_KEY}".owner_name`, 'ownerName')
      .where(`"${MODEL_MASTER_FUNCTION}".company_id = :company`, { company })
      .andWhere(`"${MODEL_MASTER_FUNCTION}".model_key NOT IN ('default')`)
      .groupBy(`"${MODEL_MASTER_KEY}".model_key`)
      .addGroupBy(`"${MODEL_MASTER_KEY}".model_identifiers`)
      .addGroupBy(`"${MODEL_MASTER_KEY}".owner_name`);
  }

  afterLoad(
    data: ModelMasterFunctionQueryResult[],
  ): ModelMasterFunctionAdjustResult[] {
    return data.map((functions) => ({
      id: functions.modelKey,
      modelIdentifiers: functions.modelIdentifiers,
      ownerName: functions.ownerName,
      penalty: this.mapArray(
        functions.aggFunctionKeyCond.split(','),
        functions.aggPenalties.split(','),
      ),
    }));
  }

  mapArray(array1, array2): { [key: string]: string } {
    const mapA1ToA2 = {};
    for (let i = 0; i < array1.length; i++) {
      mapA1ToA2[array1[i]] = Number(array2[i]).toFixed(0);
    }
    return mapA1ToA2;
  }

  getColumnHeader() {
    const listColumn: string[] = [];
    for (const key in excelManagerOption.headers) {
      listColumn.push(excelManagerOption.headers[key].keyName);
    }
    listColumn.shift();
    return listColumn;
  }

  async exportModelMasterFunction(company: string | null): Promise<Buffer> {
    const masterFunctionByModel =
      this.getQueryMasterFunctionGroupByModelKey(company);
    masterFunctionByModel.orderBy(
      `${MODEL_MASTER_KEY}.model_key ${BASE_COLLATE}`,
      'ASC',
    );

    const data = (await masterFunctionByModel.getRawMany()).map((functions) => {
      return {
        modelKey: functions.modelKey,
        ...this.mapArray(
          functions.aggFunctionKeyCond.split(','),
          functions.aggPenalties.split(','),
        ),
      };
    });

    const sheetName = 'Master Price Function';
    return this.excelManagerService.generateExcelFile(data, sheetName);
  }

  async exportModelMasterFunctionV2(company: string | null): Promise<Buffer> {
    // get model question list from table model_checklist
    const modelChecks = await this.modelChecklistRepo.find({
      order: {
        checklistType: 'ASC',
        createdAt: 'DESC',
      },
    });

    // get answer list from table model_master_function
    const masterFunctionByModel =
      this.getQueryMasterFunctionGroupByModelKey(company);
    masterFunctionByModel.orderBy(
      `${MODEL_MASTER_KEY}.model_key ${BASE_COLLATE}`,
      'ASC',
    );

    const answers = (await masterFunctionByModel.getRawMany()).map((i) => {
      return {
        modelKey: i.modelKey,
        ...this.mapArray(
          i.aggFunctionKeyCond.split(','),
          i.aggPenalties.split(','),
        ),
      };
    });

    return this.excelManagerService.generateTemplateExcelFileV2(
      modelChecks,
      answers,
    );
  }

  async putModelMasterFunction(
    company: string | undefined,
    file: Express.Multer.File,
    user: string,
  ) {
    const fileData = await this.excelManagerService.readExcelFile(
      file.buffer,
      file.mimetype,
    );

    const saveList: Partial<ModelMasterFunctionEntity>[] = [];

    const date = new Date();

    // find ModelCheckList
    const modelChecklistData = await this.modelChecklistRepo.find({
      where: { companyId: company },
    });

    const modelChecklist = modelChecklistData.reduce(
      (acc, item) => {
        acc[item.functionKey] = item.id;
        return acc;
      },
      {} as Record<string, string>,
    );

    for (const data of uniqBy(fileData, 'modelKey') as Array<{
      modelKey: string;
      [key: string]: string;
    }>) {
      const { modelKey, ...penalty } = data;
      saveList.push(
        ...Object.keys(penalty).map((val) => {
          const fnKey = this.getFunctionKey(val);
          const checkListId = modelChecklist[fnKey];

          return {
            companyId: company,
            modelKey,
            functionKeyCond: val,
            penalties: penalty[val],
            updatedBy: user,
            updatedAt: date,
            createdBy: user,
            checkListId: checkListId,
            // ...(dictAllData[`${modelKey}&&&${val}`] ? {} : { createdBy: user }),
          };
        }),
      );
    }

    await this.transactionSaveModelmasterFunctionUploadExcel(
      saveList,
      this.modelMasterFunctionRepo.manager,
    );

    // await this.modelMasterFunctionRepo.save(saveList, { chunk: 500 });
    return null;
  }

  async buildSavelistRows(
    modelChecks: ModelChecklistEntity[],
    fileData: Record<string, any>[],
    company: string | undefined,
    user: string,
  ) {
    // group by key form table model_checklist
    const modelChecklist = modelChecks.reduce(
      (acc, item) => {
        acc[item.functionKey] = item.id;
        return acc;
      },
      {} as Record<string, string>,
    );

    let saveList: Partial<ModelMasterFunctionEntity>[] = [];
    const date = new Date();

    const objModel = uniqBy(fileData, 'PRODUCT_ID') as Array<{
      PRODUCT_ID: string;
      [key: string]: string;
    }>;

    // add =skip when was type "question"
    let quesKeyAll: string[] = [];
    for (const model of modelChecks) {
      if (model.checklistType === ChecklistType.QUESTION) {
        const quesKey = `${model.functionSection}.${model.functionKey}`;
        quesKeyAll.push(quesKey);
      }
    }

    for (const item of objModel) {
      for (const field in item) {
        if (field === 'PRODUCT_ID') continue;

        const fnKey = this.getFunctionKey(field);
        const checkListId = modelChecklist[fnKey];

        const productId = get(item, 'PRODUCT_ID', 'unknow-modelkey');

        const checkProductKey = await this.modelMastersRepo.findOneBy({
          modelKey: productId,
        });

        if (isEmpty(checkProductKey)) {
          throw this.baseExceptionService.exception(
            'NOT_FOUND_DATA',
            `Product id ${productId} is not match.`,
          );
        }

        const newRow = {
          companyId: company,
          modelKey: productId,
          functionKeyCond: field,
          penalties: isNumber(item[field]) ? `${item[field]}` : '0',
          updatedBy: user,
          updatedAt: date,
          createdBy: user,
          checkListId: checkListId,
        };
        saveList.push(newRow);

        // type "question" add "=skip" a new row

        for (const keyOfQuestion of quesKeyAll) {
          if (field.includes(keyOfQuestion)) {
            // process working add
            const keycond = `${keyOfQuestion}=skip`;

            saveList = filter(saveList, function (f) {
              return !(
                f.modelKey === productId && f.functionKeyCond === keycond
              );
            });

            const newRowSkip = {
              companyId: company,
              modelKey: productId,
              functionKeyCond: keycond,
              penalties: '0',
              updatedBy: user,
              updatedAt: date,
              createdBy: user,
              checkListId: checkListId,
            };

            saveList.push(newRowSkip);
          }
        }
      }
    }

    return saveList;
  }

  async transactionSaveModelmasterFunctionUploadExcel(
    saveList: Partial<ModelMasterFunctionEntity>[],
    manager: EntityManager,
  ) {
    await manager.transaction(async (man: EntityManager): Promise<void> => {
      for (let i = 0; i < saveList.length; i += 1000) {
        await man
          .createQueryBuilder()
          .insert()
          .into(ModelMasterFunctionEntity)
          .values(saveList.slice(i, i + 1000))
          .orUpdate(
            ['penalties', 'updated_at', 'updated_by', 'check_list_id'],
            ['company_id', 'model_key', 'function_key_cond'],
          )
          .execute();
      }
    });
  }

  async buildRemovelistRows(
    modelChecks: ModelChecklistEntity[],
    willBeRemoveRowData: RemoveRowData[],
    company: string | undefined,
  ) {
    const list = [] as Array<{
      companyId: any;
      modelKey: string;
      functionKeyCond: string[];
    }>;
    const moduleOptionIndb = ['=functional', '=non_functional', '=skip'];
    const questionOptionIndb = ['=skip'];

    for (const product of willBeRemoveRowData) {
      for (const keyUnsed of product.keys) {
        for (const cit of modelChecks) {
          const keycit = `${cit.functionSection}.${cit.functionKey}`;
          if (keycit === keyUnsed) {
            let removeRows: string[] = [];

            if (cit.checklistType === ChecklistType.MODULE) {
              removeRows = map(moduleOptionIndb, function (opt) {
                return `${keyUnsed}${opt}`;
              });
            } else {
              removeRows.push(`${keyUnsed}${questionOptionIndb[0]}`);
              for (const answer of cit.questionChoices) {
                const row = `${keyUnsed}=${get(answer, 'id', null)}`; // Column key using the question choice ID
                if (row) removeRows.push(row);
              }
            }
            list.push({
              companyId: company,
              modelKey: product.productID,
              functionKeyCond: removeRows,
            });
          }
        }
      }
    }
    return list;
  }

  async transactionRemoveModelmasterFunctionUploadExcel(
    removelist: Array<{
      companyId: any;
      modelKey: string;
      functionKeyCond: string[];
    }>,
    manager: EntityManager,
  ) {
    for (const removeitem of removelist) {
      await manager.delete(ModelMasterFunctionEntity, {
        companyId: removeitem.companyId,
        modelKey: removeitem.modelKey,
        functionKeyCond: In(removeitem.functionKeyCond),
      });
    }
  }

  async putModelMasterFunctionV2(
    company: string | undefined,
    file: Express.Multer.File,
    user: string,
  ) {
    // get model question list from table model_checklist
    const modelChecks = await this.modelChecklistRepo.find({
      order: {
        checklistType: 'ASC',
        createdAt: 'DESC',
      },
    });

    // make sure data checklist not to be empty
    if (modelChecks.length === 0) {
      const mesErr = 'Table model_checklist is empty';
      throw this.baseExceptionService.exception('NOT_FOUND_DATA', mesErr);
    }

    // read a rows in file excel .xlsx
    const { willBeRemoveRowData, willBeSaveData } =
      await this.excelManagerService.readExcelFileV2(file, modelChecks);

    // build data row for continew delete
    const removerows = await this.buildRemovelistRows(
      modelChecks,
      willBeRemoveRowData,
      company,
    );

    // build data row for continue save
    const saverows = await this.buildSavelistRows(
      modelChecks,
      willBeSaveData,
      company,
      user,
    );

    const queryRunner =
      this.modelMasterFunctionRepo.manager.connection.createQueryRunner();

    try {
      await queryRunner.connect();
      await queryRunner.startTransaction();
      const man = queryRunner.manager;

      // Chunk removerows into batches of 500
      for (let i = 0; i < removerows.length; i += 500) {
        const chunkedRemoveRows = removerows.slice(i, i + 500);

        // Transaction for safe database removal in chunks
        await this.transactionRemoveModelmasterFunctionUploadExcel(
          chunkedRemoveRows,
          man,
        );
      }

      // save on db with transaction for safe database and data
      await this.transactionSaveModelmasterFunctionUploadExcel(saverows, man);

      await queryRunner.commitTransaction();
    } catch (error) {
      if (queryRunner.isTransactionActive) {
        await queryRunner.rollbackTransaction();
      }
      throw error;
    } finally {
      await queryRunner.release();
    }

    const activityLog = new ModelPriceActivitiesEntity();
    const logDetail: ModelPriceActivityDetail[] = [];

    saverows.forEach((row) => {
      const isExistedKey = logDetail.find(
        (item) => item.companyId === company && item.modelKey === row.modelKey,
      );
      if (isExistedKey) {
        isExistedKey.modelMasterFunction.push({
          keyCond: row.functionKeyCond ?? '',
          penalties: row.penalties ?? '0',
        });
      } else {
        logDetail.push({
          companyId: row.companyId ?? '',
          modelKey: row.modelKey ?? '',
          modelMaster: {
            systemCodeList: [],
            modelMasterGrades: [],
          },
          modelMasterFunction: [
            {
              keyCond: row.functionKeyCond ?? '',
              penalties: row.penalties ?? '0',
            },
          ],
        });
      }
    });

    activityLog.type = ModelPriceActivityType.FILE;
    activityLog.table = ModelPriceActivityTable.MODEL_MASTER_FUNCTION;
    activityLog.detail = logDetail;
    activityLog.companyId = company ?? '';
    activityLog.createdBy = user;
    activityLog.createdAt = new Date();

    await this.modelPriceActivitiesRepo.save(activityLog);

    return true;
  }

  getFunctionKey(functionKeyCond: string): string {
    const functionName = functionKeyCond.split('.');
    if (functionName.length > 1) {
      const key = functionName[1].split('=');

      return key.length > 1 ? key[0] : functionName[1];
    }

    return functionKeyCond;
  }

  private async getModelMasterData(
    companyId: string | null,
    modelKey: string,
    checklistType: ChecklistType,
    orderDirection: 'ASC' | 'DESC',
  ) {
    const modelChecklist = await this.getModelCheckList(
      companyId,
      checklistType,
    );

    if (modelChecklist.length < 1) {
      throw this.baseExceptionService.exception(
        'NOT_FOUND_DATA',
        'MODEL_CHECKLIST_NOT_FOUND',
      );
    }

    const modelMaster = await this.modelMastersRepo.findOneBy({
      modelKey,
    });
    if (!modelMaster) {
      throw this.baseExceptionService.exception(
        'NOT_FOUND_DATA',
        'MODEL_KEY_NOT_FOUND',
      );
    }

    const modelMasterFunction = await this.modelMasterFunctionRepo.find({
      where: {
        ...(companyId ? { companyId } : undefined),
        modelKey,
        functionKeyCond: ILike(`${modelChecklist[0].functionSection}%`),
      },
      order: {
        createdAt: orderDirection,
      },
    });

    const mapFunctionKeyCondObj = keyBy(modelMasterFunction, 'functionKeyCond');

    return { modelChecklist, modelMaster, mapFunctionKeyCondObj };
  }

  async getModelMasterFunctionByModelKey(
    companyId: string | null,
    modelKey: string,
  ) {
    const { modelChecklist, mapFunctionKeyCondObj } =
      await this.getModelMasterData(
        companyId,
        modelKey,
        ChecklistType.MODULE,
        'DESC',
      );

    const result: ModelMasterFunctionByModelKeyResult[] = modelChecklist.map(
      (item) => {
        const {
          id: checkListId,
          companyId,
          functionKey,
          functionSection,
          checklistType,
          checklistNameTh,
          checklistNameEn,
        } = item;

        const functionalStr = `${functionSection}.${functionKey}=functional`;
        const nonFunctionalStr = `${functionSection}.${functionKey}=non_functional`;
        const skipStr = `${functionSection}.${functionKey}=skip`;

        const functional: ModelMasterFunctionEntity | null =
          mapFunctionKeyCondObj[functionalStr];

        const nonFunctional: ModelMasterFunctionEntity | null =
          mapFunctionKeyCondObj[nonFunctionalStr];

        const skip: ModelMasterFunctionEntity | null =
          mapFunctionKeyCondObj[skipStr];

        if (functional || nonFunctional || skip) {
          return {
            checkListId,
            companyId,
            modelKey,
            functionKey,
            functionSection,
            checklistType,
            checklistNameTh,
            checklistNameEn,
            checked: true,
            functionalPenalties: functional?.penalties ?? '0',
            functionalKeyCond: functionalStr,
            nonFunctionalPenalties: nonFunctional?.penalties ?? '0',
            nonFunctionalKeyCond: nonFunctionalStr,
            skipPenalties: skip?.penalties ?? '0',
            skipKeyCond: skipStr,
          };
        }

        return {
          checkListId,
          companyId,
          modelKey,
          functionKey,
          functionSection,
          checklistType,
          checklistNameTh,
          checklistNameEn,
          checked: false,
          functionalPenalties: '0',
          functionalKeyCond: functionalStr,
          nonFunctionalPenalties: '0',
          nonFunctionalKeyCond: nonFunctionalStr,
          skipPenalties: '0',
          skipKeyCond: skipStr,
        };
      },
    );

    return result;
  }

  async getModelMasterQuestionByModelKey(
    companyId: string | null,
    modelKey: string,
  ) {
    const { modelChecklist, mapFunctionKeyCondObj } =
      await this.getModelMasterData(
        companyId,
        modelKey,
        ChecklistType.QUESTION,
        'ASC',
      );

    const result: ModelMasterQuestionByModelKeyResult[] = modelChecklist.map(
      (item) => {
        const {
          id: checkListId,
          companyId,
          functionKey,
          functionSection,
          checklistType,
          checklistNameTh,
          checklistNameEn,
          questionType,
          questionChoices,
        } = item;

        let isCheck: boolean = false;
        let skipPenalties: string = '0';
        const skipKeyCond: string = `${functionSection}.${functionKey}=skip`;

        const questionChoicesTarget: QuestionChoices[] = questionChoices.map(
          (c) => {
            let penalties = '0';
            const mockKeyCond = `${functionSection}.${functionKey}=${c.id}`;

            if (mapFunctionKeyCondObj[mockKeyCond]) {
              isCheck = true;
              penalties = mapFunctionKeyCondObj[mockKeyCond].penalties;
            } else {
              isCheck = false;
            }

            return {
              id: c.id,
              answerEn: c.answerEn,
              answerTh: c.answerTh,
              penalties,
              keyCond: mockKeyCond,
            };
          },
        );

        if (mapFunctionKeyCondObj[skipKeyCond]) {
          isCheck = true;
          skipPenalties = mapFunctionKeyCondObj[skipKeyCond].penalties;
        }

        return {
          checkListId,
          companyId,
          modelKey,
          functionKey,
          functionSection,
          checklistType,
          checklistNameTh,
          checklistNameEn,
          questionType,
          questionChoices: questionChoicesTarget,
          checked: isCheck,
          skipPenalties,
          skipKeyCond,
        };
      },
    );

    return result;
  }

  async getModelCheckList(
    companyId: string | null,
    checklistType: ChecklistType,
  ) {
    return await this.modelChecklistRepo.find({
      where: {
        ...(companyId ? { companyId } : undefined),
        checklistType,
      },
      order: {
        createdAt: 'ASC',
      },
    });
  }
}

export const excelManagerOption: Options = {
  headers: {
    PRODUCT_ID: {
      keyName: 'modelKey',
      type: IConvertToType.string,
      isRequired: true,
      subHeader: 'ID',
    },
    PRODUCT_COUNTRY_THAI: {
      keyName: 'product_information.country_of_purchase=th',
      type: IConvertToType.numString,
      isRequired: true,
      subHeader: 'ประเทศที่ซื้อโทรศัพท์\nไทย',
      options: {
        decimal: 0,
      },
    },
    PRODUCT_COUNTRY_OTHERS: {
      keyName: 'product_information.country_of_purchase=etc',
      type: IConvertToType.numString,
      isRequired: true,
      subHeader: 'ประเทศที่ซื้อโทรศัพท์\nต่างประเทศ',
      options: {
        decimal: 0,
      },
    },
    SCREEN_DISPLAY_NORMAL: {
      keyName: 'product_information.screen_display=normal',
      type: IConvertToType.numString,
      isRequired: true,
      subHeader: 'การแสดงภาพหน้าจอ\nปกติ',
      options: {
        decimal: 0,
      },
    },
    SCREEN_DISPLAY_ABNORMAL: {
      keyName: 'product_information.screen_display=abnormal',
      type: IConvertToType.numString,
      isRequired: true,
      subHeader: 'การแสดงภาพหน้าจอ\nไม่ปกติ',
      options: {
        decimal: 0,
      },
    },
    ICLOUD_OR_GOOGLE_ACCOUNT_CAN_SIGN_OUT: {
      keyName: 'product_information.icloud_or_google_account=can_sign_out',
      type: IConvertToType.numString,
      isRequired: true,
      subHeader: 'ออกจาก iCloud หรือ Google Account\nได้',
      options: {
        decimal: 0,
      },
    },
    ICLOUD_OR_GOOGLE_ACCOUNT_CANNOT_SIGN_OUT: {
      keyName: 'product_information.icloud_or_google_account=cannot_sign_out',
      type: IConvertToType.numString,
      isRequired: true,
      subHeader: 'ออกจาก iCloud หรือ Google Account\nไม่ได้',
      options: {
        decimal: 0,
      },
    },
    'BATTERY_HEALTH_ABOVE_80%': {
      keyName: 'product_information.battery_health=above_80_percent',
      type: IConvertToType.numString,
      isRequired: true,
      subHeader: 'สุขภาพแบตเตอรี่\nสูงกว่าหรือเท่ากับ 80%',
      options: {
        decimal: 0,
      },
    },
    'BATTERY_HEALTH_BELOW_80%': {
      keyName: 'product_information.battery_health=below_80_percent',
      type: IConvertToType.numString,
      isRequired: true,
      subHeader: 'สุขภาพแบตเตอรี่\nต่ำกว่า 80%',
      options: {
        decimal: 0,
      },
    },
    ACCESSORIES_COMPLETED: {
      keyName: 'product_information.additional_accessories=complete',
      type: IConvertToType.numString,
      isRequired: true,
      subHeader: 'อุปกรณ์เสริม (กล่อง,หูฟัง,สายชาร์จ,หัวชาร์จ)\nครบ',
      options: {
        decimal: 0,
      },
    },
    ACCESSORIES_INCOMPLETED: {
      keyName: 'product_information.additional_accessories=incomplete',
      type: IConvertToType.numString,
      isRequired: true,
      subHeader: 'อุปกรณ์เสริม (กล่อง,หูฟัง,สายชาร์จ,หัวชาร์จ)\nไม่ครบ',
      options: {
        decimal: 0,
      },
    },
    DEVICE_CONDITION_NO_MARK: {
      keyName: 'product_information.device_condition=no_marks_new',
      type: IConvertToType.numString,
      isRequired: true,
      subHeader: 'สภาพตัวเครื่อง\nลักษณะ ไม่มีรอย สภาพใหม่',
      options: {
        decimal: 0,
      },
    },
    DEVICE_CONDITION_MINOR_MARK: {
      keyName: 'product_information.device_condition=minor_marks',
      type: IConvertToType.numString,
      isRequired: true,
      subHeader: 'สภาพตัวเครื่อง\nลักษณะ มีรอยเล็กน้อย (2-3 จุด)',
      options: {
        decimal: 0,
      },
    },
    DEVICE_CONDITION_MAJOR_MARK: {
      keyName: 'product_information.device_condition=major_marks',
      type: IConvertToType.numString,
      isRequired: true,
      subHeader: 'สภาพตัวเครื่อง\nลักษณะ มีรอยมาก บุบตามตัวเครื่อง',
      options: {
        decimal: 0,
      },
    },
    BLUETOOTH_FUNCTIONAL: {
      keyName: 'remobie_check_list.bluetooth=functional',
      type: IConvertToType.numString,
      isRequired: true,
      subHeader: 'Bluetooth\nใช้งานได้',
      options: {
        decimal: 0,
      },
    },
    BLUETOOTH_NON_FUNCTIONAL: {
      keyName: 'remobie_check_list.bluetooth=non_functional',
      type: IConvertToType.numString,
      isRequired: true,
      subHeader: 'Bluetooth\nใช้งานไม่ได้',
      options: {
        decimal: 0,
      },
    },
    WIFI_FUNCTIONAL: {
      keyName: 'remobie_check_list.wifi=functional',
      type: IConvertToType.numString,
      isRequired: true,
      subHeader: 'WIFI\nใช้งานได้',
      options: {
        decimal: 0,
      },
    },
    WIFI_NON_FUNCTIONAL: {
      keyName: 'remobie_check_list.wifi=non_functional',
      type: IConvertToType.numString,
      isRequired: true,
      subHeader: 'WIFI\nใช้งานไม่ได้',
      options: {
        decimal: 0,
      },
    },
    FRONT_CAMERA_FUNCTIONAL: {
      keyName: 'remobie_check_list.front_camera=functional',
      type: IConvertToType.numString,
      isRequired: true,
      subHeader: 'กล้องหน้า\nใช้งานได้',
      options: {
        decimal: 0,
      },
    },
    FRONT_CAMERA_NON_FUNCTIONAL: {
      keyName: 'remobie_check_list.front_camera=non_functional',
      type: IConvertToType.numString,
      isRequired: true,
      subHeader: 'กล้องหน้า\nใช้งานไม่ได้',
      options: {
        decimal: 0,
      },
    },
    REAR_CAMERA_FUNCTIONAL: {
      keyName: 'remobie_check_list.rear_camera=functional',
      type: IConvertToType.numString,
      isRequired: true,
      subHeader: 'กล้องหลัง\nใช้งานได้',
      options: {
        decimal: 0,
      },
    },
    REAR_CAMERA_NON_FUNCTIONAL: {
      keyName: 'remobie_check_list.rear_camera=non_functional',
      type: IConvertToType.numString,
      isRequired: true,
      subHeader: 'กล้องหลัง\nใช้งานไม่ได้',
      options: {
        decimal: 0,
      },
    },
    MICROPHONE_FUNCTIONAL: {
      keyName: 'remobie_check_list.microphone=functional',
      type: IConvertToType.numString,
      isRequired: true,
      subHeader: 'ไมโครโฟน\nใช้งานได้',
      options: {
        decimal: 0,
      },
    },
    MICROPHONE_NON_FUNCTIONAL: {
      keyName: 'remobie_check_list.microphone=non_functional',
      type: IConvertToType.numString,
      isRequired: true,
      subHeader: 'ไมโครโฟน\nใช้งานไม่ได้',
      options: {
        decimal: 0,
      },
    },
    BIOMETRICS_FUNCTIONAL: {
      keyName: 'remobie_check_list.biometric=functional',
      type: IConvertToType.numString,
      isRequired: true,
      subHeader: 'Face ID/Touch ID\nใช้งานได้',
      options: {
        decimal: 0,
      },
    },
    BIOMETRICS_NON_FUNCTIONAL: {
      keyName: 'remobie_check_list.biometric=non_functional',
      type: IConvertToType.numString,
      isRequired: true,
      subHeader: 'Face ID/Touch ID\nใช้งานไม่ได้',
      options: {
        decimal: 0,
      },
    },
    POWER_CHARGING_FUNCTIONAL: {
      keyName: 'remobie_check_list.power_charging=functional',
      type: IConvertToType.numString,
      isRequired: true,
      subHeader: 'การชาร์จไฟ\nใช้งานได้',
      options: {
        decimal: 0,
      },
    },
    POWER_CHARGING_NON_FUNCTIONAL: {
      keyName: 'remobie_check_list.power_charging=non_functional',
      type: IConvertToType.numString,
      isRequired: true,
      subHeader: 'การชาร์จไฟ\nใช้งานไม่ได้',
      options: {
        decimal: 0,
      },
    },
    PROXIMITY_SENSOR_FUNCTIONAL: {
      keyName: 'remobie_check_list.proximity_sensor=functional',
      type: IConvertToType.numString,
      isRequired: true,
      subHeader: 'เซนเซอร์ระยะห่าง\nใช้งานได้',
      options: {
        decimal: 0,
      },
    },
    PROXIMITY_SENSOR_NON_FUNCTIONAL: {
      keyName: 'remobie_check_list.proximity_sensor=non_functional',
      type: IConvertToType.numString,
      isRequired: true,
      subHeader: 'เซนเซอร์ระยะห่าง\nใช้งานไม่ได้',
      options: {
        decimal: 0,
      },
    },
    VIBRATION_FUNCTIONAL: {
      keyName: 'remobie_check_list.vibration=functional',
      type: IConvertToType.numString,
      isRequired: true,
      subHeader: 'ระบบสั่น\nใช้งานได้',
      options: {
        decimal: 0,
      },
    },
    VIBRATION_NON_FUNCTIONAL: {
      keyName: 'remobie_check_list.vibration=non_functional',
      type: IConvertToType.numString,
      isRequired: true,
      subHeader: 'ระบบสั่น\nใช้งานไม่ได้',
      options: {
        decimal: 0,
      },
    },
    SPEAKER_FUNCTIONAL: {
      keyName: 'remobie_check_list.speaker=functional',
      type: IConvertToType.numString,
      isRequired: true,
      subHeader: 'ลำโพง\nใช้งานได้',
      options: {
        decimal: 0,
      },
    },
    SPEAKER_NON_FUNCTIONAL: {
      keyName: 'remobie_check_list.speaker=non_functional',
      type: IConvertToType.numString,
      isRequired: true,
      subHeader: 'ลำโพง\nใช้งานไม่ได้',
      options: {
        decimal: 0,
      },
    },
    EAR_SPEAKER_FUNCTIONAL: {
      keyName: 'remobie_check_list.earpiece=functional',
      type: IConvertToType.numString,
      isRequired: true,
      subHeader: 'ลำโพงแนบหู\nใช้งานได้',
      options: {
        decimal: 0,
      },
    },
    EAR_SPEAKER_NON_FUNCTIONAL: {
      keyName: 'remobie_check_list.earpiece=non_functional',
      type: IConvertToType.numString,
      isRequired: true,
      subHeader: 'ลำโพงแนบหู\nใช้งานไม่ได้',
      options: {
        decimal: 0,
      },
    },
    VOLUME_BUTTON_FUNCTIONAL: {
      keyName: 'remobie_check_list.volume_button=functional',
      type: IConvertToType.numString,
      isRequired: true,
      subHeader: 'ปุ่มเพิ่ม-ลดเสียง\nใช้งานได้',
      options: {
        decimal: 0,
      },
    },
    VOLUME_BUTTON_NON_FUNCTIONAL: {
      keyName: 'remobie_check_list.volume_button=non_functional',
      type: IConvertToType.numString,
      isRequired: true,
      subHeader: 'ปุ่มเพิ่ม-ลดเสียง\nใช้งานไม่ได้',
      options: {
        decimal: 0,
      },
    },
    TOUCH_SCREEN_FUNCTIONAL: {
      keyName: 'remobie_check_list.touch_screen=functional',
      type: IConvertToType.numString,
      isRequired: true,
      subHeader: 'การสัมผัสหน้าจอ\nใช้งานได้',
      options: {
        decimal: 0,
      },
    },
    TOUCH_SCREEN_NON_FUNCTIONAL: {
      keyName: 'remobie_check_list.touch_screen=non_functional',
      type: IConvertToType.numString,
      isRequired: true,
      subHeader: 'การสัมผัสหน้าจอ\nใช้งานไม่ได้',
      options: {
        decimal: 0,
      },
    },
  },
};
