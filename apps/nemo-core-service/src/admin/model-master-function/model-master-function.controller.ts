import {
  Controller,
  Get,
  Put,
  Query,
  Req,
  UploadedFile,
  UseInterceptors,
  Param,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { CrudController } from '../../crud';
import { ModelMasterFunctionEntity } from '../../entities';
import { Repository } from 'typeorm';
import { Request } from 'express';
import { ModelMasterFunctionService } from './model-master-function.service';
import {
  mappingUrlWithCompanyId,
  Role,
  Permission,
  PermissionAction,
} from '../../config';
import { GetModelMaserFunctionDto } from './dto';
import { Roles, WithUser, Permissions } from '../../decorators';
import { FileInterceptor } from '@nestjs/platform-express';
import { WithUserContext } from 'src/interfaces';

@Controller('v1/admin/model-master-function')
export class ModelMasterFunctionController extends CrudController<ModelMasterFunctionEntity> {
  constructor(
    @InjectRepository(ModelMasterFunctionEntity)
    repo: Repository<ModelMasterFunctionEntity>,
    private readonly modelMasterFunctionService: ModelMasterFunctionService,
  ) {
    super(ModelMasterFunctionEntity, 'model_master_function', repo, {
      resourceKeyPath: 'modelKey',
      order: { updatedAt: 'asc' },
    });
  }

  @Get()
  @Permissions([Permission.CMS_MODEL_MANAGE + PermissionAction.VIEW])
  async getModelMasterFunction(
    @Req() context: Request,
    @Query() query: GetModelMaserFunctionDto,
  ) {
    const xCompany = context.headers['x-company'] as string;
    const company = mappingUrlWithCompanyId(xCompany);
    return await this.modelMasterFunctionService.customQueryModelMasterFunction(
      company,
      query,
    );
  }

  @Put()
  @Permissions([Permission.CMS_MODEL_MANAGE + PermissionAction.UPLOAD])
  @UseInterceptors(FileInterceptor('file'))
  async uploadFile(
    @Req() context: Request,
    @UploadedFile() file: Express.Multer.File,
    @WithUser() user: WithUserContext,
  ) {
    const xCompany = context.headers['x-company'] as string;
    const company = mappingUrlWithCompanyId(xCompany);

    return this.modelMasterFunctionService.putModelMasterFunctionV2(
      company ?? undefined,
      file,
      user.userKey,
    );
  }

  @Get('/export')
  @Permissions([Permission.CMS_MODEL_MANAGE + PermissionAction.DOWNLOAD])
  async exportModelMasterFunction(@Req() context: Request) {
    const xCompany = context.headers['x-company'] as string;
    const company = mappingUrlWithCompanyId(xCompany);

    const buf =
      await this.modelMasterFunctionService.exportModelMasterFunctionV2(
        company,
      );
    return {
      base64String: buf.toString('base64'),
    };
  }

  @Get('/:id/function')
  @Permissions([Permission.CMS_MODEL_MANAGE + PermissionAction.VIEW])
  async getModelMasterFunctionByModelKey(
    @Req() context: Request,
    @Param('id') id: string,
  ) {
    const xCompany = context.headers['x-company'] as string;
    const company = mappingUrlWithCompanyId(xCompany);
    return await this.modelMasterFunctionService.getModelMasterFunctionByModelKey(
      company,
      id,
    );
  }

  @Get('/:id/question')
  @Permissions([Permission.CMS_MODEL_MANAGE + PermissionAction.VIEW])
  async getModelMasterQuestionByModelKey(
    @Req() context: Request,
    @Param('id') id: string,
  ) {
    const xCompany = context.headers['x-company'] as string;
    const company = mappingUrlWithCompanyId(xCompany);
    return await this.modelMasterFunctionService.getModelMasterQuestionByModelKey(
      company,
      id,
    );
  }
}
