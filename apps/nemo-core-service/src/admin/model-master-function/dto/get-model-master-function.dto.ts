import { SchemaObject } from '@nestjs/swagger/dist/interfaces/open-api-spec.interface';
import { GetModelMasterFunctionRequest } from 'contracts';

import { createZodDto, zodToOpenAPI } from 'nestjs-zod';
import { z } from 'nestjs-zod/z';
import { toZod } from 'tozod';

const GetModelMaserFunctionSchema: toZod<GetModelMasterFunctionRequest> =
  z.object({
    pagination: z.string().optional(),
    orderBy: z.string().optional(),
    page: z.string().optional(),
    pageSize: z.string().optional(),
  });

export const GetBranchesOpenApi = zodToOpenAPI(
  GetModelMaserFunctionSchema,
) as SchemaObject;

export class GetModelMaserFunctionDto extends createZodDto(
  GetModelMaserFunctionSchema,
) {}
