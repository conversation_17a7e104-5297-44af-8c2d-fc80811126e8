import { MiddlewareConsumer, Module, NestModule } from '@nestjs/common';
import {
  ModelChecklistEntity,
  ModelMasterFunctionEntity,
  ModelMasterEntity,
  ModelPriceActivitiesEntity,
} from '../../entities';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ModelMasterFunctionController } from './model-master-function.controller';

import { WithUserMiddleware } from '../../middlewares';
import {
  ModelMasterFunctionService,
  excelManagerOption,
} from './model-master-function.service';
import { ExcelManagerModule } from '../../excel/excel-manager.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      ModelMasterFunctionEntity,
      ModelChecklistEntity,
      ModelMasterEntity,
      ModelPriceActivitiesEntity,
    ]),
    ExcelManagerModule.register(excelManagerOption),
  ],
  controllers: [ModelMasterFunctionController],
  providers: [ModelMasterFunctionService],
})
export class ModelMasterFunctionModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(WithUserMiddleware).forRoutes(ModelMasterFunctionController);
  }
}
