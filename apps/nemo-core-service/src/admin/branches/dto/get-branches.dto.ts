import { SchemaObject } from '@nestjs/swagger/dist/interfaces/open-api-spec.interface';
import { GetBranchesRequest } from 'contracts';

import { createZodDto, zodToOpenAPI } from 'nestjs-zod';
import { z } from 'nestjs-zod/z';
import { toZod } from 'tozod';

const GetBranchesSchema: toZod<GetBranchesRequest> = z.object({
  companyId: z.string().optional(),
  pagination: z.string().optional(),
  orderBy: z.string().optional(),
  page: z.string().optional(),
  pageSize: z.string().optional(),
});

export const GetBranchesOpenApi = zodToOpenAPI(
  GetBranchesSchema,
) as SchemaObject;

export class GetBranchesDto extends createZodDto(GetBranchesSchema) {}
