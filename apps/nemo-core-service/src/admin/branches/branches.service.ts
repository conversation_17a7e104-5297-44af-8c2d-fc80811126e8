import { Injectable } from '@nestjs/common';
import { Request } from 'express';
import { InjectRepository } from '@nestjs/typeorm';
import { EntityManager, Repository, SelectQueryBuilder } from 'typeorm';
import { BranchEntity, CompanyEntity, branchType } from '../../entities';
import { GetBranchesResponse } from 'contracts';
import { WithUserContext } from '../../interfaces';
import { uniqBy } from 'lodash';
import {
  ExcelManagerService,
  IConvertToType,
  Options,
} from '../../excel/excel-manager.service';
import { BaseExceptionService } from '../../exceptions';

@Injectable()
export class BranchesService {
  constructor(
    @InjectRepository(CompanyEntity)
    private readonly companyRepo: Repository<CompanyEntity>,
    @InjectRepository(BranchEntity)
    private readonly branchRepo: Repository<BranchEntity>,
    private readonly excelManagerService: ExcelManagerService,
    private readonly baseExceptionService: BaseExceptionService,
  ) {}
  buildSearchQuery(
    context: Request & { withUserContext?: WithUserContext },
    listQuery: SelectQueryBuilder<BranchEntity>,
  ): SelectQueryBuilder<BranchEntity> {
    const branchId = context.query.branchId as string;
    const title = context.query.title as string;
    const branchType = context.query.branchType as string[];
    const label = context.query.label as string;

    const conditions = [
      branchId && `r.branchId ILIKE '%${branchId}%'`,
      title && `r.title ILIKE '%${title}%'`,
      label && `(r.branchId ILIKE '%${label}%' OR r.title ILIKE '%${label}%')`,
      branchType && this.checkBranchType('r.branchType', branchType),
    ].filter(Boolean);
    if (conditions.length) listQuery.andWhere(conditions.join(' AND '));

    return listQuery;
  }

  checkBranchType(field: string, branchType: string[]): string {
    if (branchType.length === 1 && branchType[0] === '') {
      return '';
    }
    return `${field} IN ('${branchType.map((s) => s).join("','")}')`;
  }

  afterLoad(_context: Request, data: BranchEntity[]): GetBranchesResponse[] {
    return data.map((branch) => ({
      ...branch,
      label: `${branch.branchId} ${branch.title}`,
    }));
  }

  async exportBranches(data: BranchEntity[]) {
    const sheetName = 'Branches';
    this.excelManagerService.options = excelBranchesOption;
    return this.excelManagerService.generateExcelFile(data, sheetName);
  }

  async putBranches(company: string | undefined, file: Express.Multer.File) {
    this.excelManagerService.options = excelBranchesOption;
    const fileData = await this.excelManagerService.readExcelFile(
      file.buffer,
      file.mimetype,
    );
    const saveList: Partial<BranchEntity>[] = [];
    const companyData = await this.companyRepo.findOne({
      where: { companyId: company },
    });
    const typeDisplay = Object.values(branchType);

    const validateCostCenterRegex = /^[A-Z0-9]{10}$/;
    if (companyData) {
      for (const branch of uniqBy(fileData, 'branchId')) {
        const saveData = new BranchEntity();
        const dataBranchType = branch.branchType.toUpperCase();
        const costCenter = branch.costCenter?.toUpperCase();
        if (branchType[dataBranchType] === 'Partner' && !branch.partnerName) {
          throw this.baseExceptionService.exception(
            'INVALID_DATA_FORMAT',
            `Partner name is required for branch type ${branchType.PARTNER}.`,
          );
        }

        if (costCenter && !validateCostCenterRegex.test(costCenter)) {
          throw this.baseExceptionService.exception(
            'INVALID_DATA_FORMAT',
            `Invalid cost center format.`,
          );
        }

        if (
          !Object.values(branchType)
            .map((val) => val.toUpperCase())
            .includes(dataBranchType)
        ) {
          throw this.baseExceptionService.exception(
            'INVALID_DATA_FORMAT',
            `Invalid branch type. Branch type must be ${typeDisplay
              .slice(0, -1)
              .join(', ')} or ${typeDisplay[typeDisplay.length - 1]}.`,
          );
        }

        if (branch.zipCode && branch.zipCode.length !== 5) {
          throw this.baseExceptionService.exception(
            'INVALID_DATA_FORMAT',
            `Invalid zip code. Zip code must have 5 digits.`,
          );
        }

        saveData.companyId = companyData.companyId;
        saveData.branchId = branch.branchId;
        saveData.branchType = branchType[dataBranchType];
        saveData.title = branch.title;
        saveData.titleEn = branch.titleEn;
        saveData.addressTh = branch.addressTh;
        saveData.addressEn = branch.addressEn;
        saveData.provinceTh = branch.provinceTh;
        saveData.provinceEn = branch.provinceEn;
        saveData.districtTh = branch.districtTh;
        saveData.districtEn = branch.districtEn;
        saveData.subDistrictTh = branch.subDistrictTh;
        saveData.subDistrictEn = branch.subDistrictEn;
        saveData.zipCode = branch.zipCode;
        saveData.latitude = branch.latitude;
        saveData.longitude = branch.longitude;
        saveData.costCenter = costCenter;
        if (branchType[dataBranchType] === 'Partner') {
          saveData.partnerName = branch.partnerName;
        }

        saveList.push(saveData);
      }

      await this.branchRepo.manager.transaction(
        async (man: EntityManager): Promise<void> => {
          for (let i = 0; i < saveList.length; i += 1000) {
            await man
              .createQueryBuilder()
              .insert()
              .into(BranchEntity)
              .values(saveList.slice(i, i + 1000))
              .orUpdate(
                [
                  'branch_type',
                  'title',
                  'updated_at',
                  'title_en',
                  'address_th',
                  'address_en',
                  'province_th',
                  'province_en',
                  'district_th',
                  'district_en',
                  'sub_district_th',
                  'sub_district_en',
                  'zip_code',
                  'latitude',
                  'longitude',
                  'cost_center',
                  'partner_name',
                ],
                ['company_id', 'branch_id'],
              )
              .execute();
          }
        },
      );
    }
  }
}

export const excelBranchesOption: Options = {
  headers: {
    BRANCH_ID: {
      keyName: 'branchId',
      type: IConvertToType.string,
      isRequired: true,
    },
    COST_CENTER: {
      keyName: 'costCenter',
      type: IConvertToType.string,
      isRequired: false,
    },
    BRANCH_TYPE: {
      keyName: 'branchType',
      type: IConvertToType.string,
      isRequired: true,
    },
    PARTNER_NAME: {
      keyName: 'partnerName',
      type: IConvertToType.stringKey,
      isRequired: false,
      options: {
        maxLength: 30,
      },
    },
    TITLE_TH: {
      keyName: 'title',
      type: IConvertToType.string,
      isRequired: true,
    },
    TITLE_EN: {
      keyName: 'titleEn',
      type: IConvertToType.string,
      isRequired: true,
    },
    ADDRESS_TH: {
      keyName: 'addressTh',
      type: IConvertToType.string,
      isRequired: false,
    },
    ADDRESS_EN: {
      keyName: 'addressEn',
      type: IConvertToType.string,
      isRequired: false,
    },
    PROVINCE_TH: {
      keyName: 'provinceTh',
      type: IConvertToType.string,
      isRequired: false,
    },
    PROVINCE_EN: {
      keyName: 'provinceEn',
      type: IConvertToType.string,
      isRequired: false,
    },
    DISTRICT_TH: {
      keyName: 'districtTh',
      type: IConvertToType.string,
      isRequired: false,
    },
    DISTRICT_EN: {
      keyName: 'districtEn',
      type: IConvertToType.string,
      isRequired: false,
    },
    SUB_DISTRICT_TH: {
      keyName: 'subDistrictTh',
      type: IConvertToType.string,
      isRequired: false,
    },
    SUB_DISTRICT_EN: {
      keyName: 'subDistrictEn',
      type: IConvertToType.string,
      isRequired: false,
    },
    ZIP_CODE: {
      keyName: 'zipCode',
      type: IConvertToType.numString,
      isRequired: false,
    },
    LATITUDE: {
      keyName: 'latitude',
      type: IConvertToType.number,
      isRequired: false,
      options: {
        decimal: 7,
        max: { value: 90.0000001 },
        min: { value: -90.0000001 },
      },
    },
    LONGITUDE: {
      keyName: 'longitude',
      type: IConvertToType.number,
      isRequired: false,
      options: {
        decimal: 7,
        max: { value: 180.0000001 },
        min: { value: -180.0000001 },
      },
    },
  },
};
