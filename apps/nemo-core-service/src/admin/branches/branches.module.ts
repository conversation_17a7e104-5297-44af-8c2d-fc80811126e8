import { MiddlewareConsumer, Module, NestModule } from '@nestjs/common';
import { BranchEntity, CompanyEntity } from '../../entities';
import { TypeOrmModule } from '@nestjs/typeorm';
import { BranchesController } from './branches.controller';

import { WithUserMiddleware } from '../../middlewares';
import { BranchesService, excelBranchesOption } from './branches.service';
import { ExcelManagerModule } from '../../excel/excel-manager.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([BranchEntity, CompanyEntity]),
    ExcelManagerModule.register(excelBranchesOption),
  ],
  controllers: [BranchesController],
  providers: [BranchesService],
})
export class BranchesModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(WithUserMiddleware).forRoutes(BranchesController);
  }
}
