import {
  Controller,
  Get,
  Query,
  Put,
  Req,
  UploadedFile,
  UseInterceptors,
  Param,
  Patch,
  Body,
} from '@nestjs/common';
import { Request } from 'express';

import {
  mappingUrlWithCompanyId,
  Permission,
  PermissionAction,
  getPermssionAll,
  FilterRole,
} from '../../config';
import { AdminUsersService } from './users.service';
import { WithUser, Permissions } from '../../decorators';
import { WithUserContext } from '../../interfaces';
import { FileInterceptor } from '@nestjs/platform-express';
import { GetUsersDto, PatchUsersDto, PutUserUpsertDto } from './dto';
@Controller('/v1/admin/users')
export class AdminUsersController {
  constructor(readonly adminUsersService: AdminUsersService) {}

  @Get('/me')
  @Permissions(getPermssionAll(PermissionAction.VIEW, FilterRole.CMS))
  async getMe(@WithUser() user: WithUserContext) {
    return this.adminUsersService.getMe(user);
  }

  @Get()
  @Permissions([Permission.CMS_STAFF_MANAGE + PermissionAction.VIEW])
  async getUsers(@Req() context: Request, @Query() query: GetUsersDto) {
    const xCompany = context.headers['x-company'] as string;
    const company = mappingUrlWithCompanyId(xCompany);

    // return await this.adminUsersService.customQueryUser(
    //   company as string,
    //   query,
    // );
    return await this.adminUsersService.getUserList(company as string, query);
  }

  @Put()
  @Permissions([Permission.CMS_STAFF_MANAGE + PermissionAction.UPLOAD])
  @UseInterceptors(FileInterceptor('file'))
  async saveUser(
    @Req() req: Request,
    @WithUser() user: WithUserContext,
    @UploadedFile() file: Express.Multer.File,
  ) {
    const xCompany = req.headers['x-company'] as string;
    const company = mappingUrlWithCompanyId(xCompany);

    return this.adminUsersService.saveEmployee(
      company ? company : undefined,
      file,
      user,
    );
  }

  @Get('/:id')
  @Permissions([Permission.CMS_STAFF_MANAGE + PermissionAction.VIEW])
  async getUserById(@Param('id') id: string) {
    return await this.adminUsersService.getUserById(id);
  }

  @Patch('/:id')
  @Permissions([Permission.CMS_STAFF_MANAGE + PermissionAction.UPDATE])
  async patchUserById(
    @Param('id') id: string,
    @Body() body: PatchUsersDto,
    @WithUser() user: WithUserContext,
  ) {
    return await this.adminUsersService.updateUserById(id, body, user);
  }

  @Put('/upsert')
  @Permissions([Permission.CMS_STAFF_MANAGE + PermissionAction.UPDATE])
  async upsertUser(
    @Body() body: PutUserUpsertDto,
    @WithUser() user: WithUserContext,
  ) {
    return await this.adminUsersService.upsertUser(body, user);
  }
}
