import { SchemaObject } from '@nestjs/swagger/dist/interfaces/open-api-spec.interface';
import { GetUsersRequest } from 'contracts';

import { createZodDto, zodToOpenAPI } from 'nestjs-zod';
import { z } from 'nestjs-zod/z';
import { toZod } from 'tozod';

const GetUsersSchema: toZod<GetUsersRequest> = z.object({
  searchEmployee: z.string().optional(),
  role: z.string().optional(),
  branch: z.string().optional(),
  page: z.string().optional(),
  pageSize: z.string().optional(),
  pagination: z.string().optional(),
  orderBy: z.string().optional(),
});

export const GetUsersOpenApi = zodToOpenAPI(GetUsersSchema) as SchemaObject;

export class GetUsersDto extends createZodDto(GetUsersSchema) {}
