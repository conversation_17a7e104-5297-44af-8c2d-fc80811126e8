import { z } from 'nestjs-zod/z';
import { createZodDto, zodToOpenAPI } from 'nestjs-zod';
import { SchemaObject } from '@nestjs/swagger/dist/interfaces/open-api-spec.interface';

const PutUserUpsertSchema = z
  .object({
    employeeId: z.string(),
    firstNameTh: z.string().min(1).max(100),
    lastNameTh: z.string().min(1).max(100),
    firstNameEn: z.string().min(1).max(100),
    lastNameEn: z.string().min(1).max(100),
    email: z.string().email(),
    frontShopRoles: z
      .array(
        z.object({
          roleId: z.string(),
          branchId: z.string(),
        }),
      )
      .optional(),
    cmsAdminRoles: z.array(z.string()).optional(),
  })
  .refine(
    (data) =>
      (data.frontShopRoles !== undefined && data.frontShopRoles.length > 0) ||
      (data.cmsAdminRoles !== undefined && data.cmsAdminRoles.length > 0),
    () => ({
      path: ['frontShopRoles or cmsAdminRoles'],
      message: `must have at least one role in frontShopRoles or cmsAdminRoles`,
    }),
  );

export const PutUserUpsertOpenApi = zodToOpenAPI(
  PutUserUpsertSchema,
) as SchemaObject;
export class PutUserUpsertDto extends createZodDto(PutUserUpsertSchema) {}
