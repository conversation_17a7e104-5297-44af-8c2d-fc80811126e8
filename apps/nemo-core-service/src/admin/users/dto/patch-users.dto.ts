import { SchemaObject } from '@nestjs/swagger/dist/interfaces/open-api-spec.interface';
import { createZodDto, zodToOpenAPI } from 'nestjs-zod';
import { z } from 'nestjs-zod/z';

const PatchUsersSchema = z.object({
  name: z.string().min(1).max(200),
  nameEn: z.string().min(1).max(200),
  isActive: z.boolean(),
  roles: z.array(
    z.object({
      roleId: z.string(),
      branchId: z.string().optional(),
    }),
  ),
  email: z.string().email(),
});

export const PatchUsersOpenApi = zodToOpenAPI(PatchUsersSchema) as SchemaObject;

export class PatchUsersDto extends createZodDto(PatchUsersSchema) {}
