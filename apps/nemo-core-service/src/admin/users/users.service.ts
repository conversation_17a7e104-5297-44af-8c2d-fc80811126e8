import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import {
  BranchEntity,
  UserEntity,
  CompanyEntity,
  CompanyRoleEntity,
  SiteType,
  RoleEntity,
  GeneralActivitiesEntity,
  UserRoleBranchEntity,
  PermissionGroupEntity,
} from '../../entities';
import { Repository, EntityManager, In, ILike } from 'typeorm';
import { WithUserContext } from '../../interfaces';
import { BaseExceptionService } from '../../exceptions';
import {
  GetMeResponse,
  UserItems,
  RoleConfig,
  PermissionGroupConfig,
} from 'contracts';
import { DEFAULT_PAGE_SIZE, Pagination } from '../../crud';
import {
  ExcelManagerService,
  Options,
  IConvertToType,
} from '../../excel/excel-manager.service';
import { GetUsersDto, PatchUsersDto, PutUserUpsertDto } from './dto';
import { uniqBy } from 'lodash';
import { CacheManagerService } from '../../cache-manager/cache-manager.service';
import { validateEmail } from '../../utils/general';
export const USER_SUBQUERY_TABLE_NAME = 'userSub';

@Injectable()
export class AdminUsersService {
  constructor(
    @InjectRepository(UserEntity)
    private readonly userRepo: Repository<UserEntity>,
    @InjectRepository(BranchEntity)
    private readonly branchRepo: Repository<BranchEntity>,
    @InjectRepository(CompanyEntity)
    private readonly companyRepo: Repository<CompanyEntity>,
    @InjectRepository(CompanyRoleEntity)
    private readonly companyRoleRepo: Repository<CompanyRoleEntity>,
    @InjectRepository(RoleEntity)
    private readonly roleRepo: Repository<RoleEntity>,
    @InjectRepository(GeneralActivitiesEntity)
    private readonly generalActivitiesRepository: Repository<GeneralActivitiesEntity>,
    @InjectRepository(UserRoleBranchEntity)
    private readonly userRoleBranch: Repository<UserRoleBranchEntity>,

    private readonly baseException: BaseExceptionService,
    private readonly excelManagerService: ExcelManagerService,
    private readonly cacheManager: CacheManagerService,
  ) {}

  async getMe(user: WithUserContext): Promise<GetMeResponse> {
    const companyId = user.company;
    // Find user
    const userData = await this.userRepo.findOne({
      where: {
        userKey: user.userKey,
        companyId,
      },
    });

    // Prevent user not exists
    if (!userData) {
      throw this.baseException.exception('NOT_FOUND_DATA');
    }

    // parse to dict
    // const userRoles = userData.roles?.reduce((prev, curr) => ({
    //   role: [...prev.role, ...curr.role],
    // }));

    // const branchRoles: BranchRole[] = [
    //   {
    //     branchId: '',
    //     branchName: '',
    //     role: userRoles ? userRoles.role : [],
    //   },
    // ];

    const branch = await this.userRoleBranch.find({
      relations: ['role'],
      where: {
        companyId,
        userKey: userData.userKey,
        role: {
          companyId,
          type: SiteType.CMS,
        },
      },
    });

    const branchRoles = branch.map((item) => item.roleId);

    const role = (await this.userRoleBranch.manager.find(RoleEntity, {
      select: {
        roleId: true,
        roleName: true,
        rolePermissions: {
          permissionId: true,
          view: true,
          create: true,
          update: true,
          delete: true,
          download: true,
          upload: true,
        },
      },
      relations: ['rolePermissions'],
      where: {
        companyId,
        roleId: In(branchRoles),
      },
    })) as RoleConfig[];

    const permissionGroup = (await this.userRoleBranch.manager.find(
      PermissionGroupEntity,
      {
        select: {
          permissionGroupId: true,
          label: true,
          isInMenu: true,
          permissions: {
            permissionId: true,
            label: true,
            iconName: true,
            iconNameActive: true,
          },
        },
        relations: ['permissions'],
        where: {
          companyId,
          type: SiteType.CMS,
        },
        order: {
          sortIndex: 'ASC',
          permissions: {
            sortIndex: 'ASC',
          },
        },
      },
    )) as PermissionGroupConfig[];

    // Return response
    return {
      userKey: userData.userKey,
      name: userData.name ?? '',
      branchRoles: [
        {
          branchId: 'ADMIN',
          branchName: 'ADMIN',
          roles: branchRoles,
        },
      ],
      roleConfig: role,
      permissionGroupConfig: permissionGroup,
      userType: userData.userType, // WW as default and required field -> always has value
    };
  }

  async getUserList(company: string, query: GetUsersDto) {
    const { page, pageSize, pagination, orderBy, ...queryWhere } = query;
    const limit = +(pageSize ?? DEFAULT_PAGE_SIZE);
    const skip = limit * +(page ?? '1') - limit;
    const disablePagination = /false/i.test(pagination ?? '');
    const coditionObject = (key: string) => {
      return {
        companyId: company,
        ...(queryWhere.searchEmployee && {
          [key]: ILike(`%${queryWhere.searchEmployee}%`),
        }),
        userRoleBranch: {
          ...(queryWhere.role && {
            role: {
              roleId: queryWhere.role,
            },
          }),
          ...(queryWhere.branch && {
            branch: {
              branchId: queryWhere.branch,
            },
          }),
        },
      };
    };

    const conditionQuery = [coditionObject('name'), coditionObject('userKey')];
    const userList = await this.userRepo.find({
      select: ['userKey', 'name', 'nameEng', 'updatedAt', 'companyId', 'email'],
      join: {
        alias: 'user',
        leftJoinAndSelect: {
          userRoleBranch: 'user.userRoleBranch',
          role: 'userRoleBranch.role',
          branch: 'userRoleBranch.branch',
        },
      },
      where: conditionQuery,
      order: {
        ...(orderBy && {
          [orderBy.split(' ')[0]]: orderBy.split(' ')[1] as 'ASC' | 'DESC',
        }),
      },
      take: limit,
      skip: skip,
    });

    const userCount = await this.userRepo.count({
      where: conditionQuery,
    });

    const paginationResult: Pagination = {
      page: disablePagination ? 1 : +(page ?? '1'),
      pageSize: disablePagination ? +userCount : limit,
      totalRecords: +(userCount ?? '1'),
    };
    return {
      items: await this.afterLoad(userList),
      paginationResult,
    };
  }

  async getAllUsers(companyId: string): Promise<UserItems[]> {
    const results = await this.userRepo.find({ where: { companyId } });

    const users: UserItems[] = [];

    results.forEach((user) => {
      const newData = {} as UserItems;
      newData.userKey = user.userKey;
      newData.name = user.name ?? '';

      users.push(newData);
    });

    return users;
  }

  async getUserById(userKey: string) {
    const queryUserData = await this.userRepo.findOne({
      where: { userKey },
    });

    if (!queryUserData) {
      throw this.baseException.exception('NOT_FOUND_DATA');
    }

    const spliteName = this.splitName(queryUserData.name);
    const splitNameEn = this.splitName(queryUserData.nameEng);

    const personalData = {
      userKey: queryUserData.userKey,
      name: spliteName[0],
      lastname: spliteName[1],
      nameEn: splitNameEn[0],
      lastnameEn: splitNameEn[1],
      email: queryUserData.email,
    };

    const queryData = await this.userRoleBranch.find({
      where: { userKey },
      relations: ['role', 'branch', 'user'],
    });

    const roles = {
      frontshop: [],
      cms: [],
    };

    queryData.forEach((item) => {
      const roleType = item.role.type.toLowerCase();
      const roleData = {
        roleId: item.role.roleId,
      };

      if (roleType === 'frontshop') {
        roleData['branch'] = {
          branchId: item.branch.branchId,
          branchName: item.branch.title,
        };
      }

      roles[roleType].push(roleData);
    });

    const userData = {
      ...personalData,
      roles,
    };

    return userData;
  }

  private getUniqueRoleData(roleData: any[]): any[] {
    const tempSetData = new Set<string>();
    return roleData.filter((item) => {
      const key = item.branchId
        ? `${item.roleId}_${item.branchId}`
        : item.roleId;
      if (!tempSetData.has(key)) {
        tempSetData.add(key);
        return true;
      }
      return false;
    });
  }

  private baseUniqRoleDataValidation(
    role: any,
    rolesIds: string[],
    frontshopRoleIds: string[],
    branchesIds: string[],
  ) {
    if (!rolesIds.includes(role.roleId)) {
      throw this.baseException.exception('ROLE_NOT_EXISTS', 'Role not found');
    }
    if (frontshopRoleIds.includes(role.roleId) && !role.branchId) {
      throw this.baseException.exception(
        'BODY_PAYLOAD_INVALID',
        'Branch ID is required, when role type is Frontshop',
      );
    }
    if (role.branchId && !branchesIds.includes(role.branchId)) {
      throw this.baseException.exception(
        'BRANCH_NOT_EXISTS',
        'Branch not found',
      );
    }
  }

  async updateUserById(
    userKey: string,
    body: PatchUsersDto,
    user: WithUserContext,
  ) {
    const queryUserData = await this.userRepo.findOne({
      where: { userKey },
    });

    if (!queryUserData) {
      throw this.baseException.exception('NOT_FOUND_DATA', 'User not found');
    }

    const allRoles = await this.roleRepo.find({
      where: { companyId: queryUserData.companyId },
    });
    const allBranches = await this.branchRepo.find({
      where: { companyId: queryUserData.companyId },
    });

    const rolesIds = allRoles.map((role) => role.roleId);
    const frontshopRoleIds = allRoles
      .filter((role) => role.type === SiteType.FRONTSHOP)
      .map((role) => role.roleId);
    const branchesIds = allBranches.map((branch) => branch.branchId);

    const submitUser = {
      userKey: user.userKey,
      companyId: user.company,
    } as UserEntity;

    // remove duplicate data
    const roleData = body.roles;
    const uniqRoleData = this.getUniqueRoleData(roleData);

    const saveUserBranchData: Partial<UserRoleBranchEntity>[] = [];
    uniqRoleData.forEach((role) => {
      this.baseUniqRoleDataValidation(
        role,
        rolesIds,
        frontshopRoleIds,
        branchesIds,
      );

      const saveData = new UserRoleBranchEntity();
      saveData.companyId = queryUserData.companyId;
      saveData.userKey = queryUserData.userKey;
      saveData.roleId = role.roleId;
      saveData.branchId = role.branchId ?? 'ADMIN_BRANCH';

      saveUserBranchData.push(saveData);
    });

    const saveUser = new UserEntity();
    saveUser.userKey = queryUserData.userKey;
    saveUser.name = body.name;
    saveUser.nameEng = body.nameEn;
    saveUser.updatedAt = new Date();
    saveUser.companyId = queryUserData.companyId;
    saveUser.isActive = body.isActive;
    saveUser.updatedUser = submitUser;
    saveUser.email = body.email;

    const queryRunner = this.userRepo.manager.connection.createQueryRunner();

    const activity = new GeneralActivitiesEntity();
    activity.type = 'user-web';

    try {
      await queryRunner.connect();
      await queryRunner.startTransaction();
      const manager = queryRunner.manager;

      // delete existing user role branch
      await manager.delete(UserRoleBranchEntity, {
        userKey,
        companyId: queryUserData.companyId,
      });

      //update user information
      await manager.update(
        UserEntity,
        { userKey: saveUser.userKey, companyId: saveUser.companyId },
        saveUser,
      );

      //update user role branch
      await manager.save(UserRoleBranchEntity, saveUserBranchData);
      // Commit transaction
      await queryRunner.commitTransaction();
      await this.baseSaveActivitySuccess(
        activity,
        user,
        userKey,
        saveUser,
        saveUserBranchData,
      );
      return null;
    } catch (err: any) {
      await this.baseSaveActivityFail(activity, user, err);
      await queryRunner.rollbackTransaction();
      throw err;
    } finally {
      await queryRunner.release();
    }
  }

  private async baseSaveActivitySuccess(
    activity: GeneralActivitiesEntity,
    user: WithUserContext,
    userKey: string,
    saveUser: UserEntity,
    saveUserBranchData: Partial<UserRoleBranchEntity>[],
    type?: string,
  ) {
    activity.detail = {
      error: null,
      at: new Date(),
      result: 'success',
      detail: type
        ? { user: saveUser, userRoleBranch: saveUserBranchData, type }
        : { user: saveUser, userRoleBranch: saveUserBranchData },
    };
    activity.createdBy = user.userKey;
    activity.companyId = user.company;
    await this.generalActivitiesRepository.save(activity);
    this.cacheManager.removeData(`user-roles-${user.company}:${userKey}`);
  }

  private async baseSaveActivityFail(
    activity: GeneralActivitiesEntity,
    user: WithUserContext,
    error: any,
  ) {
    activity.detail = {
      error: error.message,
      at: new Date(),
      result: 'error',
    };
    activity.createdBy = user.userKey;
    activity.companyId = user.company;
    await this.generalActivitiesRepository.save(activity);
  }

  splitName(name: string | undefined) {
    if (!name) {
      return ['', ''];
    }

    const index = name.indexOf(' ');
    if (index === -1) {
      return [name, ''];
    }

    return [name.substring(0, index), name.substring(index + 1)];
  }

  async afterLoad(data: UserEntity[]): Promise<UserItems[]> {
    const adjustedData = data.map((user) => {
      let userRoleBranchList = user.userRoleBranch;
      if (userRoleBranchList !== undefined) {
        //find element list that role.type is FRONTSHOP
        const frontShopList = userRoleBranchList?.filter(
          (item) => item.role.type === SiteType.FRONTSHOP,
        );
        //find element list that role.type is CMS
        const cmsList = userRoleBranchList?.filter(
          (item) => item.role.type === SiteType.CMS,
        );
        //merge frontShopList and cmsList
        userRoleBranchList = [...frontShopList, ...cmsList];
      }
      let branchList = userRoleBranchList?.map((item) => item.branch);
      //filter branchList that branchId is not ADMIN_BRANCH
      branchList = branchList?.filter(
        (branch) => branch.branchId !== 'ADMIN_BRANCH',
      );

      const branchIdList = branchList
        ? branchList.map((item) => item.branchId)
        : [];
      const branchNameList = branchList
        ? branchList?.map((item) => item.title)
        : [];
      const roleList = userRoleBranchList
        ? userRoleBranchList?.map((item) => item.role.roleName)
        : [];
      const { ...userData } = user;

      return {
        userKey: userData.userKey,
        name: userData.name ?? '',
        nameEng: userData.nameEng ?? '',
        updatedAt: userData.updatedAt?.toISOString() ?? '',
        branchIds: branchIdList ?? [],
        branchTitles: branchNameList ?? [],
        roles: roleList ?? [],
        email: userData.email ?? '',
      };
    });
    return adjustedData;
  }

  async saveEmployee(
    company: string | undefined,
    file: Express.Multer.File,
    user: WithUserContext,
  ) {
    // first sheet
    this.excelManagerService.options = userOption;
    const userData = await this.excelManagerService.readExcelFile(
      file.buffer,
      file.mimetype,
    );

    const toSaveUserList: Partial<UserEntity>[] = [];
    const toSaveUserRoleBranchList: Partial<UserRoleBranchEntity>[] = [];

    const companyData = await this.companyRepo.findOne({
      where: { companyId: company },
    });

    const branchData = await this.branchRepo.find({
      where: { companyId: company },
    });
    const branchIdList = branchData.map((branch) => branch.branchId);

    const roleData = await this.roleRepo.find({
      where: { companyId: company },
    });

    if (companyData) {
      const userKeys: string[] = [];
      // prepare for save user data
      for (const [, value] of Object.entries(userData)) {
        const saveData = new UserEntity();
        const saveRoleData = new UserRoleBranchEntity();
        const submitUser = {
          userKey: user.userKey,
          companyId: companyData.companyId,
        } as UserEntity;
        saveData.companyId = companyData.companyId;
        saveData.userKey = value.userKey;
        saveData.name = `${value.empName} ${value.empSName}`;
        saveData.nameEng = `${value.empNameEng} ${value.empSNameEng}`;
        saveData.createdUser = submitUser;
        saveData.updatedUser = submitUser;

        if (!validateEmail(value.email)) {
          throw this.baseException.exception(
            'BODY_PAYLOAD_INVALID',
            'Invalid email pattern',
          );
        }
        saveData.email = value.email;

        const empRoleName = value.empType.toUpperCase();
        const role = roleData.find((item) => item.roleName === empRoleName);

        if (!role) {
          throw this.baseException.exception('ROLE_NOT_EXISTS');
        }
        const siteType = value.siteType.toUpperCase();

        if (![SiteType.CMS, SiteType.FRONTSHOP].includes(siteType)) {
          throw this.baseException.exception(
            'BODY_PAYLOAD_INVALID',
            'Invalid site type',
          );
        }
        if (role.type !== siteType) {
          throw this.baseException.exception(
            'BODY_PAYLOAD_INVALID',
            'Role type not match with site type',
          );
        }

        if (siteType === SiteType.FRONTSHOP) {
          if (!value.branchId) {
            throw this.baseException.exception(
              'BODY_PAYLOAD_INVALID',
              'Branch ID is required, when site is Frontshop',
            );
          }

          if (!branchIdList.includes(value.branchId)) {
            throw this.baseException.exception('BRANCH_NOT_EXISTS');
          }

          saveRoleData.branchId = value.branchId;
        } else {
          saveRoleData.branchId = 'ADMIN_BRANCH';
        }

        saveData.roles = [];
        saveData.position = [];

        toSaveUserList.push(saveData);
        saveRoleData.roleId = role.roleId;
        saveRoleData.userKey = value.userKey;
        saveRoleData.companyId = companyData.companyId;
        toSaveUserRoleBranchList.push(saveRoleData);
        userKeys.push(value.userKey);
      }

      const existingUserRoleBranch = await this.userRoleBranch.find({
        where: {
          userKey: In(userKeys),
        },
      });

      const existingUserRoleBranchSet = new Set();

      existingUserRoleBranch.forEach((item) => {
        existingUserRoleBranchSet.add(
          `${item.userKey},${item.roleId},${item.branchId}`,
        );
      });

      const uniqSaveUserList = uniqBy(toSaveUserList, 'userKey');

      const uniqSaveUserRoleBranchList = this.removeDuplicateUserData(
        toSaveUserRoleBranchList,
      ) as Partial<UserRoleBranchEntity>[];

      const newSaveUserRoleBranchList = uniqSaveUserRoleBranchList.filter(
        (item) =>
          !existingUserRoleBranchSet.has(
            `${item.userKey},${item.roleId},${item.branchId}`,
          ),
      );

      const activity = new GeneralActivitiesEntity();
      activity.type = 'user-file';

      // return toSaveUserList
      await this.userRepo.manager.transaction(
        async (man: EntityManager): Promise<void> => {
          for (let i = 0; i < uniqSaveUserList.length; i += 1000) {
            try {
              const _ = await man
                .createQueryBuilder()
                .insert()
                .into(UserEntity)
                .values(uniqSaveUserList.slice(i, i + 1000))
                .orUpdate(
                  ['name', 'name_eng', 'updated_at', 'updated_by', 'email'],
                  ['company_id', 'user_key'],
                )
                .execute();
            } catch (err: any) {
              activity.detail = {
                error: err,
                at: new Date(),
                result: 'error',
              };
              activity.createdBy = user.userKey;
              activity.companyId = user.company;

              await this.generalActivitiesRepository.save(activity);
              throw new Error(err);
            }
          }

          for (let i = 0; i < newSaveUserRoleBranchList.length; i += 1000) {
            try {
              const _ = await man
                .createQueryBuilder()
                .insert()
                .into(UserRoleBranchEntity)
                .values(newSaveUserRoleBranchList.slice(i, i + 1000))
                .execute();
            } catch (err: any) {
              activity.detail = {
                error: err,
                at: new Date(),
                result: 'error',
              };
              activity.createdBy = user.userKey;
              activity.companyId = user.company;

              await this.generalActivitiesRepository.save(activity);
              throw new Error(err);
            }
          }
        },
      );

      activity.detail = {
        error: null,
        at: new Date(),
        result: 'success',
        rows: toSaveUserList.length,
        detail: {
          user: uniqSaveUserList,
          userRoleBranch: newSaveUserRoleBranchList,
        },
      };
      activity.createdBy = user.userKey;
      activity.companyId = user.company;

      await this.generalActivitiesRepository.save(activity);

      // gnerate key to remove from userKey
      const keyToRemove = uniqSaveUserList.map(
        (user) => `user-roles-${user.companyId}:${user.userKey}`,
      );

      // keys array of keys that need to remove
      await this.cacheManager.removeBatchData(keyToRemove);
    }

    return null;
  }

  async upsertUser(body: PutUserUpsertDto, user: WithUserContext) {
    const userKey = body.employeeId;
    const companyId = user.company;
    const submitUser = {
      userKey: user.userKey,
      companyId: user.company,
    } as UserEntity;

    const roleDataFrontShop = body.frontShopRoles;
    const roleDataCmsAdmin = body.cmsAdminRoles?.map((roleId) => ({
      roleId,
      branchId: undefined,
    }));
    const roleData = [
      ...(roleDataFrontShop ?? []),
      ...(roleDataCmsAdmin ?? []),
    ];

    const allRoles = await this.roleRepo.find({
      where: { companyId },
    });
    const allBranches = await this.branchRepo.find({
      where: { companyId },
    });
    const rolesIds = allRoles.map((role) => role.roleId);
    const frontshopRoleIds = allRoles
      .filter((role) => role.type === SiteType.FRONTSHOP)
      .map((role) => role.roleId);
    const branchesIds = allBranches.map((branch) => branch.branchId);

    //re-format roleData to uniqRoleData
    const uniqRoleData = this.getUniqueRoleData(roleData);
    //check uniqRoleData format and add UserRoleBranchEntity array
    const saveUserBranchData: Partial<UserRoleBranchEntity>[] = [];
    uniqRoleData.forEach((role) => {
      this.baseUniqRoleDataValidation(
        role,
        rolesIds,
        frontshopRoleIds,
        branchesIds,
      );
      if (!frontshopRoleIds.includes(role.roleId) && role.branchId) {
        throw this.baseException.exception(
          'BODY_PAYLOAD_INVALID',
          'Branch ID is not required, when role type is CMS',
        );
      }

      const saveData = new UserRoleBranchEntity();
      saveData.companyId = companyId;
      saveData.userKey = body.employeeId;
      saveData.roleId = role.roleId;
      saveData.branchId = role.branchId ?? 'ADMIN_BRANCH';

      saveUserBranchData.push(saveData);
    });

    const queryUserData = await this.userRepo.findOne({
      where: { userKey },
    });

    const queryRunner = this.userRepo.manager.connection.createQueryRunner();

    const saveUser = new UserEntity();
    saveUser.userKey = body.employeeId;
    saveUser.name = body.firstNameTh + ' ' + body.lastNameTh;
    saveUser.nameEng = body.firstNameEn + ' ' + body.lastNameEn;
    saveUser.companyId = companyId;
    saveUser.isActive = true;
    if (!queryUserData) {
      saveUser.createdUser = submitUser;
    }
    saveUser.updatedUser = submitUser;
    saveUser.email = body.email;

    const activity = new GeneralActivitiesEntity();
    activity.type = 'user-api';

    //transaction
    try {
      await queryRunner.connect();
      await queryRunner.startTransaction();
      const manager = queryRunner.manager;
      if (!queryUserData) {
        //insert new user
        await manager.save(UserEntity, saveUser);
      } else {
        //update user information
        await manager.update(
          UserEntity,
          { userKey: saveUser.userKey, companyId: saveUser.companyId },
          saveUser,
        );
        // delete existing user role branch
        await manager.delete(UserRoleBranchEntity, {
          userKey,
          companyId,
        });
      }
      //insert new user role branch
      await manager.save(UserRoleBranchEntity, saveUserBranchData);
      // Commit transaction
      await queryRunner.commitTransaction();
      await this.baseSaveActivitySuccess(
        activity,
        user,
        userKey,
        saveUser,
        saveUserBranchData,
        queryUserData ? 'update' : 'create',
      );
      return null;
    } catch (err: any) {
      await this.baseSaveActivityFail(activity, user, err);
      await queryRunner.rollbackTransaction();
      throw err;
    } finally {
      await queryRunner.release();
    }
  }

  removeDuplicateUserData(userData: any) {
    const uniqData = new Set<string>();
    return userData.filter((data) => {
      const currentData = JSON.stringify(data);
      if (uniqData.has(currentData)) {
        return false;
      }
      uniqData.add(currentData);
      return true;
    });
  }
}

export const userOption: Options = {
  headers: {
    EMP_ID: {
      keyName: 'userKey',
      type: IConvertToType.string,
      isRequired: true,
      subHeader: 'รหัสพนักงาน',
    },
    EMP_NAME: {
      keyName: 'empName',
      type: IConvertToType.string,
      isRequired: true,
      subHeader: 'ชื่อไทย',
    },
    EMP_SNAME: {
      keyName: 'empSName',
      type: IConvertToType.string,
      isRequired: true,
      subHeader: 'นามสกุลไทย',
    },
    NAME_ENG: {
      keyName: 'empNameEng',
      type: IConvertToType.string,
      isRequired: true,
      subHeader: 'ชื่ออังกฤษ',
    },
    SNAME_ENG: {
      keyName: 'empSNameEng',
      type: IConvertToType.string,
      isRequired: true,
      subHeader: 'นามสกุลอังกฤษ',
    },
    SITE: {
      keyName: 'siteType',
      type: IConvertToType.string,
      isRequired: true,
      subHeader: 'FrontShop/CMS',
    },
    EMP_TYPE2: {
      keyName: 'empType2',
      type: IConvertToType.string,
      isRequired: false,
      subHeader: 'EMP_TYPE2',
    },
    EMP_TYPE: {
      keyName: 'empType',
      type: IConvertToType.string,
      isRequired: true,
      subHeader: 'EMP_TYPE',
    },
    SHOP_CODE: {
      keyName: 'branchId',
      type: IConvertToType.string,
      isRequired: false,
      subHeader: 'SHOP_CODE',
    },
    EMAIL: {
      keyName: 'email',
      type: IConvertToType.string,
      isRequired: true,
      subHeader: 'EMAIL',
    },
  },
};
