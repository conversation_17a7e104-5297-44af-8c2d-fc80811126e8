import {
  Body,
  Controller,
  Get,
  Param,
  Patch,
  Query,
  Req,
} from '@nestjs/common';
import { Request } from 'express';
import { InjectRepository } from '@nestjs/typeorm';
import { CrudController } from '../../crud';
import { IssueReportEntity } from '../../entities';
import { EntityManager, Repository, SelectQueryBuilder } from 'typeorm';
import { AdminIssueReportsService } from './issue-reports.service';
import {
  mappingUrlWithCompanyId,
  Permission,
  PermissionAction,
} from 'src/config';
import { WithUser, Permissions } from '../../decorators';
import { WithUserContext } from 'src/interfaces';
import { GetIssueReportsDto } from 'src/shop/issue-reports/dto';
import { PatchEmailChangeDto } from './dto';

@Controller('v1/admin/issue-reports')
export class IssueReportsController extends CrudController<IssueReportEntity> {
  constructor(
    @InjectRepository(IssueReportEntity)
    repo: Repository<IssueReportEntity>,
    private readonly adminIssueReportsService: AdminIssueReportsService,
  ) {
    super(IssueReportEntity, 'issue_report', repo, {
      resourceKeyPath: 'issueReportId',
      order: { updatedAt: 'asc' },
      defaultPopulate: () => {
        return ['createdUser', 'reviewedUser'];
      },
      defaultFilter: async (
        request: Request,
        listQuery: SelectQueryBuilder<IssueReportEntity>,
      ) => {
        const xCompany = request.headers['x-company'] as string;

        const company = mappingUrlWithCompanyId(xCompany);

        return listQuery.andWhere(`r.companyId = :company`, {
          company: company,
        });
      },
      searchFilter: async (
        request: Request,
        _em: EntityManager,
        listQuery: SelectQueryBuilder<IssueReportEntity>,
      ) => this.adminIssueReportsService.buildSearchQuery(request, listQuery),
    });
  }
  @Get()
  @Permissions([Permission.CMS_ISSUE_REPORT + PermissionAction.VIEW])
  async getJobs(@Req() context: Request, @Query() query: GetIssueReportsDto) {
    return super.findAll(context, query);
  }

  @Get('/:id')
  @Permissions([Permission.CMS_ISSUE_REPORT + PermissionAction.VIEW])
  async getIssueReportById(@Req() context: Request, @Param('id') id: string) {
    return super.findOne(context, id);
  }

  @Patch('/:id/email-change/reject')
  @Permissions([Permission.CMS_ISSUE_REPORT + PermissionAction.UPDATE])
  async patchEmailChangeRejected(
    @Param('id') id: string,
    @Body() body: PatchEmailChangeDto,
    @WithUser() user: WithUserContext,
  ) {
    return await this.adminIssueReportsService.patchEmailChangeRejected(
      id,
      body,
      user,
    );
  }

  @Patch('/:id/email-change/approve')
  @Permissions([Permission.CMS_ISSUE_REPORT + PermissionAction.UPDATE])
  async approveEmailChange(
    @Req() context: Request,
    @Param('id') id: string,
    @WithUser() user: WithUserContext,
  ) {
    return await this.adminIssueReportsService.approveEmailChange(id, user);
  }
}
