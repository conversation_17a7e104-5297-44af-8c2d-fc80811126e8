import { SchemaObject } from '@nestjs/swagger/dist/interfaces/open-api-spec.interface';
import { createZodDto, zodToOpenAPI } from 'nestjs-zod';
import { z } from 'nestjs-zod/z';

const PatchEmailChangeSchema = z.object({
  remark: z.string().max(200).optional(),
});

export const PatchEmailChangeOpenApi = zodToOpenAPI(
  PatchEmailChangeSchema,
) as SchemaObject;

export class PatchEmailChangeDto extends createZodDto(PatchEmailChangeSchema) {}
