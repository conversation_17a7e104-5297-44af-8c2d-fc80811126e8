import { MiddlewareConsumer, Module, NestModule } from '@nestjs/common';
import {
  IssueReportEntity,
  JobEntity,
  UserEntity,
  ContractEntity,
  VoucherEntity,
  BranchEntity,
  EmailActivitiesEntity,
} from '../../entities';
import { TypeOrmModule } from '@nestjs/typeorm';

import { IssueReportsController } from './issue-reports.controller';

import { WithUserMiddleware } from '../../middlewares';
import { AdminIssueReportsService } from './issue-reports.service';
import { ContractsService } from '../../shop/contracts/contracts.service';
import { S3Service } from '../../storage/s3.service';
import { SmtpService } from '../../smtp/smtp.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      JobEntity,
      UserEntity,
      IssueReportEntity,
      ContractEntity,
      VoucherEntity,
      BranchEntity,
      EmailActivitiesEntity,
    ]),
  ],
  controllers: [IssueReportsController],
  providers: [
    AdminIssueReportsService,
    ContractsService,
    S3Service,
    SmtpService,
  ],
})
export class IssueReportsModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(WithUserMiddleware).forRoutes(IssueReportsController);
  }
}
