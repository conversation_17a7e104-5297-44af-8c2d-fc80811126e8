import { Injectable } from '@nestjs/common';
import { Request } from 'express';
import {
  JobEntity,
  IssueReportEntity,
  IssueReportStatus,
  ContractEntity,
} from '../../entities';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, SelectQueryBuilder } from 'typeorm';
import { BaseExceptionService } from '../../exceptions';

import { WithUserContext } from '../../interfaces';
import { ContractsService } from '../../shop/contracts/contracts.service';
import { DateTime } from 'luxon';
import { PatchEmailChangeDto } from './dto';
@Injectable()
export class AdminIssueReportsService {
  constructor(
    @InjectRepository(IssueReportEntity)
    private readonly issueReportRepository: Repository<IssueReportEntity>,
    @InjectRepository(JobEntity)
    private readonly jobRepository: Repository<JobEntity>,
    private readonly baseExceptionService: BaseExceptionService,
    @InjectRepository(ContractEntity)
    private readonly contractRepository: Repository<ContractEntity>,
    private readonly contractsService: ContractsService,
  ) {}

  buildSearchQuery(
    context: Request,
    listQuery: SelectQueryBuilder<IssueReportEntity>,
  ): SelectQueryBuilder<IssueReportEntity> {
    const status = context.query.status as string[];
    const issueType = context.query.issueType as string;
    const searchId = context.query.searchId as string;

    // Construct filter conditions & apply conditions
    const conditions = [
      status && `r.status IN ('${status.map((s) => s).join("','")}')`,
      issueType && `r.issueReportType = '${issueType}'`,
      searchId &&
        `(r.jobId LIKE '%${searchId}%' OR r.issueReportId LIKE '%${searchId}%')`,
    ].filter(Boolean);

    if (conditions.length) listQuery.andWhere(conditions.join(' AND '));

    return listQuery;
  }

  async approveEmailChange(id: string, user: WithUserContext) {
    const issueReport = await this.issueReportRepository.findOne({
      where: { issueReportId: id, companyId: user.company },
    });
    if (!issueReport) {
      throw this.baseExceptionService.exception(
        'NOT_FOUND_DATA',
        'Issue report not found',
      );
    }
    if (issueReport.status !== IssueReportStatus.PENDING) {
      throw this.baseExceptionService.exception(
        'INVALID_ISSUE_STATUS_TO_UPDATE',
      );
    }

    const queryRunner =
      this.issueReportRepository.manager.connection.createQueryRunner();
    try {
      await queryRunner.connect();
      await queryRunner.startTransaction();

      //update issue report status to approved
      issueReport.status = IssueReportStatus.APPROVED;
      issueReport.reviewedAt = new Date();
      issueReport.reviewedBy = user.userKey;
      await this.issueReportRepository.save(issueReport);

      //find contract by job id
      const contract = await this.contractRepository.findOne({
        relations: ['importedVouchers'],
        where: { jobId: issueReport.jobId, companyId: user.company },
      });

      if (!contract) {
        throw this.baseExceptionService.exception(
          'NOT_FOUND_DATA',
          'contract not found',
        );
      }

      contract.customerInfo.email = issueReport.data.newEmail;
      contract.updatedBy = user.userKey;
      contract.updatedAt = new Date();
      const updatedContract = await this.contractRepository.save(contract);

      await queryRunner.commitTransaction();
      const job = await this.jobRepository.findOne({
        relations: ['campaignRedemptionCode'],
        where: { jobId: issueReport.jobId, companyId: user.company },
      });
      if (!job) {
        throw this.baseExceptionService.exception(
          'NOT_FOUND_DATA',
          'job not found',
        );
      }
      await this.contractsService.sendContractToEmail(job, updatedContract);
    } catch (e) {
      if (queryRunner.isTransactionActive) {
        // Check if transaction is active before rolling back (case error from sendContractToEmail)
        await queryRunner.rollbackTransaction();
      }
      throw e;
    } finally {
      await queryRunner.release();
    }
  }

  async patchEmailChangeRejected(
    issueReportId: string,
    body: PatchEmailChangeDto,
    user: WithUserContext,
  ) {
    const issueReport = await this.issueReportRepository.findOneBy({
      issueReportId,
    });

    if (!issueReport) {
      throw this.baseExceptionService.exception(
        'NOT_FOUND_DATA',
        `Issue report not found`,
      );
    }

    if (issueReport.status !== IssueReportStatus.PENDING) {
      throw this.baseExceptionService.exception(
        'INVALID_ISSUE_STATUS_TO_UPDATE',
      );
    }

    issueReport.status = IssueReportStatus.REJECTED;
    issueReport.remark = body.remark;
    issueReport.reviewedBy = user.userKey;
    issueReport.reviewedAt = DateTime.now().toJSDate();

    await this.issueReportRepository.save(issueReport);

    return null;
  }
}
