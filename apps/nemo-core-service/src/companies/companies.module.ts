import {
  Global,
  MiddlewareConsumer,
  Module,
  NestModule,
  RequestMethod,
} from '@nestjs/common';
import { CompaniesController } from './companies.controller';
import {
  CompanyEntity,
  SystemConfigEntity,
  UserEntity,
  UserRoleBranchEntity,
} from '../entities';
import { TypeOrmModule } from '@nestjs/typeorm';
import { WithUserMiddleware, WithBranchMiddleware } from '../middlewares';

@Global()
@Module({
  imports: [
    TypeOrmModule.forFeature([
      CompanyEntity,
      UserEntity,
      SystemConfigEntity,
      UserRoleBranchEntity,
    ]),
  ],
  controllers: [CompaniesController],
  providers: [],
  exports: [TypeOrmModule.forFeature([CompanyEntity])],
})
export class CompaniesModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(WithUserMiddleware).forRoutes(CompaniesController);
    consumer
      .apply(WithBranchMiddleware)
      .exclude({ path: '/v1/shop/companies/info', method: RequestMethod.GET })
      .forRoutes(CompaniesController);
  }
}
