import { Controller } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { omit } from 'lodash';
import { CrudController } from '../crud';
import { CompanyEntity } from '../entities';
import { EntityManager, Repository, SelectQueryBuilder } from 'typeorm';
import { Request } from 'express';

@Controller('v1/shop/companies')
export class CompaniesController extends CrudController<CompanyEntity> {
  constructor(
    @InjectRepository(CompanyEntity) repository: Repository<CompanyEntity>,
  ) {
    const searchableFields: (keyof CompanyEntity)[] = ['companyId', 'title'];
    super(CompanyEntity, 'company', repository, {
      resourceKeyPath: 'companyId',
      order: { createdAt: 'desc' },
      defaultPopulate: (_ctx, isMany) => {
        return isMany
          ? []
          : ['users', 'branches', 'jobTemplates', 'modelMasters'];
      },
      searchFilter: async (
        request: Request,
        _em: EntityManager,
        query: SelectQueryBuilder<CompanyEntity>,
      ) => {
        const search = request.query?.search as string | undefined;
        const companyId = request.query?.companyId as string | undefined;

        const title = request.query?.title as string | undefined;
        const userKeyClaimFnName = request.query?.userKeyClaimFnName as
          | string
          | undefined;
        const empUploadMapperFnName = request.query?.empUploadMapperFnName as
          | string
          | undefined;
        const advanceSearchConditions: string[] = [];
        const searchConditions: string[] = [];

        // Find user by keyword from searchable fields
        if (search && searchableFields.length) {
          const searchConditionMapParams = {};
          searchableFields.forEach((searchField) => {
            // assign key value params
            Object.assign(searchConditionMapParams, {
              [searchField]: `%${search}%`,
            });
            // build search condition in arrays
            searchConditions.push(`r.${searchField} ilike :${searchField}`);
          });
          const searchConditionsInString = `( ${searchConditions.join(
            ' OR ',
          )} )`;
          query.andWhere(searchConditionsInString, searchConditionMapParams);
        }

        if (companyId) {
          advanceSearchConditions.push(`r.companyId ilike '%${companyId}%'`);
        }
        if (title) {
          advanceSearchConditions.push(`r.title ilike '%${title}%'`);
        }
        if (userKeyClaimFnName) {
          advanceSearchConditions.push(
            `r.userKeyClaimFnName ilike '%${userKeyClaimFnName}%'`,
          );
        }
        if (empUploadMapperFnName) {
          advanceSearchConditions.push(
            `r.empUploadMapperFnName ilike '%${empUploadMapperFnName}%'`,
          );
        }

        if (advanceSearchConditions?.length) {
          const advanceSearchConditionsInString = `( ${advanceSearchConditions.join(
            ' AND ',
          )} )`;
          query.andWhere(advanceSearchConditionsInString);
        }

        return query;
      },
      sanitizeInputBody: async (
        _ctx,
        _man,
        data: Partial<CompanyEntity>,
        isCreated,
      ): Promise<Partial<CompanyEntity>> => {
        if (!isCreated) {
          // unique key arrays, omit before process an update action
          const uniqueKeys = ['companyId'];
          data = omit(data, uniqueKeys);
        }
        return data;
      },
    });
  }
}
