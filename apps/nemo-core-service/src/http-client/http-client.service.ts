import { Injectable, Logger } from '@nestjs/common';
import axios, { AxiosInstance } from 'axios';
import { BaseResponse } from 'contracts';

interface InitConfig {
  apiKeyConfig: {
    baseURL: string;
  };
}

interface HeaderOption {
  ['Authorization']?: string;
}

@Injectable()
export class HttpClient {
  private readonly logger = new Logger(HttpClient.name);

  private axios!: AxiosInstance;
  private baseURL: string | undefined;

  constructor() {}

  public init(config: InitConfig) {
    this.baseURL = config.apiKeyConfig.baseURL;
    this.axios = axios.create({
      baseURL: this.baseURL,
    });
    this.axios.interceptors.request.use((v) => {
      this.logger.log(
        `HttpClientService.request ${v.url}> ${JSON.stringify(v.data)}`,
      );
      return v;
    });
    this.axios.interceptors.response.use(
      (v) => {
        this.logger.log(
          `HttpClientService.response > ${JSON.stringify(v.data)}`,
        );
        return v;
      },
      (err) => {
        if (err.response) {
          this.logger.log(
            `ERR.response ${err.config.url}> ${JSON.stringify(
              err.response.data,
            )}`,
          );
          throw err.response.data;
        }
        throw err;
      },
    );
  }

  public async post<T = any>(
    path: string,
    body: any,
    headerOption?: HeaderOption,
  ): Promise<BaseResponse<T>> {
    const resp = await this.axios.post(path, body, {
      headers: {
        ...headerOption,
      },
    });
    return resp.data;
  }

  public async get<T = any>(
    path: string,
    params: any,
    headerOption?: HeaderOption,
  ): Promise<BaseResponse<T>> {
    const resp = await this.axios.get(path, {
      headers: {
        ...headerOption,
      },
      params,
    });
    return resp.data;
  }

  public async patch<T = any>(
    path: string,
    body: any,
    headerOption?: HeaderOption,
  ): Promise<BaseResponse<T>> {
    const resp = await this.axios.patch(path, body, {
      headers: {
        ...headerOption,
      },
    });
    return resp.data;
  }

  public async delete<T = any>(
    path: string,
    headerOption?: HeaderOption,
  ): Promise<BaseResponse<T>> {
    const resp = await this.axios.delete(path, {
      headers: {
        ...headerOption,
      },
    });
    return resp.data;
  }
}
