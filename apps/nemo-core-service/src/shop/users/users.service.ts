import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import {
  BranchEntity,
  UserEntity,
  UserRoleBranchEntity,
  RoleEntity,
  PermissionGroupEntity,
  SiteType,
} from '../../entities';
import { In, Repository } from 'typeorm';
import { WithUserContext } from '../../interfaces';
import { BaseExceptionService } from '../../exceptions';
import { GetMeResponse, PermissionGroupConfig, RoleConfig } from 'contracts';

@Injectable()
export class UsersService {
  constructor(
    @InjectRepository(UserEntity)
    private readonly userEntity: Repository<UserEntity>,
    @InjectRepository(BranchEntity)
    private readonly branchEntity: Repository<BranchEntity>,
    @InjectRepository(UserRoleBranchEntity)
    private readonly userRoleBranch: Repository<UserRoleBranchEntity>,
    private readonly baseException: BaseExceptionService,
  ) {}

  async getMe(user: WithUserContext): Promise<GetMeResponse> {
    // Find user
    const userData = await this.userEntity.findOne({
      where: {
        companyId: user.company,
        userKey: user.userKey,
      },
    });

    // Prevent user not exists
    if (!userData) {
      throw this.baseException.exception('NOT_FOUND_DATA');
    }

    // Find branches by branchId from user role
    // const branchs = await this.branchEntity.find({
    //   where: {
    //     branchId: In(userData?.roles?.map((role) => role.branchId) ?? []),
    //   },
    // });

    // Map branch roles
    // const branchRoles: BranchRole[] =
    //   userData?.roles
    //     ?.map((role) => {
    //       return {
    //         branchId: role.branchId,
    //         branchName:
    //           branchs.find((branch) => branch.branchId === role.branchId)
    //             ?.title ?? '',
    //         // Filter manager and sale role
    //         // role: role.role.filter((o) => o === 'Manager' || o === 'Sale'),
    //         role: ['Manager', 'Sale'],
    //         rolesPermission: [
    //           {
    //             roleId: '1',
    //             roleName: 'Manager1',
    //             permissions: [
    //               {
    //                 permissionId: '1',
    //                 permissionLabel: 'Permission 1',
    //                 permissionGroupId: '1',
    //                 permissionGroupLabel: 'Permission Group 1',
    //                 view: true,
    //                 create: true,
    //                 update: true,
    //                 delete: true,
    //               },
    //             ],
    //           },
    //           {
    //             roleId: '2',
    //             roleName: 'Manager2',
    //             permissions: [
    //               {
    //                 permissionId: '1',
    //                 permissionLabel: 'Permission 1',
    //                 permissionGroupId: '1',
    //                 permissionGroupLabel: 'Permission Group 1',
    //                 view: true,
    //                 create: true,
    //                 update: true,
    //                 delete: true,
    //               },
    //             ],
    //           },
    //         ],
    //       } as BranchRole;
    //     })
    //     // Filter empty role
    //     .filter((o) => o.role.length)
    //     // Filter branchId and branchName
    //     .filter((o) => o.branchId && o.branchName) ?? [];

    const branch = await this.userRoleBranch
      .createQueryBuilder('userRoleBranch')
      .select('userRoleBranch.branchId', 'branchId')
      .addSelect('array_agg(userRoleBranch.roleId)', 'roles')
      .addSelect('branch.title', 'branchName')
      .leftJoin('userRoleBranch.role', 'role')
      .leftJoin('userRoleBranch.branch', 'branch')
      .where(
        'userRoleBranch.companyId = :companyId AND userRoleBranch.userKey = :userKey AND role.type = :type',
        {
          companyId: user.company,
          userKey: user.userKey,
          type: SiteType.FRONTSHOP,
        },
      )
      .groupBy('userRoleBranch.branchId, branch.title')
      .getRawMany();

    const role = (await this.userRoleBranch.manager.find(RoleEntity, {
      select: {
        roleId: true,
        roleName: true,
        rolePermissions: {
          permissionId: true,
          view: true,
          create: true,
          update: true,
          delete: true,
          download: true,
          upload: true,
        },
      },
      relations: ['rolePermissions'],
      where: {
        companyId: user.company,
        roleId: In(
          branch.reduce((prev, curr) => {
            return [...prev, ...curr.roles];
          }, []),
        ),
      },
    })) as RoleConfig[];

    const permissionGroup = (await this.userRoleBranch.manager.find(
      PermissionGroupEntity,
      {
        select: {
          permissionGroupId: true,
          label: true,
          isInMenu: true,
          permissions: {
            permissionId: true,
            label: true,
            iconName: true,
            iconNameActive: true,
          },
        },
        relations: ['permissions'],
        where: {
          companyId: user.company,
          type: SiteType.FRONTSHOP,
        },
        order: {
          sortIndex: 'ASC',
          permissions: {
            sortIndex: 'ASC',
          },
        },
      },
    )) as PermissionGroupConfig[];

    // Return response
    return {
      userKey: userData.userKey,
      name: userData.name ?? '',
      branchRoles: branch,
      roleConfig: role,
      permissionGroupConfig: permissionGroup,
      userType: userData.userType, // WW as default and required field -> always has value
    };
  }

  async getUsersInBranch(companyId: string, branchId: string) {
    // Get user by branch id
    const result = await this.userEntity
      .createQueryBuilder('user')
      .select(['user.userKey', 'user.name'])
      .leftJoin('user.userRoleBranch', 'userRoleBranch')
      .where('user.companyId = :companyId', {
        companyId,
      })
      .andWhere('userRoleBranch.branchId = :branchId', {
        branchId,
      })
      .getMany();

    return result;
  }
}
