import { SchemaObject } from '@nestjs/swagger/dist/interfaces/open-api-spec.interface';
import { createZodDto, zodToOpenAPI } from 'nestjs-zod';
import { z } from 'nestjs-zod/z';

const CreateFromDraftSchema = z.object({
  estimationId: z.string(),
  deviceKey: z.string().min(15).max(15),
  deviceKey2: z.union([z.string().min(15).max(15), z.null()]).optional(),
  colorId: z.string().optional(),
  phoneNumber: z
    .string()
    .length(10, 'Phone number must be exactly 10 digits')
    .regex(/^0/, 'Phone number must start with 0'),
  answers: z.object({
    product_information: z.record(z.any()),
    product_images: z.record(z.any()),
    product_additional_information: z.record(z.any()).optional(),
    product_additional_images: z.record(z.any()).optional(),
    media: z.record(z.any()).optional(),
  }),
});

export const CreateFromDraftOpenApi = zodToOpenAPI(
  CreateFromDraftSchema,
) as SchemaObject;

export class CreateFromDraftDto extends createZodDto(CreateFromDraftSchema) {}
