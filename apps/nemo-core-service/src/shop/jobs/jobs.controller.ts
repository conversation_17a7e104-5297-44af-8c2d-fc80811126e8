import {
  Body,
  Controller,
  Get,
  Post,
  Query,
  Req,
  Param,
  UploadedFile,
  UseInterceptors,
  Put,
  Patch,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { CrudController } from '../../crud';
import {
  CustomerInfoType,
  JobActivitiesType,
  JobEntity,
  JobStatus,
} from '../../entities';
import { EntityManager, Repository, SelectQueryBuilder } from 'typeorm';
import {
  CommentJobDto,
  ConfirmContractDto,
  CreateJobDraftDto,
  CreateJobDto,
  GetJobsDto,
  GetJobsHistoryDto,
  UpdateCustomerInfoJobDto,
  UpdateJobDto,
  SignContractDto,
  IdentityRejectedDto,
  CreateFromDraftDto,
  PostOcrDto,
} from './dto';
import { Request } from 'express';
import { WithBranchContext, WithUserContext } from '../../interfaces';
import { JobsService } from './jobs.service';
import {
  <PERSON>rudRemoveRoute,
  WithBranch,
  WithUser,
  PermissionsWithBranch,
} from '../../decorators';
import { ImageJobParamDto } from './dto/image-param.dto';
import { FileInterceptor } from '@nestjs/platform-express';
import { DataChangeSlug, Summary } from '../../subscriber';
import {
  mappingUrlWithCompanyId,
  Permission,
  PermissionAction,
} from '../../config';
import { ContractsService } from '../contracts/contracts.service';
import { CampaignService } from '../campaign/campaign.service';
import { SignDepositContractDto } from './dto/sign-deposit-contract.dto';
import { CreateJobDraftSignDto } from './dto/create-job-draft-sign.dto';
import { GetMediaUrlDto } from './dto/get-media-url';

@CrudRemoveRoute(['removeOne', 'softRemoveOne', 'restore'])
@Controller('v1/shop/jobs')
export class JobsController extends CrudController<JobEntity> {
  constructor(
    @InjectRepository(JobEntity) repo: Repository<JobEntity>,
    private readonly jobsService: JobsService,
    private readonly contractsService: ContractsService,
    private readonly campaignService: CampaignService,
  ) {
    super(JobEntity, 'job', repo, {
      resourceKeyPath: 'jobId',
      order: { updatedAt: 'asc' },
      defaultPopulate: () => {
        return ['contract', 'deliveryOrder', 'color'];
      },
      defaultFilter: async (
        request: Request,
        listQuery: SelectQueryBuilder<JobEntity>,
      ) => {
        const xCompany = request.headers['x-company'] as string;

        const company = mappingUrlWithCompanyId(xCompany);

        return listQuery.andWhere(`r.companyId = :company`, {
          company: company,
        });
      },
      searchFilter: async (
        request: Request,
        _em: EntityManager,
        listQuery: SelectQueryBuilder<JobEntity>,
      ) => this.jobsService.buildSearchQuery(request, listQuery),
      sanitizeInputBody: async (
        _ctx,
        _man,
        data: Partial<JobEntity>,
        isCreated,
      ): Promise<Partial<JobEntity>> =>
        this.jobsService.sanitizeInputBody(data, isCreated),
      computeUpdatePayload: async (
        _ctx,
        _man,
        raw: JobEntity,
        data,
      ): Promise<Partial<JobEntity>> =>
        this.jobsService.computeUpdatePayload(raw, data),
      preSave: [
        async (_ctx, _man, data: any, isCreated): Promise<any> =>
          this.jobsService.preSave(_man, data, isCreated),
      ],
    });
  }

  @Get()
  @PermissionsWithBranch([
    Permission.SHOP_JOB_MY + PermissionAction.VIEW,
    Permission.SHOP_JOB_HX + PermissionAction.VIEW,
    Permission.SHOP_JOB_TEAM + PermissionAction.VIEW,
    Permission.SHOP_ALLOCATION_ORDER_ALL + PermissionAction.VIEW,
  ])
  async getJobs(
    @Req() context: Request,
    @Query() query: GetJobsDto,
    @WithUser() user: WithUserContext,
    @WithBranch() branch: WithBranchContext,
  ) {
    // Transform orderBy
    query.orderBy = this.jobsService.transformOrderBy(query.orderBy);

    // Add shopUserKey and branch to query
    if (context.query.isExcludeUser !== 'true') {
      context.query.branch = branch.branch;
      context.query.shopUserKey = user.userKey;
    }

    return super.findAll(context, query);
  }

  @Get('/:id/with-relation')
  @PermissionsWithBranch([
    Permission.SHOP_ISSUE_REPORT + PermissionAction.CREATE,
  ])
  async getJobWithRelation(
    @Param('id') id: string,
    @Query('type') type: 'customer-info',
    @Query('relations') relations: string[],
    @WithUser() user: WithUserContext,
  ) {
    const { company } = user;
    const job = await this.jobsService.getJobWithRelation({
      jobId: id,
      relations: relations ?? [],
      type,
      company,
    });

    return job;
  }

  @Get('/branch')
  @PermissionsWithBranch([
    Permission.SHOP_OTHERS_OCR_CONFIRM + PermissionAction.UPDATE,
    Permission.SHOP_JOB_HX + PermissionAction.VIEW,
    Permission.SHOP_JOB_TEAM + PermissionAction.VIEW,
    Permission.SHOP_DELIVERY_ORDER_ALL + PermissionAction.CREATE,
    Permission.SHOP_DELIVERY_ORDER_ALL + PermissionAction.UPDATE,
  ])
  async getJobsBranch(
    @Req() context: Request,
    @Query() query: GetJobsDto,
    @WithBranch() branch: WithBranchContext,
  ) {
    // Transform orderBy
    query.orderBy = this.jobsService.transformOrderBy(query.orderBy);
    // Add branch to query
    context.query.branch = branch.branch;
    return super.findAll(context, query);
  }

  @Get('/history')
  @PermissionsWithBranch([
    Permission.SHOP_JOB_MY + PermissionAction.VIEW,
    Permission.SHOP_JOB_HX + PermissionAction.VIEW,
    Permission.SHOP_JOB_TEAM + PermissionAction.VIEW,
  ])
  async getJobsHistory(
    @Req() context: Request,
    @Query() query: GetJobsHistoryDto,
  ) {
    // Add done state to query
    context.query.status = [
      JobStatus.PURCHASED,
      JobStatus.REJECT_BY_CUSTOMER,
      JobStatus.REJECT_BY_SHOP,
    ];
    return super.findAll(context, { ...query, orderBy: 'updatedAt desc' });
  }

  @Get('/:id')
  @PermissionsWithBranch([
    Permission.SHOP_JOB_MY + PermissionAction.VIEW,
    Permission.SHOP_JOB_HX + PermissionAction.VIEW,
    Permission.SHOP_JOB_TEAM + PermissionAction.VIEW,
  ])
  async getJob(
    @Req() context: Request,
    @Param('id') id: string,
    @WithUser() user: WithUserContext,
    @WithBranch() branch: WithBranchContext,
  ) {
    let job = await super.findOne(context, id);
    job = await this.jobsService.getMediaUrl(job);
    job = await this.jobsService.getCampaignInformation(job);

    // Check job permission
    if (context.query.isHistory !== 'true') {
      this.jobsService.checkJobPermission(job, user, branch);
    }
    return job;
  }

  @Post('/draft-sign')
  @PermissionsWithBranch([Permission.SHOP_JOB_MY + PermissionAction.CREATE])
  async createJobDraftSign(
    @Body() body: CreateJobDraftSignDto,
    @WithUser() user: WithUserContext,
    @WithBranch() branch: WithBranchContext,
  ) {
    const job = await this.jobsService.prepareJobDraftSign(body, user, branch);

    return { jobId: job.jobId };
  }

  @Post('/draft')
  @PermissionsWithBranch([Permission.SHOP_JOB_MY + PermissionAction.CREATE])
  async createJobDraft(
    @Req() req: Request,
    @Body() body: CreateJobDraftDto,
    @WithUser() user: WithUserContext,
    @WithBranch() branch: WithBranchContext,
  ) {
    const jobEntity = await this.jobsService.prepareJobDraft(
      body,
      user,
      branch,
    );

    return jobEntity;
  }

  @Post()
  @PermissionsWithBranch([Permission.SHOP_JOB_MY + PermissionAction.CREATE])
  async createJob(
    @Req() req: Request,
    @Body() body: CreateJobDto,
    @WithUser() user: WithUserContext,
  ) {
    const jobEntity = this.jobsService.prepareJobQuoteRequest(body, user);

    return await super.update(req, body.jobId, jobEntity);
  }

  @Post('/:id/assign')
  @PermissionsWithBranch([Permission.SHOP_JOB_MY + PermissionAction.UPDATE])
  async assignJob(
    @Req() req: Request,
    @Param('id') id: string,
    @WithUser() user: WithUserContext,
  ) {
    const jobEntity = this.jobsService.prepareAssignJob(user);

    return await super.update(req, id, jobEntity);
  }

  @Patch('/:id')
  @PermissionsWithBranch([Permission.SHOP_JOB_MY + PermissionAction.CREATE])
  async updateJob(
    @Req() req: Request,
    @Param('id') id: string,
    @Body() body: UpdateJobDto,
    @WithUser() user: WithUserContext,
  ) {
    const jobEntity = this.jobsService.defaultPrepareJob({
      updatedBy: user.userKey,
      checkListValues: body.checkListValues,
    });

    return await super.update(req, id, jobEntity);
  }

  @Get('/:id/contract')
  @PermissionsWithBranch([Permission.SHOP_JOB_HX + PermissionAction.DOWNLOAD])
  async getContractBuffer(@Param('id') id: string) {
    return await this.contractsService.getPDFBuffer(id, 'contractLink');
  }

  @Get('/:id/transaction')
  @PermissionsWithBranch([Permission.SHOP_JOB_HX + PermissionAction.DOWNLOAD])
  async getTransactionBuffer(@Param('id') id: string) {
    return await this.contractsService.getPDFBuffer(id, 'transactionLink');
  }

  @Get('/:id/contract/presignedUrl')
  @PermissionsWithBranch([Permission.SHOP_JOB_HX + PermissionAction.DOWNLOAD])
  async getContractUrl(@Param('id') id: string) {
    return await this.contractsService.getPDFPresignedUrl(id, 'contractLink');
  }

  @Get('/:id/transaction/presignedUrl')
  @PermissionsWithBranch([Permission.SHOP_JOB_HX + PermissionAction.DOWNLOAD])
  async getTransactionUrl(@Param('id') id: string) {
    return await this.contractsService.getPDFPresignedUrl(
      id,
      'transactionLink',
    );
  }

  @Get('/:id/upload/:key')
  @PermissionsWithBranch([
    Permission.SHOP_JOB_MY + PermissionAction.VIEW,
    Permission.SHOP_ISSUE_REPORT + PermissionAction.VIEW,
  ])
  async getPreviewFile(
    @WithUser() user: WithUserContext,
    @Param() imageJobParamDto: ImageJobParamDto,
  ) {
    return this.jobsService.getImageJob(user, imageJobParamDto);
  }

  @Put('/:id/upload/:slug/:key')
  @PermissionsWithBranch([
    Permission.SHOP_JOB_MY + PermissionAction.CREATE,
    Permission.SHOP_ISSUE_REPORT + PermissionAction.CREATE,
  ])
  @UseInterceptors(FileInterceptor('file'))
  async uploadFile(
    @WithUser() user: WithUserContext,
    @Param() imageJobParamDto: ImageJobParamDto,
    @UploadedFile() file: Express.Multer.File,
  ) {
    const fileStream = file.buffer; // Assuming the file buffer is a readable stream

    return this.jobsService.uploadImageJob(fileStream, user, imageJobParamDto);
  }

  @Post('/:id/comment')
  @PermissionsWithBranch([Permission.SHOP_JOB_MY + PermissionAction.CREATE])
  async commentJob(
    @Req() req: Request,
    @Param('id') id: string,
    @Body() body: CommentJobDto,
    @WithUser() user: WithUserContext,
  ) {
    const jobEntity = this.jobsService.defaultPrepareJob({
      updatedBy: user.userKey,
      isAdditionalCheckList: true,
    });

    const job = await super.update(req, id, jobEntity);
    // Insert job activities
    await this.jobsService.insertJobActivities(
      job,
      JobActivitiesType.COMMENT,
      {
        summary: Summary.COMMENTED,
        branchId: job.branchId,
        status: job.status,
        shopUserKey: job.shopUserKey,
        adminUserKey: job.adminUserKey,
        displayName: user.name ?? user.userKey,
        message: body.message,
        dataChangeSlug: DataChangeSlug.COMMENTED,
      },
      user,
    );

    return job;
  }

  @Post('/:id/completed-estimate')
  @PermissionsWithBranch([Permission.SHOP_JOB_MY + PermissionAction.CREATE])
  async completedEstimate(
    @Req() req: Request,
    @Param('id') id: string,
    @WithUser() user: WithUserContext,
  ) {
    const jobEntity = this.jobsService.defaultPrepareJob({
      updatedBy: user.userKey,
      status: JobStatus.PRICE_ESTIMATED,
    });

    const job = await super.update(req, id, jobEntity);

    // Insert job activities
    await this.jobsService.insertJobActivities(
      job,
      JobActivitiesType.UPDATE,
      {
        summary: Summary.JOB_UPDATED,
        branchId: job.branchId,
        status: job.status,
        shopUserKey: job.shopUserKey,
        adminUserKey: job.adminUserKey,
        displayName: user.name ?? user.userKey,
        dataChangeSlug: DataChangeSlug.JOB_UPDATED,
      },
      user,
    );

    return job;
  }

  @Post('/:id/reject-by-shop')
  @PermissionsWithBranch([Permission.SHOP_JOB_MY + PermissionAction.CREATE])
  async rejectByShop(
    @Req() req: Request,
    @Param('id') id: string,
    @WithUser() user: WithUserContext,
  ) {
    await this.campaignService.removeJobIdInSelectedCampaign(id);
    const jobEntity = this.jobsService.prepareRejectJob(
      user,
      JobStatus.REJECT_BY_SHOP,
    );
    return await super.update(req, id, jobEntity);
  }

  @Post('/:id/reject-by-customer')
  @PermissionsWithBranch([Permission.SHOP_JOB_MY + PermissionAction.CREATE])
  async rejectByCustomer(
    @Req() req: Request,
    @Param('id') id: string,
    @WithUser() user: WithUserContext,
  ) {
    await this.campaignService.removeJobIdInSelectedCampaign(id);
    const jobEntity = this.jobsService.prepareRejectJob(
      user,
      JobStatus.REJECT_BY_CUSTOMER,
    );
    return await super.update(req, id, jobEntity);
  }

  @Post('/:id/identity/verify-by-dip-chip')
  @PermissionsWithBranch([Permission.SHOP_JOB_MY + PermissionAction.CREATE])
  async verifyByDipChip(
    @Param('id') id: string,
    @Body() body: UpdateCustomerInfoJobDto,
    @WithUser() user: WithUserContext,
  ) {
    return await this.jobsService.verifyByDipChip(user, id, body);
  }

  @Get('/:id/contract/draft')
  @PermissionsWithBranch([Permission.SHOP_JOB_MY + PermissionAction.CREATE])
  async getDraftContract(@Req() context: Request, @Param('id') id: string) {
    console.time('getDraftContract - JobID:' + id);
    context.query.populate = 'campaigns,contract';
    const job = await super.findOne(context, id);
    const res = await this.jobsService.getDraftContract(job);
    console.timeEnd('getDraftContract - JobID:' + id);
    return res;
  }

  @Post('/:id/contract/sign')
  @PermissionsWithBranch([Permission.SHOP_JOB_MY + PermissionAction.CREATE])
  async getSignContract(
    @Req() context: Request,
    @Param('id') id: string,
    @Body() body: SignContractDto,
  ) {
    context.query.populate = 'campaigns,contract';
    const job = await super.findOne(context, id);
    return await this.jobsService.getSignContract(body, job);
  }

  @Post('/deposit-contract/sign')
  @PermissionsWithBranch([Permission.SHOP_JOB_MY + PermissionAction.CREATE])
  async getSignDepositContract(
    @Body() body: SignDepositContractDto,
    @WithUser() user: WithUserContext,
  ) {
    return await this.jobsService.getSignDepositContract({
      company: user.company,
      body,
    });
  }

  @Post('/:id/contract/send-email')
  @PermissionsWithBranch([Permission.SHOP_JOB_HX + PermissionAction.UPDATE])
  async sendEmailContract(
    @Param('id') id: string,
    @WithUser() user: WithUserContext,
  ) {
    return await this.jobsService.sendEmailContract({ jobId: id, user });
  }

  @Post('/:id/contract/confirm')
  @PermissionsWithBranch([Permission.SHOP_JOB_MY + PermissionAction.CREATE])
  async confirmContract(
    @Param('id') id: string,
    @Body() body: ConfirmContractDto,
    @WithUser() user: WithUserContext,
  ) {
    return await this.jobsService.confirmPurchasedJob(user, id, body);
  }

  @Post('/:id/identity/request')
  @PermissionsWithBranch([Permission.SHOP_JOB_MY + PermissionAction.CREATE])
  async identityReviewRequest(
    @Param('id') id: string,
    @Body() body: UpdateCustomerInfoJobDto,
    @WithUser() user: WithUserContext,
  ) {
    return await this.jobsService.identityReviewRequest(user, id, body);
  }

  @Post('/:id/identity/reject')
  @PermissionsWithBranch([
    Permission.SHOP_OTHERS_OCR_CONFIRM + PermissionAction.UPDATE,
  ])
  async identityReject(
    @Param('id') id: string,
    @Body() body: IdentityRejectedDto,
    @WithUser() user: WithUserContext,
  ) {
    return await this.jobsService.identityReject(user, id, body);
  }

  @Post('/:id/identity/verify-by-manager')
  @PermissionsWithBranch([
    Permission.SHOP_OTHERS_OCR_CONFIRM + PermissionAction.UPDATE,
  ])
  async verifyByManager(
    @Req() req: Request,
    @Param('id') id: string,
    @WithUser() user: WithUserContext,
  ) {
    const existingJob = await super.findOne(req, id);
    const jobEntity = this.jobsService.prepareVerifiedJob(
      user,
      CustomerInfoType.IDENTITY_VERIFICATION,
      existingJob.contract,
      existingJob.status,
    );

    return await super.update(req, id, jobEntity);
  }

  @Get('/estimation-activities/check/:estimationActivitiesId')
  @PermissionsWithBranch([Permission.SHOP_JOB_MY + PermissionAction.CREATE])
  async getEstimationActivities(
    @Param('estimationActivitiesId') id: string,
    @WithUser() user: WithUserContext,
  ) {
    return await this.jobsService.getEstimationActivities(id, user.company);
  }

  @Patch('/:id/create-from-draft')
  @PermissionsWithBranch([Permission.SHOP_JOB_MY + PermissionAction.CREATE])
  async createFromDraft(
    @Param('id') id: string,
    @Body() body: CreateFromDraftDto,
    @WithUser() user: WithUserContext,
  ) {
    return await this.jobsService.createFromDraft(user, id, body);
  }

  @Post('/:id/ocr')
  @PermissionsWithBranch([Permission.SHOP_JOB_MY + PermissionAction.CREATE])
  async postOcr(
    @Param('id') id: string,
    @Body() body: PostOcrDto,
    @WithUser() user: WithUserContext,
  ) {
    return await this.jobsService.getOcrResult(user, id, body);
  }

  @Get('/:id/presign-upload/:key')
  @PermissionsWithBranch([Permission.SHOP_JOB_MY + PermissionAction.CREATE])
  async getPresigned(
    @Param('id') id: string,
    @Param('key') key: string,
    @WithUser() user: WithUserContext,
  ) {
    return await this.jobsService.getPresigned(id, key, user);
  }

  @Post('/get-presign')
  async getMediaUrl(@Body() body: GetMediaUrlDto) {
    return await this.jobsService.getMediaUrlFromPath(body);
  }

  @Patch('/:id/select-campaign')
  @PermissionsWithBranch([Permission.SHOP_JOB_MY + PermissionAction.CREATE])
  async selectCampaign(
    @Param('id') id: string,
    @Body() body: { campaignCodes: string[] | null },
    @WithUser() user: WithUserContext,
  ) {
    return await this.jobsService.updateSelectCampaign(user, id, body);
  }
}
