import { CacheModule } from '@nestjs/cache-manager';
import { Global, Module } from '@nestjs/common';
import { CacheManagerService } from './cache-manager.service';
import { redisStore } from 'cache-manager-ioredis-yet';

@Global()
@Module({
  imports: [
    CacheModule.registerAsync({
      isGlobal: true,
      useFactory: async () => ({
        store: await redisStore({
          host: process.env.REDIS_HOST,
          port: Number(process.env.REDIS_PORT),
          password: process.env.REDIS_PASSWORD,
          ttl: Number(process.env.REDIS_TTL),
          tls: process.env.REDIS_TLS === 'true' ? {} : undefined,
          db: process.env.REDIS_DB ? Number(process.env.REDIS_DB) : 0,
        }),
      }),
    }),
  ],
  providers: [CacheManagerService],
  exports: [CacheManagerService],
})
export class CacheManagerModule {}
