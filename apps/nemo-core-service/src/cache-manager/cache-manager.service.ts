import { CACHE_KEY_ALL } from '../config/constants.config';
import { isNil } from 'lodash';
import { Inject, Injectable } from '@nestjs/common';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { RedisStore } from 'cache-manager-ioredis-yet';
import { InjectRepository } from '@nestjs/typeorm';
import { SystemConfigEntity, UserEntity } from '../entities';
import { Repository } from 'typeorm';
import { RedisUtils } from '../utils/redis.util';
import { BaseExceptionService } from '../exceptions';

export interface CacheStore extends RedisStore {
  store: RedisStore;
}

// Public Types
export type Cacher<T> = () => Promise<T>;
export type TTLResolver<T> = (v: T) => number | undefined;
export interface OnceOptions<T> {
  /**
   * ttl in seconds to save the the cache result.
   */
  ttl: TTLResolver<T>;
  /**
   * If true, neglect the cache query, and fetch with resolver.
   * Once done update the cache.
   * If false (default) use cache, if cache misses, use resolver
   * Once done update the cache.
   */
  ignoreCache: boolean;
}

// Private Types
type OnceOptionFormat<T> = number | TTLResolver<T> | Partial<OnceOptions<T>>;

/**
 * Utility function to resolve nested arguments into the `OnceOptions` interface.
 * @param ttlOrOptions
 * @returns transformed to simpler form
 */
function toOnceOptions<T>(
  ttlOrOptions: OnceOptionFormat<T> = {},
): OnceOptions<T> {
  if (typeof ttlOrOptions === 'number') {
    return {
      ttl: () => ttlOrOptions,
      ignoreCache: false,
    };
  }
  if (typeof ttlOrOptions === 'function') {
    return {
      ttl: ttlOrOptions,
      ignoreCache: false,
    };
  }
  return {
    ttl: () => undefined,
    ignoreCache: false,
    ...ttlOrOptions,
  };
}

@Injectable()
export class CacheManagerService {
  constructor(
    @Inject(CACHE_MANAGER) private memoryCache: CacheStore,
    @InjectRepository(SystemConfigEntity)
    private readonly systemConfigRepository: Repository<SystemConfigEntity>,
    @InjectRepository(UserEntity)
    private readonly userRepository: Repository<UserEntity>,
  ) {}
  defaultTime = +(process.env.REDIS_TTL || 3600);

  public isHealthy() {
    // const redisClient = this.memoryCache.store.getClient();
    // return redisClient?.connector?.stream?._readableState?.reading;
  }

  public async invalidateCache(key: string) {
    // Get data from cache
    const result = await this.getData(key);

    // Prevent cache invalid
    if (!result) {
      // Throw exception
      throw new BaseExceptionService().exception('NOT_FOUND_DATA');
    }

    // Regex user role
    const userRolesRegex = /user-roles-(.*):(.*)/.exec(key);

    // Regex config
    const configRegex = /config-(.*):(.*)/.exec(key);

    // Declare data
    let data: unknown;

    // Check user roles regex
    if (userRolesRegex?.length) {
      // Destruct user role regex
      const [, companyId, userKey] = userRolesRegex;

      // Get user roles
      const user = await this.userRepository.findOne({
        select: ['roles'],
        where: {
          companyId,
          userKey,
        },
      });

      if (!user) {
        // Throw exception
        throw new BaseExceptionService().exception('NOT_FOUND_DATA');
      }

      // Assign user roles to data variable
      data = user.roles;
    }

    // Check config role regex
    if (configRegex?.length) {
      // Destruct config regex
      const [, companyId, configKey] = configRegex;

      // Get data from systemconfig
      const config = await this.systemConfigRepository.findOne({
        where: {
          companyId,
          configKey,
        },
      });

      if (!config) {
        // Throw exception
        throw new BaseExceptionService().exception('NOT_FOUND_DATA');
      }

      // Assign data to data variable
      data = config.data;
    }

    // Override cache data
    await this.setData(key, data, RedisUtils.dayTTL);
  }

  public async getData(key: string): Promise<any> {
    const data = await this.memoryCache.get(key);
    return data;
  }

  public async getBatchData(pattern: string): Promise<any> {
    const data = await this.memoryCache.store.client.keys(pattern);
    return data;
  }

  public async setData(key: string, val: any, ttl?: number) {
    await this.memoryCache.set(key, val, (ttl || this.defaultTime) * 1000);
  }

  public async incrData(key: string, ttl = RedisUtils.dayTTL): Promise<any> {
    // INCR command increments the value of the key and returns the new value
    const data = await this.memoryCache.store.client
      .multi()
      .incr(key)
      .expire(key, ttl)
      .exec();

    if (!data) {
      throw new Error('Redis incr error');
    }

    return data[0][1];
  }

  public async removeData(key: string) {
    if (key === CACHE_KEY_ALL) {
      await this.memoryCache.reset();
    } else {
      await this.memoryCache.del(key);
    }
    return key;
  }

  public async removeBatchData(keys: string[]) {
    if (keys.length) {
      await this.memoryCache.store.mdel(...keys);
    }
  }

  /**
   * Cache the result of cacher if cache is missed from the caching source
   *
   * @param key - key to lookup for the cache
   * @param cacher - callback to resolve for value in case of cache misses.
   */
  public once<T>(key: string, cacher: Cacher<T>): Promise<T>;
  /**
   * Cache the result of cacher if cache is missed from the caching souce
   *
   * @param key - key to lookup for the cache
   * @param ttl - compute ttl based on received payload, useful if the received payload is a accessToken (with expiration)
   * @param cacher - callback to resolve for value in case of cache misses.
   */
  // eslint-disable-next-line prettier/prettier
  public once<T>(
    key: string,
    ttl: TTLResolver<T>,
    cacher: Cacher<T>,
  ): Promise<T>;
  /**
   * Cache the result of cacher if cache is missed from the caching source
   *
   * @param key - key to lookup for the cache
   * @param ttl - static TTL use for storing thie cache
   * @param cacher - callback to resolve for value in case of cache misses.
   */
  // eslint-disable-next-line @typescript-eslint/unified-signatures
  public once<T>(key: string, ttl: number, cacher: Cacher<T>): Promise<T>;
  /**
   * Cache the result of cacher if cache is missed from the caching source
   *
   * @param key - key to lookup for the cache
   * @param options - cache options defined.
   * @param cacher - callback to resolve for value in case of cache misses.
   */
  // eslint-disable-next-line @typescript-eslint/unified-signatures
  public once<T>(
    key: string,
    options: OnceOptions<T>,
    cacher: Cacher<T>,
  ): Promise<T>;
  // implementation interface.
  async once<T>(
    key: string,
    ttlOrCacher: number | Cacher<T> | TTLResolver<T> | OnceOptions<T>,
    cacherOrUndefined?: Cacher<T>,
  ): Promise<T> {
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
    const cacher =
      typeof cacherOrUndefined !== 'undefined'
        ? cacherOrUndefined
        : <Cacher<T>>ttlOrCacher;
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
    const options = toOnceOptions(
      typeof cacherOrUndefined !== 'undefined'
        ? <OnceOptionFormat<T>>ttlOrCacher
        : {},
    );
    if (!options.ignoreCache) {
      const inCache = await this.getData(key);
      if (inCache) {
        return inCache;
      }
    }
    const val = await cacher();
    const ttlInSeconds = Math.floor(options.ttl(val) || this.defaultTime);
    if (isNil(val)) {
      return val;
    }
    this.setData(key, val, ttlInSeconds);
    return val;
  }
}
