import { DynamicModule, Global, Module } from '@nestjs/common';
import { AES128MessageService } from './encrypt-decrypt.service';

@Global()
@Module({
  exports: [AES128MessageService],
})
export class EncryptDecryptModule {
  static register(): DynamicModule {
    return {
      module: EncryptDecryptModule,
      providers: [
        {
          provide: AES128MessageService,
          useFactory: () => {
            // Get aes key
            const aesKey = process.env.AES128_KEY;

            // Get aes salt
            const aesSalt = process.env.AES128_SALT;

            // Prevent key or salt invalid
            if (!aesKey || !aesSalt) {
              throw new Error(
                'AES_KEY and AES_SALT must be defined in .env file',
              );
            }

            // Initial aes service
            return new AES128MessageService(aesKey, aesSalt);
          },
        },
      ],
      exports: [AES128MessageService],
    };
  }
}
