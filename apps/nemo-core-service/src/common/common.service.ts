import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import {
  CompanyEntity,
  ModelMasterEntity,
  SystemConfigEntity,
  ModelMasterColorEntity,
  BranchEntity,
  EstimationActivitiesEntity,
  IModelChecklistResult,
  IModelChecklistResultItemModule,
  IModelChecklistResultItemQuestion,
  IModuleStatus,
  ILegalDocumentType,
  branchType,
  ModelMasterFunctionEntity,
  ChecklistType,
  QuestionType,
  LegalDocumentEntity,
} from '../entities';
import { ArrayContains, In, Repository } from 'typeorm';
import { BaseExceptionService, CommonExceptionService } from '../exceptions';
import { AES128MessageService } from '../encrypt-decrypt-message/encrypt-decrypt.service';
import { CompanyInfoResponse, IKeyLabel } from 'contracts';
import { MasterAddressEntity } from '../entities/address-master.entity';
import { getDistanceFromCoordinate } from '../utils';
import { StoreListDto } from './dto/store-list.dto';
import {
  createRandomStringCharNum,
  ILanguage,
  tranformRomToNum,
  validatePhoneNumber,
} from '../utils/general';
import { CreateOrderDto } from './dto/create-order.dto';
import { DateTime } from 'luxon';
import { CacheManagerService } from '../../src/cache-manager/cache-manager.service';
import { RedisUtils } from '../../src/utils/redis.util';
import {
  convertOrderResponse,
  getQuestionOptionKeyCond,
  mapModuleStatusKeyCond,
  mergeAddress,
} from '../../src/utils/common-order';
import { isBoolean } from 'lodash';
import { UpdateOrderDto } from './dto/update-order.dto';
import { BASE_COLLATE } from '../../src/config';

export type AddressLovGroupKeys =
  | 'province'
  | 'zipcode'
  | 'district'
  | 'subdistrict';

export interface SearchAddressFilter {
  zipcode: string;
  subdistrictCode: string;
  districtCode: string;
  provinceCode: string;
}

export const isAddressLovGroupKey = (o: string): o is AddressLovGroupKeys => {
  return /^(province|zipcode|district|subdistrict)$/.test(o);
};

// internal selection pair
const _selectionPairs: Record<
  AddressLovGroupKeys,
  [keyof MasterAddressEntity, string]
> = {
  zipcode: ['zipcode', 'zipcode'],
  district: ['districtCode', "district::jsonb -> 'th'"],
  province: ['provinceCode', "province::jsonb -> 'th'"],
  subdistrict: ['subdistrictCode', "subdistrict::jsonb -> 'th'"],
};

const BRANCH = 'branch';

@Injectable()
export class CommonService {
  constructor(
    @InjectRepository(CompanyEntity)
    private readonly repository: Repository<CompanyEntity>,
    @InjectRepository(SystemConfigEntity)
    private readonly systemConfigRepository: Repository<SystemConfigEntity>,
    @InjectRepository(MasterAddressEntity)
    private readonly masterAddressRepository: Repository<MasterAddressEntity>,
    @InjectRepository(ModelMasterEntity)
    private readonly modelMasterRepository: Repository<ModelMasterEntity>,
    @InjectRepository(ModelMasterFunctionEntity)
    private readonly modelMasterFunctionRepository: Repository<ModelMasterFunctionEntity>,
    @InjectRepository(EstimationActivitiesEntity)
    private readonly estimationActivitiesRepository: Repository<EstimationActivitiesEntity>,
    @InjectRepository(BranchEntity)
    private readonly branchRepository: Repository<BranchEntity>,
    @InjectRepository(LegalDocumentEntity)
    private readonly legalDocumentRepository: Repository<LegalDocumentEntity>,
    private readonly baseExceptionService: BaseExceptionService,
    private readonly aes128MessageService: AES128MessageService,
    private readonly cacheManager: CacheManagerService,
  ) {}

  async getCompanyInfo(companyId: string): Promise<CompanyInfoResponse> {
    // Find company info
    const company = await this.repository.findOne({ where: { companyId } });

    // Prevent company not found
    if (!company) {
      throw this.baseExceptionService.exception('NOT_FOUND_DATA');
    }

    // Get firebase config
    const config = await this.systemConfigRepository.find({
      where: {
        companyId,
        configKey: In(['firebase_frontend', 'ms', 'ga']),
      },
    });

    // Prevent config invalid
    if (!config) {
      throw this.baseExceptionService.exception('NOT_FOUND_DATA');
    }

    // Get firebase config
    const firebaseConfig = config.find(
      (item) => item.configKey === 'firebase_frontend',
    );

    // Get ms config
    const msConfig = config.find((item) => item.configKey === 'ms');

    // Prevent config invalid
    if (!firebaseConfig?.data.length || !msConfig?.data.length) {
      throw this.baseExceptionService.exception('NOT_FOUND_DATA');
    }

    // Decrypt firebase config
    const decryptFirebaseConfig = JSON.parse(
      this.aes128MessageService.decrypt(
        Buffer.from(firebaseConfig?.data[0], 'base64'),
      ),
    );

    // Decrypt ms config
    const decryptMsConfig = JSON.parse(
      this.aes128MessageService.decrypt(
        Buffer.from(msConfig?.data[0], 'base64'),
      ),
    );

    const gaConfig = config.find((item) => item.configKey === 'ga');

    // Response
    return {
      ...company,
      firebaseConfig: decryptFirebaseConfig,
      msConfig: decryptMsConfig,
      gaConfig: { ...gaConfig?.data },
    };
  }

  async listAddressItem(
    lovGroupKey: AddressLovGroupKeys,
    addressFilter: Partial<SearchAddressFilter>,
  ): Promise<IKeyLabel[]> {
    const selection = _selectionPairs[lovGroupKey];
    const possibleChoies = await this.masterAddressRepository
      .createQueryBuilder()
      .select(selection[0], 'key')
      .distinct(true)
      .addSelect(selection[1], 'label')
      .where({
        ...((addressFilter?.provinceCode && {
          provinceCode: addressFilter?.provinceCode,
        }) ||
          {}),
        ...((addressFilter?.zipcode && {
          zipcode: addressFilter?.zipcode,
        }) ||
          {}),
        ...((addressFilter?.districtCode && {
          districtCode: addressFilter?.districtCode,
        }) ||
          {}),
        ...((addressFilter?.subdistrictCode && {
          subdistrictCode: addressFilter?.subdistrictCode,
        }) ||
          {}),
      })
      .orderBy(selection[0], 'ASC')
      .cache(120 * 1000)
      .getRawMany();

    return possibleChoies;
  }

  encryptData(data: any) {
    // Switch case data type
    switch (typeof data) {
      case 'string':
        return this.aes128MessageService.encrypt(data).toString('base64');
      case 'object':
        return this.aes128MessageService
          .encrypt(JSON.stringify(data))
          .toString('base64');
      default:
        return this.aes128MessageService
          .encrypt(data.toString())
          .toString('base64');
    }
  }

  decryptData(data: any) {
    // Decrypt data
    return this.aes128MessageService.decrypt(Buffer.from(data, 'base64'));
  }

  async getDeviceDetail(
    systemCode: string,
    companyId: string,
    language: ILanguage,
  ) {
    const result = await this.modelMasterRepository.find({
      where: [
        {
          systemCode,
          companyId,
        },
        {
          systemCodeList: ArrayContains([systemCode]),
          companyId,
        },
      ],
    });
    if (result.length === 0 || !result) {
      // return default sku case
      return {
        id: null,
        deviceImageUrl: null,
        brand: '',
        name: '',
        skus: [
          {
            id: 'default',
            storage: null,
          },
        ],
        isPurchasable: false,
      };
      // throw new CommonExceptionService().exception(
      //   'NOT_FOUND_DATA',
      //   language,
      //   'Model not found',
      //   null,
      //   'DEVICE_SYSTEM_CODE_NOT_FOUND',
      // );
    }

    return {
      id: systemCode,
      deviceImageUrl: result[0].modelImageUrl,
      brand: result[0].modelIdentifiers?.brand,
      name: result[0].modelIdentifiers?.model,
      skus: result
        .map((model) => {
          return {
            id: model.modelKey,
            storage: tranformRomToNum(model.modelIdentifiers?.rom),
          };
        })
        .sort((a, b) => {
          if (a.storage && b.storage) {
            return a.storage - b.storage;
          } else if (!a.storage) {
            return 1;
          } else {
            return -1;
          }
        }),
      isPurchasable: true,
    };
  }

  async getStoreList(
    companyId: string,
    acceptLanguage: string,
    query: StoreListDto,
  ) {
    const currentLatitude = query.lat ? Number(query.lat) : null;
    const currentLongitude = query.long ? Number(query.long) : null;

    //custom query select
    const querySelectBranch = this.branchRepository
      .createQueryBuilder(`${BRANCH}`)
      .where(
        `"${BRANCH}".branch_type = :type and "${BRANCH}".company_id = :companyId`,
        { companyId, type: branchType.SHOP },
      )
      .orderBy(
        `"${BRANCH}".${
          acceptLanguage === 'en' ? 'title_en' : 'title'
        } ${BASE_COLLATE}`,
        'ASC',
      );
    const storeList = await querySelectBranch.getRawMany();

    let respList: any[] = [];
    let respListNull: any[] = [];
    storeList.forEach((store) => {
      const destLa = store.branch_latitude;
      const destLo = store.branch_longitude;
      let distance: number | null;
      if (destLa && destLo && currentLatitude && currentLongitude) {
        distance = getDistanceFromCoordinate(
          currentLatitude,
          currentLongitude,
          destLa,
          destLo,
        );
      } else {
        distance = null;
      }
      const storeAddressConvertName: any = {
        addressTh: store.branch_address_th,
        addressEn: store.branch_address_en,
        subDistrictTh: store.branch_sub_district_th,
        subDistrictEn: store.branch_sub_district_en,
        districtTh: store.branch_district_th,
        districtEn: store.branch_district_en,
        provinceTh: store.branch_province_th,
        provinceEn: store.branch_province_en,
        zipCode: store.branch_zip_code,
      };
      const address = mergeAddress(acceptLanguage, storeAddressConvertName);
      const titleEn =
        store.branch_title_en === '' ? null : store.branch_title_en;
      const respObj = {
        id: store.branch_branch_id,
        name: acceptLanguage === 'en' ? titleEn : store.branch_title,
        imageUrl: store.branch_image_url,
        address,
        latitude: Number(store.branch_latitude),
        longitude: Number(store.branch_longitude),
        distance: distance ? Number(distance.toFixed(2)) : distance,
      };
      if (distance || distance === 0) {
        respList.push(respObj);
      } else {
        respListNull.push(respObj);
      }
    });
    //sort by distance asc
    respList.sort((a, b) => a.distance - b.distance);
    //concat with distance null list
    respList = respList.concat(respListNull);
    return respList;
  }
  async updateEstimationActivity(
    orderId: string,
    xCompany: string,
    acceptLanguage: ILanguage,
    body: UpdateOrderDto,
  ) {
    //--- find estimation_activities where id = orderId and companyId = xCompany ---
    const estimationActivity =
      await this.estimationActivitiesRepository.findOne({
        where: { companyId: xCompany, id: orderId },
      });
    if (!estimationActivity) {
      throw new CommonExceptionService().exception(
        'NOT_FOUND_DATA',
        acceptLanguage,
        'Order Id not found',
        null,
        'ORDER_ID_NOT_FOUND',
      );
    }

    const { storeId, customerInfo, isAcceptPDPA, pdpaVersion } = body;

    if (isAcceptPDPA === false) {
      throw new CommonExceptionService().exception(
        'BODY_PAYLOAD_INVALID',
        acceptLanguage,
        'Request body error: PDPA accept need when request with PDPA version',
        null,
        'ORDER_ACCEPT_PDPA_REQUIRED',
      );
    }

    const { legalDocumentId } =
      (await this.validateOrderBody({
        language: acceptLanguage,
        body,
        companyId: xCompany,
        modelMasterColors: undefined,
      })) || {};

    //--- update entity data
    if (estimationActivity) {
      const toUpdate = {
        ...(customerInfo?.firstname !== undefined && {
          firstName: customerInfo.firstname ? customerInfo.firstname : null,
        }),
        ...(customerInfo?.lastname !== undefined && {
          lastName: customerInfo.lastname ? customerInfo.lastname : null,
        }),
        ...(customerInfo?.phoneNumber !== undefined && {
          phoneNumber: customerInfo.phoneNumber
            ? customerInfo.phoneNumber
            : null,
        }),
        ...(customerInfo?.email !== undefined && {
          email: customerInfo.email ? customerInfo.email : null,
        }),

        ...(pdpaVersion !== undefined && {
          PDPAVersion: pdpaVersion === 0 ? null : pdpaVersion,
        }),
        ...(legalDocumentId !== undefined && {
          legalDocumentId,
        }),
        ...(isAcceptPDPA !== undefined && { isAcceptPDPA }),
        ...(storeId !== undefined && { branchId: storeId || null }), //NOSONAR
        updatedAt: new Date(),
      };

      await this.estimationActivitiesRepository.update(
        { id: estimationActivity.id, companyId: xCompany },
        toUpdate,
      );
    }
    return null;
  }

  async getAppraisal(modelKey: string, companyId: string, language: ILanguage) {
    const modelData = await this.modelMasterFunctionRepository.find({
      where: { modelKey: modelKey, companyId: companyId },
      relations: ['modelChecklist'],
    });

    if (modelData.length === 0 || !modelData) {
      throw new CommonExceptionService().exception(
        'NOT_FOUND_DATA',
        language,
        'SKU not found',
        null,
        'APPRAISAL_SKU_NOT_FOUND',
      );
    }

    const modelChecklistData = modelData
      .filter((data) => data.modelChecklist !== null)
      .map((data) => data.modelChecklist!);

    const colorList = [] as any;
    const moduleList = [] as any;
    const questionList = [] as any;

    const modelMasterData = await this.modelMasterRepository.findOne({
      where: { modelKey: modelKey, companyId: companyId },
      relations: ['modelMasterColors'],
    });

    if (modelMasterData) {
      const colorData = modelMasterData.modelMasterColors;
      colorData?.forEach((data) => {
        const color = {
          id: data.id,
          name: language === 'th' ? data.nameTh : data.nameEn,
        };

        colorList.push(color);
      });
    }

    modelChecklistData.forEach((data) => {
      if (data.checklistType === ChecklistType.MODULE) {
        const moduleData = {
          id: data.id,
          name: language === 'th' ? data.checklistNameTh : data.checklistNameEn,
          code: data.moduleCode,
          iconImageUrl: data.iconImageUrl,
          isRequired: data.isRequired,
        };

        moduleList.push(moduleData);
      }

      if (data.checklistType === ChecklistType.QUESTION) {
        const questionData = {
          id: data.id,
          question:
            language === 'th' ? data.checklistNameTh : data.checklistNameEn,
          description:
            language === 'th'
              ? data.checklistDescriptionTh
              : data.checklistDescriptionEn,
          type: data.questionType,
          isRequired: data.isRequired,
          imageUrl: data.iconImageUrl,
          choices:
            data.questionType !== QuestionType.TEXT
              ? this.selectChoicesFromLanguage(data.questionChoices, language)
              : null,
        };

        questionList.push(questionData);
      }
    });

    return {
      colors: colorList,
      modules: this.removeDuplicates(moduleList),
      questions: this.removeDuplicates(questionList),
    };
  }

  selectChoicesFromLanguage(questionChoices: any, language: string) {
    const targetChoice = language === 'th' ? 'answerTh' : 'answerEn';
    const dataReturn = questionChoices.map((choice) => {
      const returnChoiceValue = { ...choice };
      returnChoiceValue['answer'] = returnChoiceValue[targetChoice];
      delete returnChoiceValue['answerTh'];
      delete returnChoiceValue['answerEn'];
      return returnChoiceValue;
    });

    return dataReturn;
  }

  removeDuplicates(data: any[]) {
    const ids = new Set<string>();
    return data.filter((item) => {
      if (ids.has(item.id)) {
        return false;
      } else {
        ids.add(item.id);
        return true;
      }
    });
  }

  async getLegalDocument(companyId: string, language: string) {
    const document = await this.legalDocumentRepository.manager
      .createQueryBuilder()
      .select('*')
      .from((subQuery) => {
        return (
          subQuery
            .select('*')
            // add rank version of each document type
            .addSelect(
              'ROW_NUMBER() OVER (PARTITION BY ld."type" ORDER BY ld.version DESC)',
              'rank',
            )
            .from(LegalDocumentEntity, 'ld')
            .where(`ld.company_id = '${companyId}'`)
        );
      }, 'Ranking')
      // query only rank 1 of each document type
      .where(`"rank" = 1`)
      .getRawMany();

    return document.map((document) => {
      return {
        id: document.id,
        content_url:
          language === 'th' ? document.content_url_th : document.content_url_en,
        version: parseFloat(document.version),
        type: document.type,
        create_date: document.created_at,
        update_date: document.updated_at,
      };
    });
  }

  async validateOrderBody({
    language,
    body,
    companyId,
    modelMasterColors,
  }: {
    language: ILanguage;
    body: any;
    companyId: string;
    modelMasterColors?: ModelMasterColorEntity[];
  }) {
    const { customerInfo, isAcceptPDPA, pdpaVersion, colorId, storeId } = body;

    let legalDocumentId: string | undefined = undefined;
    let color: any = undefined;
    let store: any = undefined;

    if (customerInfo) {
      const { phoneNumber } = customerInfo;
      if (phoneNumber && !validatePhoneNumber(phoneNumber)) {
        throw new CommonExceptionService().exception(
          'BODY_PAYLOAD_INVALID',
          language,
          'Request body with invalid format: phoneNumber',
          undefined,
          'ORDER_CUSTOMER_INFO_INVALID',
        );
      }
    }

    if (isAcceptPDPA === true) {
      if (!pdpaVersion) {
        throw new CommonExceptionService().exception(
          'BODY_PAYLOAD_INVALID',
          language,
          'Request body error: PDPA version need when PDPA accept',
          undefined,
          'ORDER_PDPA_VERSION_REQUIRED',
        );
      }
      const legalDoc = await this.legalDocumentRepository.findOne({
        where: {
          type: ILegalDocumentType.PDPA,
          version: pdpaVersion,
          companyId,
        },
      });
      if (!legalDoc) {
        throw new CommonExceptionService().exception(
          'BODY_PAYLOAD_INVALID',
          language,
          'Request body pdpaVersion is not existed in system',
          undefined,
          'ORDER_PDPA_VERSION_NOT_FOUND',
        );
      }
      legalDocumentId = legalDoc.id;
    } else if (pdpaVersion) {
      throw new CommonExceptionService().exception(
        'BODY_PAYLOAD_INVALID',
        language,
        'Request body error: PDPA accept need when request with PDPA version',
        undefined,
        'ORDER_ACCEPT_PDPA_REQUIRED',
      );
    }

    if (storeId) {
      const foundStore = await this.branchRepository.findOne({
        where: { companyId, branchId: storeId },
      });
      if (!foundStore) {
        throw new CommonExceptionService().exception(
          'BODY_PAYLOAD_INVALID',
          language,
          'Request body storeId is not existed in system',
          undefined,
          'ORDER_STORE_ID_NOT_FOUND',
        );
      }
      store = foundStore;
    }

    if (colorId) {
      if (!modelMasterColors) {
        throw new CommonExceptionService().exception(
          'BODY_PAYLOAD_INVALID',
          language,
          'Request body colorId is not existed in model',
          undefined,
          'ORDER_MODEL_COLOR_INVALID',
        );
      }
      const foundColor = modelMasterColors.findIndex(
        (item: ModelMasterColorEntity) => item.id === colorId,
      );
      if (foundColor === -1) {
        throw new CommonExceptionService().exception(
          'BODY_PAYLOAD_INVALID',
          language,
          'Request body colorId is not existed in model',
          undefined,
          'ORDER_MODEL_COLOR_INVALID',
        );
      }
      color = modelMasterColors[foundColor];
    }

    return { legalDocumentId, color, store };
  }

  async generateEstimateActivitiesIdWithoutRunningNumber(
    currentDate,
  ): Promise<string> {
    const id = `${currentDate}-${createRandomStringCharNum(5)}`;

    return id;
  }

  async generateEstimateActivitiesId(currentDate): Promise<string> {
    const estimateActivitiesCount = await this.cacheManager.incrData(
      `estimateActivities_${currentDate}`,
      RedisUtils.dayTTL,
    );
    const id = `${currentDate}-${String(estimateActivitiesCount).padStart(
      7,
      '0',
    )}`;

    return id;
  }

  async fallBackGenerateEAId(currentDate: string): Promise<{
    eaId: string;
    eaCount: number;
  }> {
    const latestEaIdInDate = await this.estimationActivitiesRepository
      .createQueryBuilder('estimationActivities')
      .select(['estimationActivities.id', 'estimationActivities.createdAt'])
      .where('estimationActivities.id LIKE :latestId', {
        latestId: `%${currentDate}-%`,
      })
      .orderBy('estimationActivities.id', 'DESC')
      .getOne();

    if (!latestEaIdInDate) {
      return {
        eaId: `${currentDate}-0000001`,
        eaCount: 1,
      };
    }

    const eaIdCountFromDb =
      parseInt(latestEaIdInDate.id.slice(-7).replace(/^0*/g, '') || '0') + 1;

    const eaId = `${currentDate}-${String(eaIdCountFromDb).padStart(7, '0')}`;

    return {
      eaId,
      eaCount: eaIdCountFromDb,
    };
  }

  async createOrder({
    skuId,
    headerData: { companyId, language },
    body,
  }: {
    skuId: string;
    headerData: { companyId: string; language: ILanguage };
    body: CreateOrderDto;
  }) {
    const { modules, questions } = body;

    const modelMasterItem = await this.modelMasterRepository.findOne({
      where: { modelKey: skuId, companyId: companyId },
      relations: {
        modelMasterColors: true,
        modelMasterFunction: { modelChecklist: true },
      },
    });

    if (!modelMasterItem) {
      throw new CommonExceptionService().exception(
        'NOT_FOUND_DATA',
        language,
        'SKU not existed in system',
        undefined,
        'ORDER_SKU_NOT_FOUND',
      );
    }

    const {
      modelMasterFunction,
      modelMasterColors,
      modelMasterGrades: modelMasterGradeSnapshot,
    } = modelMasterItem;

    if (!modelMasterFunction) {
      throw new CommonExceptionService().exception(
        'NOT_FOUND_DATA',
        language,
        'SKU not existed in system',
        undefined,
        'ORDER_SKU_NOT_FOUND',
      );
    }

    const { legalDocumentId, color, store } =
      (await this.validateOrderBody({
        body,
        companyId,
        modelMasterColors,
        language,
      })) || {};

    const moduleMasterFunctionPointer: { [key: string]: { fnName: string } } =
      {};
    const questionMasterFunctionPointer: {
      [key: string]: { fnName: string; type: QuestionType | null };
    } = {};
    const moduleMasterFunctionByFunction: any = {};
    const questionMasterFunctionByFunction: any = {};
    const requireModuleChecklistIds = new Set();
    const requireQuestionChecklistIds = new Set();

    modelMasterFunction.forEach(
      ({
        checkListId,
        penalties,
        functionKeyCond,
        modelChecklist,
      }: ModelMasterFunctionEntity) => {
        const {
          isRequired,
          checklistType,
          checklistNameTh: modelChecklistNameTh,
          checklistNameEn: modelChecklistNameEn,
          checklistDescriptionTh: modelCheckListDescriptionTh,
          checklistDescriptionEn: modelCheckListDescriptionEn,
          iconImageUrl,
          moduleCode: modelChecklistModuleCode,
          questionChoices,
          questionType = null,
        } = modelChecklist || {};

        const data: any = {
          modelChecklistId: checkListId,
          functionKeyCond,
          penalties,
          modelChecklistNameTh,
          modelChecklistNameEn,
          modelCheckListDescriptionTh,
          modelCheckListDescriptionEn,
          iconImageUrl,
          isRequired: isBoolean(isRequired) ? isRequired : null, // require: field must not null when found in checklistAnswer
          isSkip: null, // set in result
        };

        const [fnName, keyCondValue] = functionKeyCond.split('=');

        if (checkListId) {
          if (checklistType === ChecklistType.MODULE) {
            if (isRequired) {
              requireModuleChecklistIds.add(checkListId);
            }
            moduleMasterFunctionPointer[checkListId] = { fnName };
            if (!moduleMasterFunctionByFunction[fnName]) {
              moduleMasterFunctionByFunction[fnName] = {};
            }
            moduleMasterFunctionByFunction[fnName][keyCondValue] = {
              ...data,
              modelChecklistModuleCode,
              moduleStatus: null, // set in result
            };
          } else if (checklistType === ChecklistType.QUESTION) {
            if (isRequired) {
              requireQuestionChecklistIds.add(checkListId);
            }
            questionMasterFunctionPointer[checkListId] = {
              fnName,
              type: questionType,
            };
            if (!questionMasterFunctionByFunction[fnName]) {
              questionMasterFunctionByFunction[fnName] = {};
            }
            questionMasterFunctionByFunction[fnName][keyCondValue] = {
              ...data,
              answerType: questionType,
              questionChoices, // set again in result
              questionAnswerText: null, // set in result
            };
          }
        }
      },
    );

    const moduleChecklistResult: IModelChecklistResultItemModule[] = [];
    const questionChecklistResult: IModelChecklistResultItemQuestion[] = [];

    let maxPrice: string =
      modelMasterGradeSnapshot.find((item) => item.grade.toUpperCase() === 'A')
        ?.purchasePrice ?? '';

    let estimatedPrice: number = Number(maxPrice);

    modules?.forEach(({ moduleId, status }) => {
      requireModuleChecklistIds.delete(moduleId);
      const { fnName } = moduleMasterFunctionPointer[moduleId] || {};
      if (!fnName) {
        throw new CommonExceptionService().exception(
          'BODY_PAYLOAD_INVALID',
          language,
          'Some request body moduleId is not existed in system',
          undefined,
          'ORDER_MODULE_NOT_FOUND',
        );
      }

      const checklist = moduleMasterFunctionByFunction[fnName];

      const keyCondValue = mapModuleStatusKeyCond[status];
      const result = checklist[keyCondValue];
      if (!result) {
        // ERROR THROW
        // throw new CommonExceptionService().exception(
        //   'BODY_PAYLOAD_INVALID',
        //   language,
        //   'no result match in master function ???',
        // );
      }

      result.isSkip = status.toLowerCase() === IModuleStatus.SKIP.toLowerCase();

      if (result.isRequired && result.isSkip) {
        throw new CommonExceptionService().exception(
          'BODY_PAYLOAD_INVALID',
          language,
          'Request body error: checklist with required answer cannot skip',
          undefined,
          'ORDER_SKIP_INVALID',
        );
      }

      const penaltiesAdd =
        Number(result.penalties) && !isNaN(Number(result.penalties))
          ? Number(result.penalties)
          : 0;
      estimatedPrice += penaltiesAdd;
      result.moduleStatus = status as IModuleStatus;
      moduleChecklistResult.push(result);
    });

    questions?.forEach(({ questionId, answerIds, value, isSkip }) => {
      requireQuestionChecklistIds.delete(questionId);
      const { fnName, type } = questionMasterFunctionPointer[questionId] || {};
      if (!type || !fnName) {
        throw new CommonExceptionService().exception(
          'BODY_PAYLOAD_INVALID',
          language,
          'Some request body questionsId not found in system',
          undefined,
          'ORDER_QUESTION_NOT_FOUND',
        );
      }
      let result: any = null;
      const checklist = questionMasterFunctionByFunction[fnName];

      if ([QuestionType.OPTION, QuestionType.SELECTION].includes(type)) {
        let keyCondValue = '';
        if (type === QuestionType.OPTION) {
          keyCondValue = getQuestionOptionKeyCond(isSkip, answerIds);
          result = checklist[keyCondValue];
          if (!result) {
            // ERROR THROW
            // throw new CommonExceptionService().exception(
            //   'BODY_PAYLOAD_INVALID',
            //   language,
            //   'no result match in master function ???',
            // );
          }

          const choiceIdList = result.questionChoices.map((item) => item.id);
          answerIds.forEach((id) => {
            if (!choiceIdList.includes(id)) {
              throw new CommonExceptionService().exception(
                'BODY_PAYLOAD_INVALID',
                language,
                'Request body error: answer not in choices',
                undefined,
                'ORDER_ANSWER_INVALID',
              );
            }
          });

          result.questionChoices = result.questionChoices.map((item) => ({
            id: item.id,
            choiceNameTh: item.answerTh,
            choiceNameEn: item.answerEn,
            iconImageUrl: item.iconImageUrl,
            isSelected: !isSkip && answerIds?.includes(item.id),
          }));
        } else if (type === QuestionType.SELECTION) {
          if (answerIds?.length !== 1 && !isSkip) {
            throw new CommonExceptionService().exception(
              'BODY_PAYLOAD_INVALID',
              language,
              'Request body error: not skip selection type question must have 1 answer',
              undefined,
              'ORDER_SELECTION_ANS_INVALID',
            );
          }
          result = checklist[isSkip ? 'skip' : answerIds[0]];
          if (!result) {
            if (isSkip) {
              // ERROR THROW
              // throw new CommonExceptionService().exception(
              //   'BODY_PAYLOAD_INVALID',
              //   language,
              //   'no result match in master function ???',
              // );
            } else {
              throw new CommonExceptionService().exception(
                'BODY_PAYLOAD_INVALID',
                language,
                'Request body error: answer not in choices',
                undefined,
                'ORDER_ANSWER_INVALID',
              );
            }
          }

          result.questionChoices = result.questionChoices.map((item) => ({
            id: item.id,
            choiceNameTh: item.answerTh,
            choiceNameEn: item.answerEn,
            iconImageUrl: item.iconImageUrl,
            isSelected: !isSkip && answerIds[0] === item.id,
          }));
        }
        const penaltiesAdd =
          Number(result.penalties) && !isNaN(Number(result.penalties))
            ? Number(result.penalties)
            : 0;
        estimatedPrice += penaltiesAdd;
      } else {
        result = checklist.freetext;
        if (!isSkip) {
          result.questionAnswerText = value ?? null;
        }
      }

      if (result.isRequired && isSkip) {
        throw new CommonExceptionService().exception(
          'BODY_PAYLOAD_INVALID',
          language,
          'Request body error: checklist with required answer cannot skip',
          undefined,
          'ORDER_SKIP_INVALID',
        );
      }
      result.isSkip = isSkip;

      questionChecklistResult.push(result);
    });

    if (
      requireModuleChecklistIds.size !== 0 ||
      requireQuestionChecklistIds.size !== 0
    ) {
      throw new CommonExceptionService().exception(
        'BODY_PAYLOAD_INVALID',
        language,
        'Required checklist is missing',
        undefined,
        'ORDER_REQUIRED_CHECKLIST_INCOMPLETE',
      );
    }

    const modelChecklistResult: IModelChecklistResult = {
      [ChecklistType.MODULE]: moduleChecklistResult,
      [ChecklistType.QUESTION]: questionChecklistResult,
    };

    const currentDate = DateTime.now().toFormat('yyMMdd');
    const genId =
      await this.generateEstimateActivitiesIdWithoutRunningNumber(currentDate);

    const data = {
      id: genId,
      companyId,
      modelKey: skuId,
      deviceId: body.deviceId,
      estimatedPrice,
      modelMasterGradeSnapshot,
      modelChecklistResult,
      colorId: body.colorId || null, //NOSONAR
      branchId: body.storeId || null, //NOSONAR
      imei1: body.imei1 || null, //NOSONAR
      imei2: body.imei2 || null, //NOSONAR
      firstName: body.customerInfo?.firstname || null, //NOSONAR
      lastName: body.customerInfo?.lastname || null, //NOSONAR
      phoneNumber: body.customerInfo?.phoneNumber || null, //NOSONAR
      email: body.customerInfo?.email || null, //NOSONAR
      isAcceptPDPA: body.isAcceptPDPA || null, //NOSONAR
      PDPAVersion: body.pdpaVersion || null, //NOSONAR
      legalDocumentId: legalDocumentId || null, //NOSONAR
    };

    const toSaveData: any = Object.entries(data)
      .filter(([key, value]) => value !== null)
      .reduce((obj, [key, value]) => {
        obj[key] = value;
        return obj;
      }, {});

    const estimationActivitiesEntity = new EstimationActivitiesEntity();
    Object.assign(estimationActivitiesEntity, toSaveData);

    const queryRunner =
      this.estimationActivitiesRepository.manager.connection.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();
    try {
      const insertResult = await queryRunner.manager
        .getRepository(EstimationActivitiesEntity)
        .createQueryBuilder()
        .insert()
        .values(estimationActivitiesEntity)
        .orIgnore()
        .execute();

      if (!insertResult.raw.length) {
        estimationActivitiesEntity.id =
          await this.generateEstimateActivitiesIdWithoutRunningNumber(
            currentDate,
          );

        await queryRunner.manager.save(
          EstimationActivitiesEntity,
          estimationActivitiesEntity,
        );
      }
      await queryRunner.commitTransaction();
    } catch (err) {
      await queryRunner.rollbackTransaction();
      throw err;
    } finally {
      await queryRunner.release();
    }

    const toApis = convertOrderResponse({
      estimationActivity: estimationActivitiesEntity,
      modelMaster: modelMasterItem,
      color,
      store,
      language,
    });
    return toApis;
  }

  async getActivitiesByDeviceId(
    deviceId: string,
    companyId: string,
    language: ILanguage,
  ) {
    const result = this.estimationActivitiesRepository.find({
      where: { deviceId, companyId },
      relations: ['modelMaster', 'modelMasterColor', 'branch'],
      order: { updatedAt: 'DESC' },
    });

    return (await result).map((estimation) =>
      convertOrderResponse({
        estimationActivity: estimation,
        modelMaster: estimation.modelMaster,
        color: estimation.modelMasterColor,
        store: estimation.branch,
        language,
      }),
    );
  }
}
