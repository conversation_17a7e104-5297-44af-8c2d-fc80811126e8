import {
  Body,
  Controller,
  Get,
  Headers,
  Param,
  Post,
  Put,
  Query,
} from '@nestjs/common';
import { CommonService, isAddressLovGroupKey } from './common.service';
import { Role, mappingUrlWithCompanyId } from '../config/constants.config';
import { BaseExceptionService } from '../exceptions';
import { CacheManagerService } from '../cache-manager/cache-manager.service';
import { EncryptDecryptDto } from './dto/encrypt-decrypt.dto';
import { StoreListDto } from './dto/store-list.dto';
import { Roles } from '../decorators';
import { WithCompany } from '../decorators/with-company.decorators';
import { WithCompanyContext } from '../interfaces/with-company.interface';
import { getLanguageFromHeader } from '../../src/utils/general';
import { CreateOrderDto } from './dto/create-order.dto';
import { UpdateOrderDto } from './dto/update-order.dto';
import { version } from '../config/app.json';

export const LanguageHeader = 'accept-language';

@Controller('v1/common')
export class CommonController {
  constructor(
    private readonly commonService: CommonService,
    private readonly baseExceptionService: BaseExceptionService,
    private readonly cacheManagerService: CacheManagerService,
  ) {}

  @Get('/version')
  version() {
    return {
      version: version,
    };
  }

  @Get('/shop/companies/info')
  async getCompanyInfo(@Headers() headers: any) {
    const xCompany = headers['x-company'];

    const company = mappingUrlWithCompanyId(xCompany);

    if (!company) {
      // Throw exception
      throw this.baseExceptionService.exception('COMPANY_INVALID');
    }

    return this.commonService.getCompanyInfo(company);
  }

  @Get('/addresses/:addressGroupKey')
  async getAddresses(
    @Param('addressGroupKey') addressGroupKey: any,
    @Query() query: any,
  ) {
    if (!isAddressLovGroupKey(addressGroupKey)) {
      throw this.baseExceptionService.exception('NOT_FOUND_DATA');
    }
    return this.commonService.listAddressItem(addressGroupKey, query);
  }

  @Get('/cache/invalidate/:key')
  async invalidateCache(@Param('key') key: string) {
    return this.cacheManagerService.invalidateCache(key);
  }

  @Get('/client/device/code/:systemCode')
  async getDeviceDetail(
    @Headers() headers: any,
    @WithCompany() company: WithCompanyContext,
    @Param('systemCode') systemCode: string,
  ) {
    const acceptLanguage = getLanguageFromHeader(headers);

    return this.commonService.getDeviceDetail(
      systemCode,
      company.companyId,
      acceptLanguage,
    );
  }

  @Get('/client/appraisal/sku/:skuID')
  async getAppraisal(
    @Headers() headers: any,
    @WithCompany() company: WithCompanyContext,
    @Param('skuID') skuID: string,
  ) {
    const acceptLanguage = getLanguageFromHeader(headers);
    const result = await this.commonService.getAppraisal(
      skuID,
      company.companyId,
      acceptLanguage,
    );

    return { modules: result.modules };
  }

  @Put('/client/saleorder/:orderId')
  async updateOrder(
    @Headers() headers: any,
    @Param('orderId') orderId: string,
    @WithCompany() company: WithCompanyContext,

    @Body() body: UpdateOrderDto,
  ) {
    const acceptLanguage = getLanguageFromHeader(headers);

    return await this.commonService.updateEstimationActivity(
      orderId,
      company.companyId,
      acceptLanguage,
      body,
    );
  }
  @Post('/client/saleorder/sku/:skuId')
  async createOrder(
    @Headers() header: any,
    @WithCompany() company: WithCompanyContext,
    @Param('skuId') skuId: string,
    @Body() body: CreateOrderDto,
  ) {
    const { companyId } = company;
    const language = getLanguageFromHeader(header);
    const { questions, colorId, ...newBody } = body;
    return this.commonService.createOrder({
      skuId,
      headerData: {
        companyId,
        language,
      },
      body: newBody,
    });
  }

  @Get('/client/saleorder/sku/:deviceId')
  async getActivities(
    @Headers() header: any,
    @WithCompany() company: WithCompanyContext,
    @Param('deviceId') deviceId: string,
  ) {
    const { companyId } = company;
    const language = getLanguageFromHeader(header);
    return this.commonService.getActivitiesByDeviceId(
      deviceId,
      companyId,
      language,
    );
  }

  @Get('/legal-documents')
  async getLegalDocument(
    @Headers() headers: any,
    @WithCompany() company: WithCompanyContext,
  ) {
    const acceptLanguage = getLanguageFromHeader(headers);
    return this.commonService.getLegalDocument(
      company.companyId,
      acceptLanguage,
    );
  }

  @Get('/store')
  async getStoreList(
    @Headers() headers: any,
    @WithCompany() company: WithCompanyContext,
    @Query() query: StoreListDto,
  ) {
    const xCompany = company.companyId;
    const acceptLanguage = getLanguageFromHeader(headers);

    return this.commonService.getStoreList(xCompany, acceptLanguage, query);
  }

  @Post('/encrypt')
  @Roles([Role.SUPER_ADMIN])
  async encryptData(@Body() { data }: EncryptDecryptDto) {
    return this.commonService.encryptData(data);
  }

  @Post('/decrypt')
  @Roles([Role.SUPER_ADMIN])
  async decryptData(@Body() { data }: EncryptDecryptDto) {
    return this.commonService.decryptData(data);
  }
}
