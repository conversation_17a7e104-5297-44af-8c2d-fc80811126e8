import { createZodDto } from 'nestjs-zod';
import { z } from 'nestjs-zod/z';

const CreateOrderSchema = z.object({
  deviceId: z
    .string({
      invalid_type_error: 'ORDER_BODY_INCOMPLETE',
      required_error: 'ORDER_BODY_INCOMPLETE',
    })
    .min(1, { message: 'ORDER_BODY_INCOMPLETE' })
    .max(200, { message: 'ORDER_BODY_INCOMPLETE' }),
  modules: z
    .array(
      z.object({
        moduleId: z.string().min(1),
        status: z.enum(['pass', 'fail', 'skip'], {
          errorMap: (issue, ctx) => ({
            message: 'ORDER_MODULE_STATUS_INVALID',
          }),
        }),
      }),
    )
    .nullable()
    .optional(),
  questions: z
    .array(
      z.object({
        questionId: z.string().min(1),
        isSkip: z.boolean(),
        answerIds: z.array(z.string().min(1)),
        value: z.string().max(200).nullable().optional(),
      }),
    )
    .nullable()
    .optional(),
  storeId: z.string().nullable().optional(),
  customerInfo: z
    .object({
      firstname: z
        .string({ invalid_type_error: 'ORDER_CUSTOMER_INFO_INVALID' })
        .max(200, { message: 'ORDER_CUSTOMER_INFO_INVALID' })
        .nullable()
        .optional(),
      lastname: z
        .string({ invalid_type_error: 'ORDER_CUSTOMER_INFO_INVALID' })
        .max(200, { message: 'ORDER_CUSTOMER_INFO_INVALID' })
        .nullable()
        .optional(),
      email: z.preprocess(
        (arg) => {
          if (typeof arg === 'string' && arg === '') {
            return undefined;
          } else {
            return arg;
          }
        },
        z
          .string({ invalid_type_error: 'ORDER_CUSTOMER_INFO_INVALID' })
          .max(200, { message: 'ORDER_CUSTOMER_INFO_INVALID' })
          .email({ message: 'ORDER_CUSTOMER_INFO_INVALID' })
          .nullable()
          .optional(),
      ),
      phoneNumber: z
        .string({ invalid_type_error: 'ORDER_CUSTOMER_INFO_INVALID' })
        .max(100, { message: 'ORDER_CUSTOMER_INFO_INVALID' })
        .nullable()
        .optional(),
    })
    .nullable()
    .optional(),
  imei1: z.string().max(100).nullable().optional(),
  imei2: z.string().max(100).nullable().optional(),
  colorId: z.string().nullable().optional(),
  isAcceptPDPA: z.boolean().nullable().optional(),
  pdpaVersion: z.number().nullable().optional(),
});

export class CreateOrderDto extends createZodDto(CreateOrderSchema) {}
