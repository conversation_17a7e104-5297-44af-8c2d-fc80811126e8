import { createZodDto } from 'nestjs-zod';
import { z } from 'nestjs-zod/z';

const UpdateOrderSchema = z.object({
  storeId: z.string().nullable().optional(),
  customerInfo: z
    .object({
      firstname: z
        .string({ invalid_type_error: 'ORDER_CUSTOMER_INFO_INVALID' })
        .max(200, { message: 'ORDER_CUSTOMER_INFO_INVALID' })
        .nullable()
        .optional(),
      lastname: z
        .string({ invalid_type_error: 'ORDER_CUSTOMER_INFO_INVALID' })
        .max(200, { message: 'ORDER_CUSTOMER_INFO_INVALID' })
        .nullable()
        .optional(),
      email: z.preprocess(
        (arg) => {
          if (typeof arg === 'string' && arg === '') {
            return undefined;
          } else {
            return arg;
          }
        },
        z
          .string({ invalid_type_error: 'ORDER_CUSTOMER_INFO_INVALID' })
          .max(200, { message: 'ORDER_CUSTOMER_INFO_INVALID' })
          .email({ message: 'ORDER_CUSTOMER_INFO_INVALID' })
          .nullable()
          .optional(),
      ),
      phoneNumber: z
        .string({ invalid_type_error: 'ORDER_CUSTOMER_INFO_INVALID' })
        .max(100, { message: 'ORDER_CUSTOMER_INFO_INVALID' })
        .nullable()
        .optional(),
    })
    .nullable()
    .optional(),
  isAcceptPDPA: z.boolean().nullable().optional(),
  pdpaVersion: z.number().nullable().optional(),
});

export class UpdateOrderDto extends createZodDto(UpdateOrderSchema) {}
