import {
  Global,
  MiddlewareConsumer,
  Module,
  NestModule,
  RequestMethod,
} from '@nestjs/common';
import {
  CompanyEntity,
  SystemConfigEntity,
  ConfigActivitiesEntity,
  ModelMasterEntity,
  ModelMasterFunctionEntity,
  LegalDocumentEntity,
  EstimationActivitiesEntity,
  BranchEntity,
} from '../entities';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CommonController } from './common.controller';
import { CommonService } from './common.service';
import { MasterAddressEntity } from '../entities/address-master.entity';
import { WithCompanyMiddleware, WithUserMiddleware } from '../middlewares';

const ExcludeMiddlewarePath = [
  {
    path: '/v1/common/version',
    method: RequestMethod.GET,
  },
  {
    path: '/v1/common/shop/companies/info',
    method: RequestMethod.GET,
  },
  {
    path: '/v1/common/addresses/:addressGroupKey',
    method: RequestMethod.GET,
  },
  {
    path: '/v1/common/cache/invalidate/:key',
    method: RequestMethod.GET,
  },
];

@Global()
@Module({
  imports: [
    TypeOrmModule.forFeature([
      CompanyEntity,
      SystemConfigEntity,
      MasterAddressEntity,
      ConfigActivitiesEntity,
      ModelMasterEntity,
      BranchEntity,
      ModelMasterFunctionEntity,
      LegalDocumentEntity,
      EstimationActivitiesEntity,
    ]),
  ],
  controllers: [CommonController],
  providers: [CommonService],
})
export class CommonModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer
      .apply(WithUserMiddleware)
      .exclude(...ExcludeMiddlewarePath)
      .forRoutes(
        {
          path: '/v1/common/decrypt',
          method: RequestMethod.POST,
        },
        {
          path: '/v1/common/encrypt',
          method: RequestMethod.POST,
        },
      );
    consumer
      .apply(WithCompanyMiddleware)
      .exclude(...ExcludeMiddlewarePath)
      .forRoutes(CommonController);
  }
}
