export const selectExportDBCols = {
  columns: [
    `r.createdAt`,
    `r.updatedAt`,
    `r.companyId`,
    `r.jobId`,
    `r.deviceKey`,
    `r.deviceKey2`,
    `r.branchId`,
    `r.modelKey`,
    `r.thaiId`,
    `r.createdBy`,
    `r.updatedBy`,
    `r.requestedAt`,
    `r.assignedAt`,
    `r.estimatedAt`,
    `r.purchasedAt`,
    `r.rejectedAt`,
    `r.completedShopAt`,
    `r.status`,
    `r.modelIdentifiers`,
    `r.suggestedPrice`,
    `r.purchasedPrice`,
    `r.currentGrade`,
    `r.shopUserKey`,
    `r.shopUserName`,
    `r.adminUserKey`,
    `r.adminUserName`,
    `r.deliveryOrderId`,
    `r.shippingStatus`,
    `r.receiverUserKey`,
    `r.receivingRemark`,
    `r.receivedAt`,
    `r.qcBy`,
    `r.qcAt`,
    `r.repairedBy`,
    `r.repairedAt`,
    `r.assignRepairAt`,
    `r.qcStatus`,
    `r.inspectedBy`,
    `r.inspectedAt`,
    `r.assignInspectAt`,
    `r.estimatedGrade`,
    `r.costPrice`,
    `r.retailPrice`,
    `r.wholeSalePrice`,
    `r.retailMargin`,
    `r.wholeSaleMargin`,
    `r.marginWholeSaleBaht`,
    `r.marginRetailBaht`,
    `r.colorId`,
  ],
};

export enum AllJobStatus {
  SHOP_WAITING_PROCESS = 'Shop_รอดำเนินการ',
  AFS_ESTIMATING = 'AFS_กำลังประเมินราคา',
  SHOP_ESTIMATE_SUCCESS = 'Shop_ประเมินราคาสำเร็จ',
  SHOP_WAITING_APPROVE = 'Shop_รออนุมัติ',
  SHOP_WAITING_SIGNED = 'Shop_รอทำสัญญา',
  SHOP_NOT_APPROVE = 'Shop_ไม่อนุมัติ',
  SHOP_APPROVE_SUCCESS = 'Shop_อนุมัติสำเร็จ',
  SHOP_PURCHASED = 'Shop_รับซื้อสำเร็จ',
  AFS_QC = 'AFS_QC',
  AFS_REPAIR = 'AFS_Repair',
  AFS_INSPECTION = 'AFS_Inspection',
  SCM_READY = 'SCM_พร้อมจำหน่าย',
  SHOP_CANCEL = 'Shop_ยกเลิก',
  AO_CREATED = 'AO_CREATED',
  AO_RECEIVED = 'AO_RECEIVED',
}

export enum ProductStatus {
  READY = 'พร้อมขาย',
  SCARP = 'ขายซาก',
}

export enum JobExportType {
  REPORT = 'REPORT',
  PRODUCT = 'PRODUCT',
  SAP = 'SAP',
}

export const headerList = {
  PRODUCT: [
    'jobId',
    'deviceKey',
    'brand',
    'model',
    'currentGrade',
    'status',
    'costPrice',
    'retailPrice',
    'marginRetailBaht',
    'retailMargin',
    'wholeSalePrice',
    'marginWholeSaleBaht',
    'wholeSaleMargin',
    'inspectedAt',
  ],
  SAP: [
    'purchasedAt',
    'receivedAt',
    'jobId',
    'modelKey',
    'rom',
    'estimatedGrade',
    'model',
    'matCode',
  ],
};

export enum DeviceColorMap {
  black = 'ดำ',
  white = 'ขาว',
  gold = 'ทอง',
  silver = 'เงิน',
  green = 'เขียว',
  yellow = 'เหลือง',
  blue = 'ฟ้า',
  red = 'แดง',
  pink = 'ชมพู',
  orange = 'ส้ม',
  cyan = 'น้ำเงิน',
  purple = 'ม่วง',
}

export const exportFileName = {
  REPORT: 'TransactionReport',
  PRODUCT: 'ReadySaleProduct',
};
