import { join } from 'path';

import { Logger } from '@nestjs/common';
import { config } from 'dotenv';
import { DataSource } from 'typeorm';
import { PostgresConnectionOptions } from 'typeorm/driver/postgres/PostgresConnectionOptions';
import { SnakeNamingStrategy } from 'typeorm-naming-strategies';

config();

export const postgresDbConfig = (): PostgresConnectionOptions => ({
  type: 'postgres',
  host: process.env.POSTGRES_HOST ?? 'localhost',
  port: Number(process.env.POSTGRES_PORT ?? '5432'),
  username: process.env.POSTGRES_USER,
  password: process.env.POSTGRES_PASSWORD,
  database: process.env.POSTGRES_DATABASE,
  schema: process.env.POSTGRES_SCHEMA,
  logging: false,
  entities: [join(__dirname, '../entities/*.entity{.ts,.js}')],
  migrations: [join(__dirname, '../migrations/*{.ts,.js}')],
  synchronize: false,
  // ssl:
  //   process.env.NODE_ENV === 'development'
  //     ? false
  //     : { rejectUnauthorized: false },
  namingStrategy: new SnakeNamingStrategy(),
});

if (process.env.NODE_ENV === 'development') {
  Logger.debug(postgresDbConfig());
}

export default new DataSource(postgresDbConfig());
