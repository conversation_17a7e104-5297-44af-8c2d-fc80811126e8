import { MigrationInterface, QueryRunner } from "typeorm";

export class AddNewPermission1737442331854 implements MigrationInterface {
    name = 'AddNewPermission1737442331854'

    public async up(queryRunner: QueryRunner): Promise<void> {
          await queryRunner.query(
            `INSERT INTO "core"."permission" ("company_id", "permission_id", "label", "permission_group_id", "sort_index", "icon_name","view","create","update","delete","download","upload","icon_name_active") 
            VALUES ('WW', 'PS-0032', 'Transfer Voucher', 'PG-0010', 2,'tab-transfer-voucher-inactive', true, false, true, false, false, false, 'tab-transfer-voucher-active')`
          );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `DELETE FROM "core"."permission" WHERE company_id = $1 AND permission_id = $2 AND permission_group_id = $3`,
            ["WW", "PS-0032", "PG-0010"]
        );
    }

}
