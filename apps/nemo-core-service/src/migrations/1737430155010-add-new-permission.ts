import { MigrationInterface, QueryRunner } from "typeorm";

export class AddNewPermission1737430155010 implements MigrationInterface {
    name = 'AddNewPermission1737430155010'

    public async up(queryRunner: QueryRunner): Promise<void> {
          await queryRunner.query(
            `INSERT INTO "core"."permission" ("company_id", "permission_id", "label", "permission_group_id", "sort_index", "icon_name","view","create","update","delete","download","upload","icon_name_active") 
            VALUES ('WW', 'PS-0031', 'SAP Report', 'PG-0003', 2,'tab-sap-report-inactive', true, false, false, false, true, false, 'tab-sap-report-active')`
          );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `DELETE FROM "core"."role_permission" WHERE company_id = $1 AND permission_id = $2 AND permission_group_id = $3`,
            ["WW", "PS-0031", "PG-0003"]
        );
    }

}
