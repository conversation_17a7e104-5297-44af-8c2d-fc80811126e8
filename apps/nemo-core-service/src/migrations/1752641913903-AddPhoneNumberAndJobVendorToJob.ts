import { MigrationInterface, QueryRunner } from "typeorm";

export class AddPhoneNumberAndJobVendorToJob1752641913903 implements MigrationInterface {
    name = 'AddPhoneNumberAndJobVendorToJob1752641913903'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "core"."job" ADD "phone_number" text`);
        await queryRunner.query(`ALTER TABLE "core"."job" ADD "job_vendor" character varying(30) NOT NULL DEFAULT 'MASS'`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "core"."job" DROP COLUMN "job_vendor"`);
        await queryRunner.query(`ALTER TABLE "core"."job" DROP COLUMN "phone_number"`);
    }

}
