import { MigrationInterface, QueryRunner } from "typeorm";

export class RemoveCampaignCodeKey1737544336869 implements MigrationInterface {
    name = 'RemoveCampaignCodeKey1737544336869'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "core"."campaign_redemption_code" DROP CONSTRAINT "PK_db10537a3a4ca1804a7d935c96e"`);
        await queryRunner.query(`ALTER TABLE "core"."campaign_redemption_code" ADD CONSTRAINT "PK_75efd10b281239e95dff5b399b6" PRIMARY KEY ("company_id", "redemption_code")`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "core"."campaign_redemption_code" DROP CONSTRAINT "PK_75efd10b281239e95dff5b399b6"`);
        await queryRunner.query(`ALTER TABLE "core"."campaign_redemption_code" ADD CONSTRAINT "PK_db10537a3a4ca1804a7d935c96e" PRIMARY KEY ("company_id", "campaign_code", "redemption_code")`);
    }

}
