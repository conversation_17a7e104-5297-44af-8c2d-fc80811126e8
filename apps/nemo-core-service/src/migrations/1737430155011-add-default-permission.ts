import { MigrationInterface, QueryRunner } from "typeorm";

export class AddDefaultPermission1737430155011 implements MigrationInterface {
    name = 'AddDefaultPermission1737430155011'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `INSERT INTO "core"."role_permission" ("company_id", "role_id", "permission_id", "view", "create", "update", "delete", "download", "upload") 
            VALUES 
            ('WW', 'SUPER_ADMIN', 'PS-0031', true, false, false, false, true, false),
            ('WW', 'RECEIVE', 'PS-0031', true, false, false, false, true, false)`
          );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `DELETE FROM "core"."role_permission" WHERE company_id = $1 AND permission_id = $2 AND (role_id = $3 OR role_id = $4)`,
            ["WW", "PS-0031", "SUPER_ADMIN", "RECEIVE"]
        );
    }

}
