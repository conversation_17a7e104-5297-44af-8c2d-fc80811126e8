import { MigrationInterface, QueryRunner } from "typeorm";

export class AddSiteType1733220963471 implements MigrationInterface {
    name = 'AddSiteType1733220963471'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "core"."permission" DROP COLUMN "type"`);
        await queryRunner.query(`ALTER TABLE "core"."permission_group" ADD "type" character varying(20) NOT NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "core"."permission_group" DROP COLUMN "type"`);
        await queryRunner.query(`ALTER TABLE "core"."permission" ADD "type" character varying(20) NOT NULL`);
    }

}
