import { MigrationInterface, QueryRunner } from "typeorm";

export class RoleCreatedByNullable1733220857043 implements MigrationInterface {
    name = 'RoleCreatedByNullable1733220857043'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "core"."role" DROP CONSTRAINT "FK_404df4aa0a9c1fefa735e943ed4"`);
        await queryRunner.query(`ALTER TABLE "core"."role" DROP CONSTRAINT "FK_f80741046d7b8b3e0f6bb7c1433"`);
        await queryRunner.query(`ALTER TABLE "core"."role" ALTER COLUMN "created_by" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "core"."role" ALTER COLUMN "updated_by" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "core"."role" ADD CONSTRAINT "FK_404df4aa0a9c1fefa735e943ed4" FOREIGN KEY ("created_by", "company_id") REFERENCES "core"."user"("user_key","company_id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "core"."role" ADD CONSTRAINT "FK_f80741046d7b8b3e0f6bb7c1433" FOREIGN KEY ("updated_by", "company_id") REFERENCES "core"."user"("user_key","company_id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "core"."role" DROP CONSTRAINT "FK_f80741046d7b8b3e0f6bb7c1433"`);
        await queryRunner.query(`ALTER TABLE "core"."role" DROP CONSTRAINT "FK_404df4aa0a9c1fefa735e943ed4"`);
        await queryRunner.query(`ALTER TABLE "core"."role" ALTER COLUMN "updated_by" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "core"."role" ALTER COLUMN "created_by" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "core"."role" ADD CONSTRAINT "FK_f80741046d7b8b3e0f6bb7c1433" FOREIGN KEY ("updated_by", "company_id") REFERENCES "core"."user"("user_key","company_id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "core"."role" ADD CONSTRAINT "FK_404df4aa0a9c1fefa735e943ed4" FOREIGN KEY ("created_by", "company_id") REFERENCES "core"."user"("user_key","company_id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

}
