import { PermissionGroupEntity, SiteType } from "../entities";
import { MigrationInterface, QueryRunner } from "typeorm";

const permissionGroupList = [
{
    permissionGroupId: 'PG-0001',
    label: 'Admin',
    sortIndex: 0,
    type: SiteType.CMS,
    isInMenu: true
},
{
    permissionGroupId: 'PG-0002',
    label: 'Price estimator',
    sortIndex: 1,
    type: SiteType.CMS,
    isInMenu: true
},
{
    permissionGroupId: 'PG-0003',
    label: 'Receive',
    sortIndex: 2,
    type: SiteType.CMS,
    isInMenu: true
},
{
    permissionGroupId: 'PG-0004',
    label: 'QC',
    sortIndex: 3,
    type: SiteType.CMS,
    isInMenu: true
},
{
    permissionGroupId: 'PG-0005',
    label: 'Repair',
    sortIndex: 4,
    type: SiteType.CMS,
    isInMenu: true
},
{
    permissionGroupId: 'PG-0006',
    label: 'Inspection',
    sortIndex: 5,
    type: SiteType.CMS,
    isInMenu: true
},
{  
    permissionGroupId: 'PG-0007',
    label: 'Product',
    sortIndex: 6,
    type: SiteType.CMS,
    isInMenu: true
},
{
    permissionGroupId: 'PG-0008',
    label: 'Supply chain',
    sortIndex: 7,
    type: SiteType.CMS,
    isInMenu: true
},
{
    permissionGroupId: 'PG-0009',        
    label: 'การตั้งค่า',
    sortIndex: 8,
    type: SiteType.CMS,
    isInMenu: true
},
{
    permissionGroupId: 'PG-0010',
    label: 'Marketing',
    sortIndex: 9,
    type: SiteType.CMS,
    isInMenu: true
},
{
    permissionGroupId: 'PG-0011',
    label: 'Master data',
    sortIndex: 10,
    type: SiteType.CMS,
    isInMenu: true
},
{
    permissionGroupId: 'PG-0012',
    label: 'Help desk',
    sortIndex: 11,
    type: SiteType.CMS,
    isInMenu: true
},
{
    permissionGroupId: 'PG-0013',
    label: 'Frontshop',
    sortIndex: 0,
    type: SiteType.FRONTSHOP,
    isInMenu: true
},
{
    permissionGroupId: 'PG-0014',
    label: 'Manager',
    sortIndex: 1,
    type: SiteType.FRONTSHOP,
    isInMenu: true
},
{
    permissionGroupId: 'PG-0015',
    label: 'รับสินค้า',
    sortIndex: 2,
    type: SiteType.FRONTSHOP,
    isInMenu: true
},
{
    permissionGroupId: 'PG-0016',
    label: 'Help desk',
    sortIndex: 3,
    type: SiteType.FRONTSHOP,
    isInMenu: true
},
{
    permissionGroupId: 'PG-0017',
    label: 'สิทธิ์อื่นๆ',
    sortIndex: 99,
    type: SiteType.FRONTSHOP,
    isInMenu: false
}
]

export class SeedPermissionGroup1733279710795 implements MigrationInterface {
    name = 'SeedPermissionGroup1733279710795'

    public async up(queryRunner: QueryRunner): Promise<void> {
        const saveData = [] as PermissionGroupEntity[];

        permissionGroupList.forEach((group) => {
            const permissionGroup = new PermissionGroupEntity();
            permissionGroup.companyId = 'WW';
            permissionGroup.isInMenu = group.isInMenu;
            permissionGroup.permissionGroupId = group.permissionGroupId;
            permissionGroup.label = group.label;
            permissionGroup.sortIndex = group.sortIndex;
            permissionGroup.type = group.type;

            saveData.push(permissionGroup);
        })

        await queryRunner.manager.save(saveData);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.manager.delete(PermissionGroupEntity, {
            companyId: 'WW',
        });
    }

}
