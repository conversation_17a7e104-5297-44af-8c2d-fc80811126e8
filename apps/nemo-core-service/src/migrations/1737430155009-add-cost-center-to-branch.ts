import { MigrationInterface, QueryRunner } from "typeorm";

export class AddCostCenterToBranch1737430155009 implements MigrationInterface {
    name = 'AddCostCenterToBranch1737430155009'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "core"."branch" ADD "cost_center" character varying(10)`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "core"."branch" DROP COLUMN "cost_center"`);
    }

}
