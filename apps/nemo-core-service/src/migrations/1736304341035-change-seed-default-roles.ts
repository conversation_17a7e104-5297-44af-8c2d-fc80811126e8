import { RolePermissionEntity } from "../entities";
import { MigrationInterface, QueryRunner, In } from "typeorm";

const rolePermissionMap = [    
    {
        companyId: 'WW',
        roleId: 'SUPER_ADMIN',
        permissionId: 'PS-0014',
        download: false,
    },  
    {
        companyId: 'WW',
        roleId: 'SUPER_ADMIN',
        permissionId: 'PS-0019',
        download: true,
    },
    {
        companyId: 'WW',
        roleId: 'INSPECTION',
        permissionId: 'PS-0014',
        download: false,
    },
    {
        companyId: 'WW',
        roleId: 'MARKETING',
        permissionId: 'PS-0019',
        download: true,
    },
]

export class ChangeSeedDefaultRoles1736304341035 implements MigrationInterface {
    name = 'ChangeSeedDefaultRoles1736304341035'

    public async up(queryRunner: QueryRunner): Promise<void> {
        const saveData = [] as RolePermissionEntity[];
        rolePermissionMap.forEach((rolePermissions) => {
            const rolePermission = new RolePermissionEntity();
            rolePermission.companyId = rolePermissions.companyId;
            rolePermission.roleId = rolePermissions.roleId;
            rolePermission.permissionId = rolePermissions.permissionId;
            rolePermission.download = rolePermissions.download;

            saveData.push(rolePermission);
        })

        await queryRunner.manager.save(saveData);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        const saveData = [] as RolePermissionEntity[];
        rolePermissionMap.forEach((rolePermissions) => {
            const rolePermission = new RolePermissionEntity();
            rolePermission.companyId = rolePermissions.companyId;
            rolePermission.roleId = rolePermissions.roleId;
            rolePermission.permissionId = rolePermissions.permissionId;
            rolePermission.download = !rolePermissions.download;

            saveData.push(rolePermission);
        })

        await queryRunner.manager.save(saveData);
    }

}
