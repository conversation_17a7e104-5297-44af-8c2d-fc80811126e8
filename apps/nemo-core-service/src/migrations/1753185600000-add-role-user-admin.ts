import { MigrationInterface, QueryRunner } from "typeorm";
import {  RoleEntity, RolePermissionEntity, SiteType } from "../entities";


export class AddRoleUserAdmin1753185600000 implements MigrationInterface {
  name = 'AddRoleUserAdmin1753185600000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    const role = new RoleEntity()
    role.companyId = 'WW'
    role.roleId = 'USER_ADMIN'
    role.roleName = 'USER_ADMIN'
    role.type = SiteType.CMS
    await queryRunner.manager.save(RoleEntity,role)

    const rolePermissionPS0001 = new RolePermissionEntity()
    rolePermissionPS0001.companyId = 'WW'
    rolePermissionPS0001.roleId = 'USER_ADMIN'
    rolePermissionPS0001.permissionId = 'PS-0001'
    rolePermissionPS0001.view = true

    const rolePermissionPS0002 = new RolePermissionEntity()
    rolePermissionPS0002.companyId = 'WW'
    rolePermissionPS0002.roleId = 'USER_ADMIN'
    rolePermissionPS0002.permissionId = 'PS-0002'
    rolePermissionPS0002.view = true
    rolePermissionPS0002.update = true

    const rolePermissionPS0022 = new RolePermissionEntity()
    rolePermissionPS0022.companyId = 'WW'
    rolePermissionPS0022.roleId = 'USER_ADMIN'
    rolePermissionPS0022.permissionId = 'PS-0022'
    rolePermissionPS0022.view = true

    await queryRunner.manager.save([rolePermissionPS0001, rolePermissionPS0002, rolePermissionPS0022])

  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    const existingRole = await queryRunner.manager.findBy(RoleEntity, {
      companyId: 'WW',
      roleId: 'USER_ADMIN'
    });
    if (existingRole) {
        await queryRunner.manager.delete(RoleEntity, {
          companyId: 'WW',
          roleId: 'USER_ADMIN' 
        });
        await queryRunner.manager.delete(RolePermissionEntity, {
          companyId: 'WW',
          roleId: 'USER_ADMIN'
        });
    }
  }
}
