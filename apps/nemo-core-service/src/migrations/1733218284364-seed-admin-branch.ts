import { BranchEntity, branchType } from "../entities";
import { MigrationInterface, QueryRunner } from "typeorm";


export class SeedAdminBranch1733218284364 implements MigrationInterface {
    name = 'SeedAdminBranch1733218284364'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "core"."branch" ADD "cost_center" character varying(10)`);

        const branch = new BranchEntity();
        branch.companyId = 'WW';
        branch.branchId = 'ADMIN_BRANCH';
        branch.title = 'ADMIN BRANCH';
        branch.branchType = branchType.ADMIN
        
        await queryRunner.manager.save(branch);

        await queryRunner.query(`ALTER TABLE "core"."branch" DROP COLUMN "cost_center"`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.manager.delete(BranchEntity, {
            companyId: 'WW',
            branchId: 'ADMIN_BRANCH',
        });
    }

}
