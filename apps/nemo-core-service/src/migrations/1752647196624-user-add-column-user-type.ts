import { MigrationInterface, QueryRunner } from "typeorm";

export class UserAddColumnUserType1752647196624 implements MigrationInterface {
    name = 'UserAddColumnUserType1752647196624'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "core"."user" ADD "user_type" character varying(30) NOT NULL DEFAULT 'WW'`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "core"."user" DROP COLUMN "user_type"`);
    }

}
