import { RoleEntity, RolePermissionEntity, SiteType } from "../entities";
import { MigrationInterface, Query<PERSON><PERSON><PERSON>, In } from "typeorm";

const roleList: Partial<RoleEntity>[] = [
    {
        roleId: 'SALES',
        companyId: 'WW',
        roleName: 'SALES',
        type: SiteType.FRONTSHOP,
    },
    {
        roleId: 'MANAGER',
        companyId: 'WW',
        roleName: 'MANAGER',
        type: SiteType.FRONTSHOP
    },
    {
        roleId: 'SUPER_ADMIN',
        companyId: 'WW',
        roleName: 'SUPER_ADMIN',
        type: SiteType.CMS
    },
    {
        roleId: 'ADMIN',
        companyId: 'WW',
        roleName: 'ADMIN',
        type: SiteType.CMS
    },
    {
        roleId: 'PRICE_ESTIMATOR',
        companyId: 'WW',
        roleName: 'PRICE_ESTIMATOR',
        type: SiteType.CMS
    },
    {
        roleId: 'RECEIVE',
        companyId: 'WW',
        roleName: 'RECEIVE',
        type: SiteType.CMS
    },
    {
        roleId: 'QC',
        companyId: 'WW',
        roleName: 'QC',
        type: SiteType.CMS
    },
    {
        roleId: 'REPAIR',
        companyId: 'WW',
        roleName: 'REPAIR',
        type: SiteType.CMS
    },
    {
        roleId: 'INSPECTION',
        companyId: 'WW',
        roleName: 'INSPECTION',
        type: SiteType.CMS
    },
    {
        roleId: 'PRODUCT',
        companyId: 'WW',
        roleName: 'PRODUCT',
        type: SiteType.CMS
    },
    {
        roleId: 'SUPPLY_CHAIN',
        companyId: 'WW',
        roleName: 'SUPPLY_CHAIN',
        type: SiteType.CMS
    },
    {
        roleId: 'MARKETING',
        companyId: 'WW',
        roleName: 'MARKETING',
        type: SiteType.CMS
    },
    {
        roleId: 'RCC',
        companyId: 'WW',
        roleName: 'RCC',
        type: SiteType.CMS
    },
]

const rolePermissionMap = {
    'SALES' : [
        {
            companyId: 'WW',
            roleId: 'SALES',
            permissionId: 'PS-0024',
            view: true,
            create: true,
            update: false,
            delete: false,
            download: false,
            upload: false,
        },
        {
            companyId: 'WW',
            roleId: 'SALES',
            permissionId: 'PS-0025',
            view: true,
            create: false,
            update: true,
            delete: false,
            download: true,
            upload: false,
        },
        {
            companyId: 'WW',
            roleId: 'SALES',
            permissionId: 'PS-0027',
            view: true,
            create: false,
            update: false,
            delete: false,
            download: false,
            upload: false,
        },
        {
            companyId: 'WW',
            roleId: 'SALES',
            permissionId: 'PS-0028',
            view: true,
            create: false,
            update: true,
            delete: false,
            download: true,
            upload: false,
        },
        {
            companyId: 'WW',
            roleId: 'SALES',
            permissionId: 'PS-0029',
            view: true,
            create: true,
            update: false,
            delete: false,
            download: false,
            upload: false,
        },
    ],
    'MANAGER' : [
        {
            companyId: 'WW',
            roleId: 'MANAGER',
            permissionId: 'PS-0024',
            view: true,
            create: true,
            update: false,
            delete: false,
            download: false,
            upload: false,
        },
        {
            companyId: 'WW',
            roleId: 'MANAGER',
            permissionId: 'PS-0025',
            view: true,
            create: false,
            update: true,
            delete: false,
            download: true,
            upload: false,
        },
        {
            companyId: 'WW',
            roleId: 'MANAGER',
            permissionId: 'PS-0026',
            view: true,
            create: false,
            update: false,
            delete: false,
            download: false,
            upload: false,
        },
        {
            companyId: 'WW',
            roleId: 'MANAGER',
            permissionId: 'PS-0027',
            view: true,
            create: true,
            update: true,
            delete: false,
            download: false,
            upload: false,
        },
        {
            companyId: 'WW',
            roleId: 'MANAGER',
            permissionId: 'PS-0028',
            view: true,
            create: false,
            update: true,
            delete: false,
            download: true,
            upload: false,
        },
        {
            companyId: 'WW',
            roleId: 'MANAGER',
            permissionId: 'PS-0029',
            view: true,
            create: true,
            update: false,
            delete: false,
            download: false,
            upload: false,
        },
        {
            companyId: 'WW',
            roleId: 'MANAGER',
            permissionId: 'PS-0030',
            view: true,
            create: false,
            update: true,
            delete: false,
            download: false,
            upload: false,
        },
    ],
    'SUPER_ADMIN': [
        {
            companyId: 'WW',
            roleId: 'SUPER_ADMIN',
            permissionId: 'PS-0001',
            view: true,
            create: true,
            update: true,
            delete: false,
            download: false,
            upload: false,
        },
        {
            companyId: 'WW',
            roleId: 'SUPER_ADMIN',
            permissionId: 'PS-0002',
            view: true,
            create: false,
            update: true,
            delete: false,
            download: false,
            upload: true,
        },
        {
            companyId: 'WW',
            roleId: 'SUPER_ADMIN',
            permissionId: 'PS-0003',
            view: true,
            create: false,
            update: false,
            delete: false,
            download: true,
            upload: false,
        },
        {
            companyId: 'WW',
            roleId: 'SUPER_ADMIN',
            permissionId: 'PS-0004',
            view: true,
            create: false,
            update: true,
            delete: false,
            download: false,
            upload: false,
        },
        {
            companyId: 'WW',
            roleId: 'SUPER_ADMIN',
            permissionId: 'PS-0005',
            view: true,
            create: false,
            update: true,
            delete: false,
            download: false,
            upload: false,
        },
        {
            companyId: 'WW',
            roleId: 'SUPER_ADMIN',
            permissionId: 'PS-0006',
            view: true,
            create: false,
            update: true,
            delete: false,
            download: false,
            upload: false,
        },
        {
            companyId: 'WW',
            roleId: 'SUPER_ADMIN',
            permissionId: 'PS-0007',
            view: true,
            create: false,
            update: true,
            delete: false,
            download: false,
            upload: false,
        },
        {
            companyId: 'WW',
            roleId: 'SUPER_ADMIN',
            permissionId: 'PS-0008',
            view: true,
            create: false,
            update: true,
            delete: false,
            download: false,
            upload: false,
        },
        {
            companyId: 'WW',
            roleId: 'SUPER_ADMIN',
            permissionId: 'PS-0009',
            view: true,
            create: false,
            update: false,
            delete: false,
            download: false,
            upload: false,
        },
        {
            companyId: 'WW',
            roleId: 'SUPER_ADMIN',
            permissionId: 'PS-0010',
            view: true,
            create: false,
            update: true,
            delete: false,
            download: false,
            upload: false,
        },
        {
            companyId: 'WW',
            roleId: 'SUPER_ADMIN',
            permissionId: 'PS-0011',
            view: true,
            create: false,
            update: true,
            delete: false,
            download: false,
            upload: false,
        },
        {
            companyId: 'WW',
            roleId: 'SUPER_ADMIN',
            permissionId: 'PS-0012',
            view: true,
            create: false,
            update: false,
            delete: false,
            download: false,
            upload: false,
        },
        {
            companyId: 'WW',
            roleId: 'SUPER_ADMIN',
            permissionId: 'PS-0013',
            view: true,
            create: false,
            update: true,
            delete: false,
            download: true,
            upload: false,
        },
        {
            companyId: 'WW',
            roleId: 'SUPER_ADMIN',
            permissionId: 'PS-0014',
            view: true,
            create: false,
            update: true,
            delete: false,
            download: true,
            upload: false,
        },
        {
            companyId: 'WW',
            roleId: 'SUPER_ADMIN',
            permissionId: 'PS-0015',
            view: true,
            create: false,
            update: true,
            delete: false,
            download: true,
            upload: false,
        },
        {
            companyId: 'WW',
            roleId: 'SUPER_ADMIN',
            permissionId: 'PS-0016',
            view: true,
            create: true,
            update: true,
            delete: false,
            download: true,
            upload: false,
        },
        {
            companyId: 'WW',
            roleId: 'SUPER_ADMIN',
            permissionId: 'PS-0017',
            view: true,
            create: false,
            update: true,
            delete: false,
            download: false,
            upload: false,
        },
        {
            companyId: 'WW',
            roleId: 'SUPER_ADMIN',
            permissionId: 'PS-0018',
            view: true,
            create: false,
            update: false,
            delete: false,
            download: true,
            upload: true,
        },
        {
            companyId: 'WW',
            roleId: 'SUPER_ADMIN',
            permissionId: 'PS-0019',
            view: true,
            create: false,
            update: true,
            delete: false,
            download: false,
            upload: true,
        },
        {
            companyId: 'WW',
            roleId: 'SUPER_ADMIN',
            permissionId: 'PS-0020',
            view: true,
            create: false,
            update: true,
            delete: false,
            download: true,
            upload: true,
        },
        {
            companyId: 'WW',
            roleId: 'SUPER_ADMIN',
            permissionId: 'PS-0021',
            view: true,
            create: false,
            update: false,
            delete: false,
            download: true,
            upload: true,
        },
        {
            companyId: 'WW',
            roleId: 'SUPER_ADMIN',
            permissionId: 'PS-0022',
            view: true,
            create: false,
            update: false,
            delete: false,
            download: true,
            upload: true,
        },
        {
            companyId: 'WW',
            roleId: 'SUPER_ADMIN',
            permissionId: 'PS-0023',
            view: true,
            create: false,
            update: true,
            delete: false,
            download: false,
            upload: false,
        },
    ],
    'ADMIN': [
        {
            companyId: 'WW',
            roleId: 'ADMIN',
            permissionId: 'PS-0001',
            view: true,
            create: true,
            update: true,
            delete: false,
            download: false,
            upload: false,
        },
        {
            companyId: 'WW',
            roleId: 'ADMIN',
            permissionId: 'PS-0002',
            view: true,
            create: false,
            update: true,
            delete: false,
            download: false,
            upload: true,
        },
        {
            companyId: 'WW',
            roleId: 'ADMIN',
            permissionId: 'PS-0003',
            view: true,
            create: false,
            update: false,
            delete: false,
            download: true,
            upload: false,
        },
        {
            companyId: 'WW',
            roleId: 'ADMIN',
            permissionId: 'PS-0022',
            view: true,
            create: false,
            update: false,
            delete: false,
            download: true,
            upload: true,
        },
    ],
    'PRICE_ESTIMATOR': [
        {
            companyId: 'WW',
            roleId: 'PRICE_ESTIMATOR',
            permissionId: 'PS-0003',
            view: true,
            create: false,
            update: false,
            delete: false,
            download: true,
            upload: false,
        },
        {
            companyId: 'WW',
            roleId: 'PRICE_ESTIMATOR',
            permissionId: 'PS-0004',
            view: true,
            create: false,
            update: true,
            delete: false,
            download: false,
            upload: false,
        },
        {
            companyId: 'WW',
            roleId: 'PRICE_ESTIMATOR',
            permissionId: 'PS-0005',
            view: true,
            create: false,
            update: true,
            delete: false,
            download: false,
            upload: false,
        },
        {
            companyId: 'WW',
            roleId: 'PRICE_ESTIMATOR',
            permissionId: 'PS-0020',
            view: true,
            create: false,
            update: true,
            delete: false,
            download: true,
            upload: true,
        },
    ],
    'RECEIVE': [
        {
            companyId: 'WW',
            roleId: 'RECEIVE',
            permissionId: 'PS-0003',
            view: true,
            create: false,
            update: false,
            delete: false,
            download: true,
            upload: false,
        },
        {
            companyId: 'WW',
            roleId: 'RECEIVE',
            permissionId: 'PS-0006',
            view: true,
            create: false,
            update: true,
            delete: false,
            download: false,
            upload: false,
        },
        {
            companyId: 'WW',
            roleId: 'RECEIVE',
            permissionId: 'PS-0007',
            view: true,
            create: false,
            update: true,
            delete: false,
            download: false,
            upload: false,
        },
    ],
    'QC': [
        {
            companyId: 'WW',
            roleId: 'QC',
            permissionId: 'PS-0003',
            view: true,
            create: false,
            update: false,
            delete: false,
            download: true,
            upload: false,
        },
        {
            companyId: 'WW',
            roleId: 'QC',
            permissionId: 'PS-0008',
            view: true,
            create: false,
            update: true,
            delete: false,
            download: false,
            upload: false,
        },
        {
            companyId: 'WW',
            roleId: 'QC',
            permissionId: 'PS-0009',
            view: true,
            create: false,
            update: false,
            delete: false,
            download: false,
            upload: false,
        },
    ],
    'REPAIR': [
        {
            companyId: 'WW',
            roleId: 'REPAIR',
            permissionId: 'PS-0003',
            view: true,
            create: false,
            update: false,
            delete: false,
            download: true,
            upload: false,
        },
        {
            companyId: 'WW',
            roleId: 'REPAIR',
            permissionId: 'PS-0010',
            view: true,
            create: false,
            update: true,
            delete: false,
            download: false,
            upload: false,
        },
        {
            companyId: 'WW',
            roleId: 'REPAIR',
            permissionId: 'PS-0011',
            view: true,
            create: false,
            update: true,
            delete: false,
            download: false,
            upload: false,
        },
        {
            companyId: 'WW',
            roleId: 'REPAIR',
            permissionId: 'PS-0012',
            view: true,
            create: false,
            update: false,
            delete: false,
            download: false,
            upload: false,
        },
    ],
    'INSPECTION': [
        {
            companyId: 'WW',
            roleId: 'INSPECTION',
            permissionId: 'PS-0003',
            view: true,
            create: false,
            update: false,
            delete: false,
            download: true,
            upload: false,
        },
        {
            companyId: 'WW',
            roleId: 'INSPECTION',
            permissionId: 'PS-0013',
            view: true,
            create: false,
            update: true,
            delete: false,
            download: true,
            upload: false,
        },
        {
            companyId: 'WW',
            roleId: 'INSPECTION',
            permissionId: 'PS-0014',
            view: true,
            create: false,
            update: true,
            delete: false,
            download: true,
            upload: false,
        },
    ],
    'PRODUCT': [
        {
            companyId: 'WW',
            roleId: 'PRODUCT',
            permissionId: 'PS-0003',
            view: true,
            create: false,
            update: false,
            delete: false,
            download: true,
            upload: false,
        },
        {
            companyId: 'WW',
            roleId: 'PRODUCT',
            permissionId: 'PS-0015',
            view: true,
            create: false,
            update: true,
            delete: false,
            download: true,
            upload: false,
        },
        {
            companyId: 'WW',
            roleId: 'PRODUCT',
            permissionId: 'PS-0017',
            view: true,
            create: false,
            update: true,
            delete: false,
            download: false,
            upload: false,
        },
        {
            companyId: 'WW',
            roleId: 'PRODUCT',
            permissionId: 'PS-0021',
            view: true,
            create: false,
            update: false,
            delete: false,
            download: true,
            upload: true,
        },
    ],
    'SUPPLY_CHAIN': [
        {
            companyId: 'WW',
            roleId: 'SUPPLY_CHAIN',
            permissionId: 'PS-0003',
            view: true,
            create: false,
            update: false,
            delete: false,
            download: true,
            upload: false,
        },
        {
            companyId: 'WW',
            roleId: 'SUPPLY_CHAIN',
            permissionId: 'PS-0016',
            view: true,
            create: true,
            update: true,
            delete: false,
            download: true,
            upload: false,
        },
    ],
    'MARKETING': [
        {
            companyId: 'WW',
            roleId: 'MARKETING',
            permissionId: 'PS-0003',
            view: true,
            create: false,
            update: false,
            delete: false,
            download: true,
            upload: false,
        },
        {
            companyId: 'WW',
            roleId: 'MARKETING',
            permissionId: 'PS-0018',
            view: true,
            create: false,
            update: false,
            delete: false,
            download: true,
            upload: true,
        },
        {
            companyId: 'WW',
            roleId: 'MARKETING',
            permissionId: 'PS-0019',
            view: true,
            create: false,
            update: true,
            delete: false,
            download: false,
            upload: true,
        },
    ],
    'RCC': [
        {
            companyId: 'WW',
            roleId: 'RCC',
            permissionId: 'PS-0003',
            view: true,
            create: false,
            update: false,
            delete: false,
            download: true,
            upload: false,
        },
        {
            companyId: 'WW',
            roleId: 'RCC',
            permissionId: 'PS-0023',
            view: true,
            create: false,
            update: true,
            delete: false,
            download: false,
            upload: false,
        },
    ]
}

export class SeedDefaultRoles1733711195610 implements MigrationInterface {
    name = 'SeedDefaultRoles1733711195610'

    public async up(queryRunner: QueryRunner): Promise<void> {
        console.log(Object.keys(rolePermissionMap))
        await queryRunner.manager.save(RoleEntity, roleList);
        for (const roleId of Object.keys(rolePermissionMap)) {
            await queryRunner.manager.save(RolePermissionEntity, rolePermissionMap[roleId as keyof typeof rolePermissionMap]);
        }
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.manager.delete(RoleEntity, {
            companyId: 'WW',
            roleId: In(roleList.map(role => role.roleId)),
        });
    }

}
