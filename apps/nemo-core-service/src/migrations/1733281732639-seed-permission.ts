import { PermissionEntity } from "../entities";
import { MigrationInterface, QueryRunner } from "typeorm";

const permissionList = [
{
    permissionId: 'PS-0001',
    permissionGroupId: 'PG-0001',
    label: 'จัดการ Role',
    sortIndex: 0,
    view:  true,
    create: true,
    edit: true,
    delete: false,
    download: false,
    upload: false,
    iconName: 'tab-role-manage-inactive',
},
{
    permissionId: 'PS-0002',
    permissionGroupId: 'PG-0001',
    label: 'จัดการพนักงาน',
    sortIndex: 1,
    view:  true,
    create: false,
    edit: true,
    delete: false,
    download: false,
    upload: true,
    iconName: 'tab-staff-manage-inactive',
},
{
    permissionId: 'PS-0003',
    permissionGroupId: 'PG-0001',
    label: 'รายงาน',
    sortIndex: 2,
    view:  true,
    create: false,
    edit: false,
    delete: false,
    download: true,
    upload: false,
    iconName: 'tab-all-job-report-inactive',
},
{
    permissionId: 'PS-0004',
    permissionGroupId: 'PG-0002',
    label: 'รายการประเมินราคา',
    sortIndex: 0,
    view:  true,
    create: false,
    edit: true,
    delete: false,
    download: false,
    upload: false,
    iconName: 'tab-all-jobs-inactive',
},
{
    permissionId: 'PS-0005',
    permissionGroupId: 'PG-0002',
    label: 'งานของฉัน',
    sortIndex: 1,
    view:  true,
    create: false,
    edit: true,
    delete: false,
    download: false,
    upload: false,
    iconName: 'tab-my-jobs-inactive',
},
{
    permissionId: 'PS-0006',
    permissionGroupId: 'PG-0003',
    label: 'รายการรับสินค้า',
    sortIndex: 0,
    view:  true,
    create: false,
    edit: true,
    delete: false,
    download: false,
    upload: false,
    iconName: 'tab-appointment-status-inactive',
},
{
    permissionId: 'PS-0007',
    permissionGroupId: 'PG-0003',
    label: 'ตรวจรับสินค้ารายชิ้น',
    sortIndex: 1,
    view:  true,
    create: false,
    edit: true,
    delete: false,
    download: false,
    upload: false,
    iconName: 'tab-receive-by-job-inactive',
},
{
    permissionId: 'PS-0008',
    permissionGroupId: 'PG-0004',
    label: 'รายการ QC สินค้า',
    sortIndex: 0,
    view:  true,
    create: false,
    edit: true,
    delete: false,
    download: false,
    upload: false,
    iconName: 'tab-qc-status-inactive',
},
{
    permissionId: 'PS-0009',
    permissionGroupId: 'PG-0004',
    label: 'งาน QC ของฉัน',
    sortIndex: 1,
    view:  true,
    create: false,
    edit: false,
    delete: false,
    download: false,
    upload: false,
    iconName: 'tab-my-qc-job-inactive',
},
{
    permissionId: 'PS-0010',
    permissionGroupId: 'PG-0005',
    label: 'รอดำเนินการ',
    sortIndex: 0,
    view:  true,
    create: false,
    edit: true,
    delete: false,
    download: false,
    upload: false,
    iconName: 'tab-repair-inactive',
},
{
    permissionId: 'PS-0011',
    permissionGroupId: 'PG-0005',
    label: 'การดำเนินงานของฉัน',
    sortIndex: 1,
    view:  true,
    create: false,
    edit: true,
    delete: false,
    download: false,
    upload: false,
    iconName: 'tab-my-repair-job-inactive',
},
{
    permissionId: 'PS-0012',
    permissionGroupId: 'PG-0005',
    label: 'ดำเนินการเสร็จสิ้น',
    sortIndex: 2,
    view:  true,
    create: false,
    edit: false,
    delete: false,
    download: false,
    upload: false,
    iconName: 'tab-ready-for-inspect-inactive',
},
{
    permissionId: 'PS-0013',
    permissionGroupId: 'PG-0006',
    label: 'การตรวจสอบสินค้า',
    sortIndex: 0,
    view:  true,
    create: false,
    edit: true,
    delete: false,
    download: true,
    upload: false,
    iconName: 'tab-inspect-inactive',
},
{
    permissionId: 'PS-0014',
    permissionGroupId: 'PG-0006',
    label: 'การตรวจสอบสินค้าของฉัน',
    sortIndex: 1,
    view:  true,
    create: false,
    edit: true,
    delete: false,
    download: true,
    upload: false,
    iconName: 'tab-my-inspect-inactive',
},
{
    permissionId: 'PS-0015',
    permissionGroupId: 'PG-0007',
    label: 'สินค้ารอยืนยันราคา',
    sortIndex: 0,
    view:  true,
    create: false,
    edit: true,
    delete: false,
    download: true,
    upload: false,
    iconName: 'tab-product-inactive',
},
{
    permissionId: 'PS-0016',
    permissionGroupId: 'PG-0008',
    label: 'รายการ AO',
    sortIndex: 0,
    view:  true,
    create: true,
    edit: true,
    delete: false,
    download: true,
    upload: false,
    iconName: 'tab-ao-inactive',
},
{
    permissionId: 'PS-0017',
    permissionGroupId: 'PG-0009',
    label: 'ตั้งค่ามาตรฐาน',
    sortIndex: 0,
    view:  true,
    create: false,
    edit: true,
    delete: false,
    download: false,
    upload: false,
    iconName: 'tab-setting-operation-cost-inactive',
},
{
    permissionId: 'PS-0018',
    permissionGroupId: 'PG-0010',
    label: 'จัดการ Voucher',
    sortIndex: 0,
    view:  true,
    create: false,
    edit: false,
    delete: false,
    download: true,
    upload: true,
    iconName: 'tab-voucher-inactive',
},
{
    permissionId: 'PS-0019',
    permissionGroupId: 'PG-0010',
    label: 'Trade up campaign',
    sortIndex: 1,
    view:  true,
    create: false,
    edit: true,
    delete: false,
    download: false,
    upload: true,
    iconName: 'tab-trade-up-campaign-inactive',
},
{
    permissionId: 'PS-0020',
    permissionGroupId: 'PG-0011',
    label: 'ตารางราคา',
    sortIndex: 0,
    view:  true,
    create: false,
    edit: true,
    delete: false,
    download: true,
    upload: true,
    iconName: 'tab-model-price-inactive',
},
{
    permissionId: 'PS-0021',
    permissionGroupId: 'PG-0011',
    label: 'ตาราง Average cost',
    sortIndex: 1,
    view:  true,
    create: false,
    edit: false,
    delete: false,
    download: true,
    upload: true,
    iconName: 'tab-average-cost-report-inactive',
},
{
    permissionId: 'PS-0022',
    permissionGroupId: 'PG-0011',
    label: 'จัดการสาขา',
    sortIndex: 2,
    view:  true,
    create: false,
    edit: false,
    delete: false,
    download: true,
    upload: true,
    iconName: 'tab-branches-inactive',
},
{
    permissionId: 'PS-0023',
    permissionGroupId: 'PG-0012',
    label: 'แจ้งปัญหา',
    sortIndex: 0,
    view:  true,
    create: false,
    edit: true,
    delete: false,
    download: false,
    upload: false,
    iconName: 'tab-help-desk-inactive',
},
{
    permissionId: 'PS-0024',
    permissionGroupId: 'PG-0013',
    label: 'งานของฉัน',
    sortIndex: 0,
    view:  true,
    create: true,
    edit: false,
    delete: false,
    download: false,
    upload: false,
    iconName: 'ic-home-inactive',
},
{
    permissionId: 'PS-0025',
    permissionGroupId: 'PG-0013',
    label: 'ประวัติการทำงาน',
    sortIndex: 1,
    view:  true,
    create: false,
    edit: true,
    delete: false,
    download: true,
    upload: false,
    iconName: 'ic-box-inactive',
},
{
    permissionId: 'PS-0026',
    permissionGroupId: 'PG-0014',
    label: 'งานของทีม',
    sortIndex: 0,
    view:  true,
    create: false,
    edit: false,
    delete: false,
    download: false,
    upload: false,
    iconName: 'ic-team-inactive',
},
{
    permissionId: 'PS-0027',
    permissionGroupId: 'PG-0014',
    label: 'รายการ DO',
    sortIndex: 1,
    view:  true,
    create: true,
    edit: true,
    delete: false,
    download: false,
    upload: false,
    iconName: 'ic-truck-inactive',
},
{
    permissionId: 'PS-0028',
    permissionGroupId: 'PG-0015',
    label: 'รายการสินค้า AO',
    sortIndex: 0,
    view:  true,
    create: false,
    edit: true,
    delete: false,
    download: true,
    upload: false,
    iconName: 'tab-receive-by-job-inactive',
},
{
    permissionId: 'PS-0029',
    permissionGroupId: 'PG-0016',
    label: 'แจ้งปัญหา',
    sortIndex: 0,
    view:  true,
    create: true,
    edit: false,
    delete: false,
    download: false,
    upload: false,
    iconName: 'tab-help-desk-inactive',
},
{
    permissionId: 'PS-0030',
    permissionGroupId: 'PG-0017',
    label: 'การอนุมัติ OCR',
    sortIndex: 0,
    view:  true,
    create: false,
    edit: true,
    delete: false,
    download: false,
    upload: false,
}
]

export class SeedPermission1733281732639 implements MigrationInterface {
    name = 'SeedPermission1733281732639'

    public async up(queryRunner: QueryRunner): Promise<void> {
        const saveData = [] as PermissionEntity[]; 
        // const saveData = [] as PermissionGroupEntity[];

        await queryRunner.query(`ALTER TABLE "core"."permission" ADD "icon_name_active" character varying(200)`);

        permissionList.forEach((group) => {
            const permissionGroup = new PermissionEntity();
            permissionGroup.companyId = 'WW';
            permissionGroup.label = group.label;
            permissionGroup.sortIndex = group.sortIndex;
            permissionGroup.permissionId = group.permissionId;
            permissionGroup.permissionGroupId = group.permissionGroupId;
            permissionGroup.view = group.view;
            permissionGroup.create = group.create;
            permissionGroup.update = group.edit;
            permissionGroup.delete = group.delete;
            permissionGroup.download = group.download;
            permissionGroup.upload = group.upload;
            permissionGroup.iconName = group.iconName;


            saveData.push(permissionGroup);
        })

        await queryRunner.manager.save(saveData);

        await queryRunner.query(`ALTER TABLE "core"."permission" DROP COLUMN "icon_name_active"`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.manager.delete(PermissionEntity, {
            companyId: 'WW',
        });
    }

}
