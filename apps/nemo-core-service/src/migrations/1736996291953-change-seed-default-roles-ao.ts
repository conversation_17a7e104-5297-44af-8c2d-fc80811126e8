import { RolePermissionEntity } from "../entities";
import { MigrationInterface, QueryRunner } from "typeorm";


export class ChangeSeedDefaultRolesAO1736996291953 implements MigrationInterface {
    name = 'ChangeSeedDefaultRolesAO1736996291953'

    public async up(queryRunner: QueryRunner): Promise<void> {
        const aoRoles = await queryRunner.manager.findBy(RolePermissionEntity,{
            companyId: 'WW', permissionId: 'PS-0028'
        })

        aoRoles.forEach((role) => {
            role.download = false;
        })

        await queryRunner.manager.save(aoRoles);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        const aoRoles = await queryRunner.manager.findBy(RolePermissionEntity,{
            companyId: 'WW', permissionId: 'PS-0028'
        })

        aoRoles.forEach((role) => {
            role.download = true;
        })

        await queryRunner.manager.save(aoRoles);
    }

}
