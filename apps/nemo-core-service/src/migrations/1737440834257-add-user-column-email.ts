import { MigrationInterface, QueryRunner } from "typeorm";

export class AddUserColumnEmail1737440834257 implements MigrationInterface {
    name = 'AddUserColumnEmail1737440834257'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "core"."user" ADD "email" character varying(200)`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "core"."user" DROP COLUMN "email"`);
    }

}
