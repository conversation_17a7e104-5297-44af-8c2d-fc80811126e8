import { MigrationInterface, QueryRunner } from "typeorm";

export class RemoveParentColumnsFinal1752572985407 implements MigrationInterface {
    name = 'RemoveParentColumnsFinal1752572985407'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "core"."model_master" DROP CONSTRAINT "FK_model_master_parent"`);
        await queryRunner.query(`ALTER TABLE "core"."model_master" DROP COLUMN "parent_company_id"`);
        await queryRunner.query(`ALTER TABLE "core"."model_master" DROP COLUMN "parent_model_key"`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "core"."model_master" ADD "parent_model_key" character varying(200)`);
        await queryRunner.query(`ALTER TABLE "core"."model_master" ADD "parent_company_id" character varying(200)`);
        await queryRunner.query(`ALTER TABLE "core"."model_master" ADD CONSTRAINT "FK_model_master_parent" FOREIGN KEY ("parent_company_id", "parent_model_key") REFERENCES "core"."model_master"("company_id","model_key") ON DELETE SET NULL ON UPDATE NO ACTION`);
    }

}
