import { PermissionEntity } from "../entities";
import { MigrationInterface, QueryRunner } from "typeorm";


export class ChangeSeedPermission1736304341034 implements MigrationInterface {
    name = 'ChangeSeedPermission1736304341034'

    public async up(queryRunner: QueryRunner): Promise<void> {
        const permissionInspect = new PermissionEntity();
        permissionInspect.companyId = 'WW';
        permissionInspect.label = 'การตรวจสอบสินค้า\nของฉัน';
        permissionInspect.permissionId = 'PS-0014';
        permissionInspect.download = false

        const permissionTradeUp = new PermissionEntity();
        permissionTradeUp.companyId = 'WW';
        permissionTradeUp.permissionId = 'PS-0019';
        permissionTradeUp.download = true
        await queryRunner.manager.save([permissionInspect, permissionTradeUp]);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        const permissionInspect = new PermissionEntity();
        permissionInspect.companyId = 'WW';
        permissionInspect.label = 'การตรวจสอบสินค้าของฉัน';
        permissionInspect.permissionId = 'PS-0014';
        permissionInspect.download = true

        const permissionTradeUp = new PermissionEntity();
        permissionTradeUp.companyId = 'WW';
        permissionTradeUp.permissionId = 'PS-0019';
        permissionTradeUp.download = false
        await queryRunner.manager.save([permissionInspect, permissionTradeUp]);
    }

}
