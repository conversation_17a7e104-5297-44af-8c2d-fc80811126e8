import { MigrationInterface, QueryRunner } from "typeorm";

export class AddOwnerNameAndParentRelationsToModelMaster1752552615239 implements MigrationInterface {
    name = 'AddOwnerNameAndParentRelationsToModelMaster1752552615239'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Add new columns to model_master table
        await queryRunner.query(`ALTER TABLE "core"."model_master" ADD "owner_name" character varying(30) NOT NULL DEFAULT 'WW'`);
        await queryRunner.query(`ALTER TABLE "core"."model_master" ADD "parent_company_id" character varying(200)`);
        await queryRunner.query(`ALTER TABLE "core"."model_master" ADD "parent_model_key" character varying(200)`);
        
        // Add foreign key constraint for self-relation
        await queryRunner.query(`ALTER TABLE "core"."model_master" ADD CONSTRAINT "FK_model_master_parent" FOREIGN KEY ("parent_company_id", "parent_model_key") REFERENCES "core"."model_master"("company_id","model_key") ON DELETE SET NULL ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Drop foreign key constraint
        await queryRunner.query(`ALTER TABLE "core"."model_master" DROP CONSTRAINT "FK_model_master_parent"`);
        
        // Drop columns
        await queryRunner.query(`ALTER TABLE "core"."model_master" DROP COLUMN "parent_model_key"`);
        await queryRunner.query(`ALTER TABLE "core"."model_master" DROP COLUMN "parent_company_id"`);
        await queryRunner.query(`ALTER TABLE "core"."model_master" DROP COLUMN "owner_name"`);
    }
}
