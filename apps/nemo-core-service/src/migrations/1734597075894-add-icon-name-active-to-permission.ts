import { MigrationInterface, QueryRunner } from "typeorm";

export class AddIconNameActiveToPermission1734597075894 implements MigrationInterface {
    name = 'AddIconNameActiveToPermission1734597075894'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "core"."permission" ADD "icon_name_active" character varying(200)`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "core"."permission" DROP COLUMN "icon_name_active"`);
    }

}
