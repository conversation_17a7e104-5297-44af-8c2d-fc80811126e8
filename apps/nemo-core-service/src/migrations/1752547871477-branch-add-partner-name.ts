import { MigrationInterface, QueryRunner } from "typeorm";

export class BranchAddPartnerName1752547871477 implements MigrationInterface {
    name = 'BranchAddPartnerName1752547871477'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "core"."branch" ADD "partner_name" character varying(30)`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "core"."branch" DROP COLUMN "partner_name"`);
    }

}
