import { MigrationInterface, QueryRunner } from "typeorm";

export class Roles1733126969277 implements MigrationInterface {
    name = 'Roles1733126969277'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "core"."permission_group" ("created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "company_id" character varying(200) NOT NULL, "permission_group_id" character varying(200) NOT NULL, "label" character varying(200) NOT NULL, "sort_index" integer NOT NULL, "is_in_menu" boolean NOT NULL DEFAULT false, CONSTRAINT "PK_af4af58a2528cf0040ceb59b27b" PRIMARY KEY ("company_id", "permission_group_id"))`);
        await queryRunner.query(`CREATE TABLE "core"."permission" ("created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "company_id" character varying(200) NOT NULL, "permission_id" character varying(200) NOT NULL, "label" character varying(200) NOT NULL, "permission_group_id" character varying(200) NOT NULL, "sort_index" integer NOT NULL, "type" character varying(20) NOT NULL, "icon_name" character varying(200), "view" boolean NOT NULL DEFAULT false, "create" boolean NOT NULL DEFAULT false, "update" boolean NOT NULL DEFAULT false, "delete" boolean NOT NULL DEFAULT false, "download" boolean NOT NULL DEFAULT false, "upload" boolean NOT NULL DEFAULT false, CONSTRAINT "PK_00c130ff0a83650d4c4ba30f152" PRIMARY KEY ("company_id", "permission_id"))`);
        await queryRunner.query(`CREATE TABLE "core"."role_permission" ("created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "company_id" character varying(200) NOT NULL, "role_id" character varying(200) NOT NULL, "permission_id" character varying(200) NOT NULL, "view" boolean NOT NULL DEFAULT false, "create" boolean NOT NULL DEFAULT false, "update" boolean NOT NULL DEFAULT false, "delete" boolean NOT NULL DEFAULT false, "download" boolean NOT NULL DEFAULT false, "upload" boolean NOT NULL DEFAULT false, CONSTRAINT "PK_d1faca7e0e8959081e470cbf705" PRIMARY KEY ("company_id", "role_id", "permission_id"))`);
        await queryRunner.query(`CREATE TABLE "core"."role" ("created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "company_id" character varying(200) NOT NULL, "role_id" character varying(200) NOT NULL, "role_name" character varying(200) NOT NULL, "type" character varying(20) NOT NULL, "created_by" character varying(200) NOT NULL, "updated_by" character varying(200) NOT NULL, CONSTRAINT "UQ_875233359ab4eb180f3ed8f4566" UNIQUE ("company_id", "role_name", "type"), CONSTRAINT "PK_cff0d1b0b39dd0741dd5e62ba66" PRIMARY KEY ("company_id", "role_id"))`);
        await queryRunner.query(`CREATE TABLE "core"."user_role_branch" ("created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "company_id" character varying(200) NOT NULL, "user_key" character varying(200) NOT NULL, "role_id" character varying(200) NOT NULL, "branch_id" character varying(200) NOT NULL, CONSTRAINT "PK_214df7e32c7ea58089fc0e21c2e" PRIMARY KEY ("company_id", "user_key", "role_id", "branch_id"))`);
        await queryRunner.query(`ALTER TABLE "core"."permission_group" ADD CONSTRAINT "FK_6e1196ac0c29b90dcbfe1ab47a9" FOREIGN KEY ("company_id") REFERENCES "core"."company"("company_id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "core"."permission" ADD CONSTRAINT "FK_55de2163b6d463bccac8b27941c" FOREIGN KEY ("company_id") REFERENCES "core"."company"("company_id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "core"."permission" ADD CONSTRAINT "FK_f78d84357f542d6c08c4f608e47" FOREIGN KEY ("permission_group_id", "company_id") REFERENCES "core"."permission_group"("permission_group_id","company_id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "core"."role_permission" ADD CONSTRAINT "FK_dfffa50b50e1e9addff78fb253d" FOREIGN KEY ("company_id") REFERENCES "core"."company"("company_id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "core"."role_permission" ADD CONSTRAINT "FK_9f2c36be7d2d5813aca426a79a9" FOREIGN KEY ("company_id", "permission_id") REFERENCES "core"."permission"("company_id","permission_id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "core"."role_permission" ADD CONSTRAINT "FK_b7f43fcef3b46a7db7ac44c8a5f" FOREIGN KEY ("company_id", "role_id") REFERENCES "core"."role"("company_id","role_id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "core"."role" ADD CONSTRAINT "FK_6c1faaaceb897cf61c5c91dcebf" FOREIGN KEY ("company_id") REFERENCES "core"."company"("company_id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "core"."role" ADD CONSTRAINT "FK_404df4aa0a9c1fefa735e943ed4" FOREIGN KEY ("created_by", "company_id") REFERENCES "core"."user"("user_key","company_id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "core"."role" ADD CONSTRAINT "FK_f80741046d7b8b3e0f6bb7c1433" FOREIGN KEY ("updated_by", "company_id") REFERENCES "core"."user"("user_key","company_id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "core"."user_role_branch" ADD CONSTRAINT "FK_f20c050f3027375dbbacaadcca1" FOREIGN KEY ("company_id") REFERENCES "core"."company"("company_id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "core"."user_role_branch" ADD CONSTRAINT "FK_14e1bd5d55b96598fa7a54e3593" FOREIGN KEY ("company_id", "user_key") REFERENCES "core"."user"("company_id","user_key") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "core"."user_role_branch" ADD CONSTRAINT "FK_84391aeda5ca9c9f83fe27f0a4c" FOREIGN KEY ("company_id", "role_id") REFERENCES "core"."role"("company_id","role_id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "core"."user_role_branch" ADD CONSTRAINT "FK_58236e8ca79aa75ecf04a51fdba" FOREIGN KEY ("company_id", "branch_id") REFERENCES "core"."branch"("company_id","branch_id") ON DELETE CASCADE ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "core"."user_role_branch" DROP CONSTRAINT "FK_58236e8ca79aa75ecf04a51fdba"`);
        await queryRunner.query(`ALTER TABLE "core"."user_role_branch" DROP CONSTRAINT "FK_84391aeda5ca9c9f83fe27f0a4c"`);
        await queryRunner.query(`ALTER TABLE "core"."user_role_branch" DROP CONSTRAINT "FK_14e1bd5d55b96598fa7a54e3593"`);
        await queryRunner.query(`ALTER TABLE "core"."user_role_branch" DROP CONSTRAINT "FK_f20c050f3027375dbbacaadcca1"`);
        await queryRunner.query(`ALTER TABLE "core"."role" DROP CONSTRAINT "FK_f80741046d7b8b3e0f6bb7c1433"`);
        await queryRunner.query(`ALTER TABLE "core"."role" DROP CONSTRAINT "FK_404df4aa0a9c1fefa735e943ed4"`);
        await queryRunner.query(`ALTER TABLE "core"."role" DROP CONSTRAINT "FK_6c1faaaceb897cf61c5c91dcebf"`);
        await queryRunner.query(`ALTER TABLE "core"."role_permission" DROP CONSTRAINT "FK_b7f43fcef3b46a7db7ac44c8a5f"`);
        await queryRunner.query(`ALTER TABLE "core"."role_permission" DROP CONSTRAINT "FK_9f2c36be7d2d5813aca426a79a9"`);
        await queryRunner.query(`ALTER TABLE "core"."role_permission" DROP CONSTRAINT "FK_dfffa50b50e1e9addff78fb253d"`);
        await queryRunner.query(`ALTER TABLE "core"."permission" DROP CONSTRAINT "FK_f78d84357f542d6c08c4f608e47"`);
        await queryRunner.query(`ALTER TABLE "core"."permission" DROP CONSTRAINT "FK_55de2163b6d463bccac8b27941c"`);
        await queryRunner.query(`ALTER TABLE "core"."permission_group" DROP CONSTRAINT "FK_6e1196ac0c29b90dcbfe1ab47a9"`);
        await queryRunner.query(`DROP TABLE "core"."user_role_branch"`);
        await queryRunner.query(`DROP TABLE "core"."role"`);
        await queryRunner.query(`DROP TABLE "core"."role_permission"`);
        await queryRunner.query(`DROP TABLE "core"."permission"`);
        await queryRunner.query(`DROP TABLE "core"."permission_group"`);
    }

}
