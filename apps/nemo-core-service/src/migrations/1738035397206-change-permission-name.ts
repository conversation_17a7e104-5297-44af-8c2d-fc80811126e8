import { MigrationInterface, QueryRunner } from "typeorm";

export class ChangePermissionName1738035397206 implements MigrationInterface {
    name = 'ChangePermissionName1738035397206'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `UPDATE  "core"."permission"
            SET label = 'Transfer voucher'
            WHERE company_id = $1 AND permission_id = $2`,
            ["WW", "PS-0032"]
          );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `UPDATE  "core"."permission"
            SET label = 'Transfer Voucher'
            WHERE company_id = $1 AND permission_id = $2`,
            ["WW", "PS-0032"]
          );
    }

}
