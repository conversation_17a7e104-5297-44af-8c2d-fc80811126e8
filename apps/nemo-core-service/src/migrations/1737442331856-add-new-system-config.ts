import { MigrationInterface, QueryRunner } from "typeorm";

export class AddNewSystemConfig1737442331854 implements MigrationInterface {
    name = 'AddNewSystemConfig1737442331854'

    public async up(queryRunner: QueryRunner): Promise<void> {
          await queryRunner.query(
            `INSERT INTO "core"."system_config" ("company_id", "config_key", "data") 
            VALUES ('WW', 'sla_time', $1)`,
            [
                {
                    priceEstimatorJob: 300000, 
                    managerApprove: 300000
                }
            ]
          );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `DELETE FROM "core"."system_config" WHERE company_id = $1 AND config_key = $2`,
            ["WW", "sla_time"]
        );
    }

}
