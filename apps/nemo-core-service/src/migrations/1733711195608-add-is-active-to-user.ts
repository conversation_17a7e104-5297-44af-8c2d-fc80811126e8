import { MigrationInterface, QueryRunner } from "typeorm";

export class AddIsActiveToUser1733711195608 implements MigrationInterface {
    name = 'AddIsActiveToUser1733711195608'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "core"."user" ADD "is_active" boolean NOT NULL DEFAULT true`);
        await queryRunner.query(`ALTER TABLE "core"."user" ADD "created_by" character varying(200)`);
        await queryRunner.query(`COMMENT ON COLUMN "core"."user"."created_by" IS 'identifier user'`);
        await queryRunner.query(`ALTER TABLE "core"."user" ADD "updated_by" character varying(200)`);
        await queryRunner.query(`COMMENT ON COLUMN "core"."user"."updated_by" IS 'identifier user'`);
        await queryRunner.query(`ALTER TABLE "core"."user" ADD CONSTRAINT "FK_ffbcc0337ebff526dc8bcbb0ae0" FOREIGN KEY ("created_by", "company_id") REFERENCES "core"."user"("user_key","company_id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "core"."user" ADD CONSTRAINT "FK_4efaec0743f08acdf6378aa84b4" FOREIGN KEY ("updated_by", "company_id") REFERENCES "core"."user"("user_key","company_id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "core"."user" DROP CONSTRAINT "FK_4efaec0743f08acdf6378aa84b4"`);
        await queryRunner.query(`ALTER TABLE "core"."user" DROP CONSTRAINT "FK_ffbcc0337ebff526dc8bcbb0ae0"`);
        await queryRunner.query(`COMMENT ON COLUMN "core"."user"."updated_by" IS 'identifier user'`);
        await queryRunner.query(`ALTER TABLE "core"."user" DROP COLUMN "updated_by"`);
        await queryRunner.query(`COMMENT ON COLUMN "core"."user"."created_by" IS 'identifier user'`);
        await queryRunner.query(`ALTER TABLE "core"."user" DROP COLUMN "created_by"`);
        await queryRunner.query(`ALTER TABLE "core"."user" DROP COLUMN "is_active"`);
    }

}
