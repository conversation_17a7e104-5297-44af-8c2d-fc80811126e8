import { PermissionEntity } from "../entities";
import { MigrationInterface, QueryRunner } from "typeorm";

const permissionList = [
{
    permissionId: 'PS-0001',
    iconNameActive: 'tab-role-manage-active',
},
{
    permissionId: 'PS-0002',
    iconNameActive: 'tab-staff-manage-active',
},
{
    permissionId: 'PS-0003',
    iconNameActive: 'tab-all-job-report-active',
},
{
    permissionId: 'PS-0004',
    iconNameActive: 'tab-all-jobs-active',
},
{
    permissionId: 'PS-0005',
    iconNameActive: 'tab-my-jobs-active',
},
{
    permissionId: 'PS-0006',
    iconNameActive: 'tab-appointment-status-active',
},
{
    permissionId: 'PS-0007',
    iconNameActive: 'tab-receive-by-job-active',
},
{
    permissionId: 'PS-0008',
    iconNameActive: 'tab-qc-status-active',
},
{
    permissionId: 'PS-0009',
    iconNameActive: 'tab-my-qc-job-active',
},
{
    permissionId: 'PS-0010',
    iconNameActive: 'tab-repair-active',
},
{
    permissionId: 'PS-0011',
    iconNameActive: 'tab-my-repair-job-active',
},
{
    permissionId: 'PS-0012',
    iconNameActive: 'tab-ready-for-inspect-active',
},
{
    permissionId: 'PS-0013',
    iconNameActive: 'tab-inspect-active',
},
{
    permissionId: 'PS-0014',
    iconNameActive: 'tab-my-inspect-active',
},
{
    permissionId: 'PS-0015',
    iconNameActive: 'tab-product-active',
},
{
    permissionId: 'PS-0016',
    iconNameActive: 'tab-ao-active',
},
{
    permissionId: 'PS-0017',
    iconNameActive: 'tab-setting-operation-cost-active',
},
{
    permissionId: 'PS-0018',
    iconNameActive: 'tab-voucher-active',
},
{
    permissionId: 'PS-0019',
    iconNameActive: 'tab-trade-up-campaign-active',
},
{
    permissionId: 'PS-0020',
    iconNameActive: 'tab-model-price-active',
},
{
    permissionId: 'PS-0021',
    iconNameActive: 'tab-average-cost-report-active',
},
{
    permissionId: 'PS-0022',
    iconNameActive: 'tab-branches-active',
},
{
    permissionId: 'PS-0023',
    iconNameActive: 'tab-help-desk-active',
},
{
    permissionId: 'PS-0024',
    iconNameActive: 'ic-home-active',
},
{
    permissionId: 'PS-0025',
    iconNameActive: 'ic-box-active',
},
{
    permissionId: 'PS-0026',
    iconNameActive: 'ic-team-active',
},
{
    permissionId: 'PS-0027',
    iconNameActive: 'ic-truck-active',
},
{
    permissionId: 'PS-0028',
    iconNameActive: 'tab-receive-by-job-active',
},
{
    permissionId: 'PS-0029',
    iconNameActive: 'tab-help-desk-active',
}
]

export class SeedIconActive1734597636033 implements MigrationInterface {
    name = 'SeedIconActive1734597636033'

    public async up(queryRunner: QueryRunner): Promise<void> {
        const saveData = [] as PermissionEntity[]; 
        // const saveData = [] as PermissionGroupEntity[];

        permissionList.forEach((group) => {
            const permissionGroup = new PermissionEntity();
            permissionGroup.companyId = 'WW';
            permissionGroup.iconNameActive = group.iconNameActive;
            permissionGroup.permissionId = group.permissionId;

            saveData.push(permissionGroup);
        })

        await queryRunner.manager.save(saveData);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // await queryRunner.manager.delete(PermissionEntity, {
        //     companyId: 'WW',
        // });
        await queryRunner.manager.createQueryBuilder()
        .update(PermissionEntity)
        .set({ iconNameActive: null })
        .where("companyId = 'WW'")
        .execute();
    }

}
