import { PermissionEntity } from "../entities";
import { MigrationInterface, QueryRunner } from "typeorm";


export class ChangeSeedPermissionAO1736996291952 implements MigrationInterface {
    name = 'ChangeSeedPermissionAO1736996291952'

    public async up(queryRunner: QueryRunner): Promise<void> {
        const permissionAO = new PermissionEntity();
        permissionAO.companyId = 'WW';
        permissionAO.permissionId = 'PS-0028';
        permissionAO.download = false

        await queryRunner.manager.save([permissionAO]);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        const permissionAO = new PermissionEntity();
        permissionAO.companyId = 'WW';
        permissionAO.permissionId = 'PS-0028';
        permissionAO.download = true

        await queryRunner.manager.save([permissionAO]);
    }

}
