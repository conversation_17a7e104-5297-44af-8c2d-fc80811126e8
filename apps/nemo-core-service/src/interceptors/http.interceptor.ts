import {
  Injectable,
  NestInterceptor,
  ExecutionContext,
  CallHandler,
  StreamableFile,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { BaseResponse } from 'contracts';

@Injectable()
export class HttpInterceptor implements NestInterceptor {
  intercept(
    _context: ExecutionContext,
    next: CallHandler,
  ): Observable<BaseResponse | StreamableFile> {
    return next.handle().pipe(
      map((data) => {
        // Make response
        const response: BaseResponse = {
          code: '1000',
          success: true,
          message: 'Success',
          data,
          error: null,
        };

        // Return response
        return data instanceof StreamableFile ? data : response;
      }),
    );
  }
}
