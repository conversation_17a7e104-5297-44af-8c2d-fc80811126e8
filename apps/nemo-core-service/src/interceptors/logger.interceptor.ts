import {
  CallHandler,
  ExecutionContext,
  Injectable,
  NestInterceptor,
  Scope,
} from '@nestjs/common';
import { Request, Response } from 'express';
import { HttpArgumentsHost } from '@nestjs/common/interfaces';
import { Observable, throwError } from 'rxjs';
import { catchError, tap } from 'rxjs/operators';

import { NemoLoggerService } from '../logger/logger.service';
import { defaultValue } from '../utils';

export const createLogAppRequest = (
  httpArg: HttpArgumentsHost,
  respBody: any,
) => {
  const req = httpArg.getRequest<Request>();
  const res = httpArg.getResponse<Response>();

  return {
    method: defaultValue(req.method),
    uri: defaultValue(req.path),
    rawUri: defaultValue(`${req.hostname}${req.path}`),
    reqHeader: req.headers || {},
    reqBody: req.body || {},
    respStatus: res.statusCode,
    respBody: respBody || {},
  };
};

@Injectable({ scope: Scope.REQUEST })
export class LoggerInterceptor implements NestInterceptor {
  private readonly logger = new NemoLoggerService();

  public intercept(
    context: ExecutionContext,
    next: CallHandler,
  ): Observable<any> {
    // Http context
    const http = context.switchToHttp();

    // Request context
    const req = http.getRequest();

    // Get trace id from headers context
    const traceId = defaultValue(req.headers['trace-id']);

    // Get ip address
    const ipAddress = req.socket.remoteAddress;

    // Get app name
    const appName = req.headers['x-app-id'];

    // Set trace id
    this.logger.setTraceId(traceId);

    // Set ip address
    this.logger.setIpAddress(ipAddress);

    // Set app name
    this.logger.setAppName(appName);

    return next.handle().pipe(
      tap((resBody) => {
        const execTime = Date.now() - http.getRequest()._startTime;
        const httpArgumentsHost: HttpArgumentsHost = context.switchToHttp();
        this.logger.request(
          createLogAppRequest(httpArgumentsHost, resBody),
          execTime,
        );
      }),
      catchError((err) => {
        const execTime = Date.now() - http.getRequest()._startTime;
        const httpArgumentsHost: HttpArgumentsHost = context.switchToHttp();
        this.logger.request(
          createLogAppRequest(httpArgumentsHost, err),
          execTime,
        );
        return throwError(() => err);
      }),
    );
  }
}
