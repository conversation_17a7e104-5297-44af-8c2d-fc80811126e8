import { Injectable, LoggerService as NestLoggerService } from '@nestjs/common';
import { defaultValue } from '../utils';
import { omit } from 'lodash';

export interface LogRequest {
  method: string;
  uri: string;
  rawUri: string;
  reqHeader: object;
  reqBody: object;
  respStatus: number;
  respBody: object;
}

@Injectable()
export class NemoLoggerService implements NestLoggerService {
  private className = '-';
  private appName = '-';
  private traceId: string | string[] = '-';
  private ipAddress = '-';
  private userId = '-';
  private requestTime = Date.now();

  getTraceId() {
    return this.traceId;
  }

  getIpAddress() {
    return this.ipAddress;
  }

  getUserId() {
    return this.userId;
  }

  getAppName() {
    return this.appName;
  }

  getRequestTime() {
    return this.requestTime;
  }

  setClassName(className: string) {
    this.className = className;
  }

  setTraceId(traceId: string | string[]) {
    this.traceId = traceId;
  }

  setIpAddress(ipAddress: string) {
    this.ipAddress = ipAddress;
  }

  setUserId(userId: string) {
    this.userId = userId;
  }

  setAppName(appName: string) {
    this.appName = appName;
  }

  setRequestTime(requestTime: number) {
    this.requestTime = requestTime;
  }

  setHeaderRequest(request: any) {
    if (request.headers) {
      this.setTraceId(request.headers.traceId);
      this.setUserId(request.headers.userid);
      this.setRequestTime(parseInt(request.headers.requestTime, 10));
    }
    this.setIpAddress(request.ip);
  }

  public log(message: string) {
    this.printMessage(this.createApiLog('info', message));
  }

  public error(message: string, trace = '') {
    this.printMessage(this.createApiLog('error', message));
    if (trace) {
      this.printMessage(this.createApiLog('error', trace));
    }
  }

  public warn(message: string) {
    this.printMessage(this.createApiLog('warn', message));
  }

  public debug(message: string) {
    this.printMessage(this.createApiLog('debug', message));
  }

  public verbose(message: string) {
    this.printMessage(this.createApiLog('verbose', message));
  }

  public logWithClassName(message: string, className: string) {
    this.setClassName(className);
    this.printMessage(this.createApiLog('info', message));
  }

  public errorWithClassName(message: string, className: string, trace = '') {
    this.setClassName(className);
    this.printMessage(this.createApiLog('error', message));
    if (trace) {
      this.printMessage(this.createApiLog('error', trace));
    }
  }

  public warnWithClassName(message: string, className: string) {
    this.setClassName(className);
    this.printMessage(this.createApiLog('warn', message));
  }

  public debugWithClassName(message: string, className: string) {
    this.setClassName(className);
    this.printMessage(this.createApiLog('debug', message));
  }

  public verboseWithClassName(message: string, className: string) {
    this.setClassName(className);
    this.printMessage(this.createApiLog('verbose', message));
  }

  public request(req: LogRequest, execTime: number) {
    const maxBodyLength = 200;
    const log = {
      trace_id: this.getTraceId(),
      method: req.method,
      path: req.rawUri,
      header: JSON.stringify(
        omit(
          {
            ipAddess: this.getIpAddress(),
            ...this.defaultObject(req.reqHeader),
          },
          ['authorization'],
        ),
      ),
      body: {
        execTime,
        reqBody: JSON.stringify(this.defaultObject(req.reqBody)).slice(
          0,
          maxBodyLength,
        ),
        respBody: JSON.stringify(this.defaultObject(req.respBody)).slice(
          0,
          maxBodyLength,
        ),
      },
    };

    this.printMessage(JSON.stringify(log));
  }

  public audit(
    eventCode: string,
    subEventCode: string,
    data: { [key: string]: any } = {},
  ) {
    const log = {
      // ...this.createPrefixFormat('info', 'AuditLog'),
      EVENT_CODE: this.defaultTxt(eventCode),
      SUB_EVENT_CODE: this.defaultTxt(subEventCode),
      message: JSON.stringify(this.defaultObject(data)),
    };

    this.printMessage(JSON.stringify(log));
  }

  public createApiLog(_logLevel: string, message: any) {
    const log = {
      // disable hard to investigate log in cloudwatch
      // ...this.createPrefixFormat(logLevel, 'ApiLog'),
      className: this.defaultTxt(this.className),
      functionName: '-',
      message: this.defaultTxt(message),
    };

    this.printMessage(JSON.stringify(log));
  }

  public printMessage(message: any) {
    if (message) {
      process.stdout.write(message);
      process.stdout.write('\n');
    }
  }

  private defaultTxt(txt: string) {
    return defaultValue(txt);
  }

  private defaultObject(obj: any) {
    return obj || {};
  }
}
