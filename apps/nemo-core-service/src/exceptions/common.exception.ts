import { Injectable } from '@nestjs/common';
import {
  BaseExceptionPayload,
  COMMON_EXCEPTIONS,
  CommonExceptionPayload,
  COMMON_EXCEPTIONS_TITLE_DESCRIPTION,
} from '../config';
import { formatErrorLog } from '../utils/general';

export interface CommonError {
  title: string;
  description: string;
}

@Injectable()
export class CommonExceptionService {
  code?: string;
  success?: boolean;
  status?: number;
  message?: string;
  data?: any;
  error?: CommonError;

  constructor(
    code?: string,
    success?: boolean,
    status?: number,
    message?: string,
    data?: any,
    error?: CommonError,
  ) {
    this.code = code;
    this.success = success;
    this.status = status;
    this.message = message;
    this.data = data;
    this.error = error;
  }

  exception<T = any>(
    code: keyof typeof COMMON_EXCEPTIONS,
    language: keyof CommonExceptionPayload,
    message?: string,
    data?: T,
    titleDescriptionKey?: keyof typeof COMMON_EXCEPTIONS_TITLE_DESCRIPTION,
  ) {
    const exception: BaseExceptionPayload = COMMON_EXCEPTIONS[code as string];
    const titleDescription: typeof COMMON_EXCEPTIONS_TITLE_DESCRIPTION =
      COMMON_EXCEPTIONS_TITLE_DESCRIPTION[titleDescriptionKey as string];

    console.log(
      formatErrorLog('common', exception.status, code, exception.code),
    );

    throw new CommonExceptionService(
      exception.code.toString(),
      false,
      exception.status,
      message ?? exception.message,
      data,
      {
        title: titleDescription
          ? titleDescription[language].title
          : exception.commonException?.[language].title ?? '',
        description: titleDescription
          ? titleDescription[language].description
          : exception.commonException?.[language].description ?? '',
      },
    );
  }
}
