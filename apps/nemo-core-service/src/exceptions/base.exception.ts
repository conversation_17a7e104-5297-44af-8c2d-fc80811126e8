import { Injectable } from '@nestjs/common';
import { BASE_EXCEPTIONS, BaseExceptionPayload } from '../config';
import { formatErrorLog } from '../utils/general';

@Injectable()
export class BaseExceptionService {
  code?: string;
  success?: boolean;
  status?: number;
  message?: string;
  data?: any;

  constructor(
    code?: string,
    success?: boolean,
    status?: number,
    message?: string,
    data?: any,
  ) {
    this.code = code;
    this.success = success;
    this.status = status;
    this.message = message;
    this.data = data;
  }

  exception<T = any>(code: keyof typeof BASE_EXCEPTIONS, data?: T) {
    const exception: BaseExceptionPayload = BASE_EXCEPTIONS[code as string];

    console.log(formatErrorLog('base', exception.status, code, exception.code));

    throw new BaseExceptionService(
      exception.code.toString(),
      false,
      exception.status,
      exception.message,
      data,
    );
  }
}
