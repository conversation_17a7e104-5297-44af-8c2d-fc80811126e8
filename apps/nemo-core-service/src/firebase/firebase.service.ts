import { Injectable } from '@nestjs/common';
import { FirebaseSystemConfig } from 'contracts';
import { initializeApp, cert, App } from 'firebase-admin/app';
import { getAuth } from 'firebase-admin/auth';
import { getFirestore } from 'firebase-admin/firestore';
import { AES128MessageService } from '../encrypt-decrypt-message/encrypt-decrypt.service';
import { SystemConfigService } from '../system-config/system-config.service';
import { RedisUtils } from '../utils/redis.util';
import { CacheManagerService } from '../cache-manager/cache-manager.service';
import { BaseExceptionService } from '../exceptions';
import { snakeCase } from 'lodash';

@Injectable()
export class FirebaseService {
  // Declare firebase admin app
  private firebaseApp?: App;
  private firestore?: FirebaseFirestore.Firestore;

  constructor(
    private readonly aes128MessgePipe: AES128MessageService,
    private readonly systemConfigService: SystemConfigService<string>,
    private readonly cacheManager: CacheManagerService,
    private readonly baseExceptionService: BaseExceptionService,
  ) {}

  async InitializeFirebase(companyId: string) {
    // Prevent firebase app valid
    if (this.firebaseApp) return;

    // Declare firebase config
    let firebaseConfig: Record<string, any> = {};

    // Formatter cache config key
    const cacheFirebaseConfigKey = RedisUtils.getSystemConfigKey(
      `${companyId}:firebase_backend`,
    );

    // Get cache
    const cacheFirebaseConfig = await this.cacheManager.getData(
      cacheFirebaseConfigKey,
    );

    // Get aes key
    const aesKey = process.env['AES128_KEY'];

    // Get aes salt
    const aesSalt = process.env['AES128_SALT'];

    // Prevent key or salt invalid
    if (!aesKey || !aesSalt) {
      throw new Error('AES_KEY and AES_SALT must be defined in env');
    }

    // Check cache is valid
    if (cacheFirebaseConfig) {
      // Decrypt firebase config
      const firebaseSystemConfig = JSON.parse(
        this.aes128MessgePipe.decrypt(
          Buffer.from(cacheFirebaseConfig[0], 'base64'),
        ),
      );

      // Mapping firebase config snake case
      for (const [key, value] of Object.entries(firebaseSystemConfig)) {
        firebaseConfig[snakeCase(key)] = value;
      }
    } else {
      // Query get firebase config from system-config
      const result = await this.systemConfigService.getSystemConfig(
        companyId,
        'firebase_backend',
      );
      // Prevent config not found
      if (!result.length) {
        throw this.baseExceptionService.exception(
          'NOT_FOUND_DATA',
          'Firebase config not found',
        );
      }

      // Decrypt firebase config
      const decryptFirebaseConfig = this.aes128MessgePipe.decrypt(
        Buffer.from(result[0], 'base64'),
      );

      // Extract data from result
      const firebaseSystemConfig: FirebaseSystemConfig = JSON.parse(
        decryptFirebaseConfig,
      ) as FirebaseSystemConfig;

      // Config firebase admin
      firebaseConfig = {
        type: firebaseSystemConfig.type,
        project_id: firebaseSystemConfig.projectId,
        private_key_id: firebaseSystemConfig.privateKeyId,
        private_key: firebaseSystemConfig.privateKey,
        client_email: firebaseSystemConfig.clientEmail,
        client_id: firebaseSystemConfig.clientId,
        auth_uri: firebaseSystemConfig.authUri,
        token_uri: firebaseSystemConfig.tokenUri,
        auth_provider_x509_cert_url: firebaseSystemConfig.authProviderCertUrl,
        client_x509_cert_url: firebaseSystemConfig.clientCertUrl,
        universe_domain: firebaseSystemConfig.universeDomain,
      };
    }

    // Assign to firebase admin app
    this.firebaseApp = initializeApp({
      credential: cert(firebaseConfig),
    });

    // Get firestore
    const db = getFirestore(this.firebaseApp);

    // Set ignore undefined properties value
    db.settings({ ignoreUndefinedProperties: true });

    this.firestore = db;
  }

  // Get firebase admin app
  getFirebase() {
    return this.firebaseApp;
  }

  // Verify id token
  async verifyIdToken(idToken: string) {
    return await getAuth(this.firebaseApp).verifyIdToken(idToken);
  }

  async addData(collectionRef: string, data: Record<string, any>) {
    // Prevent firebase app is not initialized
    if (!this.firestore) {
      return;
    }

    // Get document reference
    const docRef = this.firestore.collection(collectionRef);

    // Set data
    return await docRef.add(data);
  }

  async setData(storeDoc: string, data: Record<string, any>) {
    // Prevent firebase app is not initialized
    if (!this.firestore) {
      return;
    }

    // Get document reference
    const docRef = this.firestore.doc(storeDoc);

    // Set data
    return await docRef.set(data);
  }
}
