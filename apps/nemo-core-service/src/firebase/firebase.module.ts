import { Global, Module } from '@nestjs/common';
import { FirebaseService } from './firebase.service';
import { SystemConfigService } from '../system-config/system-config.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigActivitiesEntity, JobEntity } from '../entities';
@Global()
@Module({
  imports: [TypeOrmModule.forFeature([ConfigActivitiesEntity, JobEntity])],
  providers: [FirebaseService, SystemConfigService],
  exports: [FirebaseService],
})
export class FirebaseModule {}
