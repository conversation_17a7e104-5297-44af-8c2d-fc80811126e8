import { Inject, Injectable } from '@nestjs/common';
import * as excel from 'exceljs';
import ExcelJs from 'exceljs';
import { BaseExceptionService } from '../exceptions';
import {
  ChecklistType,
  JobEntity,
  JobStatus,
  ModelChecklistEntity,
} from '../../src/entities';
import { SelectQueryBuilder } from 'typeorm';
import {
  getJobsStatusFromCodeStatus,
  getProductStatusFromCodeStatus,
  jobDateTimeColumn,
  jobMapDatColumn,
  jobUserColumn,
  qcStatusCodeMapping,
  shippingStatusCodeMapping,
} from '../../src/utils/job/excel-export';
import { DeviceColorMap, JobExportType } from '../../src/config';
import { Stream } from 'stream';
import { Response } from 'express';
import {
  every,
  filter,
  get,
  isArray,
  isBoolean,
  isEmpty,
  isNumber,
  isString,
  isUndefined,
  size,
  some,
  split,
} from 'lodash';
import { AES128MessageService } from '../../src/encrypt-decrypt-message/encrypt-decrypt.service';
/**
 * note for headers records min and max
 * - min is first less than value not min acceptable value
 * - max is first more than value not max acceptable value
 */
export interface Options {
  // headers: Record<string, Header>;
  headers: Record<string, Header>;
  maxRows?: number;
  headerRowsCount?: number;
}

export interface Header {
  keyName: string;
  subHeader?: string;
  type: IConvertToType;
  isRequired?: boolean;
  options?: {
    decimal?: number;
    max?: { value?: number; referenceField?: string };
    min?: { value?: number; referenceField?: string };
    maxLength?: number;
  };
}

export interface ResponseOfReadExcelFileV2 {
  willBeSaveData: Record<string, any>[];
  willBeRemoveRowData: RemoveRowData[];
}

export interface RemoveRowData {
  productID: string;
  keys: string[];
}

export interface ResultOfFunctionConvertOptionForTemplate {
  header: HeaderExcel[];
  subHeader: SubKeyTitleList[];
}

export interface HeaderExcel {
  header: string;
  key: string;
  width: number;
}
export interface SubKeyTitleList {
  key: string;
  subHeader?: string;
}

export interface ColumnModuleData {
  name: string;
  isRequired: boolean;
  check: string;
  fnValue: string;
  nonfnValue: string;
  skipValue: string;
}

export enum IConvertToType {
  string = 'STRING',
  number = 'NUMBER',
  numString = 'STRING_OF_NUMBER',
  stringKey = 'STRING_KEY',
}

export enum INumTypeValidate {
  isInteger = 'IS_INTEGER',
  maxDecimal = 'MAX_DECIMAL',
  maxValue = 'MAX_VALUE',
  minValue = 'MIN_VALUE',
}

const DEFAULT_SHEET = 'default-sheet';
const ERROR_NAN = 'ERROR_NAN';
const EXCEL_FILE_TYPE = [
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
];

@Injectable()
export class ExcelManagerService {
  public options: Options;

  constructor(
    @Inject('EXCEL_OPTIONS') options: Options,
    private readonly aes128MessageService: AES128MessageService,
    private readonly baseExceptionService: BaseExceptionService,
  ) {
    this.options = options;
  }

  moduleCheckOptionColumn = [
    '.check',
    '=functional',
    '=non_functional',
    '=skip',
  ];

  validate(rowData: any) {
    const jsonResult: any = {};
    const headerKeys = Object.keys(this.options.headers);

    //map rowData value with keyName
    for (let i = 0; i < headerKeys.length; i++) {
      const headerKey = headerKeys[i];
      const header = this.options.headers[headerKey];
      const rowDataValue = rowData[headerKey];

      if (rowDataValue !== undefined && rowDataValue !== null) {
        const convertResult = this.convertValueToKnownType({
          value: rowDataValue,
          toType: header.type,
          headerKey: headerKey,
          rowData: rowData,
        });

        if (convertResult === ERROR_NAN) {
          throw this.baseExceptionService.exception(
            'INVALID_DATA_TYPE',
            `${headerKey} INVALID_DATA_TYPE`,
          );
        }

        if (convertResult === null && header.isRequired) {
          throw this.baseExceptionService.exception(
            'REQUIRED_FIELD_INCOMPLETE',
            `${headerKey} is required`,
          );
        }

        jsonResult[header.keyName] = convertResult;
      } else {
        if (header.isRequired) {
          throw this.baseExceptionService.exception(
            'REQUIRED_FIELD_INCOMPLETE',
            `${headerKey} is required`,
          );
        }
        jsonResult[header.keyName] = null;
      }
    }
    return jsonResult;
  }

  async readExcelFile(
    fileStream: Buffer,
    fileType: string,
    sheetName: string = DEFAULT_SHEET,
    maxFileSize: number = 5242880,
  ): Promise<Record<string, any>[]> {
    if (!EXCEL_FILE_TYPE.includes(fileType)) {
      throw this.baseExceptionService.exception(
        'INVALID_FILE_TYPE',
        `${fileType} is in invalid file type`,
      );
    }
    if (fileStream.byteLength > maxFileSize) {
      throw this.baseExceptionService.exception(
        'INVALID_FILE_SIZE',
        `This file exceeds file size limit(${(
          maxFileSize /
          1024 /
          1024
        ).toFixed(2)} MB)`,
      );
    }

    let workbook: excel.Workbook;
    try {
      workbook = new excel.Workbook();
      await workbook.xlsx.load(fileStream);
    } catch (error) {
      throw this.baseExceptionService.exception(
        'INVALID_FILE_TYPE',
        `The uploaded file is not a valid excel file`,
      );
    }
    const jsonData: Record<string, any>[] = [];

    // find subHeader
    const optionHeader = this.options.headers;
    const maxRows = this.options.maxRows;
    const headerRowsCount = this.options.headerRowsCount;
    let skipSecondRow = false;
    const requiredHeaders: string[] = [];

    for (const key in optionHeader) {
      skipSecondRow = optionHeader[key]['subHeader'] !== undefined;
      if (optionHeader[key].isRequired) requiredHeaders.push(key);
    }

    // Selected first sheet if sheetname not provided
    const worksheet =
      sheetName === DEFAULT_SHEET
        ? workbook.worksheets[0]
        : workbook.getWorksheet(sheetName);

    if (worksheet) {
      const dataRowsCount = worksheet.actualRowCount;
      if (
        maxRows &&
        headerRowsCount &&
        dataRowsCount - headerRowsCount > maxRows
      ) {
        throw this.baseExceptionService.exception('DATA_ROW_EXCEED_LIMIT');
      }
      const firstRow = worksheet.getRow(1);
      const keys = firstRow.values;
      const headerInKeys: string[] = [];
      if (keys && keys.length) {
        for (const i in keys) {
          if (requiredHeaders.includes(keys[i])) {
            headerInKeys.push(keys[i]);
          }
        }

        if (headerInKeys.length !== requiredHeaders.length) {
          throw this.baseExceptionService.exception(
            'REQUIRED_FIELD_INCOMPLETE',
            `Required headerKey incomplete`,
          );
        }
      } else {
        throw this.baseExceptionService.exception(
          'REQUIRED_FIELD_INCOMPLETE',
          `headerKey is required`,
        );
      }

      let rowCount = 0;

      worksheet.eachRow((row, rowNumber) => {
        rowCount = rowNumber;

        if (rowNumber === 1) return;
        if (skipSecondRow && rowNumber === 2) return;

        const obj: Record<string, any> = {};
        const rowLength = row?.values?.length as number;
        for (let i = 1; i < rowLength; i++) {
          obj[keys[i]] = row.values[i]?.result ?? row.values[i];
        }

        jsonData.push(this.validate(obj));
      });

      if (rowCount === 0) {
        throw this.baseExceptionService.exception(
          'REQUIRED_FIELD_INCOMPLETE',
          `headerKey is required`,
        );
      }
    } else {
      throw this.baseExceptionService.exception(
        'INVALID_SHEET_NAME',
        `Invalid excel sheet name ${sheetName}`,
      );
    }

    return jsonData;
  }

  validateTypeOfValue(
    newObj: Record<string, any>,
    requiredHeadersKey: string[],
  ): boolean {
    // validate case data is invalid type
    const keyIsTypeBoolean = ['.check'];

    // Helper function to throw exception with proper error messages
    const throwError = (field: string, type: string) => {
      const err = `${field} is not ${type}`;
      throw this.baseExceptionService.exception('INVALID_DATA_TYPE', err);
    };

    // Helper function to validate string fields
    const validateStringField = (field: string, value: any) => {
      if (!isString(value)) throwError(field, 'string');
    };

    // Helper function to validate boolean fields
    const validateBooleanField = (
      field: string,
      value: any,
      isRequired: boolean,
    ) => {
      if (isRequired) {
        if (!isBoolean(value)) throwError(field, 'true/false');
      } else {
        if (!isUndefined(value) && !isBoolean(value))
          throwError(field, 'true/false');
      }
    };

    // Helper function to validate number fields
    const validateNumberField = (
      field: string,
      value: any,
      isRequired: boolean,
      isCheck: boolean,
    ) => {
      if (isRequired && isCheck) {
        if (!isNumber(value)) throwError(field, 'numeric');
      } else {
        if (!isUndefined(value) && !isNumber(value))
          throwError(field, 'numeric');
      }
    };

    // Iterate through each field in newObj and validate
    for (const field in newObj) {
      const value = newObj[field];
      const isRequired = requiredHeadersKey.includes(field);

      if (field === 'PRODUCT_ID') {
        // Validate PRODUCT_ID as a string
        validateStringField(field, value);
      } else if (keyIsTypeBoolean.some((kStr) => field.includes(kStr))) {
        // Validate boolean fields
        validateBooleanField(field, value, isRequired);
      } else {
        // Validate numeric fields
        const splitField = split(field, '=')[0];
        const isCheck = newObj[`${splitField}.check`] as boolean;
        validateNumberField(field, value, isRequired, isCheck);
      }
    }

    return true;
  }

  validateRelated(
    newObj: Record<string, any>,
    modelChecks: ModelChecklistEntity[],
  ) {
    const uncheckQuestionKey: string[] = [];
    for (const model of modelChecks) {
      const { functionKey, functionSection } = model;

      let checked = false;
      const values: any[] = [];
      if (model.checklistType === ChecklistType.MODULE) {
        for (const optionCheck of this.moduleCheckOptionColumn) {
          const columnkey = `${functionSection}.${functionKey}${optionCheck}`;
          if (optionCheck === '.check') {
            checked = newObj[columnkey] as boolean;
          } else {
            values.push(newObj[columnkey]);
          }
        }
      } else {
        // Define the columns for question check options (here, only a check is required)
        const questionCheckOptionColumn = ['.check', '=skip'];
        const columnkey = `${functionSection}.${functionKey}${questionCheckOptionColumn[0]}`;
        checked = newObj[columnkey] as boolean;

        if (isArray(model.questionChoices)) {
          // If the question has choices, add a column for each possible answer
          for (const ques of model.questionChoices) {
            const colkey = `${functionSection}.${functionKey}=${get(
              ques,
              'id',
              'unknown',
            )}`; // Column key using the question choice ID

            values.push(newObj[colkey]);
          }
        }
      }

      if (checked) {
        const everyUndefinedOrEmpty = every(values, function (value) {
          return isUndefined(value) || isNaN(Number(value));
        });
        if (everyUndefinedOrEmpty) {
          throw this.baseExceptionService.exception(
            'REQUIRED_FIELD_INCOMPLETE',
            `${functionKey} is required`,
          );
        }
      } else {
        uncheckQuestionKey.push(`${functionSection}.${functionKey}`);
      }
    }

    return uncheckQuestionKey;
  }
  clenObjUnused(
    newObj: Record<string, any>,
    unused: string[],
  ): Record<string, any> {
    const releaseFunctional = { ...newObj };
    for (const key in newObj) {
      if (
        some(unused, function (f) {
          return key.includes(f);
        }) ||
        key.includes('.check')
      ) {
        delete releaseFunctional[key];
      }
    }
    return releaseFunctional;
  }

  async readExcelFileV2(
    masterFile: Express.Multer.File,
    modelChecks: ModelChecklistEntity[],
    sheetName: string = DEFAULT_SHEET,
    maxFileSize: number = 5242880,
  ): Promise<ResponseOfReadExcelFileV2> {
    // defind of children property in masterfile
    const { mimetype, buffer } = masterFile;

    //  *** verify type of file ***
    // @reject when file type is not match .xlsx
    if (!EXCEL_FILE_TYPE.includes(mimetype)) {
      const mesError = `${mimetype} is in invalid file type`;
      throw this.baseExceptionService.exception('INVALID_FILE_TYPE', mesError);
    }

    //  *** verify file buffer size of file ***
    // @reject when file size is over limit of system
    if (buffer.byteLength > maxFileSize) {
      const maxSize = (maxFileSize / 1024 / 1024).toFixed(2);
      const mesError = `This file exceeds file size limit(${maxSize} MB)`;
      throw this.baseExceptionService.exception('INVALID_FILE_SIZE', mesError);
    }

    let workbook: excel.Workbook;
    try {
      workbook = new excel.Workbook();
      await workbook.xlsx.load(buffer);
    } catch (error) {
      const mesError = `The uploaded file is not a valid excel file`;
      throw this.baseExceptionService.exception('INVALID_FILE_TYPE', mesError);
    }

    // defind variable
    const requiredHeadersKey: string[] = [];

    for (const i of modelChecks) {
      // find required column key for check required column value
      if (i.isRequired) {
        for (const optionCheck of this.moduleCheckOptionColumn) {
          const columnkey = `${i.functionSection}.${i.functionKey}${optionCheck}`;
          requiredHeadersKey.push(columnkey);
        }
      }
    }

    // Selected first sheet if sheetname not provided
    const worksheet =
      sheetName === DEFAULT_SHEET
        ? workbook.worksheets[0]
        : workbook.getWorksheet(sheetName);

    // @reject request when file name is not provided
    if (!worksheet) {
      const mesErr = `Invalid excel sheet name ${sheetName}`;
      throw this.baseExceptionService.exception('INVALID_SHEET_NAME', mesErr);
    }

    const firstRow = worksheet.getRow(1);
    const keys = firstRow.values as string[];

    // @reject request when first row (row key all) is not allowed
    if (!keys || !isArray(keys)) {
      const mesErr = 'Header key is required. Please recheck column key';
      throw this.baseExceptionService.exception(
        'REQUIRED_FIELD_INCOMPLETE',
        mesErr,
      );
    }

    // @reject request when require column key is not match with master column key (requiredHeadersKey)
    if (
      some(requiredHeadersKey, function (masterKey) {
        return !keys.includes(masterKey);
      })
    ) {
      const mesErr = 'Some required column header key is loss';
      throw this.baseExceptionService.exception(
        'REQUIRED_FIELD_INCOMPLETE',
        mesErr,
      );
    }

    const willBeSaveData: Record<string, any>[] = [];
    const willBeRemoveRowData: RemoveRowData[] = [];

    worksheet.eachRow((row, idx) => {
      if (idx === 1 || idx === 2) return; // skip row header column & sub header column

      const newObj: Record<string, any> = {};

      const columnLength = size(keys);
      for (let i = 1; i < columnLength; i++) {
        newObj[keys[i]] = row.values[i];
      }

      // check & @reject when value is invalid type
      const isPassValidateType = this.validateTypeOfValue(
        newObj,
        requiredHeadersKey,
      );

      // check & @reject when value is not related
      const unused = this.validateRelated(newObj, modelChecks);

      const res = this.clenObjUnused(newObj, unused);

      // add data will be remove when not check
      const removeItem: RemoveRowData = {
        productID: newObj['PRODUCT_ID'],
        keys: unused,
      };
      willBeRemoveRowData.push(removeItem);

      if (isPassValidateType) willBeSaveData.push(res);
    });

    return { willBeSaveData, willBeRemoveRowData };
  }

  convertValueToKnownType({
    value,
    toType,
    blankTo = null,
    nanTo = ERROR_NAN,
    headerKey,
    rowData,
  }: {
    value: any;
    toType: IConvertToType;
    blankTo?: any;
    nanTo?: any;
    headerKey?: string;
    rowData?: any;
  }) {
    if (value === undefined || value === null) {
      return blankTo;
    }

    let stringValue = String(value).trim();
    // case email: value type is object
    // {
    //    text: { richText: [ [Object] ] },
    //     hyperlink: 'mailto:<EMAIL>'
    // }
    //if value type is object and have hyperlink key
    if (typeof value === 'object' && value.hyperlink) {
      stringValue = value.hyperlink.replace('mailto:', '');
    }

    if (stringValue === '') {
      return blankTo;
    }

    if (toType === IConvertToType.string) {
      return stringValue;
    }

    if (toType === IConvertToType.stringKey && headerKey !== undefined) {
      return this.validateStringKey(stringValue, headerKey);
    }

    stringValue = stringValue.replace(/,/g, '');
    const numericValue = Number(stringValue);

    if (!isNaN(numericValue)) {
      if (
        headerKey !== undefined &&
        this.options.headers[headerKey].options !== undefined
      ) {
        this.validateNumericValue(numericValue, headerKey, rowData);
      }

      if (toType === IConvertToType.number) {
        return numericValue;
      }

      if (toType === IConvertToType.numString) {
        return stringValue;
      }
    }

    return nanTo;
  }

  validateStringKey(stringValue: string, headerKey: string): string {
    //change stringValue to uppercase
    stringValue = stringValue.toUpperCase();
    //regex of A-Z,0-9
    const regex = /^[A-Z0-9]+$/;
    //check if stringValue match regex
    if (!regex.test(stringValue)) {
      throw this.baseExceptionService.exception(
        'INVALID_DATA_FORMAT',
        `${headerKey} INVALID_DATA_FORMAT`,
      );
    }
    this.validateMaxLengthString(stringValue, headerKey);
    return stringValue;
  }

  validateMaxLengthString(stringValue: string, headerKey: string): void {
    const header = this.options.headers[headerKey];
    const maxLength = header.options?.maxLength;
    if (maxLength && stringValue.length > maxLength) {
      throw this.baseExceptionService.exception(
        'INVALID_DATA_FORMAT',
        `${headerKey} exceeds maximum length of ${maxLength} characters`,
      );
    }
  }

  validateNumericValue(numericValue: number, headerKey: string, rowData?: any) {
    const headerWithOptions = this.options.headers[headerKey].options;
    const decimal = headerWithOptions?.decimal;
    const max = headerWithOptions?.max;
    const min = headerWithOptions?.min;

    const referenceFieldMax = max?.referenceField;
    const referenceFieldMin = min?.referenceField;

    const referenceFieldValueMax = referenceFieldMax
      ? rowData[referenceFieldMax]
      : null;
    const referenceFieldValueMin = referenceFieldMin
      ? rowData[referenceFieldMin]
      : null;

    let validateDec: boolean = true,
      validateMax: boolean = true,
      validateMin: boolean = true;
    if (decimal !== undefined) {
      validateDec = this.validateNumberValue({
        value: numericValue,
        typeValidate: INumTypeValidate.maxDecimal,
        validateOption: decimal,
      });
    }

    if (max !== undefined) {
      const maxReferenceValue = referenceFieldValueMax || max.value;
      validateMax = this.validateNumberValue({
        value: numericValue,
        typeValidate: INumTypeValidate.maxValue,
        validateOption: Number(maxReferenceValue),
      });
    }

    if (min !== undefined) {
      const minReferenceValue = referenceFieldValueMin || min.value;
      validateMin = this.validateNumberValue({
        value: numericValue,
        typeValidate: INumTypeValidate.minValue,
        validateOption: Number(minReferenceValue),
      });
    }

    if (!validateDec) {
      throw this.baseExceptionService.exception(
        'INVALID_DATA_FORMAT',
        `${headerKey} is in invalid format`,
      );
    }

    if (!validateMax || !validateMin) {
      throw this.baseExceptionService.exception(
        'INVALID_RANGE',
        `${headerKey} is in invalid range`,
      );
    }
  }

  validateNumberValue({
    value,
    typeValidate,
    validateOption = 0,
  }: {
    value: number;
    typeValidate: INumTypeValidate;
    validateOption?: number;
  }) {
    if (typeValidate === INumTypeValidate.isInteger) {
      return value % 1 === 0;
    }
    if (typeValidate === INumTypeValidate.maxDecimal) {
      return Number(value.toFixed(validateOption)) === value;
    }
    if (typeValidate === INumTypeValidate.maxValue) {
      return value < validateOption;
    }
    if (typeValidate === INumTypeValidate.minValue) {
      return value > validateOption;
    }

    return false;
  }

  convertOptionHeaderToHeaderAndSubHeaderExcel(
    optionHeader: Record<string, Header>,
  ): any {
    const headerExcelList: HeaderExcel[] = [];
    const subHeaderList: SubKeyTitleList[] = [];
    for (const key in optionHeader) {
      const keyOptionHeader = optionHeader[key];
      const headerExcel: HeaderExcel = {
        header: key,
        key: keyOptionHeader.keyName,
        width: 10,
      };
      const subKey: SubKeyTitleList = {
        key: keyOptionHeader.keyName,
        subHeader: keyOptionHeader.subHeader,
      };
      headerExcelList.push(headerExcel);
      if (keyOptionHeader.subHeader) {
        subHeaderList.push(subKey);
      }
    }
    return { headerExcelList: headerExcelList, subHeaderList: subHeaderList };
  }

  async generateExcelFile(
    dataList: any[],
    sheetName: string,
    headersCondition?: {},
  ): Promise<Buffer> {
    const workbook = new ExcelJs.Workbook();
    const worksheet = workbook.addWorksheet(sheetName);

    const { headerExcelList, subHeaderList } =
      this.convertOptionHeaderToHeaderAndSubHeaderExcel(this.options.headers);

    //set header column
    worksheet.columns = headerExcelList;
    const subHeaderObjData = {};
    subHeaderList.forEach((data) => {
      subHeaderObjData[data.key] = data.subHeader;
    });

    // set header and subheader text
    if (headersCondition) {
      const listHeaderCondition = Object.getOwnPropertyNames(headersCondition);
      let rowCount = 1;

      if (Object.keys(headersCondition).length > 0) {
        worksheet.getRow(rowCount).values = [];
        listHeaderCondition.forEach((header, index) => {
          worksheet.getCell(`A${index + 1}`).value = headersCondition[header];
        });

        rowCount = rowCount + 1;
      }

      worksheet.getRow(listHeaderCondition.length + rowCount).values =
        subHeaderObjData;
    } else if (subHeaderList.length !== 0) {
      //add sub header
      worksheet.addRow(subHeaderObjData);
    }
    //add data
    dataList.forEach((data) => {
      const listProp = Object.getOwnPropertyNames(data);
      const rowList = {};
      listProp.forEach((p) => {
        if (Number.isNaN(data[p])) {
          rowList[p] = '';
        } else {
          rowList[p] = data[p];
        }
      });

      worksheet.addRow(rowList);
    });

    const buffer = (await workbook.xlsx.writeBuffer()) as Buffer;

    return buffer;
  }

  convertOptionForMasterTemplate(
    myDatalist: ModelChecklistEntity[],
  ): ResultOfFunctionConvertOptionForTemplate {
    // Initialize default header and subheader for the Excel sheet
    const headerExcelList: HeaderExcel[] = [
      {
        header: 'PRODUCT_ID', // This represents the column name in Excel
        key: 'ID', // This is the internal key used to identify the column
        width: 30, // Width of the column
      },
    ];

    const subHeaderExcelList: SubKeyTitleList[] = [
      {
        key: 'ID', // Key for the subheader, correlating to the main header
        subHeader: 'ID', // Subheader text to display
      },
    ];

    // Lists for holding the dynamically generated question headers and subheaders
    const headerQuestionExcelList: HeaderExcel[] = [];
    const subHeaderQuestionExcelList: SubKeyTitleList[] = [];

    // Define the columns for module check options (each type of check has its own column)
    const moduleCheckOptionColumn = [
      '.check',
      '=functional',
      '=non_functional',
      '=skip',
    ];

    // Define the columns for question check options (here, only a check is required)
    const questionCheckOptionColumn = ['.check'];

    // Filter the input data by checklist type for modules and questions
    const moduleOnly = filter(myDatalist, function (f) {
      return f.checklistType === ChecklistType.MODULE; // Select only 'MODULE' type items
    });

    const questionOnly = filter(myDatalist, function (f) {
      return f.checklistType === ChecklistType.QUESTION; // Select only 'QUESTION' type items
    });

    // Map event type (check, functional, non-functional, skip) to corresponding Thai text for modules
    function mapSubtitleModule(nameTH: string, moduleEvent: string) {
      switch (moduleEvent) {
        case moduleCheckOptionColumn[0]:
          return `เช็ค${nameTH}`; // Check event
        case moduleCheckOptionColumn[1]:
          return `${nameTH}ใช้ได้`; // Functional event
        case moduleCheckOptionColumn[2]:
          return `${nameTH}ใช้ไม่ได้`; // Non-functional event
        case moduleCheckOptionColumn[3]:
          return `ข้าม${nameTH}`; // Skip event
        default:
          return 'Unknown'; // Fallback for undefined events
      }
    }

    // Process each module item and add columns for each event type (check, functional, etc.)
    for (const item of moduleOnly) {
      for (const eventValue of moduleCheckOptionColumn) {
        const colkey = `${item.functionSection}.${item.functionKey}${eventValue}`; // Column key

        const newColumn: HeaderExcel = {
          header: colkey, // The Excel header name (combination of section, key, and event)
          key: colkey, // Internal key
          width: 28, // Column width
        };
        headerExcelList.push(newColumn); // Add the new column to the header list

        const newSubColumn: SubKeyTitleList = {
          key: colkey, // Internal key
          subHeader: mapSubtitleModule(item.checklistNameTh, eventValue), // Subtitle (mapped to Thai)
        };
        subHeaderExcelList.push(newSubColumn); // Add the new subheader to the subheader list
      }
    }

    // Process each question item and add columns for check options and possible answers
    for (const questionItem of questionOnly) {
      const fnSecKey = questionItem.functionSection; // Function section key for the question
      const fnKey = questionItem.functionKey; // Function key for the question

      // Add check columns for the question items
      for (const eventValue of questionCheckOptionColumn) {
        const colkey = `${fnSecKey}.${fnKey}${eventValue}`; // Column key

        const newColumn: HeaderExcel = {
          header: colkey, // The Excel header name (combination of section, key, and check event)
          key: colkey, // Internal key
          width: 28, // Column width
        };
        headerQuestionExcelList.push(newColumn); // Add the new column to the question header list

        const newSubColumn: SubKeyTitleList = {
          key: colkey, // Internal key
          subHeader: mapSubtitleModule(
            questionItem.checklistNameTh,
            eventValue,
          ), // Subtitle (mapped to Thai)
        };
        subHeaderQuestionExcelList.push(newSubColumn); // Add the new subheader to the subheader list
      }

      // If the question has choices, add a column for each possible answer
      if (isArray(questionItem.questionChoices)) {
        for (const ques of questionItem.questionChoices) {
          const colkey = `${fnSecKey}.${fnKey}=${get(ques, 'id', 'unknown')}`; // Column key using the question choice ID

          const newColumn: HeaderExcel = {
            header: colkey, // The Excel header name (including choice ID)
            key: colkey, // Internal key
            width: 30, // Column width
          };
          headerQuestionExcelList.push(newColumn); // Add the new column to the question header list

          const newSubColumn: SubKeyTitleList = {
            key: colkey, // Internal key
            subHeader: get(ques, 'answerTh', 'unknown'), // Subtitle (mapped to the Thai answer)
          };
          subHeaderQuestionExcelList.push(newSubColumn); // Add the new subheader to the subheader list
        }
      }
    }

    // Combine the headers and subheaders from modules and questions into the result
    return {
      header: [...headerExcelList, ...headerQuestionExcelList], // All headers
      subHeader: [...subHeaderExcelList, ...subHeaderQuestionExcelList], // All subheaders
    };
  }

  async generateTemplateExcelFileV2(
    masterQuestions: ModelChecklistEntity[],
    answers: any,
  ): Promise<Buffer> {
    const workbook = new ExcelJs.Workbook();
    const worksheet = workbook.addWorksheet('Master Price Function');

    // create a structure column
    const { header, subHeader } =
      this.convertOptionForMasterTemplate(masterQuestions);

    // add main header(code)
    worksheet.columns = header;

    // add sub header(th)
    const subHeaderObjData = {};
    if (subHeader.length !== 0) {
      subHeader.forEach((data) => {
        subHeaderObjData[data.key] = data.subHeader;
      });

      worksheet.addRow(subHeaderObjData);
    }

    // Set header row height (assuming the first row is the header)
    const headerRow = worksheet.getRow(1);
    headerRow.height = 30;

    // Apply font size and alignment to header row cells
    headerRow.eachCell((cell) => {
      cell.font = { size: 14 };
      cell.alignment = {
        horizontal: 'left',
        vertical: 'middle',
        wrapText: true,
      };
    });

    // add data on rows ** important !!! section **
    if (isArray(answers)) {
      for (const values of answers) {
        const productID = get(values, 'modelKey', null);

        if (productID) {
          const newRows = {};
          newRows['ID'] = productID;

          for (const col of masterQuestions) {
            const key = `${col.functionSection}.${col.functionKey}`;
            if (col.checklistType === ChecklistType.MODULE) {
              for (const colkey of this.moduleCheckOptionColumn) {
                const field = `${key}${colkey}`;

                const withOutcolcheck = this.moduleCheckOptionColumn.slice(
                  1,
                  4,
                );

                if (colkey === '.check') {
                  const havevalue = some(withOutcolcheck, function (w) {
                    const keyfield = `${key}${w}`;
                    return !isEmpty(get(values, keyfield, null));
                  });
                  newRows[field] = havevalue;
                } else {
                  newRows[field] = Number(get(values, field, 0));
                }
              }
            } else if (isArray(col.questionChoices)) {
              for (const colkey of col.questionChoices) {
                const field = `${key}=${get(colkey, 'id', 'unknown')}`;
                newRows[field] = Number(get(values, field, 0));
                newRows[`${key}.check`] = !isEmpty(get(values, field, null));
              }
            }
          }

          // Add the row to the worksheet
          const styleRow = worksheet.addRow(newRows);

          // Set alignment for the new row cells
          styleRow.eachCell({ includeEmpty: true }, (cell, colNumber) => {
            // Apply right alignment to the cells containing values
            if (colNumber > 1) {
              cell.alignment = { horizontal: 'right' };
            }
          });
        }
      }
    }
    // return file and file is type Buffer

    const buffer = (await workbook.xlsx.writeBuffer()) as Buffer;
    return buffer;
  }

  async generateExcelFileWithStream(
    dataList: any[],
    sheetName: string,
    queryStream: SelectQueryBuilder<JobEntity>,
    mapData: { userMappedTable; branchMappedTable },
    exportType?: string,
    headersCondition?: {},
    serverRes?: Response,
  ): Promise<Stream> {
    const res = new Stream.PassThrough();
    const options = {
      stream: serverRes ?? res, // write to server response
      useStyles: false,
      useSharedStrings: false,
    };
    let headerRowCount = 0;

    const workbook = new ExcelJs.stream.xlsx.WorkbookWriter(options);
    const worksheet = workbook.addWorksheet(sheetName);

    // modify header for export type SAP
    if (exportType === JobExportType.SAP) {
      this.options.headers = this.manageExcelHeaderForSAP(this.options.headers);
    }

    const { headerExcelList, subHeaderList } =
      this.convertOptionHeaderToHeaderAndSubHeaderExcel(this.options.headers);

    //set header column
    worksheet.columns = headerExcelList;
    const subHeaderObjData = {};
    subHeaderList.forEach((data) => {
      subHeaderObjData[data.key] = data.subHeader;
    });

    // set header and subheader text
    if (headersCondition) {
      const listHeaderCondition = Object.getOwnPropertyNames(headersCondition);
      let rowCount = 1;

      if (Object.keys(headersCondition).length > 0) {
        worksheet.getRow(rowCount).values = [];
        listHeaderCondition.forEach((header, index) => {
          worksheet.getCell(`A${index + 1}`).value = headersCondition[header];
        });

        rowCount = rowCount + 1;
      }

      worksheet.getRow(listHeaderCondition.length + rowCount).values =
        subHeaderObjData;
      headerRowCount = listHeaderCondition.length;
    } else if (subHeaderList.length !== 0) {
      //add sub header
      worksheet.addRow(subHeaderObjData);
    }

    const queryData = await queryStream.stream();
    let count = 0;
    queryData.on('data', (data) => {
      count++;
      try {
        data['runningNo'] = count;
        data['jobId'] = data['r_job_id'];
        for (const key in data['r_model_identifiers']) {
          data[key] = data['r_model_identifiers'][key];
        }
        // loop map user
        for (const item of jobUserColumn) {
          data[item.entityKey] =
            mapData.userMappedTable[data[item.dbKey] ?? ''];
        }

        // map branch
        data['branchId'] = mapData.branchMappedTable[data['r_branch_id'] ?? ''];

        // map price value
        data['suggestedPrice'] =
          Number(data['r_suggested_price']) === 0
            ? undefined
            : data['r_suggested_price'];
        data['purchasedPrice'] =
          Number(data['r_purchased_price']) === 0
            ? undefined
            : data['r_purchased_price'];

        // map color
        data['deviceColor'] = DeviceColorMap[data['r_device_color']];

        //  map time & convert UTC to ICT
        for (const item of jobDateTimeColumn) {
          data[item.entityKey] =
            data[item.dbKey] && this.getICTDateTime(data[item.dbKey]);
        }

        // loop map remaining columns
        for (const item of jobMapDatColumn) {
          data[item.entityKey] = data[item.dbKey];
        }

        if (data['status'] === JobStatus.INSPECTION_AUTO_COMPLETED) {
          data['inspectedBy'] = 'ระบบตรวจสอบอัตโนมัติ';
        }

        if (exportType === JobExportType.REPORT) {
          data['status'] = getJobsStatusFromCodeStatus(
            data['status'],
            data['qcStatus'],
          );

          data['shippingStatus'] =
            shippingStatusCodeMapping[data['shippingStatus']];

          data['qcStatus'] = qcStatusCodeMapping[data['qcStatus']];
        } else if (exportType === JobExportType.PRODUCT) {
          data['status'] = getProductStatusFromCodeStatus(
            data['status'],
            data['qcStatus'],
          );
        } else if (exportType === JobExportType.SAP) {
          data['shopCode'] = data['r_branch_id'];
          data['quantity'] = 1;
          data['matCode'] = data['r_material_code'];
          data['modelKey'] = data['r_model_key'];
          data['redemptionCode'] =
            data['redemption_code'] === null
              ? ''
              : this.aes128MessageService.decrypt(
                  Buffer.from(data['redemption_code'], 'base64'),
                );
          data['costCenter'] = data['cost_center'];
        }

        worksheet.addRow(data);
      } catch (error) {
        console.log('error during map data :', error);
      }
    });

    queryData.on('end', () => {
      if (exportType !== JobExportType.SAP) {
        const sumOfRecords = `Sum of records: ${count}`;
        worksheet.getRow(headerRowCount).values = [sumOfRecords];
      }

      worksheet.commit();
      workbook.commit();
    });

    return res;
  }
  manageExcelHeaderForSAP(optionHeader: Record<string, Header>) {
    optionHeader = {
      runningNo: {
        keyName: 'runningNo',
        subHeader: 'No.',
        type: IConvertToType.string,
      },
      ...optionHeader,
      quantity: {
        keyName: 'quantity',
        subHeader: 'Qty',
        type: IConvertToType.string,
      },
      shopCode: {
        keyName: 'shopCode',
        subHeader: 'Shop Code',
        type: IConvertToType.string,
      },
      glAccount: {
        keyName: 'glAccount',
        subHeader: 'G/L Account',
        type: IConvertToType.string,
      },
      fundCode: {
        keyName: 'fundCode',
        subHeader: 'Fund code',
        type: IConvertToType.string,
      },
      costCenter: {
        keyName: 'costCenter',
        subHeader: 'Cost Center',
        type: IConvertToType.string,
      },
      purchasedPrice: {
        keyName: 'purchasedPrice',
        subHeader: 'ระบุค่า\nExt.Amount LC',
        type: IConvertToType.string,
      },
      imei: {
        keyName: 'deviceKey',
        subHeader: 'imei',
        type: IConvertToType.string,
      },
      redemptionCode: {
        keyName: 'redemptionCode',
        subHeader: 'Trade-in Voucher \n(เลขที่ คูปอง)',
        type: IConvertToType.string,
      },
    };

    optionHeader['purchasedAt'].subHeader =
      'Purchase Date\n(วันที่รับฝากสินค้า)';
    optionHeader['receivedAt'].subHeader = 'Receive Date\n(วันที่รับสินค้า)';
    optionHeader['jobId'].subHeader = 'Transaction ID';
    optionHeader['model'].subHeader = 'Model';

    return optionHeader;
  }
  getICTDateTime(utcDate: Date) {
    const ictOffsetTime = 7 * 60 * 60 * 1000;
    const ictDate = new Date(utcDate.getTime() + ictOffsetTime);

    return ictDate;
  }
}
