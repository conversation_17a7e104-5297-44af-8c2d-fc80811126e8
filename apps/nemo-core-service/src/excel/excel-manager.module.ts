import { Module, DynamicModule } from '@nestjs/common';
import { ExcelManagerService, Options } from './excel-manager.service';

@Module({})
export class ExcelManagerModule {
  static register(options: Options): DynamicModule {
    return {
      module: ExcelManagerModule,
      providers: [
        {
          provide: 'EXCEL_OPTIONS',
          useValue: options,
        },
        ExcelManagerService,
      ],
      exports: [ExcelManagerService],
    };
  }
}
