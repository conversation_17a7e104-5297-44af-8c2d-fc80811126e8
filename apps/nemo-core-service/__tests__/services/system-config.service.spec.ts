import { Test } from '@nestjs/testing';
import { BaseExceptionModule } from '../../src/base-exceptions/base-exceptions.module';
import { SystemConfigService } from '../../src/system-config/system-config.service';
import { getRepositoryToken } from '@nestjs/typeorm';
import {
  SystemConfigEntity,
  UserEntity,
  ConfigActivitiesEntity,
  JobEntity,
  ConfigType,
} from '../../src/entities';
import { CacheManagerService } from '../../src/cache-manager/cache-manager.service';
import { AES128MessageService } from '../../src/encrypt-decrypt-message/encrypt-decrypt.service';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Repository } from 'typeorm';
import { BaseExceptionService } from '../../src/exceptions';
import { getQueueToken } from '@nestjs/bull';
import { WithUserContext } from '../../src/interfaces';
import * as reCal from '../../src/utils/job/queue';
import { BASE_EXCEPTIONS } from '../../src/config';

describe('SystemConfigService', () => {
  let systemConfigService: SystemConfigService<string>;
  let systemConfigRepository: Repository<SystemConfigEntity>;
  let configActivityRepository: Repository<ConfigActivitiesEntity>;
  beforeEach(async () => {
    const module = await Test.createTestingModule({
      imports: [BaseExceptionModule.register()],
      providers: [
        SystemConfigService,
        CacheManagerService,
        {
          provide: AES128MessageService,
          useFactory: () => {
            // Get aes key
            const aesKey = process.env.AES128_KEY;

            // Get aes salt
            const aesSalt = process.env.AES128_SALT;

            // Prevent key or salt invalid
            if (!aesKey || !aesSalt) {
              throw new Error(
                'AES_KEY and AES_SALT must be defined in .env file',
              );
            }

            // Initial aes service
            return new AES128MessageService(aesKey, aesSalt);
          },
        },
        {
          provide: CACHE_MANAGER,
          useValue: {
            get: jest.fn(),
            set: jest.fn(),
            reset: jest.fn(),
            del: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(SystemConfigEntity),
          useValue: {
            findOne: jest.fn().mockReturnThis(),
            save: jest.fn().mockReturnThis(),
          },
        },
        {
          provide: getRepositoryToken(UserEntity),
          useValue: {
            findOne: jest.fn().mockReturnThis(),
          },
        },
        {
          provide: getRepositoryToken(ConfigActivitiesEntity),
          useValue: {
            findOne: jest.fn().mockReturnThis(),
            save: jest.fn().mockReturnThis(),
          },
        },
        {
          provide: getRepositoryToken(JobEntity),
          useValue: {
            findOne: jest.fn().mockReturnThis(),
          },
        },
        {
          provide: getQueueToken('re-calculate-product-queue'),
          useValue: {
            add: jest.fn(),
            process: jest.fn(),
          },
        },
      ],
    }).compile();

    systemConfigService =
      module.get<SystemConfigService<string>>(SystemConfigService);

    systemConfigRepository = module.get<Repository<SystemConfigEntity>>(
      getRepositoryToken(SystemConfigEntity),
    );

    configActivityRepository = module.get<Repository<ConfigActivitiesEntity>>(
      getRepositoryToken(ConfigActivitiesEntity),
    );
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('Get config', () => {
    it('should get config success', async () => {
      jest.spyOn(systemConfigRepository, 'findOne').mockResolvedValueOnce(
        new Promise((resolve) => {
          const entity = new SystemConfigEntity();
          entity.data = 'test';

          return resolve(entity);
        }),
      );

      const result = await systemConfigService.getSystemConfig('test', 'test');
      expect(result).toEqual('test');
    });

    it('should get operation_cost config success', async () => {
      const configAt = new Date();
      const user = {
        companyId: 'test_company',
        userKey: 'user',
        name: 'test user',
      } as UserEntity;

      jest.spyOn(systemConfigRepository, 'findOne').mockResolvedValueOnce(
        new Promise((resolve) => {
          const entity = new SystemConfigEntity();
          entity.configKey = 'operation_cost';
          entity.companyId = 'test_company';
          entity.data = {
            marketing: 3,
            operationCost: {
              logistic: 1102,
              warehouseRental: 1201,
              productPackaging: 1303,
            },
          };
          return resolve(entity);
        }),
      );

      jest.spyOn(configActivityRepository, 'findOne').mockResolvedValueOnce(
        new Promise((resolve) => {
          const configActivity = new ConfigActivitiesEntity();
          configActivity.companyId = 'test_company';
          configActivity.configBy = 'user';
          configActivity.configType = ConfigType.OPERATION_COST;
          configActivity.configValue = {
            logistic: 1,
            warehouseRental: 56,
            productPackaging: 35,
          };
          configActivity.configAt = configAt;
          configActivity.configUser = user;
          return resolve(configActivity);
        }),
      );

      jest.spyOn(configActivityRepository, 'findOne').mockResolvedValueOnce(
        new Promise((resolve) => {
          const configActivity = new ConfigActivitiesEntity();
          configActivity.companyId = 'test_company';
          configActivity.configBy = 'user';
          configActivity.configType = ConfigType.MARKETING;
          configActivity.configValue = {
            marketing: 1,
          };
          configActivity.configAt = configAt;
          configActivity.configUser = user;
          return resolve(configActivity);
        }),
      );

      const result = await systemConfigService.getSystemConfig(
        'test_company',
        'operation_cost',
      );
      expect(result).toEqual({
        marketing: 3,
        operationCost: {
          logistic: 1102,
          productPackaging: 1303,
          warehouseRental: 1201,
        },
        updateMarketingAt: configAt,
        updateMarketingBy: 'test user',
        updateOperationCostAt: configAt,
        updateOperationCostBy: 'test user',
      });
    });

    it('should get operation_cost config success (no acvitiy config)', async () => {
      jest.spyOn(systemConfigRepository, 'findOne').mockResolvedValueOnce(
        new Promise((resolve) => {
          const entity = new SystemConfigEntity();
          entity.configKey = 'operation_cost';
          entity.companyId = 'test_company';
          entity.data = {
            marketing: 3,
            operationCost: {
              logistic: 1102,
              warehouseRental: 1201,
              productPackaging: 1303,
            },
          };
          return resolve(entity);
        }),
      );

      jest
        .spyOn(configActivityRepository, 'findOne')
        .mockResolvedValueOnce(null);

      jest
        .spyOn(configActivityRepository, 'findOne')
        .mockResolvedValueOnce(null);

      const result = await systemConfigService.getSystemConfig(
        'test_company',
        'operation_cost',
      );
      expect(result).toEqual({
        marketing: 3,
        operationCost: {
          logistic: 1102,
          productPackaging: 1303,
          warehouseRental: 1201,
        },
        updateMarketingAt: '',
        updateMarketingBy: '',
        updateOperationCostAt: '',
        updateOperationCostBy: '',
      });
    });

    it('should return exception', async () => {
      jest.spyOn(systemConfigRepository, 'findOne').mockResolvedValue(null);

      try {
        await systemConfigService.getSystemConfig('test', 'test');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
      }
    });

    it('should get config undefined', async () => {
      const result = await systemConfigService.getSystemConfig('test', 'test');
      expect(result).toEqual(undefined);
    });

    it('Should return data with encrypt data', async () => {
      jest.spyOn(systemConfigRepository, 'findOne').mockResolvedValueOnce(
        new Promise((resolve) => {
          const entity = new SystemConfigEntity();
          entity.data = [
            'lKrNTzrfguYdm8MRmBAgL58cOwGxX5iu6D+ikTYM6AG3oPxbKJ1OvQ/3xOiaXxhtL5uCKH4r4ARrtwI2pxwdVwF4dOqIhKBR3nZmLus=',
          ];
          return resolve(entity);
        }),
      );

      const result = await systemConfigService.getSystemConfig('test', 'ms');

      expect(result).toEqual({
        tenant: '2c614c92-b5f5-47d6-87a3-19e9925fc5ad',
      });
    });
  });

  describe('Set config', () => {
    it('Should set config with encrypt data', async () => {
      jest.spyOn(systemConfigRepository, 'findOne').mockResolvedValueOnce(
        new Promise((resolve) => {
          const entity = new SystemConfigEntity();
          entity.data = 'test';
          return resolve(entity);
        }),
      );

      jest.spyOn(systemConfigService, 'getSystemConfig').mockResolvedValueOnce({
        data: 'test',
        configKey: 'ms',
        companyId: 'test',
        createdAt: new Date(),
      } as SystemConfigEntity);

      const result = await systemConfigService.setSystemConfig(
        'test',
        'ms',
        [],
      );

      expect(result).toEqual(undefined);
    });

    it('should set config success', async () => {
      jest.spyOn(systemConfigRepository, 'findOne').mockResolvedValueOnce(
        new Promise((resolve) => {
          const entity = new SystemConfigEntity();
          entity.data = 'test';
          return resolve(entity);
        }),
      );

      const result = await systemConfigService.setSystemConfig(
        'test',
        'test',
        [],
      );
      expect(result).toEqual(undefined);
    });
  });

  describe('setStandardConfig', () => {
    let mockConfig;
    let mockRecalQueue;
    let mockUser;

    const mockValue = {
      marketing: 3,
      operationCost: {
        logistic: 1102,
        warehouseRental: 1201,
        productPackaging: 1303,
      },
    };

    beforeEach(() => {
      mockRecalQueue = jest.spyOn(reCal, 'reCalculateProduct');
      mockRecalQueue.mockResolvedValue();
    });

    it('set config (operation_cost) success', async () => {
      mockConfig = {
        configType: 'operation_cost',
        configValue: {
          marketing: 3,
          logistic: 1102,
          warehouseRental: 1201,
          productPackaging: 1303,
        },
      };

      mockUser = {
        company: 'WW',
        userKey: 'test',
        name: 'test',
      } as WithUserContext;
      jest
        .spyOn(systemConfigService, 'getSystemConfig')
        .mockResolvedValueOnce(mockValue);

      jest
        .spyOn(systemConfigService, 'setSystemConfig')
        .mockResolvedValueOnce(mockConfig.configValue);
      const result = await systemConfigService.setStandardConfig(
        mockConfig.configType,
        mockConfig.configValue,
        mockUser,
      );
      expect(result).toBeNull();
      expect(mockRecalQueue).toHaveBeenCalled();
    });

    it('set config (marketing) success', async () => {
      mockConfig = {
        configType: 'marketing',
        configValue: {
          marketing: 1102,
        },
      };

      mockUser = {
        company: 'WW',
        userKey: 'test',
        name: 'test',
      } as WithUserContext;

      jest
        .spyOn(systemConfigService, 'getSystemConfig')
        .mockResolvedValueOnce(mockValue);

      jest
        .spyOn(systemConfigService, 'setSystemConfig')
        .mockResolvedValueOnce(mockConfig.configValue);
      const result = await systemConfigService.setStandardConfig(
        mockConfig.configType,
        mockConfig.configValue,
        mockUser,
      );
      expect(result).toBeNull();
      expect(mockRecalQueue).toHaveBeenCalled();
    });

    it('set config fail (no config type)', async () => {
      mockConfig = {
        configType: 'test',
        configValue: {
          test: 20,
        },
      };

      mockUser = {
        company: 'WW',
        userKey: 'test',
        name: 'test',
      } as WithUserContext;
      jest
        .spyOn(systemConfigService, 'getSystemConfig')
        .mockResolvedValueOnce(mockConfig.configValue);
      jest
        .spyOn(systemConfigService, 'setSystemConfig')
        .mockResolvedValueOnce(mockConfig.configValue);

      try {
        await systemConfigService.setStandardConfig(
          mockConfig.configType,
          mockConfig.configValue,
          mockUser,
        );

        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.INVALID_DATA_FORMAT.code,
        );
        expect((error as BaseExceptionService).message).toBe(
          BASE_EXCEPTIONS.INVALID_DATA_FORMAT.message,
        );
        expect((error as BaseExceptionService).data).toBe(
          `No configType: ${mockConfig.configType} in systemConfig.`,
        );
      }
    });

    it('set config fail (missing configValue key)', async () => {
      mockConfig = {
        configType: 'operation_cost',
        configValue: {
          logistic: 1102,
          warehouseRental: 1201,
        },
      };

      mockUser = {
        company: 'WW',
        userKey: 'test',
        name: 'test',
      } as WithUserContext;
      jest
        .spyOn(systemConfigService, 'getSystemConfig')
        .mockResolvedValueOnce(mockConfig.configValue);
      jest
        .spyOn(systemConfigService, 'setSystemConfig')
        .mockResolvedValueOnce(mockConfig.configValue);

      try {
        await systemConfigService.setStandardConfig(
          mockConfig.configType,
          mockConfig.configValue,
          mockUser,
        );

        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.INVALID_DATA_FORMAT.code,
        );
        expect((error as BaseExceptionService).message).toBe(
          BASE_EXCEPTIONS.INVALID_DATA_FORMAT.message,
        );
        expect((error as BaseExceptionService).data).toBe(
          'Missing configValue key.',
        );
      }
    });

    it('set config fail (not null))', async () => {
      mockConfig = {
        configType: 'marketing',
        configValue: {
          marketing: null,
        },
      };

      mockUser = {
        company: 'WW',
        userKey: 'test',
        name: 'test',
      } as WithUserContext;
      jest
        .spyOn(systemConfigService, 'getSystemConfig')
        .mockResolvedValueOnce(mockConfig.configValue);
      jest
        .spyOn(systemConfigService, 'setSystemConfig')
        .mockResolvedValueOnce(mockConfig.configValue);

      try {
        await systemConfigService.setStandardConfig(
          mockConfig.configType,
          mockConfig.configValue,
          mockUser,
        );

        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.INVALID_DATA_FORMAT.code,
        );
        expect((error as BaseExceptionService).message).toBe(
          BASE_EXCEPTIONS.INVALID_DATA_FORMAT.message,
        );
        expect((error as BaseExceptionService).data).toBe(
          'Required field should not null.',
        );
      }
    });

    it.each([
      {
        configType: 'marketing',
        configValue: {
          marketing: '',
        },
      },
      {
        configType: 'marketing',
        configValue: {
          marketing: 20.222,
        },
      },
      {
        configType: 'marketing',
        configValue: {
          marketing: '20%',
        },
      },
      {
        configType: 'marketing',
        configValue: {
          marketing: -10,
        },
      },
    ])(`set config fail (wrong format)`, async (mockConfig) => {
      mockUser = {
        company: 'WW',
        userKey: 'test',
        name: 'test',
      } as WithUserContext;
      jest
        .spyOn(systemConfigService, 'getSystemConfig')
        .mockResolvedValueOnce(mockConfig.configValue);

      try {
        await systemConfigService.setStandardConfig(
          mockConfig.configType,
          mockConfig.configValue,
          mockUser,
        );

        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.INVALID_DATA_FORMAT.code,
        );
        expect((error as BaseExceptionService).message).toBe(
          BASE_EXCEPTIONS.INVALID_DATA_FORMAT.message,
        );
        expect((error as BaseExceptionService).data).toBe(
          'Wrong number format.',
        );
      }
    });
  });
});
