import {
  DataSource,
  EntityManager,
  EntityMetadata,
  QueryRunner,
} from 'typeorm';
import { IssueReportEntity, IssueReportStatus } from '../../src/entities';
import { Test } from '@nestjs/testing';
import { IssueReportEntitySubscriber } from '../../src/subscriber';
import { FirebaseService } from '../../src/firebase/firebase.service';
import { BaseExceptionModule } from '../../src/base-exceptions/base-exceptions.module';

describe('Issue Report Subscriber', () => {
  let IssueReportSubscriber: IssueReportEntitySubscriber;
  let entityManager: EntityManager;
  let firebaseService: FirebaseService;

  const issueReportEntity = new IssueReportEntity();
  issueReportEntity.branchId = 'test';
  issueReportEntity.status = IssueReportStatus.PENDING;

  const mockedEvent = {
    entity: issueReportEntity,
    databaseEntity: {} as IssueReportEntity,
    manager: {} as EntityManager,
    updatedColumns: [],
    connection: {} as DataSource,
    queryRunner: {} as QueryRunner,
    metadata: {} as EntityMetadata,
    updatedRelations: [],
  };

  beforeAll(async () => {
    const module = await Test.createTestingModule({
      imports: [BaseExceptionModule.register()],
      providers: [
        {
          provide: DataSource,
          useValue: {
            subscribers: {
              push: jest.fn().mockReturnThis(),
            },
          },
        },
        {
          provide: FirebaseService,
          useValue: {
            setData: jest.fn().mockReturnThis(),
            addData: jest.fn().mockReturnThis(),
          },
        },
        {
          provide: EntityManager,
          useValue: {},
        },
        IssueReportEntitySubscriber,
      ],
    }).compile();

    IssueReportSubscriber = module.get<IssueReportEntitySubscriber>(
      IssueReportEntitySubscriber,
    );
    entityManager = module.get<EntityManager>(EntityManager);
    firebaseService = module.get<FirebaseService>(FirebaseService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('after update or insert IssueReportSubscriber call', () => {
    it.each([
      [IssueReportStatus.PENDING, true],
      [IssueReportStatus.APPROVED, true],
      [IssueReportStatus.REJECTED, true],
      ['not-these-status' as IssueReportStatus, false],
    ])('via afterUpdate', async (issueReportStatus, isCallFirebase) => {
      let issueReportEntity = new IssueReportEntity();
      issueReportEntity.branchId = 'test';
      issueReportEntity.status = issueReportStatus;

      await IssueReportSubscriber.afterUpdate({
        ...mockedEvent,
        entity: issueReportEntity,
        manager: entityManager as EntityManager,
      });
      if (isCallFirebase) {
        expect(firebaseService.addData).toHaveBeenCalled();
      } else {
        expect(firebaseService.addData).toHaveBeenCalledTimes(0);
      }
    });

    describe('call after insert', () => {
      it('afterInsert call with pending status', async () => {
        await IssueReportSubscriber.afterInsert({
          ...mockedEvent,
          manager: entityManager as EntityManager,
        });

        expect(firebaseService.addData).toHaveBeenCalled();
      });
    });

    describe('not found entity', () => {
      it('afterUpdate call with no entity', async () => {
        await IssueReportSubscriber.afterUpdate({
          ...mockedEvent,
          entity: undefined as any,
          manager: entityManager as EntityManager,
        });

        expect(firebaseService.addData).toHaveBeenCalledTimes(0);
      });
    });

    it('listenTo', async () => {
      const result = IssueReportSubscriber.listenTo();
      expect(result).toBe(IssueReportEntity);
    });
  });
});
