import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Test } from '@nestjs/testing';
import { Cache } from 'cache-manager';
import { CacheManagerService } from '../../src/cache-manager/cache-manager.service';
import { getRepositoryToken } from '@nestjs/typeorm';
import { SystemConfigEntity, UserEntity } from '../../src/entities';
import { CACHE_KEY_ALL } from '../../src/config';
import { BaseExceptionService } from '../../src/exceptions';

describe('CacheManagerService', () => {
  // Setups
  let memoryCache: Cache;
  let cacheManagerService: CacheManagerService;

  beforeEach(async () => {
    const module = await Test.createTestingModule({
      providers: [
        CacheManagerService,
        { provide: 'CONFIG', useValue: { get: jest.fn() } },
        {
          provide: CACHE_MANAGER,
          useValue: {
            get: jest.fn(),
            set: jest.fn(),
            reset: jest.fn(),
            del: jest.fn(),
            store: {
              client: {
                keys: jest.fn(() => ['key1', 'key2']),
              },
              mdel: jest.fn(),
            },
          },
        },
        {
          provide: getRepositoryToken(SystemConfigEntity),
          useValue: {
            findOne: jest.fn().mockReturnThis(),
          },
        },
        {
          provide: getRepositoryToken(UserEntity),
          useValue: {
            findOne: jest.fn().mockReturnThis(),
          },
        },
      ],
    }).compile();
    memoryCache = module.get(CACHE_MANAGER);
    cacheManagerService = module.get<CacheManagerService>(CacheManagerService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  // Test Case

  // Get Data
  describe('getData', () => {
    it('should return get key data', async () => {
      jest.spyOn(memoryCache, 'get').mockResolvedValue('key_data');

      const response = await cacheManagerService.getData('key');
      expect(response).toStrictEqual('key_data');
    });
  });

  describe('getBatchData', () => {
    it('should return get pattern batch data', async () => {
      const response = await cacheManagerService.getBatchData('pattern');
      expect(response).toEqual(['key1', 'key2']);
    });
  });

  // Set Data
  describe('setData', () => {
    it('should return set key data', async () => {
      jest
        .spyOn(memoryCache, 'set')
        .mockReturnValue(Promise.resolve(undefined));

      const response = await cacheManagerService.setData('key', 'key_data', 60);
      expect(response).toStrictEqual(undefined);
    });
  });

  // Remove Data
  describe('removeData', () => {
    it('reset should return key', async () => {
      jest
        .spyOn(memoryCache, 'reset')
        .mockReturnValue(Promise.resolve(undefined));

      const response = await cacheManagerService.removeData(CACHE_KEY_ALL);
      expect(response).toStrictEqual(CACHE_KEY_ALL);
    });

    it('delete should return key', async () => {
      jest
        .spyOn(memoryCache, 'del')
        .mockResolvedValue(new Promise((resolve) => resolve()));

      const response = await cacheManagerService.removeData('key');
      expect(response).toStrictEqual('key');
    });
  });

  describe('invalidate', () => {
    it('should return success', async () => {
      jest.spyOn(memoryCache, 'get').mockResolvedValue('test');

      const response = await cacheManagerService.invalidateCache('test');

      expect(response).toStrictEqual(undefined);
    });

    it('shoud return success when invalidate with user roles regex key', async () => {
      jest.spyOn(memoryCache, 'get').mockResolvedValue('test');

      const response = await cacheManagerService.invalidateCache(
        'user-roles-test:<EMAIL>',
      );

      expect(response).toStrictEqual(undefined);
    });

    it('shoud return success when invalidate with config regex key', async () => {
      jest.spyOn(memoryCache, 'get').mockResolvedValue('test');

      const response =
        await cacheManagerService.invalidateCache('config-test:test');

      expect(response).toStrictEqual(undefined);
    });

    it('should return not found cache', async () => {
      let error: any;
      try {
        await cacheManagerService.invalidateCache('test');
      } catch (err) {
        error = err;
      }

      expect(error).toBeInstanceOf(BaseExceptionService);
    });
  });

  describe('removeBatchData', () => {
    it('should remove batch data', async () => {
      await cacheManagerService.removeBatchData(['key1', 'key2']);
    });
  });

  // Once
  describe('once', () => {
    it('should return cache data', async () => {
      jest.spyOn(memoryCache, 'get').mockResolvedValue('cache_data');

      const response = await cacheManagerService.once('key', 1, (): any => {
        return 'data';
      });
      expect(response).toStrictEqual('cache_data');
    });
    it('should return data without cache', async () => {
      jest.spyOn(memoryCache, 'get').mockResolvedValue(undefined);

      const response = await cacheManagerService.once('key', 1, (): any => {
        return 'data';
      });
      expect(response).toStrictEqual('data');
    });

    it('should return cache data when ttl is functoin', async () => {
      jest.spyOn(memoryCache, 'get').mockResolvedValue('cache_data');

      const response = await cacheManagerService.once(
        'key',
        (): any => {
          return 1;
        },
        (): any => {
          return 'data';
        },
      );
      expect(response).toStrictEqual('cache_data');
    });
  });
});
