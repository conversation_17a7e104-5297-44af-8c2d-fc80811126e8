import { getRepositoryToken } from '@nestjs/typeorm';
import { Test } from '@nestjs/testing';
import { BaseExceptionModule } from '../../src/base-exceptions/base-exceptions.module';
import { getQueueToken } from '@nestjs/bull';

import {
  JobEntity,
  ContractEntity,
  VoucherEntity,
  BranchEntity,
  CompanyEntity,
  CustomerInfoType,
  SystemConfigEntity,
  AllocationOrderEntity,
  CampaignRedemptionCodeEntity,
} from '../../src/entities';

import { S3Service } from '../../src/storage/s3.service';
import { GetObjectCommandOutput } from '@aws-sdk/client-s3';

import { ContractsService } from '../../src/shop/contracts/contracts.service';
import { CacheManagerService } from '../../src/cache-manager/cache-manager.service';
import { AES128MessageService } from '../../src/encrypt-decrypt-message/encrypt-decrypt.service';

import { BaseExceptionService } from '../../src/exceptions';
import { BASE_EXCEPTIONS } from '../../src/config';
import { EntityManager, Repository } from 'typeorm';
import { mock, instance } from 'ts-mockito';

import { convertBase64ToBytes } from '../../src/utils/contract/PDF-config-general/helper';
import { mockBase64Png, mockBase64Pdf } from '../mock-data/base64';

import { mockCustomerInfo, mockJob } from '../mock-data/job';
import { SmtpService } from '../../src/smtp/smtp.service';
import generateContract from '../../src/utils/contract/PDF-generate-main-fn/ww-contract-v2';
import generateTransaction from '../../src/utils/contract/PDF-generate-main-fn/ww-transaction-v2';
import generateAoTransaction from '../../src/utils/contract/PDF-generate-main-fn/ww-transaction-ao-v1';
import generateInspectionTransaction from '../../src/utils/contract/PDF-generate-main-fn/ww-transaction-inspection-v2';
import addCampaignCode from '../../src/utils/contract/PDF-generate-main-fn/ww-campaign-stamp-contract-v2';

jest.mock('../../src/utils/contract/PDF-generate-main-fn/ww-contract-v2');
jest.mock('../../src/utils/contract/PDF-generate-main-fn/ww-transaction-v2');
jest.mock('../../src/utils/contract/PDF-generate-main-fn/ww-transaction-ao-v1');
jest.mock(
  '../../src/utils/contract/PDF-generate-main-fn/ww-transaction-inspection-v2',
);
jest.mock(
  '../../src/utils/contract/PDF-generate-main-fn/ww-campaign-stamp-contract-v2',
);

const mockGenerateContract = {
  savedPdf: 'transaction',
  stampPosition: { campaign: { barcode: [], box: { width: 0 } } },
  version: 'mockVersion',
};

describe('ContractsService', () => {
  let contractsService: ContractsService;
  let contractsRepository: Repository<ContractEntity>;
  let voucherRepository: Repository<VoucherEntity>;
  let cacheManagerService: CacheManagerService;
  let s3Service: S3Service;
  let smtpService: SmtpService;
  let mockEntityManager: EntityManager;
  let companyRepository: Repository<CompanyEntity>;

  beforeEach(async () => {
    mockEntityManager = mock(EntityManager);
    const module = await Test.createTestingModule({
      imports: [BaseExceptionModule.register()],
      providers: [
        ContractsService,
        {
          provide: S3Service,
          useValue: {
            getPreviewUrl: jest.fn(),
            uploadFile: jest.fn(),
            getFile: jest.fn(),
            uploadFileByPresignedUrl: jest.fn(),
            getBufferFile: jest.fn(),
            getFileWithSignedUrl: jest.fn(),
            getAssetFile: jest.fn(),
          },
        },
        {
          provide: CacheManagerService,
          useValue: {
            incrData: jest.fn(() => Promise.resolve(1)),
            getData: jest.fn(),
            setData: jest.fn(),
          },
        },
        {
          provide: EntityManager,
          useValue: {
            ...instance(mockEntityManager),
            createQueryBuilder: jest.fn().mockReturnThis(),
            select: jest.fn().mockReturnThis(),
            insert: jest.fn().mockReturnThis(),
            into: jest.fn().mockReturnThis(),
            values: jest.fn().mockReturnThis(),
            orIgnore: jest.fn().mockReturnThis(),
            where: jest.fn().mockReturnThis(),
            andWhere: jest.fn().mockReturnThis(),
            orderBy: jest.fn().mockReturnThis(),
            getOne: jest.fn().mockReturnThis(),
            execute: jest.fn().mockResolvedValue({ raw: [] }),
            redemptionCode: {
              slice: jest.fn().mockReturnValue('1'),
            },
            getRepository: jest.fn().mockReturnThis(),
            setLock: jest.fn().mockReturnThis(),
            setOnLocked: jest.fn().mockReturnThis(),
          },
        },
        {
          provide: AES128MessageService,
          useValue: {
            encrypt: jest.fn(() => 'encrypted'),
            decrypt: jest.fn(() => 'decrypted'),
          },
        },
        {
          provide: SmtpService,
          useValue: {
            sendMail: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(ContractEntity),
          useValue: {
            save: jest.fn().mockReturnThis(),
            findOne: jest.fn().mockReturnThis(),
          },
        },
        {
          provide: getRepositoryToken(BranchEntity),
          useValue: {
            save: jest.fn().mockReturnThis(),
            findOne: jest.fn().mockReturnThis(),
          },
        },
        {
          provide: getRepositoryToken(CompanyEntity),
          useValue: {
            save: jest.fn().mockReturnThis(),
            findOne: jest.fn().mockReturnThis(),
          },
        },
        {
          provide: getRepositoryToken(VoucherEntity),
          useValue: {
            save: jest.fn().mockReturnThis(),
            createQueryBuilder: jest.fn().mockReturnThis(),
            findOne: jest.fn().mockReturnThis(),
            select: jest.fn().mockReturnThis(),
            where: jest.fn().mockReturnThis(),
            andWhere: jest.fn().mockReturnThis(),
            orderBy: jest.fn().mockReturnThis(),
            getOne: jest.fn().mockResolvedValue({
              redemptionCode: {
                slice: jest.fn().mockReturnValue('1'),
              },
            }),
          },
        },
        {
          provide: getRepositoryToken(SystemConfigEntity),
          useValue: {
            save: jest.fn().mockReturnThis(),
            findOne: jest.fn().mockReturnThis(),
          },
        },
        {
          provide: getQueueToken('email-queue'),
          useValue: {
            add: jest.fn(),
            process: jest.fn(),
          },
        },
      ],
    }).compile();

    contractsService = module.get<ContractsService>(ContractsService);
    contractsRepository = module.get<Repository<ContractEntity>>(
      getRepositoryToken(ContractEntity),
    );
    companyRepository = module.get<Repository<CompanyEntity>>(
      getRepositoryToken(CompanyEntity),
    );
    voucherRepository = module.get<Repository<VoucherEntity>>(
      getRepositoryToken(VoucherEntity),
    );
    cacheManagerService = module.get<CacheManagerService>(CacheManagerService);
    s3Service = module.get<S3Service>(S3Service);
    smtpService = module.get<SmtpService>(SmtpService);
    mockEntityManager = module.get<EntityManager>(EntityManager);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('Provision Contract', () => {
    it('should create a new contract and provision voucher if no existing contract is provided', async () => {
      // Mocking the necessary data
      const mockBody = {
        identificationNumber: '*********',
        mobileNumber: '*********',
      } as any;

      const mockJob: JobEntity = {
        updatedBy: 'testUser',
        jobId: '1',
        companyId: 'testCompany',
      } as any;

      jest.spyOn(mockEntityManager, 'save').mockResolvedValueOnce({
        contractId: 1,
      });

      // Calling the function with the necessary parameters
      const result = await contractsService.provisionContract(
        mockEntityManager,
        mockBody,
        mockJob,
        null,
      );

      expect(result).toBeDefined();
    });
    it('should update an existing contract if provided', async () => {
      // Mocking the necessary data
      const mockBody = {
        identificationNumber: '*********',
        mobileNumber: '*********',
      } as any;

      const mockJob: JobEntity = {
        updatedBy: 'testUser',
        jobId: '1',
        companyId: 'testCompany',
      } as any;

      const mockExistingContract: ContractEntity = {
        contractId: 1,
      } as any;

      jest.spyOn(mockEntityManager, 'save').mockResolvedValueOnce({
        contractId: 1,
      });

      // Calling the function with the necessary parameters
      const result = await contractsService.provisionContract(
        mockEntityManager,
        mockBody,
        mockJob,
        mockExistingContract,
      );

      expect(result).toBeDefined();
    });
  });

  describe('Confirm Contract', () => {
    it('should confirm contract and update voucher and contract links', async () => {
      // Mocking the necessary data
      const mockJob: JobEntity = {
        jobId: '1',
        updatedBy: 'testUser',
        suggestedPrice: 100,
      } as any;

      const mockConfirmContractDto = {
        contractKey: 'mockContractKey',
      } as any;

      const mockContract: ContractEntity = {
        contractId: 1,
        jobId: '1',
      } as any;

      const mockVoucher: ContractEntity = {
        contractId: 1,
        voucherValue: 0,
      } as any;

      jest
        .spyOn(mockEntityManager, 'findOne')
        .mockResolvedValueOnce(mockContract)
        .mockResolvedValueOnce(mockVoucher);

      jest
        .spyOn(mockEntityManager, 'transaction')
        .mockResolvedValueOnce(mockVoucher);

      jest.spyOn(mockEntityManager, 'save').mockResolvedValueOnce({
        contractId: 1,
      });

      jest
        .spyOn(contractsService, 'uploadContract')
        .mockResolvedValueOnce('mockContractLink');
      jest
        .spyOn(contractsService, 'uploadTransaction')
        .mockResolvedValueOnce('mockTransactionLink');

      // Calling the function with the necessary parameters
      const result = await contractsService.confirmContract(
        mockEntityManager,
        mockJob,
        mockConfirmContractDto,
      );

      expect(result).toBeDefined();
    });

    it('should throw contract not found', async () => {
      // Mocking the necessary data
      const mockJob: JobEntity = {
        jobId: '1',
        updatedBy: 'testUser',
        suggestedPrice: 100,
      } as any;

      const mockConfirmContractDto = {
        contractKey: 'mockContractKey',
      } as any;

      jest.spyOn(mockEntityManager, 'findOne').mockResolvedValueOnce(null);

      try {
        await contractsService.confirmContract(
          mockEntityManager,
          mockJob,
          mockConfirmContractDto,
        );
        // If it doesn't throw an error, fail the test
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.NOT_FOUND_DATA.code,
        );
        expect((error as BaseExceptionService).data).toBe('Contract not found');
      }
    });

    it('should throw voucher not found', async () => {
      // Mocking the necessary data
      const mockJob: JobEntity = {
        jobId: '1',
        updatedBy: 'testUser',
        suggestedPrice: 100,
      } as any;

      const mockConfirmContractDto = {
        contractKey: 'mockContractKey',
      } as any;

      const mockContract: ContractEntity = {
        contractId: 1,
        jobId: '1',
      } as any;

      jest
        .spyOn(mockEntityManager, 'findOne')
        .mockResolvedValueOnce(mockContract);

      jest.spyOn(mockEntityManager, 'transaction').mockResolvedValueOnce(null);

      try {
        await contractsService.confirmContract(
          mockEntityManager,
          mockJob,
          mockConfirmContractDto,
        );
        // If it doesn't throw an error, fail the test
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.VOUCHER_NOT_FOUND.code,
        );
        expect((error as BaseExceptionService).data).toBe('Voucher not found');
      }
    });
    it('should throw voucher is processing', async () => {
      // Mocking the necessary data
      const mockJob: JobEntity = {
        jobId: '1',
        updatedBy: 'testUser',
        suggestedPrice: 100,
      } as any;

      const mockConfirmContractDto = {
        contractKey: 'mockContractKey',
      } as any;

      const mockContract: ContractEntity = {
        contractId: 1,
        jobId: '1',
        UpdatingVoucherAt: new Date(),
      } as any;

      const mockVoucher: ContractEntity = {
        contractId: 1,
        voucherValue: 0,
      } as any;

      jest
        .spyOn(mockEntityManager, 'findOne')
        .mockResolvedValueOnce(mockContract);

      jest
        .spyOn(mockEntityManager, 'transaction')
        .mockResolvedValueOnce(mockVoucher);

      try {
        await contractsService.confirmContract(
          mockEntityManager,
          mockJob,
          mockConfirmContractDto,
        );
        // If it doesn't throw an error, fail the test
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.VOUCHER_IS_PROCESSING.code,
        );
        expect((error as BaseExceptionService).data).toBe(
          'The voucher is processing please wait until it completed',
        );
      }
    });
    it('should return contract', async () => {
      // Mocking the necessary data
      const mockJob: JobEntity = {
        jobId: '1',
        updatedBy: 'testUser',
        suggestedPrice: 100,
      } as any;

      const mockConfirmContractDto = {
        contractKey: 'mockContractKey',
      } as any;

      const mockVoucher: ContractEntity = {
        contractId: 1,
        voucherValue: 0,
      } as any;

      const mockContract: ContractEntity = {
        contractId: 1,
        jobId: '1',
        UpdatingVoucherAt: new Date(),
        importedVouchers: mockVoucher,
      } as any;

      jest
        .spyOn(mockEntityManager, 'findOne')
        .mockResolvedValueOnce(mockContract);

      jest
        .spyOn(mockEntityManager, 'transaction')
        .mockResolvedValueOnce(mockVoucher);

      const result = await contractsService.confirmContract(
        mockEntityManager,
        mockJob,
        mockConfirmContractDto,
      );

      expect(result).toBeDefined();
    });
  });

  describe('confirmCampaignRedemptionCode', () => {
    const manager = {
      save: async (data: any) => ({}) as CampaignRedemptionCodeEntity,
    } as EntityManager;

    const mockJobDefault: JobEntity = {
      campaigns: [{ maxRedemptionCode: 3, campaignCode: 'mockCampaignCode' }],
      currentGrade: 'mockCurrentGrade',
      companyId: 'mockCompanyId',
      jobId: 'mockJobId',
    } as JobEntity;

    const mockJobInactive: JobEntity = {
      campaigns: [
        {
          maxRedemptionCode: 3,
          campaignCode: 'mockCampaignCode',
          isActive: false,
        },
      ],
      currentGrade: 'mockCurrentGrade',
      companyId: 'mockCompanyId',
      jobId: 'mockJobId',
    } as JobEntity;

    it('[happy case] should get campaign redemption code correctly', async () => {
      jest
        .spyOn(contractsService, 'getCampaignCodeAndLock')
        .mockResolvedValue({} as CampaignRedemptionCodeEntity);

      const mockJob: JobEntity = {
        ...mockJobDefault,
      } as JobEntity;

      const result = await contractsService.confirmCampaignRedemptionCode({
        manager: mockEntityManager,
        job: mockJob,
      });
      expect(result?.length).toEqual(3);
    });

    it.each([[undefined], [[]], [null]])(
      '[happy case] should return undefined campaign when %s',
      async (campaigns) => {
        const mockJob: JobEntity = {
          ...mockJobDefault,
          campaigns,
        } as JobEntity;

        const result = await contractsService.confirmCampaignRedemptionCode({
          manager,
          job: mockJob,
        });

        expect(result).toBeUndefined();
      },
    );

    it.each([['currentGrade'], ['companyId'], ['jobId']])(
      'should error invalid job for action when do not have %s',
      async (undefinedField) => {
        const mockJob: JobEntity = {
          ...mockJobDefault,
          [undefinedField]: undefined,
        } as JobEntity;
        try {
          await contractsService.confirmCampaignRedemptionCode({
            manager,
            job: mockJob,
          });
          fail('Expected method to throw an error');
        } catch (error) {
          expect(error).toBeInstanceOf(BaseExceptionService);
          expect((error as BaseExceptionService).code).toBe(
            BASE_EXCEPTIONS.INVALID_JOB_FOR_ACTION.code,
          );
        }
      },
    );
    it('[fail case] should return error CAMPAIGN_NOT_ACTIVE', async () => {
      jest
        .spyOn(contractsService, 'getCampaignCodeAndLock')
        .mockResolvedValue({} as CampaignRedemptionCodeEntity);

      const mockJob: JobEntity = {
        ...mockJobInactive,
      } as JobEntity;

      try {
        await contractsService.confirmCampaignRedemptionCode({
          manager: mockEntityManager,
          job: mockJob,
        });
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.CAMPAIGN_NOT_ACTIVE.code,
        );
      }
    });
  });

  describe('getCampaignCodeAndLock', () => {
    const defaultPropToCall = {
      campaignCode: 'mockCampaignCode',
      companyId: 'mockCompanyId',
      currentGrade: 'mockCurrentGrade',
      order: 1,
    };

    it('should get campaign code and lock successfully', async () => {
      jest.spyOn(mockEntityManager, 'getRepository').mockReturnValueOnce({
        createQueryBuilder: jest.fn().mockReturnThis(),
        setLock: jest.fn().mockReturnThis(),
        setOnLocked: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        getOne: jest
          .fn()
          .mockResolvedValueOnce({} as CampaignRedemptionCodeEntity),
      } as any);

      const result = await contractsService.getCampaignCodeAndLock({
        ...defaultPropToCall,
        manager: mockEntityManager,
      });
      expect(result).toEqual({});
    });

    it('should throw an error if campaign code is not found after max retries', async () => {
      jest.spyOn(mockEntityManager, 'getRepository').mockReturnValue({
        createQueryBuilder: jest.fn().mockReturnThis(),
        setLock: jest.fn().mockReturnThis(),
        setOnLocked: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        getOne: jest.fn().mockResolvedValue(null),
      } as any);

      try {
        await contractsService.getCampaignCodeAndLock({
          ...defaultPropToCall,
          manager: mockEntityManager,
        });
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.CAMPAIGN_REDEMPTION_CODE_NOT_FOUND.code,
        );
        expect((error as BaseExceptionService).data).toBe(
          `Campaign Redemption Code: ${defaultPropToCall.campaignCode} order ${defaultPropToCall.order} not found`,
        );
      }
    });
  });

  //   const defaultPropToCall = {
  //     campaignCode: 'mockCampaignCode',
  //     companyId: 'mockCompanyId',
  //     currentGrade: 'mockCurrentGrade',
  //     order: 1,
  //   };

  //   it('should', async () => {

  //     const result = await contractsService.getCampaignCodeAndLock({
  //       ...defaultPropToCall,
  //       manager: mockEntityManager,
  //     });
  //     expect(result).toBeDefined();
  //   });
  //   it('should', async () => {});
  //   it('should', async () => {});
  // });

  describe('Upload Contract', () => {
    const mockJob = {
      jobId: 'mockJobId',
      companyId: 'mockCompany',
    } as JobEntity;

    const mockContractKey = 'mockContractKey';
    it('should upload contract successfully', async () => {
      const link =
        'mockS3Url/company/mockCompany/jobs/mockJobId/ใบสัญญาฝากขาย_ddMMyyyy_HHmmss.pdf';

      jest
        .spyOn(cacheManagerService, 'getData')
        .mockResolvedValueOnce('mockContractBase64');

      jest.spyOn(s3Service, 'uploadFile').mockResolvedValueOnce(link);

      // Call the async function
      const result = await contractsService.uploadContract(
        mockJob,
        mockContractKey,
      );

      // Assertions
      expect(cacheManagerService.getData).toHaveBeenCalledWith(
        'mockContractKey',
      );
      expect(result).toEqual(link);
    });

    it('should throw an error when contract data is not found in cache', async () => {
      jest.spyOn(cacheManagerService, 'getData').mockResolvedValueOnce(null);

      try {
        await contractsService.uploadContract(mockJob, mockContractKey);
        // If it doesn't throw an error, fail the test
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.NOT_FOUND_DATA.code,
        );
        expect((error as BaseExceptionService).data).toBe(
          'Contract data with signature not found. The session may have expired. Please sign the contract again.',
        );
      }
    });
  });

  describe('Upload Deposit Contract', () => {
    const mockJob = {
      jobId: 'mockJobId',
      companyId: 'mockCompany',
    } as JobEntity;

    const mockContractKey = 'mockContractKey';
    it('should upload contract successfully', async () => {
      const link =
        'mockS3Url/company/mockCompany/jobs/mockJobId/เอกสารการรับฝาก_${currentDate}.pdf';

      jest
        .spyOn(cacheManagerService, 'getData')
        .mockResolvedValueOnce('mockContractBase64');

      jest.spyOn(s3Service, 'uploadFile').mockResolvedValueOnce(link);

      // Call the async function
      const result = await contractsService.uploadDepositContract(
        mockJob.jobId,
        mockJob.companyId,
        mockContractKey,
      );

      // Assertions
      expect(cacheManagerService.getData).toHaveBeenCalledWith(
        'mockContractKey',
      );
      expect(result).toEqual(link);
    });

    it('should throw an error when contract data is not found in cache', async () => {
      jest.spyOn(cacheManagerService, 'getData').mockResolvedValueOnce(null);

      try {
        await contractsService.uploadDepositContract(
          mockJob.jobId,
          mockJob.companyId,
          mockContractKey,
        );
        // If it doesn't throw an error, fail the test
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.NOT_FOUND_DATA.code,
        );
        expect((error as BaseExceptionService).data.type).toBe(
          'DEPOSIT_CONTRACT_KEY_CACHE_EXPIRED',
        );
      }
    });
  });

  describe('Upload Transaction', () => {
    const mockJob = {
      jobId: 'mockJobId',
      companyId: 'mockCompany',
    } as JobEntity;

    it('should upload contract successfully', async () => {
      const link =
        'mockS3Url/company/mockCompany/jobs/mockJobId/ใบสัญญาฝากขาย_ddMMyyyy_HHmmss.pdf';

      jest
        .spyOn(cacheManagerService, 'getData')
        .mockResolvedValueOnce('mockContractBase64');

      jest.spyOn(s3Service, 'uploadFile').mockResolvedValueOnce(link);

      jest
        .spyOn(contractsService, 'getTransaction')
        .mockResolvedValueOnce('mockBase64Transaction');

      // Call the async function
      const result = await contractsService.uploadTransaction(mockJob);

      expect(result).toEqual(link);
    });
  });

  describe('Get Pdf Path', () => {
    it('should return the PDF path when contract is found', async () => {
      // Mock data for the test case
      const mockJobId = 'testJobId';
      const mockFileType = 'pdf';

      const mockContract: ContractEntity = {
        jobId: mockJobId,
        pdf: '/nemo-media/test.pdf',
      } as any;

      jest
        .spyOn(contractsRepository, 'findOne')
        .mockResolvedValueOnce(mockContract);

      const result = await contractsService.getPdfPath(mockJobId, mockFileType);

      const expectedPdfPath = 'test.pdf';
      expect(result).toBe(expectedPdfPath);
    });

    it('should throw an exception when contract is not found', async () => {
      // Mock data for the test case
      const mockJobId = 'nonExistingJobId';
      const mockFileType = 'pdf';

      jest.spyOn(contractsRepository, 'findOne').mockResolvedValueOnce(null);

      try {
        await contractsService.getPdfPath(mockJobId, mockFileType);
        // If it doesn't throw an error, fail the test
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.NOT_FOUND_DATA.code,
        );
        expect((error as BaseExceptionService).data).toBe('contract not found');
      }
    });

    it('should throw an exception when contract link is invalid', async () => {
      // Mock data for the test case
      const mockJobId = 'testJobId';
      const mockFileType = 'pdf';

      const mockContract: ContractEntity = {
        jobId: mockJobId,
        pdf: '/invalid-link/test.pdf',
      } as any;

      jest
        .spyOn(contractsRepository, 'findOne')
        .mockResolvedValueOnce(mockContract);

      try {
        await contractsService.getPdfPath(mockJobId, mockFileType);
        // If it doesn't throw an error, fail the test
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.BODY_PAYLOAD_INVALID.code,
        );
        expect((error as BaseExceptionService).data).toBe(
          'Invalid contract link',
        );
      }
    });
  });

  describe('Get PDF Buffer', () => {
    it('should return the PDF buffer when PDF file is found', async () => {
      // Mock data for the test case
      const mockJobId = 'testJobId';
      const mockFileType = 'pdf';
      const mockPdfPath = 'test.pdf';

      jest
        .spyOn(contractsService, 'getPdfPath')
        .mockResolvedValueOnce(mockPdfPath);

      const mockBuffer = Buffer.from('mockPdfBuffer');
      jest.spyOn(s3Service, 'getBufferFile').mockResolvedValueOnce(mockBuffer);

      const result = await contractsService.getPDFBuffer(
        mockJobId,
        mockFileType,
      );

      expect(result).toEqual(mockBuffer);
    });
  });

  describe('getPDFPresignedUrl', () => {
    it('should return the presigned URL when PDF file is found', async () => {
      // Mock data for the test case
      const mockJobId = 'testJobId';
      const mockFileType = 'pdf';
      const mockPdfPath = 'test.pdf';
      const mockPresignedUrl = 'mockPresignedUrl';

      jest
        .spyOn(contractsService, 'getPdfPath')
        .mockResolvedValueOnce(mockPdfPath);

      jest
        .spyOn(s3Service, 'getFileWithSignedUrl')
        .mockResolvedValueOnce(mockPresignedUrl);

      const result = await contractsService.getPDFPresignedUrl(
        mockJobId,
        mockFileType,
      );

      expect(result).toBe(mockPresignedUrl);
    });
  });

  describe('Get Draft Contract', () => {
    const mockCompanyLogo = mockBase64Png;
    const mockBranch = 'mockBranch';
    const mockEmail = 'mockEmail';

    it('should generate draft contract type Dip-Chip success', async () => {
      const jobDipChip = {
        ...mockJob,
        contract: {
          customerInfo: {
            ...mockCustomerInfo,
            type: CustomerInfoType.DIP_CHIP,
          },
        } as ContractEntity,
      };

      jest.spyOn(s3Service, 'getAssetFile').mockResolvedValueOnce({
        Body: {
          transformToByteArray: async () => {
            const mockImg = mockBase64Png;
            // Simulate the transformToByteArray method
            return convertBase64ToBytes(mockImg); // Replace with your actual data
          },
        },
      } as GetObjectCommandOutput);

      jest.spyOn(voucherRepository, 'findOne').mockResolvedValueOnce({
        contractId: 'mockContractId',
        redemptionCode: 'mockRedemptionCode',
      } as VoucherEntity);

      jest.mocked(generateContract).mockResolvedValue(mockGenerateContract);
      // Call the async function
      const result = await contractsService.getDraftContract(
        jobDipChip,
        mockBranch,
        mockCompanyLogo,
        mockEmail,
      );

      expect(result).toBe('transaction');
    });

    it('should generate draft contract type 2FA success', async () => {
      const job2FA = {
        ...mockJob,
        contract: {
          customerInfo: {
            ...mockCustomerInfo,
            photo:
              'http://localhost:9000/nemo-media/company/WW/jobs/mock241MC17HB0TFUGDA1/image?d=1705915816983',
          },
        } as ContractEntity,
      };

      jest.spyOn(s3Service, 'getAssetFile').mockResolvedValueOnce({
        Body: {
          transformToByteArray: async () => {
            const mockImg = mockBase64Png;
            // Simulate the transformToByteArray method
            return convertBase64ToBytes(mockImg); // Replace with your actual data
          },
        },
      } as GetObjectCommandOutput);

      jest.spyOn(s3Service, 'getFile').mockResolvedValueOnce({
        Body: {
          transformToByteArray: async () => {
            const mockImg = mockBase64Png;
            // Simulate the transformToByteArray method
            return convertBase64ToBytes(mockImg); // Replace with your actual data
          },
        },
      } as GetObjectCommandOutput);

      jest.spyOn(voucherRepository, 'findOne').mockResolvedValueOnce({
        contractId: 'mockContractId',
        redemptionCode: 'mockRedemptionCode',
      } as VoucherEntity);

      // Call the async function
      const result = await contractsService.getDraftContract(
        job2FA,
        mockBranch,
        mockCompanyLogo,
        mockEmail,
      );

      expect(typeof result).toBe('string');
    });

    it('should throw Customer info not found', async () => {
      const jobEmptyCustomer = {
        ...mockJob,
        contract: {} as ContractEntity,
      };

      try {
        await contractsService.getDraftContract(
          jobEmptyCustomer,
          mockBranch,
          '',
          mockEmail,
        );
        // If it doesn't throw an error, fail the test
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.NOT_FOUND_DATA.code,
        );
        expect((error as BaseExceptionService).data).toBe(
          'Customer info not found',
        );
      }
    });

    it('should throw Company image not found - invalid url', async () => {
      const jobEmptyCustomer = {
        ...mockJob,
        contract: {
          customerInfo: {
            ...mockCustomerInfo,
            photo:
              'http://localhost:9000/nemo-media/company/WW/jobs/mock241MC17HB0TFUGDA1/image?d=1705915816983',
          },
        } as ContractEntity,
      };

      try {
        await contractsService.getDraftContract(
          jobEmptyCustomer,
          mockBranch,
          '',
          mockEmail,
        );
        // If it doesn't throw an error, fail the test
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.NOT_FOUND_DATA.code,
        );
        expect((error as BaseExceptionService).data).toBe(
          'Company image not found - invalid url',
        );
      }
    });

    it('should throw Customer photo not found - error url', async () => {
      const jobEmptyCustomer = {
        ...mockJob,
        contract: {
          customerInfo: {
            ...mockCustomerInfo,
            photo:
              'http://localhost:9000/nemo/company/WW/jobs/mock241MC17HB0TFUGDA1/image?d=1705915816983',
          },
        } as ContractEntity,
      };

      try {
        jest.spyOn(s3Service, 'getAssetFile').mockResolvedValueOnce({
          Body: {
            transformToByteArray: async () => {
              const mockImg = mockBase64Png;
              // Simulate the transformToByteArray method
              return convertBase64ToBytes(mockImg); // Replace with your actual data
            },
          },
        } as GetObjectCommandOutput);

        await contractsService.getDraftContract(
          jobEmptyCustomer,
          mockBranch,
          mockCompanyLogo,
          mockEmail,
        );
        // If it doesn't throw an error, fail the test
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.NOT_FOUND_DATA.code,
        );
        expect((error as BaseExceptionService).data).toBe(
          'Customer photo not found - error url',
        );
      }
    });
  });

  describe('Get Sign Contract', () => {
    it('should generate sign contract success', async () => {
      // Call the async function
      const result = await contractsService.getSignContract(
        mockBase64Pdf,
        mockBase64Png,
      );

      expect(typeof result.contractKey).toBe('string');
      expect(typeof result.signedContract).toBe('string');
    });
  });

  describe('Get Sign Deposit Contract', () => {
    it('should generate sign contract success', async () => {
      // Call the async function
      const result = await contractsService.getSignDepositContract(
        mockBase64Pdf,
        { sign: mockBase64Png, firstName: 'firstName', lastName: 'lastName' },
      );

      expect(typeof result.contractKey).toBe('string');
      expect(typeof result.signedContract).toBe('string');
    });
  });

  describe('Get Transaction', () => {
    it('should generate transaction success', async () => {
      jest.spyOn(s3Service, 'getAssetFile').mockResolvedValueOnce({
        Body: {
          transformToByteArray: async () => {
            return convertBase64ToBytes(mockBase64Png);
          },
        },
      } as GetObjectCommandOutput);

      jest.mocked(generateTransaction).mockResolvedValue('transaction');
      const result = await contractsService.getTransaction(mockJob);
      expect(typeof result).toBe('string');
    });
    it('should generate transaction fail', async () => {
      jest
        .spyOn(s3Service, 'getAssetFile')
        .mockResolvedValueOnce({} as GetObjectCommandOutput);
      try {
        await contractsService.getTransaction(mockJob);
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.NOT_FOUND_DATA.code,
        );
        expect((error as BaseExceptionService).data).toBe(
          'Company image not found - invalid url',
        );
      }
    });
  });

  describe('Send Contract To Email', () => {
    const mockDefaultContract = {
      customerInfo: {
        thaiName: {
          firstName: 'ชื่อจริง',
          lastName: 'นามสกุล',
        },
        engName: {
          firstName: 'firstName',
          lastName: 'lastName',
        },
        birthDate: '11/11/2556',
        email: 'mockEmail',
      },
      contractLink: 'mocklink',
      importedVouchers: {
        redemptionCode: 'mockCode',
      },
    } as ContractEntity;

    it('should contract to send email successfully', async () => {
      const mockContract = { ...mockDefaultContract };
      jest.mocked(addCampaignCode).mockResolvedValue(mockBase64Pdf);
      jest
        .spyOn(contractsRepository, 'findOne')
        .mockResolvedValue(mockContract);

      jest.spyOn(companyRepository, 'findOne').mockResolvedValue({
        title: 'companyTitle',
        companyEmail: 'companyEmail',
      } as CompanyEntity);

      jest
        .spyOn(contractsService, 'getPdfPath')
        .mockResolvedValueOnce('mockPathPdf');

      const mockBody = {
        Body: {
          transformToByteArray: async () => {
            return convertBase64ToBytes(mockBase64Pdf);
          },
        },
      } as GetObjectCommandOutput;

      jest
        .spyOn(s3Service, 'getFile')
        .mockResolvedValueOnce(mockBody)
        .mockResolvedValueOnce(mockBody);

      jest.spyOn(smtpService, 'sendMail').mockResolvedValueOnce(true);

      const result = await contractsService.sendContractToEmail(
        mockJob,
        mockContract,
      );
      expect(result).toBe(true);
    });
    it.each([
      ['V2', true, true],
      ['V2', true, false],
      ['V2', false, false],
      ['VX', true, false],
    ])(
      'should contract generate pdf correctly with version %s and campaign position %s',
      async (version, hasCampaignPosition, hasCampaign) => {
        const mockContract = { ...mockDefaultContract, version };
        const mockJobTest = { ...mockJob } as JobEntity;
        if (hasCampaignPosition) {
          mockContract.stampPosition = {
            campaign: {
              box: { width: 171.66666666666666 },
              barcode: [
                { page: 1, position: { x: 32, y: 503 } },
                { page: 1, position: { x: 211.66666666666666, y: 503 } },
                { page: 1, position: { x: 391.3333333333333, y: 503 } },
                { page: 1, position: { x: 32, y: 395 } },
              ],
            },
          };
        }
        if (hasCampaign) {
          mockJobTest.campaignRedemptionCode = Array(4).fill({
            redemptionCode: 'mock-test',
            value: 1000,
          } as CampaignRedemptionCodeEntity);
        }
        jest.mocked(addCampaignCode).mockResolvedValue(mockBase64Pdf);
        jest
          .spyOn(contractsRepository, 'findOne')
          .mockResolvedValue(mockContract);

        jest.spyOn(companyRepository, 'findOne').mockResolvedValue({
          title: 'companyTitle',
          companyEmail: 'companyEmail',
        } as CompanyEntity);

        jest
          .spyOn(contractsService, 'getPdfPath')
          .mockResolvedValueOnce('mockPathPdf');

        const mockBody = {
          Body: {
            transformToByteArray: async () => {
              return convertBase64ToBytes(mockBase64Pdf);
            },
          },
        } as GetObjectCommandOutput;

        jest
          .spyOn(s3Service, 'getFile')
          .mockResolvedValueOnce(mockBody)
          .mockResolvedValueOnce(mockBody);

        jest.spyOn(smtpService, 'sendMail').mockResolvedValueOnce(true);

        await contractsService.sendContractToEmail(mockJobTest, mockContract);
        if (version === 'V2' && hasCampaignPosition && hasCampaign) {
          expect(addCampaignCode).toHaveBeenCalled();
        } else {
          expect(addCampaignCode).not.toHaveBeenCalled();
        }
      },
    );

    it('should fail to send email - contractLink empty ', async () => {
      const mockContract = {
        customerInfo: mockDefaultContract.customerInfo,
      } as ContractEntity;

      jest
        .spyOn(contractsRepository, 'findOne')
        .mockResolvedValue(mockContract);

      const result = await contractsService.sendContractToEmail(
        mockJob,
        mockContract,
      );
      expect(result).toBe(false);
    });

    it('should fail to send email - email empty ', async () => {
      const mockContract = {
        customerInfo: { ...mockDefaultContract.customerInfo, email: '' },
        contractLink: 'mocklink',
      } as ContractEntity;

      jest
        .spyOn(contractsRepository, 'findOne')
        .mockResolvedValue(mockContract);

      const result = await contractsService.sendContractToEmail(
        mockJob,
        mockContract,
      );
      expect(result).toBe(false);
    });

    it('should fail to send email - company empty ', async () => {
      const mockContract = { ...mockDefaultContract };

      jest
        .spyOn(contractsRepository, 'findOne')
        .mockResolvedValue(mockContract);
      jest.spyOn(companyRepository, 'findOne').mockResolvedValue(null);

      const result = await contractsService.sendContractToEmail(
        mockJob,
        mockContract,
      );

      expect(result).toBe(false);
    });
  });

  describe('getTransactionInspection', () => {
    const mockCompanyLogo = mockBase64Png;

    it.each([
      [true, mockCompanyLogo],
      [false, ''],
    ])('should pass %s', async (pass, companyLogoS3Path) => {
      jest.spyOn(s3Service, 'getAssetFile').mockResolvedValueOnce({
        Body: {
          transformToByteArray: async () => {
            const mockImg = mockBase64Png;
            // Simulate the transformToByteArray method
            return convertBase64ToBytes(mockImg); // Replace with your actual data
          },
        },
      } as GetObjectCommandOutput);
      jest
        .mocked(generateInspectionTransaction)
        .mockResolvedValue('transaction');
      const result = await contractsService.getTransactionInspection(
        {} as JobEntity,
        companyLogoS3Path,
      );
      if (pass) {
        expect(typeof result).toBe('string');
      } else {
        try {
          await contractsService.getTransactionInspection(
            {} as JobEntity,
            companyLogoS3Path,
          );
          fail('Expected method to throw an error');
        } catch (error) {
          expect(error).toBeInstanceOf(BaseExceptionService);
          expect((error as BaseExceptionService).code).toBe(
            BASE_EXCEPTIONS['NOT_FOUND_DATA'].code,
          );
          expect((error as BaseExceptionService).data).toEqual(
            'Company image not found - invalid url',
          );
        }
      }
    });
  });

  describe('getTransactionAo', () => {
    const mockCompanyLogo = mockBase64Png;

    it.each([
      [true, mockCompanyLogo],
      [false, ''],
    ])('should pass %s', async (pass, companyLogoS3Path) => {
      jest.spyOn(s3Service, 'getAssetFile').mockResolvedValueOnce({
        Body: {
          transformToByteArray: async () => {
            const mockImg = mockBase64Png;
            // Simulate the transformToByteArray method
            return convertBase64ToBytes(mockImg); // Replace with your actual data
          },
        },
      } as GetObjectCommandOutput);

      jest.mocked(generateAoTransaction).mockResolvedValue('transaction');

      const result = await contractsService.getTransactionAo({
        allocationOrder: {} as AllocationOrderEntity,
        jobs: [{} as JobEntity],
        companyLogoS3Path,
      });
      if (pass) {
        expect(typeof result).toBe('string');
      } else {
        try {
          await contractsService.getTransactionAo({
            allocationOrder: {} as AllocationOrderEntity,
            jobs: [{} as JobEntity],
            companyLogoS3Path,
          });
          fail('Expected method to throw an error');
        } catch (error) {
          expect(error).toBeInstanceOf(BaseExceptionService);
          expect((error as BaseExceptionService).code).toBe(
            BASE_EXCEPTIONS['NOT_FOUND_DATA'].code,
          );
          expect((error as BaseExceptionService).data).toEqual(
            'Company image not found - invalid url',
          );
        }
      }
    });
  });
});
