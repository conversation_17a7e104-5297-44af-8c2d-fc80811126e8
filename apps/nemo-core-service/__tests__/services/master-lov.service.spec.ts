import { Test } from '@nestjs/testing';
import { Repository } from 'typeorm';
import { getRepositoryToken } from '@nestjs/typeorm';
import { BaseExceptionModule } from '../../src/base-exceptions/base-exceptions.module';
import { BaseExceptionService } from '../../src/exceptions';
import { BASE_EXCEPTIONS } from '../../src/config';
import { MasterLovService } from '../../src/master-lov/master-lov.service';
import { MasterLovEntity, Locale } from '../../src/entities';
import {
  mockMasterLovResultA,
  resultMasterLovA,
  mockMasterLovResultB,
  resultMasterLovB,
} from '../mock-data/master-lov';

describe('MasterLovService', () => {
  let masterLovService: MasterLovService;
  let masterLovRepository: Repository<MasterLovEntity>;

  beforeEach(async () => {
    const module = await Test.createTestingModule({
      imports: [BaseExceptionModule.register()],
      providers: [
        MasterLovService,
        {
          provide: getRepositoryToken(MasterLovEntity),
          useValue: {
            find: jest.fn().mockReturnThis(),
          },
        },
      ],
    }).compile();

    masterLovService = module.get<MasterLovService>(MasterLovService);
    masterLovRepository = module.get<Repository<MasterLovEntity>>(
      getRepositoryToken(MasterLovEntity),
    );
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('Get Master Lov', () => {
    it('should get List when have query data with additional value prop', async () => {
      jest
        .spyOn(masterLovRepository, 'find')
        .mockResolvedValue(mockMasterLovResultA);

      const result = await masterLovService.getMasterLov({
        type: 'A',
        locale: Locale.TH,
        companyId: 'WW',
      });

      expect(result).toEqual(resultMasterLovA);
      expect(result[0].additionalValue).toHaveProperty('chipColor');
    });

    it('should get List when have query data with no additional value prop', async () => {
      jest
        .spyOn(masterLovRepository, 'find')
        .mockResolvedValue(mockMasterLovResultB);

      const result = await masterLovService.getMasterLov({
        type: 'B',
        locale: Locale.TH,
        companyId: 'WW',
      });

      expect(result).toEqual(resultMasterLovB);
      expect(result[0].additionalValue).toEqual(undefined);
    });

    it('should throw error 2000 when have no query data', async () => {
      jest.spyOn(masterLovRepository, 'find').mockResolvedValue([]);
      try {
        await masterLovService.getMasterLov({
          type: 'A',
          locale: Locale.TH,
          companyId: 'WW',
        });
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.NOT_FOUND_DATA.code,
        );
      }
    });
  });
});
