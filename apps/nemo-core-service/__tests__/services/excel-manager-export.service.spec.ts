import { Test } from '@nestjs/testing';
import { getQueueToken } from '@nestjs/bull';
import { SelectQueryBuilder } from 'typeorm';
import { Request } from 'express';
import { getRepositoryToken } from '@nestjs/typeorm';
import { BaseExceptionModule } from '../../src/base-exceptions/base-exceptions.module';
import {
  ExcelManagerService,
  IConvertToType,
} from '../../src/excel/excel-manager.service';
import { ModelMastersService } from '../../src/admin/model-masters/model-masters.service';

import {
  ModelMasterEntity,
  CompanyEntity,
  SystemConfigEntity,
  JobEntity,
  ModelChecklistEntity,
  ModelPriceActivitiesEntity,
} from '../../src/entities';
import { ExcelManagerModule } from '../../src/excel/excel-manager.module';
import { EncryptDecryptModule } from '../../src/encrypt-decrypt-message/encrypt-decrypt.module';

import { mockExcelData } from '../mock-data/export-excel';
import { mockModelMastersAdmin } from '../mock-data/model-master-admin';
import { Repository } from 'typeorm';

describe('ExcelManagerService', () => {
  let excelManagerService: ExcelManagerService;
  let modelMastersService: ModelMastersService;
  let modelPriceActivitiesRepository: Repository<ModelPriceActivitiesEntity>;

  beforeEach(async () => {
    const module = await Test.createTestingModule({
      imports: [
        EncryptDecryptModule.register(),
        BaseExceptionModule.register(),
        ExcelManagerModule.register({
          headers: {
            PRODUCT_ID: {
              keyName: 'modelKey',
              type: IConvertToType.string,
              isRequired: true,
              subHeader: 'ID',
            },
            MODEL_PRICE: {
              keyName: 'referencePrice',
              type: IConvertToType.numString,
              isRequired: false,
              subHeader: 'ราคาเครื่อง',
            },
            PERCENT_PURCHASE: {
              keyName: 'purchasedRatio',
              type: IConvertToType.number,
              isRequired: false,
              subHeader: '% รับซื้อ',
            },
          },
        }),
      ],
      providers: [
        ModelMastersService,
        {
          provide: getRepositoryToken(ModelMasterEntity),
          useValue: {
            findOne: jest.fn().mockReturnThis(),
          },
        },
        {
          provide: getRepositoryToken(CompanyEntity),
          useValue: {
            findOne: jest.fn().mockReturnThis(),
          },
        },
        {
          provide: getRepositoryToken(SystemConfigEntity),
          useValue: {
            findOne: jest.fn().mockReturnThis(),
          },
        },
        {
          provide: getRepositoryToken(JobEntity),
          useValue: {
            findOne: jest.fn().mockReturnThis(),
          },
        },
        {
          provide: getQueueToken('re-calculate-product-queue'),
          useValue: {
            add: jest.fn(),
            process: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(ModelChecklistEntity),
          useValue: {
            find: jest.fn().mockReturnValue([{ companyId: 'WW' }]),
          },
        },
        {
          provide: getRepositoryToken(ModelPriceActivitiesEntity),
          useValue: {
            save: jest.fn().mockReturnThis(),
          },
        },
      ],
    }).compile();

    excelManagerService = module.get<ExcelManagerService>(ExcelManagerService);

    modelMastersService = module.get<ModelMastersService>(ModelMastersService);

    modelPriceActivitiesRepository = module.get<
      Repository<ModelPriceActivitiesEntity>
    >(getRepositoryToken(ModelPriceActivitiesEntity));
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('export excel', () => {
    it('export success', () => {
      const result =
        modelMastersService.exportExcelModelMasterPrice(mockExcelData);
      result.then((value) => {
        expect(value).toBeInstanceOf(Buffer);
      });
    });
  });
  describe('model master service', () => {
    it('afterLoad', () => {
      const result = modelMastersService.afterLoad(mockModelMastersAdmin);
      result.forEach((data) => {
        expect(data).toHaveProperty('brand');
        expect(data).toHaveProperty('model');
        expect(data).toHaveProperty('rom');
        expect(data).toHaveProperty('gradeA');
        expect(data).toHaveProperty('gradeB');
        expect(data).toHaveProperty('gradeC');
        expect(data).toHaveProperty('gradeD');
      });
    });
  });

  describe('Get Model Masters', () => {
    describe('buildSearchQuery', () => {
      it('should construct search query conditions based on provided parameters', () => {
        const mockQueryBuilder = {
          andWhere: jest.fn(),
        } as unknown as SelectQueryBuilder<ModelMasterEntity>;

        const mockRequest = {
          query: {
            brand: 'iPhone',
            model: '12',
            rom: '256GB',
          },
        } as unknown as Request;

        const result = modelMastersService.buildSearchQuery(
          mockRequest,
          mockQueryBuilder,
        );

        expect(result.andWhere).toHaveBeenCalledWith(
          "r.modelIdentifiers ->> 'brand' = 'iPhone' AND r.modelIdentifiers ->> 'model' = '12' AND r.modelIdentifiers ->> 'rom' = '256GB'",
        );
      });

      it('should not add conditions if parameters are not provided', () => {
        const mockQueryBuilder = {
          andWhere: jest.fn(),
        } as unknown as SelectQueryBuilder<ModelMasterEntity>;

        const mockRequest = {
          query: {},
        } as unknown as Request;

        const result = modelMastersService.buildSearchQuery(
          mockRequest,
          mockQueryBuilder,
        );

        expect(result.andWhere).not.toHaveBeenCalled();
      });
    });
  });
});
