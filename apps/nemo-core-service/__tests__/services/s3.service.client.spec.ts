import { S3Service } from '../../src/storage/s3.service';
import { S3Client } from '@aws-sdk/client-s3';
jest.mock('@aws-sdk/client-s3');

describe('S3Clinet', () => {
  // Save the original process.env to restore it later
  const originalEnv = process.env;

  beforeEach(() => {
    // Reset process.env before each test
    process.env = { ...originalEnv };
  });

  afterEach(() => {
    // Restore process.env after each test
    process.env = originalEnv;
    jest.clearAllMocks();
  });

  it('should create an S3Client with production configuration', () => {
    // Set NODE_ENV to production
    process.env.NODE_ENV = 'production';

    // Create an instance of S3Service
    const s3Service = new S3Service();

    // Ensure S3Client constructor was called with the expected configuration
    expect(S3Client).toHaveBeenCalledWith({
      region: process.env.S3_REGION,
    });

    expect(s3Service).not.toBeNull();
  });

  it('should create an S3Client with development configuration', () => {
    // Set NODE_ENV to development
    process.env.NODE_ENV = 'development';
    // Set required environment variables
    process.env.S3_ACCESS_KEY_ID = 'your-access-key-id';
    process.env.S3_SECRET_ACCESS_KEY = 'your-secret-access-key';
    process.env.S3_REGION = 'your-region';
    process.env.S3_ENDPOIN = 'your-endpoint';

    // Create an instance of S3Service
    const s3Service = new S3Service();

    // Ensure S3Client constructor was called with the expected configuration
    expect(S3Client).toHaveBeenCalledWith({
      credentials: {
        accessKeyId: 'your-access-key-id',
        secretAccessKey: 'your-secret-access-key',
      },
      region: 'your-region',
      endpoint: 'your-endpoint',
      forcePathStyle: true,
    });

    expect(s3Service).not.toBeNull();
  });

  it('should throw an error if AWS credentials are not found', () => {
    // Set NODE_ENV to development
    process.env.NODE_ENV = 'development';

    // Ensure the absence of required environment variables
    delete process.env.S3_ACCESS_KEY_ID;
    delete process.env.S3_SECRET_ACCESS_KEY;

    // Create an instance of S3Service and expect it to throw an error
    expect(() => new S3Service()).toThrowError('AWS credentials not found');
  });
});
