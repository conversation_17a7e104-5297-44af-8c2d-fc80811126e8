import {
  DataSource,
  EntityManager,
  EntityMetadata,
  QueryRunner,
} from 'typeorm';
import {
  VoucherEntity,
  BranchEntity,
  VerificationStatus,
  UserEntity,
} from '../../src/entities';
import { Test } from '@nestjs/testing';
import { VoucherEntitySubscriber } from '../../src/subscriber';
import { FirebaseService } from '../../src/firebase/firebase.service';
import { BaseExceptionModule } from '../../src/base-exceptions/base-exceptions.module';

describe('Voucher Subscriber', () => {
  let voucherSubscriber: VoucherEntitySubscriber;
  let entityManager: EntityManager;
  let firebaseService: FirebaseService;

  const voucherEntity = new VoucherEntity();

  const mockedEvent = {
    entity: voucherEntity,
    databaseEntity: {} as VoucherEntity,
    manager: {} as EntityManager,
    updatedColumns: [],
    connection: {} as DataSource,
    queryRunner: {} as QueryRunner,
    metadata: {} as EntityMetadata,
    updatedRelations: [],
  };

  const mockSave = jest.fn().mockReturnThis();

  beforeAll(async () => {
    const module = await Test.createTestingModule({
      imports: [BaseExceptionModule.register()],
      providers: [
        {
          provide: DataSource,
          useValue: {
            subscribers: {
              push: jest.fn().mockReturnThis(),
            },
          },
        },
        {
          provide: FirebaseService,
          useValue: {
            setData: jest.fn().mockReturnThis(),
            addData: jest.fn().mockReturnThis(),
          },
        },
        {
          provide: EntityManager,
          useValue: {
            getRepository: jest.fn().mockImplementation(() => {
              return { save: mockSave };
            }),
            findOne: jest.fn(() => {
              return {
                roles: [
                  {
                    branchId: 'test',
                    role: ['Sale', 'Manager'],
                  },
                ],
              };
            }),
          },
        },
        VoucherEntitySubscriber,
      ],
    }).compile();

    voucherSubscriber = module.get<VoucherEntitySubscriber>(
      VoucherEntitySubscriber,
    );
    entityManager = module.get<EntityManager>(EntityManager);
    firebaseService = module.get<FirebaseService>(FirebaseService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('After update voucherSubscriber', () => {
    it.each([true, false])('afterUpdate', async (isRoleManager) => {
      let voucherEntity = new VoucherEntity();
      voucherEntity.verificationBranch = {
        branchId: 'test',
      } as BranchEntity;
      voucherEntity.verificationStatus = VerificationStatus.REQUESTED;
      if (!isRoleManager) {
        jest.spyOn(entityManager, 'findOne').mockResolvedValueOnce(null);
      }
      await voucherSubscriber.afterUpdate({
        ...mockedEvent,
        entity: voucherEntity,
        manager: entityManager as EntityManager,
      });
      expect(firebaseService.setData).toHaveBeenCalled();
      expect(firebaseService.addData).toHaveBeenCalledTimes(
        isRoleManager ? 1 : 2,
      );
    });
    it('edge cases', async () => {
      let voucherEntity = new VoucherEntity();
      voucherEntity.updatedAt = new Date();
      voucherEntity.requesterUser = {
        userKey: 'test',
      } as UserEntity;
      voucherEntity.verificationBranch = {} as BranchEntity;
      voucherEntity.verificationStatus = VerificationStatus.REQUESTED;
      jest.spyOn(entityManager, 'findOne').mockResolvedValueOnce({
        roles: null,
      });
      await voucherSubscriber.afterUpdate({
        ...mockedEvent,
        entity: voucherEntity,
        manager: entityManager as EntityManager,
      });
      expect(firebaseService.setData).toHaveBeenCalled();
      expect(firebaseService.addData).toHaveBeenCalledTimes(2);
    });
    it('listenTo', async () => {
      const result = voucherSubscriber.listenTo();
      expect(result).toBe(VoucherEntity);
    });
  });
});
