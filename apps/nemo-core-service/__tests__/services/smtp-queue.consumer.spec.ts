import { Test } from '@nestjs/testing';
import { BaseExceptionModule } from '../../src/base-exceptions/base-exceptions.module';
import { EmailQueueConsumer } from '../../src/smtp/smtp-queue.consumer';
import { Job } from 'bull';
import { SmtpService } from '../../src/smtp/smtp.service';

describe('SMTP Queue Consumer', () => {
  let emailQueueConsumer: EmailQueueConsumer;

  beforeEach(async () => {
    const module = await Test.createTestingModule({
      imports: [BaseExceptionModule.register()],
      providers: [
        EmailQueueConsumer,
        {
          provide: SmtpService,
          useValue: {
            sendMail: jest.fn().mockReturnThis(),
          },
        },
      ],
    }).compile();

    emailQueueConsumer = module.get<EmailQueueConsumer>(EmailQueueConsumer);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('Send email queue', () => {
    it('send email success', async () => {
      const templateEmail = {
        mailOption: {
          from: '<EMAIL>',
          to: '<EMAIL>',
          subject: 'test mail queue',
        },
      };
      const result = await emailQueueConsumer.sendMail({
        data: templateEmail,
      } as Job);

      expect(result).toBeUndefined();
    });
  });
});
