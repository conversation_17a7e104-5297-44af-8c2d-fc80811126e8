import { ActivitiesType } from '../../src/subscriber';
import {
  BranchEntity,
  CompanyEntity,
  JobActivitiesEntity,
  JobEntity,
  JobStatus,
  ModelMasterEntity,
} from '../../src/entities';
import {
  makeCollectionPath,
  makeJobData,
  makePendingJobPath,
} from '../../src/subscriber/helper';

describe('Subscriber helper', () => {
  const jobEntity: JobEntity = {
    companyId: 'test',
    jobId: '123456',
    deviceKey: '',
    branchId: 'test-branch',
    modelKey: '',
    createdBy: '',
    status: JobStatus.DRAFT,
    modelIdentifiers: undefined,
    modelTemplate: new ModelMasterEntity(),
    checkList: [],
    checkListValues: undefined,
    shopUserKey: '',
    modelMaster: new ModelMasterEntity(),
    company: new CompanyEntity(),
    branch: new BranchEntity(),
    isUpdatedToEstimated: function (): boolean {
      throw new Error('Function not implemented.');
    },
    createdAt: new Date(),
    isConfirmPrice: false,
    jobVendor: 'MASS',
    phoneNumber: '0123456789',
  };

  const jobActivityEntity: JobActivitiesEntity = {
    jobId: 'jobId',
    activityId: '',
    companyId: 'test',
    type: '',
    detail: {
      summary: '',
      branchId: '123456',
      status: '',
    },
    createdBy: '',
    job: new JobEntity(),
    isAdminAction: false,
    createdAt: new Date(),
  };

  describe('Make Collection Path', () => {
    it('should return conversation path', () => {
      const response = makeCollectionPath(
        jobActivityEntity,
        ActivitiesType.CONVERSATION,
      );

      expect(response).toBe('company/test/conversation/jobId/activity');
    });

    it('should return inbox path', () => {
      const response = makeCollectionPath(
        jobActivityEntity,
        ActivitiesType.INBOX,
      );

      expect(response).toBe('company/test/inbox/123456/message');
    });

    it('should return admin path', () => {
      const response = makeCollectionPath(
        jobActivityEntity,
        ActivitiesType.INBOX,
        true,
      );

      expect(response).toBe('company/test/inbox/admin/message');
    });
  });

  describe('Make Path', () => {
    it('should return path', () => {
      const response = makePendingJobPath(jobEntity);
      expect(response).toBe('company/test/pending-job/test-branch/job/123456');
    });
  });

  describe('Make Data', () => {
    it('should return job entity data', () => {
      const job = new JobEntity();
      Object.assign(job, { ...jobEntity });
      const response = makeJobData(job);
      expect(response).toStrictEqual({
        jobId: '123456',
        adminUserKey: undefined,
        shopUserKey: '',
        createAt: +jobEntity.createdAt,
        createdBy: '',
        status: JobStatus.DRAFT,
        updatedAt: 0,
      });
    });

    it('should return job activity entity data', () => {
      const response = makeJobData(jobActivityEntity);
      expect(response).toStrictEqual({
        jobId: 'jobId',
        activityId: '',
        type: '',
        detail: {
          branchId: '123456',
          status: '',
          summary: '',
        },
        createAt: +jobActivityEntity.createdAt,
        createdBy: '',
      });
    });
  });
});
