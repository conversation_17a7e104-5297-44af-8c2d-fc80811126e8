import { Test } from '@nestjs/testing';
import { BaseExceptionModule } from '../../src/base-exceptions/base-exceptions.module';

import { LogRequest, NemoLoggerService } from '../../src/logger/logger.service';

describe('Logger Service', () => {
  let loggerService: NemoLoggerService;

  beforeEach(async () => {
    const module = await Test.createTestingModule({
      imports: [BaseExceptionModule.register()],
      providers: [NemoLoggerService],
    }).compile();

    loggerService = module.get<NemoLoggerService>(NemoLoggerService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should set the app name', () => {
    const newAppName = 'MyApp';
    loggerService.setAppName(newAppName);

    const updatedAppName = loggerService.getAppName();
    expect(updatedAppName).toEqual(newAppName);
  });

  it('should set traceId, userId, and requestTime if headers are present', () => {
    const request = {
      headers: {
        traceId: '123456',
        userid: 'user123',
        requestTime: '1638200000000',
      },
      ip: '127.0.0.1',
    };

    loggerService.setHeaderRequest(request);

    expect(loggerService.getTraceId()).toEqual('123456');
    expect(loggerService.getUserId()).toEqual('user123');
    expect(loggerService.getRequestTime()).toEqual(1638200000000);
    expect(loggerService.getIpAddress()).toEqual('127.0.0.1');
  });

  it('should not set traceId, userId, and requestTime if headers are not present', () => {
    const request = {
      ip: '127.0.0.1',
    };

    loggerService.setHeaderRequest(request);

    expect(loggerService.getTraceId()).toEqual('-');
    expect(loggerService.getUserId()).toEqual('-');

    expect(loggerService.getRequestTime()).toEqual(expect.any(Number));
    expect(loggerService.getIpAddress()).toEqual('127.0.0.1');
  });

  it('should log a message with the specified class name at info level', () => {
    const spyPrintMessage = jest.spyOn(loggerService, 'printMessage');
    const spyCreateApiLog = jest.spyOn(loggerService, 'createApiLog');

    const message = 'Test message';
    const className = 'TestClass';

    loggerService.logWithClassName(message, className);

    expect(spyCreateApiLog).toHaveBeenCalledWith('info', message);
    expect(spyPrintMessage).toHaveBeenCalledWith(
      expect.stringContaining('"className":"' + className + '"'),
    );
  });

  it('should set the class name and log a message even if class name is empty', () => {
    const spyPrintMessage = jest.spyOn(loggerService, 'printMessage');
    const spyCreateApiLog = jest.spyOn(loggerService, 'createApiLog');

    const message = 'Test message';
    const className = '';

    loggerService.logWithClassName(message, className);

    expect(spyCreateApiLog).toHaveBeenCalledWith('info', message);
    expect(spyPrintMessage).toHaveBeenCalledWith(
      expect.stringContaining('"className":"-"'),
    );
  });

  it('should log a message at info level', () => {
    const spyPrintMessage = jest.spyOn(loggerService, 'printMessage');
    const spyCreateApiLog = jest.spyOn(loggerService, 'createApiLog');

    const message = 'Test message';

    loggerService.log(message);

    expect(spyCreateApiLog).toHaveBeenCalledWith('info', message);
    expect(spyPrintMessage).toHaveBeenCalledWith(
      expect.stringContaining('"message":"' + message + '"'),
    );
  });

  it('should log an error message without trace', () => {
    const spyPrintMessage = jest.spyOn(loggerService, 'printMessage');
    const spyCreateApiLog = jest.spyOn(loggerService, 'createApiLog');

    const errorMessage = 'Error message';

    loggerService.error(errorMessage);

    expect(spyCreateApiLog).toHaveBeenCalledWith('error', errorMessage);
    expect(spyPrintMessage).toHaveBeenCalledWith(
      expect.stringContaining('"message":"' + errorMessage + '"'),
    );
  });

  it('should log an error message with trace', () => {
    const spyPrintMessage = jest.spyOn(loggerService, 'printMessage');
    const spyCreateApiLog = jest.spyOn(loggerService, 'createApiLog');

    const errorMessage = 'Error message';
    const trace = 'Error trace';

    loggerService.error(errorMessage, trace);

    expect(spyCreateApiLog).toHaveBeenCalledWith('error', errorMessage);
    expect(spyPrintMessage).toHaveBeenCalledWith(
      expect.stringContaining('"message":"' + errorMessage + '"'),
    );

    // Ensure that trace is logged
    expect(spyCreateApiLog).toHaveBeenCalledWith('error', trace);
    expect(spyPrintMessage).toHaveBeenCalledWith(
      expect.stringContaining('"message":"' + trace + '"'),
    );
  });

  it('should log a warning message', () => {
    const spyPrintMessage = jest.spyOn(loggerService, 'printMessage');
    const spyCreateApiLog = jest.spyOn(loggerService, 'createApiLog');

    const warningMessage = 'Warning message';

    loggerService.warn(warningMessage);

    expect(spyCreateApiLog).toHaveBeenCalledWith('warn', warningMessage);
    expect(spyPrintMessage).toHaveBeenCalledWith(
      expect.stringContaining('"message":"' + warningMessage + '"'),
    );
  });

  it('should log a debug message', () => {
    const spyPrintMessage = jest.spyOn(loggerService, 'printMessage');
    const spyCreateApiLog = jest.spyOn(loggerService, 'createApiLog');

    const debugMessage = 'Debug message';

    loggerService.debug(debugMessage);

    expect(spyCreateApiLog).toHaveBeenCalledWith('debug', debugMessage);
    expect(spyPrintMessage).toHaveBeenCalledWith(
      expect.stringContaining('"message":"' + debugMessage + '"'),
    );
  });

  it('should log a verbose message', () => {
    const spyPrintMessage = jest.spyOn(loggerService, 'printMessage');
    const spyCreateApiLog = jest.spyOn(loggerService, 'createApiLog');

    const verboseMessage = 'Verbose message';

    loggerService.verbose(verboseMessage);

    expect(spyCreateApiLog).toHaveBeenCalledWith('verbose', verboseMessage);
    expect(spyPrintMessage).toHaveBeenCalledWith(
      expect.stringContaining('"message":"' + verboseMessage + '"'),
    );
  });

  it('should log an error message without trace and with a specified class name', () => {
    const spyPrintMessage = jest.spyOn(loggerService, 'printMessage');
    const spyCreateApiLog = jest.spyOn(loggerService, 'createApiLog');

    const errorMessage = 'Error message';
    const className = 'TestClass';

    loggerService.errorWithClassName(errorMessage, className);

    expect(spyCreateApiLog).toHaveBeenCalledWith('error', errorMessage);

    expect(spyPrintMessage).toHaveBeenCalledWith(
      expect.stringContaining('"message":"' + errorMessage + '"'),
    );
  });

  it('should log an error message with trace and a specified class name', () => {
    const spyPrintMessage = jest.spyOn(loggerService, 'printMessage');
    const spyCreateApiLog = jest.spyOn(loggerService, 'createApiLog');

    const errorMessage = 'Error message';
    const trace = 'Error trace';
    const className = 'TestClass';

    loggerService.errorWithClassName(errorMessage, className, trace);

    expect(spyCreateApiLog).toHaveBeenCalledWith('error', errorMessage);
    expect(spyCreateApiLog).toHaveBeenCalledWith('error', trace);

    expect(spyPrintMessage).toHaveBeenCalledWith(
      expect.stringContaining('"message":"' + errorMessage + '"'),
    );
    expect(spyPrintMessage).toHaveBeenCalledWith(
      expect.stringContaining('"message":"' + trace + '"'),
    );
  });

  it('should log a warning message with a specified class name', () => {
    const spyPrintMessage = jest.spyOn(loggerService, 'printMessage');
    const spyCreateApiLog = jest.spyOn(loggerService, 'createApiLog');

    const warningMessage = 'Warning message';
    const className = 'TestClass';

    loggerService.warnWithClassName(warningMessage, className);
    expect(spyCreateApiLog).toHaveBeenCalledWith('warn', warningMessage);
    expect(spyPrintMessage).toHaveBeenCalledWith(
      expect.stringContaining('"message":"' + warningMessage + '"'),
    );
  });

  it('should log a debug message with a specified class name', () => {
    const spyPrintMessage = jest.spyOn(loggerService, 'printMessage');
    const spyCreateApiLog = jest.spyOn(loggerService, 'createApiLog');

    const debugMessage = 'Debug message';
    const className = 'TestClass';

    loggerService.debugWithClassName(debugMessage, className);

    expect(spyCreateApiLog).toHaveBeenCalledWith('debug', debugMessage);
    expect(spyPrintMessage).toHaveBeenCalledWith(
      expect.stringContaining('"message":"' + debugMessage + '"'),
    );
  });

  it('should log a verbose message with a specified class name', () => {
    const spyPrintMessage = jest.spyOn(loggerService, 'printMessage');
    const spyCreateApiLog = jest.spyOn(loggerService, 'createApiLog');

    const verboseMessage = 'Verbose message';
    const className = 'TestClass';

    loggerService.verboseWithClassName(verboseMessage, className);

    expect(spyCreateApiLog).toHaveBeenCalledWith('verbose', verboseMessage);
    expect(spyPrintMessage).toHaveBeenCalledWith(
      expect.stringContaining('"message":"' + verboseMessage + '"'),
    );
  });

  it('should log a request with provided data', () => {
    const spyPrintMessage = jest.spyOn(loggerService, 'printMessage');
    const mockReq: LogRequest = {
      method: 'GET',
      reqHeader: { 'Content-Type': 'application/json' },
      reqBody: { key: 'value' },
      respBody: { status: 'success' },
    } as any;
    const execTime = 100; // Mock execution time in milliseconds

    loggerService.request(mockReq, execTime);

    const expectedLog = {
      trace_id: '-',
      method: 'GET',
      header: JSON.stringify({
        ipAddess: '-',
        'Content-Type': 'application/json',
      }),
      body: {
        execTime: 100,
        reqBody: JSON.stringify({ key: 'value' }),
        respBody: JSON.stringify({ status: 'success' }),
      },
    };

    expect(spyPrintMessage).toHaveBeenCalledWith(JSON.stringify(expectedLog));
  });

  it('should log an audit entry with provided data', () => {
    const spyPrintMessage = jest.spyOn(loggerService, 'printMessage');
    const eventCode = 'LOGIN';
    const subEventCode = 'SUCCESS';
    const data = { username: 'john_doe', role: 'admin' };

    loggerService.audit(eventCode, subEventCode, data);

    const expectedLog = {
      EVENT_CODE: eventCode,
      SUB_EVENT_CODE: subEventCode,
      message: JSON.stringify(data),
    };

    expect(spyPrintMessage).toHaveBeenCalledWith(
      expect.stringContaining(JSON.stringify(expectedLog)),
    );
  });

  it('should log an audit entry with default values for missing data', () => {
    const spyPrintMessage = jest.spyOn(loggerService, 'printMessage');
    const eventCode = 'LOGOUT';
    const subEventCode = 'SUCCESS';

    loggerService.audit(eventCode, subEventCode);

    const expectedLog = {
      EVENT_CODE: eventCode,
      SUB_EVENT_CODE: subEventCode,
      message: JSON.stringify({}),
    };

    expect(spyPrintMessage).toHaveBeenCalledWith(
      expect.stringContaining(JSON.stringify(expectedLog)),
    );
  });
});
