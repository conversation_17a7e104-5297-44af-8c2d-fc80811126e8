import { getRepositoryToken } from '@nestjs/typeorm';
import { Test } from '@nestjs/testing';
import { BaseExceptionModule } from '../../src/base-exceptions/base-exceptions.module';
import { JobsService } from '../../src/shop/jobs/jobs.service';
import {
  JobActivitiesEntity,
  JobEntity,
  JobStatus,
  ModelMasterEntity,
  PenaltiesView,
  JobTemplateEntity,
  ContractEntity,
  CustomerInfoType,
  CustomerInfo,
  BranchEntity,
  CompanyEntity,
  JobActivitiesType,
  JobActivityDetail,
  SystemConfigEntity,
  UserEntity,
  EstimationActivitiesEntity,
  ModelMasterFunctionEntity,
  GeneralActivitiesEntity,
  AOShippingStatus,
  CampaignEntity,
  CampaignRedemptionCodeEntity,
} from '../../src/entities';
import { MasterAddressEntity } from '../../src/entities/address-master.entity';
import { EntityManager, In, Repository, SelectQueryBuilder } from 'typeorm';
import { Request } from 'express';
import { S3Service } from '../../src/storage/s3.service';
import { WithBranchContext, WithUserContext } from '../../src/interfaces';
import { mockModelMasters } from '../mock-data/model-master';
import { mockPenalties } from '../mock-data/penalties';
import { BaseExceptionService } from '../../src/exceptions';
import { BASE_EXCEPTIONS, getS3JobUrlPath } from '../../src/config';
import { ContractsService } from '../../src/shop/contracts/contracts.service';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { CacheManagerService } from '../../src/cache-manager/cache-manager.service';
import { OcrService } from '../../src/ocr/ocr.service';
import { mock, instance } from 'ts-mockito';
import { mockBase64Png } from '../mock-data/base64';
import { SignContractDto, ImageJobParamDto } from 'src/shop/jobs/dto';
import { SignDepositContractDto } from 'src/shop/jobs/dto/sign-deposit-contract.dto';
import { CreateJobDraftSignDto } from 'src/shop/jobs/dto/create-job-draft-sign.dto';
import {
  mockCompanyId,
  mockEstimationActivity,
  mockOrderId,
  mockModelMaster,
  mockResultGetFunctionFromModelMasterFn,
  mockModelMasterFunctionsForGetFunctionFromModelMasterFn,
  mockEAQuestionsJob,
} from '../../__tests__/mock-data/common-order';
import { getFunctionFromModelMasterFn } from '../../src/utils/common-order';
import {
  mockJobMediUrl,
  mockJobCampaignSelect,
  mockJobCampaignSelect2,
  mockJobCampaignSelectInvalidUser,
  mockJobCampaignSelectInvalidStatus,
  mockJob,
} from '../mock-data/job';
import { generateProductImageSlugForTemplate } from '../../src/utils/survey-template/dynamic-product-images';
import { mockCampaign } from '../mock-data/campaign';
import {
  mockCampaignRedemptionCode,
  mockCampaignRedemptionCodeList,
} from '../mock-data/campaign-redemption-code';
import { getStatusJobByType } from '../../src/utils/job/dataJob';
import { JobRequestQueueModule } from '../../src/shop/jobs/job-request-queue.module';
import { getQueueToken } from '@nestjs/bull';

jest.mock('../../src/utils/dynamic-import', () => ({
  dynamicImport: jest.fn(() => {
    return {
      fileTypeFromBuffer: jest.fn(() => {
        return {
          mime: 'image/png',
        };
      }),
    };
  }),
}));

jest.mock('../../src/utils/job/dataJob', () => {
  const original = jest.requireActual('../../src/utils/job/dataJob');
  return {
    ...original,
    getStatusJobByType: jest.fn(),
  };
});

jest.mock('../../src/utils/survey-template/dynamic-product-images', () => {
  const original = jest.requireActual(
    '../../src/utils/survey-template/dynamic-product-images',
  );
  return {
    ...original,
    generateProductImageSlugForTemplate: jest.fn(),
  };
});

jest.mock('../../src/utils/common-order', () => {
  const original = jest.requireActual('../../src/utils/common-order');
  return {
    ...original,
    getFunctionFromModelMasterFn: jest.fn(),
  };
});

describe('JobsService', () => {
  let jobsService: JobsService;
  let contractsService: ContractsService;
  let jobsRepository: Repository<JobEntity>;
  let modelMastersRepository: Repository<ModelMasterEntity>;
  let penaltiesViewRepository: Repository<PenaltiesView>;
  let jobTemplatesRepository: Repository<JobTemplateEntity>;

  let jobActivitiesRepository: Repository<JobActivitiesEntity>;
  let mockEntityManager: EntityManager;

  let branchRepository: Repository<BranchEntity>;
  let companyRepository: Repository<CompanyEntity>;

  let contractRepo: Repository<ContractEntity>;

  let estimateActivitiesRepository: Repository<EstimationActivitiesEntity>;

  let modelMasterFunctionRepository: Repository<ModelMasterFunctionEntity>;

  let campaignRepo: Repository<CampaignEntity>;
  let campaignRedemptionCodeRepo: Repository<CampaignRedemptionCodeEntity>;
  let s3Service: S3Service;
  let ocrService: OcrService;

  beforeEach(async () => {
    mockEntityManager = mock(EntityManager);
    const module = await Test.createTestingModule({
      imports: [BaseExceptionModule.register()],
      providers: [
        JobsService,
        CacheManagerService,
        {
          provide: S3Service,
          useValue: {
            getPreviewUrl: jest.fn(
              () => `company/CompanyX/jobs/123/device-image/imageKey`,
            ),
            uploadFile: jest.fn(
              () => `company/CompanyY/jobs/456/device-image/imageKey2`,
            ),
            getUploadFilePreSignedUrl: jest.fn(
              () => `company/CompanyX/jobs/jobid-0001/media/test_video_111`,
            ),
            getFile: jest.fn(),
            uploadFileByPresignedUrl: jest.fn(),
            getFileWithSignedUrl: jest.fn(),
          },
        },
        {
          provide: ContractsService,
          useValue: {
            decryptContractData: jest.fn(
              (contract: any) => `Decrypted_${contract}`,
            ),
            encryptData: jest.fn((data: any) => `Encrypted_${data}`),
            provisionContract: jest.fn(),
            confirmCampaignRedemptionCode: jest.fn(),
            confirmContract: jest.fn(),
            getDraftContract: jest.fn(() => 'draftContract'),
            getSignContract: jest.fn(() => 'signContract'),
            getSignDepositContract: jest.fn(() => 'signDepositContract'),
            sendContractToEmail: jest.fn(() => true),
            uploadDepositContract: jest.fn(() => 'depositContract'),
          },
        },
        {
          provide: EntityManager,
          useValue: instance(mockEntityManager),
        },
        {
          provide: getRepositoryToken(JobEntity),
          useValue: {
            findOne: jest.fn().mockReturnThis(),
            manager: jest.fn().mockReturnThis(),
            save: jest.fn().mockReturnThis(),
            createQueryBuilder: jest.fn().mockReturnThis(),
          },
        },
        {
          provide: getRepositoryToken(ModelMasterEntity),
          useValue: {
            findOne: jest.fn().mockReturnThis(),
          },
        },
        {
          provide: getRepositoryToken(PenaltiesView),
          useValue: {
            findOne: jest.fn().mockReturnThis(),
          },
        },
        {
          provide: getRepositoryToken(JobTemplateEntity),
          useValue: {
            findOne: jest.fn().mockReturnThis(),
          },
        },
        {
          provide: getRepositoryToken(JobActivitiesEntity),
          useValue: {
            findOne: jest.fn().mockReturnThis(),
            save: jest.fn().mockReturnThis(),
          },
        },
        {
          provide: getRepositoryToken(ContractEntity),
          useValue: {
            findOne: jest.fn().mockReturnThis(),
          },
        },
        {
          provide: getRepositoryToken(BranchEntity),
          useValue: {
            findOne: jest.fn().mockReturnThis(),
          },
        },
        {
          provide: getRepositoryToken(CompanyEntity),
          useValue: {
            findOne: jest.fn().mockReturnThis(),
          },
        },
        {
          provide: getRepositoryToken(UserEntity),
          useValue: {
            findOne: jest.fn().mockReturnThis(),
          },
        },
        {
          provide: getRepositoryToken(SystemConfigEntity),
          useValue: {
            findOne: jest.fn().mockReturnThis(),
          },
        },
        {
          provide: getRepositoryToken(EstimationActivitiesEntity),
          useValue: {
            findOne: jest.fn().mockReturnThis(),
          },
        },
        {
          provide: getRepositoryToken(MasterAddressEntity),
          useValue: {
            findOne: jest.fn().mockReturnThis(),
            createQueryBuilder: jest.fn().mockReturnThis(),
            select: jest.fn().mockReturnThis(),
            andWhere: jest.fn().mockReturnThis(),
            where: jest.fn().mockReturnThis(),
            getRawOne: jest.fn().mockReturnThis(),
          },
        },
        {
          provide: CACHE_MANAGER,
          useValue: {
            get: jest.fn(),
            set: jest.fn(),
            reset: jest.fn(),
            del: jest.fn(),
            store: {
              client: {
                multi: jest.fn().mockReturnThis(),
                incr: jest.fn().mockReturnThis(),
                expire: jest.fn().mockReturnThis(),
                exec: jest.fn().mockReturnValueOnce([[0]]),
              },
            },
          },
        },
        {
          provide: getRepositoryToken(ModelMasterFunctionEntity),
          useValue: {
            find: jest.fn().mockReturnThis(),
          },
        },
        {
          provide: getRepositoryToken(GeneralActivitiesEntity),
          useValue: {
            find: jest.fn().mockReturnThis(),
            save: jest.fn().mockReturnThis(),
          },
        },
        {
          provide: OcrService,
          useValue: {
            getOcrResult: jest.fn(() => ({
              district1: 'พระบรมมหาราชวัง',
              district2: 'เขตพระนคร',
              province: 'กรุงเทพมหานคร',
            })),
          },
        },
        {
          provide: getRepositoryToken(CampaignEntity),
          useValue: {
            findOne: jest.fn().mockReturnThis(),
          },
        },
        {
          provide: getQueueToken('job-request-queue'),
          useValue: {
            add: jest.fn(),
            process: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(CampaignRedemptionCodeEntity),
          useValue: {
            findOne: jest.fn().mockReturnThis(),
            find: jest.fn().mockReturnThis(),
            createQueryBuilder: jest.fn().mockReturnThis(),
            setLock: jest.fn().mockReturnThis(),
            setOnLocked: jest.fn().mockReturnThis(),
            select: jest.fn().mockReturnThis(),
            where: jest.fn().mockReturnThis(),
          },
        },
      ],
    }).compile();

    jobsService = module.get<JobsService>(JobsService);
    contractsService = module.get<ContractsService>(ContractsService);
    ocrService = module.get<OcrService>(OcrService);
    jobsRepository = module.get<Repository<JobEntity>>(
      getRepositoryToken(JobEntity),
    );
    modelMastersRepository = module.get<Repository<ModelMasterEntity>>(
      getRepositoryToken(ModelMasterEntity),
    );
    penaltiesViewRepository = module.get<Repository<PenaltiesView>>(
      getRepositoryToken(PenaltiesView),
    );
    jobTemplatesRepository = module.get<Repository<JobTemplateEntity>>(
      getRepositoryToken(JobTemplateEntity),
    );
    jobActivitiesRepository = module.get<Repository<JobActivitiesEntity>>(
      getRepositoryToken(JobActivitiesEntity),
    );
    branchRepository = module.get<Repository<BranchEntity>>(
      getRepositoryToken(BranchEntity),
    );
    companyRepository = module.get<Repository<CompanyEntity>>(
      getRepositoryToken(CompanyEntity),
    );

    contractRepo = module.get<Repository<ContractEntity>>(
      getRepositoryToken(ContractEntity),
    );

    estimateActivitiesRepository = module.get<
      Repository<EstimationActivitiesEntity>
    >(getRepositoryToken(EstimationActivitiesEntity));

    modelMasterFunctionRepository = module.get<
      Repository<ModelMasterFunctionEntity>
    >(getRepositoryToken(ModelMasterFunctionEntity));

    campaignRepo = module.get<Repository<CampaignEntity>>(
      getRepositoryToken(CampaignEntity),
    );
    campaignRedemptionCodeRepo = module.get<
      Repository<CampaignRedemptionCodeEntity>
    >(getRepositoryToken(CampaignRedemptionCodeEntity));
    s3Service = module.get<S3Service>(S3Service);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('Build Search Query', () => {
    it('should construct search query conditions based on provided parameters', () => {
      const mockRequest = {
        query: {
          search: 'searchKeyword',
          deviceKey: 'deviceKey',
          status: ['status1', 'status2'],
          excludeStatus: ['status3', 'status4'],
          shopUserKey: 'shopUserKey',
          aoShippingStatus: '00_SHIPPED',
        },
      } as unknown as Request;

      const mockListQuery: SelectQueryBuilder<JobEntity> = {
        andWhere: jest.fn(),
      } as unknown as SelectQueryBuilder<JobEntity>;

      const result = jobsService.buildSearchQuery(mockRequest, mockListQuery);

      expect(result.andWhere).toHaveBeenCalledWith(
        expect.any(String),
        expect.any(Object),
      );
    });

    it.each([
      ['ILIKE', false],
      ['=', true],
    ])(
      '[case isExactSearch] should use %s condition on search when isExactSearch %s and value is searchValue',
      async (condition, isExactSearch) => {
        const searchValue = 'searchValue';
        const mockRequest = {
          query: {
            search: searchValue,
            isExactSearch,
          },
        } as unknown as any;

        const mockListQuery: SelectQueryBuilder<JobEntity> = {
          andWhere: jest.fn(),
        } as unknown as SelectQueryBuilder<JobEntity>;

        const result = jobsService.buildSearchQuery(mockRequest, mockListQuery);

        expect(result.andWhere).toHaveBeenCalledWith(
          expect.stringContaining(condition),
          expect.objectContaining({
            jobId: isExactSearch ? searchValue : `%${searchValue}%`,
          }),
        );
      },
    );

    it.each`
      isExcludeUser | shopUserKey      | search
      ${true}       | ${'shopUserKey'} | ${'searchItem'}
      ${false}      | ${'shopUserKey'} | ${'searchItem'}
      ${true}       | ${'shopUserKey'} | ${undefined}
      ${false}      | ${'shopUserKey'} | ${undefined}
    `(
      'buildSearchQuery (isExcludeUser)',
      async ({ isExcludeUser, shopUserKey, search }) => {
        const mockQueryBuilder = {
          andWhere: jest.fn(),
        } as unknown as any;
        const mockRequest = {
          query: {
            isExcludeUser,
            shopUserKey,
            search,
          },
        } as unknown as any;
        const result = jobsService.buildSearchQuery(
          mockRequest,
          mockQueryBuilder,
        );

        if (search) {
          if (isExcludeUser) {
            expect(result.andWhere).toHaveBeenCalledWith(
              expect.not.stringContaining(
                'r.shopUserName ILIKE :shopUserName)',
              ),
              expect.not.objectContaining({ shopUserName: '%searchItem%' }),
            );
          } else {
            expect(result.andWhere).toHaveBeenCalledWith(
              expect.stringContaining('r.shopUserName ILIKE :shopUserName)'),
              expect.objectContaining({ shopUserName: '%searchItem%' }),
            );
          }
          expect(result.andWhere).toHaveBeenCalledTimes(2);
        } else {
          // if not provide search key word then skip andWhere from search section
          expect(result.andWhere).toHaveBeenCalledTimes(1);
        }
      },
    );
  });

  describe('Sanitize Input Body', () => {
    it('should omit jobId field if isCreated is false', () => {
      const mockData: Partial<JobEntity> = {
        jobId: '1',
      };
      const result = jobsService.sanitizeInputBody(mockData, false);

      expect(result).not.toHaveProperty('jobId');
    });
    it('should omit jobId field if isCreated is true', () => {
      const mockData: Partial<JobEntity> = {
        jobId: '1',
      };
      const result = jobsService.sanitizeInputBody(mockData, true);

      expect(result).toHaveProperty('jobId');
    });
  });

  describe('Compute Update Payload', () => {
    it.each`
      initialStatus                          | updatedStatus
      ${JobStatus.DRAFT}                     | ${JobStatus.QUOTE_REQUESTED}
      ${JobStatus.QUOTE_REQUESTED}           | ${JobStatus.ESTIMATE_PRICE_PROCESSING}
      ${JobStatus.ESTIMATE_PRICE_PROCESSING} | ${JobStatus.PRICE_ESTIMATED}
      ${JobStatus.ESTIMATE_PRICE_PROCESSING} | ${JobStatus.REJECT_BY_SHOP}
      ${JobStatus.PRICE_ESTIMATED}           | ${JobStatus.IDENTITY_REQUESTED}
      ${JobStatus.IDENTITY_REQUESTED}        | ${JobStatus.IDENTITY_REJECTED}
      ${JobStatus.PRICE_ESTIMATED}           | ${JobStatus.IDENTITY_VERIFIED}
      ${JobStatus.CAMPAIGN_SELECTED}         | ${JobStatus.PURCHASED}
    `(
      'should allow updating $initialStatus to $updatedStatus',
      ({ initialStatus, updatedStatus }) => {
        const rawJob: JobEntity = {
          status: initialStatus,
        } as JobEntity;

        const result = jobsService.computeUpdatePayload(rawJob, {
          status: updatedStatus,
        });

        expect(result).toEqual({
          status: updatedStatus,
        });
      },
    );

    it('should allow updating when checkListValues not null', () => {
      const rawJob: JobEntity = {
        isAdditionalCheckList: true,
        status: JobStatus.ESTIMATE_PRICE_PROCESSING,
      } as JobEntity;
      const updatedData: Partial<JobEntity> = {
        checkListValues: {
          value1: 'A',
          value2: 'B',
        },
      };

      const result = jobsService.computeUpdatePayload(rawJob, updatedData);

      expect(result).toEqual(updatedData);
    });

    it('should allow updating when contract not null', () => {
      const rawJob: JobEntity = {
        contract: {
          customerInfo: {
            type: CustomerInfoType.IDENTITY_VERIFICATION,
          } as CustomerInfo,
        } as ContractEntity,
        status: JobStatus.IDENTITY_REQUESTED,
      } as JobEntity;
      const updatedData: Partial<JobEntity> = {
        status: JobStatus.IDENTITY_VERIFIED,
      };

      const result = jobsService.computeUpdatePayload(rawJob, updatedData);

      expect(result).toEqual(updatedData);
    });

    const invalidInputTestCases = [
      {
        initialStatus: JobStatus.QUOTE_REQUESTED,
        updatedData: {
          status: JobStatus.QUOTE_REQUESTED,
          checkList: ['1'],
        },
        expectedErrorMessage: `Invalid input for update job - The status must be: ${JobStatus.DRAFT}. Current status is: ${JobStatus.QUOTE_REQUESTED}`,
      },
      {
        initialStatus: JobStatus.QUOTE_REQUESTED,
        updatedData: {
          status: 'test' as any,
        },
        expectedErrorMessage: `Invalid input for update job - The status must be: ${Object.values(
          JobStatus,
        ).join(' or ')}. Current status is: ${JobStatus.QUOTE_REQUESTED}`,
      },
      {
        initialStatus: JobStatus.QUOTE_REQUESTED,
        updatedData: {
          checkList: ['1'],
        },
        expectedErrorMessage: `Invalid input for update job - The status must be: ${Object.values(
          JobStatus,
        ).join(' or ')}. Current status is: ${JobStatus.QUOTE_REQUESTED}`,
      },
      // {
      //   initialStatus: JobStatus.ESTIMATE_PRICE_PROCESSING,
      //   updatedData: {
      //     checkListValues: {
      //       value1: 'A',
      //       value2: 'B',
      //     },
      //   },
      //   expectedErrorMessage:
      //     'Invalid input for update job - Requires commented',
      // },
    ];

    it.each(invalidInputTestCases)(
      'should throw an error when %s',
      (testCase) => {
        const rawJob: JobEntity = {
          status: testCase.initialStatus,
        } as JobEntity;

        try {
          jobsService.computeUpdatePayload(rawJob, testCase.updatedData);
          // If it doesn't throw an error, fail the test
          fail('Expected method to throw an error');
        } catch (error) {
          expect(error).toBeInstanceOf(BaseExceptionService);
          expect((error as BaseExceptionService).code).toBe(
            BASE_EXCEPTIONS.INVALID_INPUT_FOR_UPDATE_JOB.code,
          );
          expect((error as BaseExceptionService).data).toBe(
            testCase.expectedErrorMessage,
          );
        }
      },
    );
  });

  describe('Get Job with relation', () => {
    const mockDefaultBody = {
      jobId: 'test1',
      relations: [],
      company: 'company',
    };

    it('should success return job', async () => {
      jest
        .spyOn(jobsRepository, 'findOne')
        .mockResolvedValueOnce({ jobId: 'test1' } as JobEntity);

      await jobsService.getJobWithRelation({
        ...mockDefaultBody,
      });

      expect(jobsRepository.findOne).toHaveBeenCalledWith({
        where: {
          jobId: 'test1',
          companyId: 'company',
        },
        relations: {},
      });
    });
    it('should find job with allocationOrder relation', async () => {
      jest
        .spyOn(jobsRepository, 'findOne')
        .mockResolvedValueOnce({ jobId: 'test1' } as JobEntity);

      await jobsService.getJobWithRelation({
        ...mockDefaultBody,
        relations: ['allocationOrder'],
      });

      expect(jobsRepository.findOne).toHaveBeenCalledWith({
        where: {
          jobId: 'test1',
          companyId: 'company',
        },
        relations: {
          allocationOrder: {
            toBranch: true,
            fromBranch: true,
            createdUser: true,
            receiverUser: true,
          },
        },
      });
    });
    it('should find job with other relation', async () => {
      jest
        .spyOn(jobsRepository, 'findOne')
        .mockResolvedValueOnce({ jobId: 'test1' } as JobEntity);

      await jobsService.getJobWithRelation({
        ...mockDefaultBody,
        relations: ['branch', 'qcUser'],
      });

      expect(jobsRepository.findOne).toHaveBeenCalledWith({
        where: {
          jobId: 'test1',
          companyId: 'company',
        },
        relations: {
          branch: true,
          qcUser: true,
        },
      });
    });
    it('should find job with type customer-info', async () => {
      jest
        .spyOn(jobsRepository, 'findOne')
        .mockResolvedValueOnce({ jobId: 'test1' } as JobEntity);

      jest
        .mocked(getStatusJobByType)
        .mockReturnValueOnce([JobStatus.PURCHASED]);

      await jobsService.getJobWithRelation({
        ...mockDefaultBody,
        type: 'customer-info',
        relations: ['contract'],
      });

      expect(jobsRepository.findOne).toHaveBeenCalledWith({
        where: {
          jobId: 'test1',
          companyId: 'company',
          status: In([JobStatus.PURCHASED]),
        },
        relations: {
          contract: true,
        },
      });
    });
    it('should return invalid', async () => {
      try {
        await jobsService.getJobWithRelation({
          ...mockDefaultBody,
          relations: ['test'],
        });
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS['INVALID_JOB_RELATION'].code,
        );
      }
    });
    it('should return job not found', async () => {
      jest.spyOn(jobsRepository, 'findOne').mockResolvedValueOnce(null);

      try {
        await jobsService.getJobWithRelation({
          ...mockDefaultBody,
          jobId: 'testNotFound',
        });
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS['NOT_FOUND_DATA'].code,
        );
      }
    });
  });

  describe('Prepare Job Draft', () => {
    it('should prepare a job draft with valid model identifiers and penalties (normal flow)', async () => {
      // Mock input data
      const body = {
        modelKey: 'abc123',
        deviceKey: 'abc123',
        depositContractKey: 'abc123',
      };
      const user = {
        company: 'CompanyX',
        userKey: 'user123',
      } as WithUserContext;
      const branch = { branch: 'BranchA' };

      const mockJob = {
        jobId: '2401-0000-0001',
        companyId: 'company',
        deviceKey: 'deviceKey',
        branchId: branch.branch,
        modelKey: 'modelKey',
        createdBy: user.userKey,
        updatedBy: user.userKey,
        shopUserKey: user.userKey,
        shopUserName: 'name',
        status: JobStatus.DRAFT,
        checkListValues: {} as any,
      } as JobEntity;

      // Mock the successful response from the modelMastersRepo
      jest
        .spyOn(modelMastersRepository, 'findOne')
        .mockResolvedValueOnce(mockModelMasters);

      // Mock the successful response from the penaltiesViewRepository
      jest
        .spyOn(penaltiesViewRepository, 'findOne')
        .mockResolvedValueOnce(mockPenalties as PenaltiesView);

      // Mock the successful response from the jobTemplatesRepository
      jest
        .spyOn(jobTemplatesRepository, 'findOne')
        .mockResolvedValueOnce({ template: ['item1', 'item2'] } as any);

      const mockQueryRunner = {
        connect: jest.fn().mockResolvedValue(undefined),
        startTransaction: jest.fn().mockResolvedValue(undefined),
        commitTransaction: jest.fn().mockResolvedValue(undefined),
        rollbackTransaction: jest.fn().mockResolvedValue(undefined),
        release: jest.fn().mockResolvedValue(undefined),
      } as any;

      const mockConnection = {
        createQueryRunner: jest.fn().mockReturnValue(mockQueryRunner),
      } as any;

      const mockManager = {
        connection: mockConnection,
        getRepository: jest.fn().mockReturnThis(),
        createQueryBuilder: jest.fn().mockReturnThis(),
        insert: jest.fn().mockReturnThis(),
        orIgnore: jest.fn().mockReturnThis(),
        execute: jest.fn().mockResolvedValue({ raw: [mockJob] }),
        values: jest.fn().mockReturnThis(),
        save: jest.fn().mockResolvedValueOnce(mockJob),
      } as any;

      Object.defineProperty(jobsRepository, 'manager', {
        value: mockManager,
      });
      Object.defineProperty(mockQueryRunner, 'manager', {
        value: mockManager,
      });

      jest.spyOn(jobsRepository, 'createQueryBuilder').mockReturnValueOnce({
        select: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        getOne: jest.fn().mockResolvedValue(null),
      } as any);

      // Call the method being tested
      const result = await jobsService.prepareJobDraft(body, user, branch);

      // Assertions
      expect(result).toBeInstanceOf(JobEntity);
      expect(result.companyId).toBe(user.company);
      expect(result.deviceKey).toBe(body.deviceKey);
      expect(result.branchId).toBe(branch.branch);
      expect(result.status).toBe(JobStatus.DRAFT);
    });

    it('should prepare a job draft with valid model identifiers and penalties (fallback flow, existed job in month)', async () => {
      // Mock input data
      const body = {
        modelKey: 'abc123',
        deviceKey: 'abc123',
        depositContractKey: 'abc123',
      };
      const user = {
        company: 'CompanyX',
        userKey: 'user123',
      } as WithUserContext;
      const branch = { branch: 'BranchA' };

      const mockJob = {
        jobId: '2401-0000-0001',
        companyId: 'company',
        deviceKey: 'deviceKey',
        branchId: branch.branch,
        modelKey: 'modelKey',
        createdBy: user.userKey,
        updatedBy: user.userKey,
        shopUserKey: user.userKey,
        shopUserName: 'name',
        status: JobStatus.DRAFT,
        checkListValues: {} as any,
      } as JobEntity;

      // Mock the successful response from the modelMastersRepo
      jest
        .spyOn(modelMastersRepository, 'findOne')
        .mockResolvedValueOnce(mockModelMasters);

      // Mock the successful response from the penaltiesViewRepository
      jest
        .spyOn(penaltiesViewRepository, 'findOne')
        .mockResolvedValueOnce(mockPenalties as PenaltiesView);

      // Mock the successful response from the jobTemplatesRepository
      jest
        .spyOn(jobTemplatesRepository, 'findOne')
        .mockResolvedValueOnce({ template: ['item1', 'item2'] } as any);

      const mockQueryRunner = {
        connect: jest.fn().mockResolvedValue(undefined),
        startTransaction: jest.fn().mockResolvedValue(undefined),
        commitTransaction: jest.fn().mockResolvedValue(undefined),
        rollbackTransaction: jest.fn().mockResolvedValue(undefined),
        release: jest.fn().mockResolvedValue(undefined),
      } as any;

      const mockConnection = {
        createQueryRunner: jest.fn().mockReturnValue(mockQueryRunner),
      } as any;

      const mockManager = {
        connection: mockConnection,
        getRepository: jest.fn().mockReturnThis(),
        createQueryBuilder: jest.fn().mockReturnThis(),
        insert: jest.fn().mockReturnThis(),
        orIgnore: jest.fn().mockReturnThis(),
        execute: jest.fn().mockResolvedValue({ raw: [] }),
        values: jest.fn().mockReturnThis(),
        save: jest.fn().mockResolvedValueOnce(mockJob),
      } as any;

      Object.defineProperty(jobsRepository, 'manager', {
        value: mockManager,
      });
      Object.defineProperty(mockQueryRunner, 'manager', {
        value: mockManager,
      });

      jest.spyOn(jobsRepository, 'createQueryBuilder').mockReturnValueOnce({
        select: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        getOne: jest.fn().mockResolvedValue(null),
      } as any);

      // Call the method being tested
      const result = await jobsService.prepareJobDraft(body, user, branch);

      // Assertions
      expect(result).toBeInstanceOf(JobEntity);
      expect(result.companyId).toBe(user.company);
      expect(result.deviceKey).toBe(body.deviceKey);
      expect(result.branchId).toBe(branch.branch);
      expect(result.status).toBe(JobStatus.DRAFT);
    });

    it('should prepare a job draft with valid model identifiers and penalties (fallback flow, first job of month)', async () => {
      // Mock input data
      const body = {
        modelKey: 'abc123',
        deviceKey: 'abc123',
        depositContractKey: 'abc123',
      };
      const user = {
        company: 'CompanyX',
        userKey: 'user123',
      } as WithUserContext;
      const branch = { branch: 'BranchA' };

      const mockJob = {
        jobId: '2401-0000-0001',
        companyId: 'company',
        deviceKey: 'deviceKey',
        branchId: branch.branch,
        modelKey: 'modelKey',
        createdBy: user.userKey,
        updatedBy: user.userKey,
        shopUserKey: user.userKey,
        shopUserName: 'name',
        status: JobStatus.DRAFT,
        checkListValues: {} as any,
      } as JobEntity;

      // Mock the successful response from the modelMastersRepo
      jest
        .spyOn(modelMastersRepository, 'findOne')
        .mockResolvedValueOnce(mockModelMasters);

      // Mock the successful response from the penaltiesViewRepository
      jest
        .spyOn(penaltiesViewRepository, 'findOne')
        .mockResolvedValueOnce(mockPenalties as PenaltiesView);

      // Mock the successful response from the jobTemplatesRepository
      jest
        .spyOn(jobTemplatesRepository, 'findOne')
        .mockResolvedValueOnce({ template: ['item1', 'item2'] } as any);

      const mockQueryRunner = {
        connect: jest.fn().mockResolvedValue(undefined),
        startTransaction: jest.fn().mockResolvedValue(undefined),
        commitTransaction: jest.fn().mockResolvedValue(undefined),
        rollbackTransaction: jest.fn().mockResolvedValue(undefined),
        release: jest.fn().mockResolvedValue(undefined),
      } as any;

      const mockConnection = {
        createQueryRunner: jest.fn().mockReturnValue(mockQueryRunner),
      } as any;

      const mockManager = {
        connection: mockConnection,
        getRepository: jest.fn().mockReturnThis(),
        createQueryBuilder: jest.fn().mockReturnThis(),
        insert: jest.fn().mockReturnThis(),
        orIgnore: jest.fn().mockReturnThis(),
        execute: jest.fn().mockResolvedValue({ raw: [] }),
        values: jest.fn().mockReturnThis(),
        save: jest.fn().mockResolvedValueOnce(mockJob),
      } as any;

      Object.defineProperty(jobsRepository, 'manager', {
        value: mockManager,
      });
      Object.defineProperty(mockQueryRunner, 'manager', {
        value: mockManager,
      });

      jest.spyOn(jobsRepository, 'createQueryBuilder').mockReturnValueOnce({
        select: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        getOne: jest.fn().mockResolvedValue(mockJob as JobEntity),
      } as any);

      // Call the method being tested
      const result = await jobsService.prepareJobDraft(body, user, branch);

      // Assertions
      expect(result).toBeInstanceOf(JobEntity);
      expect(result.companyId).toBe(user.company);
      expect(result.deviceKey).toBe(body.deviceKey);
      expect(result.branchId).toBe(branch.branch);
      expect(result.status).toBe(JobStatus.DRAFT);
    });

    it('should throw an error when model master  is not found', async () => {
      // Mock input data with invalid model identifiers
      const body = {
        modelKey: 'NonExistingModelKey',
        deviceKey: 'abc123',
        depositContractKey: 'abc123',
      };
      const user = {
        company: 'CompanyX',
        userKey: 'user123',
      } as WithUserContext;
      const branch = { branch: 'BranchA' };

      // Mock response from modelMastersRepo when no model master is found
      jest.spyOn(modelMastersRepository, 'findOne').mockResolvedValueOnce(null);

      try {
        await jobsService.prepareJobDraft(body, user, branch);
        // If it doesn't throw an error, fail the test
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.INVALID_MODEL_IDENTIFIERS.code,
        );
        expect((error as BaseExceptionService).message).toBe(
          `Invalid model identifiers. Model master not found`,
        );
      }
    });

    it('should prepare a job draft when penalties & jobTemplate is not found', async () => {
      // Mock input data with invalid model identifiers
      const body = {
        modelKey: 'abc123',
        deviceKey: 'abc123',
        depositContractKey: 'abc123',
      };
      const user = {
        company: 'CompanyX',
        userKey: 'user123',
      } as WithUserContext;
      const branch = { branch: 'BranchA' };

      // Mock the successful response from the modelMastersRepo
      jest
        .spyOn(modelMastersRepository, 'findOne')
        .mockResolvedValueOnce(mockModelMasters);
      // Mock the successful response from the penaltiesViewRepository
      jest
        .spyOn(penaltiesViewRepository, 'findOne')
        .mockResolvedValueOnce(null);

      // Mock the successful response from the jobTemplatesRepository
      jest.spyOn(jobTemplatesRepository, 'findOne').mockResolvedValueOnce(null);

      const mockJob = {
        jobId: '2401-0000-0001',
        companyId: 'company',
        deviceKey: 'deviceKey',
        branchId: branch.branch,
        modelKey: 'modelKey',
        createdBy: user.userKey,
        updatedBy: user.userKey,
        shopUserKey: user.userKey,
        shopUserName: 'name',
        status: JobStatus.DRAFT,
        checkListValues: {} as any,
      } as JobEntity;

      // Mock the successful response from the modelMastersRepo
      jest
        .spyOn(modelMastersRepository, 'findOne')
        .mockResolvedValueOnce(mockModelMasters);

      // Mock the successful response from the penaltiesViewRepository
      jest
        .spyOn(penaltiesViewRepository, 'findOne')
        .mockResolvedValueOnce(mockPenalties as PenaltiesView);

      // Mock the successful response from the jobTemplatesRepository
      jest
        .spyOn(jobTemplatesRepository, 'findOne')
        .mockResolvedValueOnce({ template: ['item1', 'item2'] } as any);

      const mockQueryRunner = {
        connect: jest.fn().mockResolvedValue(undefined),
        startTransaction: jest.fn().mockResolvedValue(undefined),
        commitTransaction: jest.fn().mockResolvedValue(undefined),
        rollbackTransaction: jest.fn().mockResolvedValue(undefined),
        release: jest.fn().mockResolvedValue(undefined),
      } as any;

      const mockConnection = {
        createQueryRunner: jest.fn().mockReturnValue(mockQueryRunner),
      } as any;

      const mockManager = {
        connection: mockConnection,
        getRepository: jest.fn().mockReturnThis(),
        createQueryBuilder: jest.fn().mockReturnThis(),
        insert: jest.fn().mockReturnThis(),
        orIgnore: jest.fn().mockReturnThis(),
        execute: jest.fn().mockResolvedValue({ raw: [] }),
        values: jest.fn().mockReturnThis(),
        save: jest.fn().mockResolvedValueOnce(mockJob),
      } as any;

      Object.defineProperty(jobsRepository, 'manager', {
        value: mockManager,
      });
      Object.defineProperty(mockQueryRunner, 'manager', {
        value: mockManager,
      });

      jest.spyOn(jobsRepository, 'createQueryBuilder').mockReturnValueOnce({
        select: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        getOne: jest.fn().mockResolvedValue(mockJob as JobEntity),
      } as any);

      // Call the method being tested
      const result = await jobsService.prepareJobDraft(body, user, branch);

      // Assertions
      expect(result).toBeInstanceOf(JobEntity);
      expect(result.companyId).toBe(user.company);
      expect(result.deviceKey).toBe(body.deviceKey);
      expect(result.branchId).toBe(branch.branch);
      expect(result.status).toBe(JobStatus.DRAFT);
    });
  });

  describe('Prepare Job Quote Request', () => {
    it('should prepare a job quote request', async () => {
      // Mock input data
      const body = {
        checkListValues: { value1: 'A', value2: 'B' },
      };
      const user = { userKey: 'user123' };

      // Call the method being tested
      const result = jobsService.prepareJobQuoteRequest(
        body as any,
        user as any,
      );

      // Assertions
      expect(result).toBeInstanceOf(JobEntity);
      expect(result.updatedBy).toBe(user.userKey);
      expect(result.status).toBe(JobStatus.QUOTE_REQUESTED);
      expect(result.checkListValues).toEqual(body.checkListValues);
    });
  });

  describe('Prepare Assign Job', () => {
    it('should prepare an assign job', async () => {
      const user = { userKey: 'user123' };

      // Call the method being tested
      const result = jobsService.prepareAssignJob(user as any);

      // Assertions
      expect(result).toBeInstanceOf(JobEntity);
      expect(result.updatedBy).toBe(user.userKey);
      expect(result.status).toBe(JobStatus.ESTIMATE_PRICE_PROCESSING);
    });
  });

  describe('Prepare Reject Job', () => {
    it('should prepare a reject job', async () => {
      const user = { userKey: 'user123' };

      // Call the method being tested
      const result = jobsService.prepareRejectJob(
        user as any,
        JobStatus.REJECT_BY_SHOP,
      );

      // Assertions
      expect(result).toBeInstanceOf(JobEntity);
      expect(result.updatedBy).toBe(user.userKey);
      expect(result.status).toBe(JobStatus.REJECT_BY_SHOP);
    });
  });

  describe('Prepare Purchased Job', () => {
    it('should prepare a purchased job', async () => {
      const user = { userKey: 'user123' };

      // Call the method being tested
      const result = jobsService.preparePurchasedJob(user as any);

      // Assertions
      expect(result).toBeInstanceOf(JobEntity);
      expect(result.updatedBy).toBe(user.userKey);
      expect(result.status).toBe(JobStatus.PURCHASED);
    });
  });

  describe('Default Prepare Job', () => {
    it('should prepare an comment job', () => {
      const user = { userKey: 'user123' };

      // Call the method being tested
      const result = jobsService.defaultPrepareJob({
        updatedBy: user.userKey,
        isAdditionalCheckList: true,
      });

      // Assertions
      expect(result).toBeInstanceOf(JobEntity);
      expect(result.updatedBy).toBe(user.userKey);

      expect(result.isAdditionalCheckList).toEqual(true);
    });
  });

  describe('JobsService - getImageJob and uploadImageJob', () => {
    it('should generate the correct S3 key path for getImageJob', async () => {
      // Mock input data
      const user = { company: 'CompanyX' } as WithUserContext;
      const imageJobParamDto: ImageJobParamDto = {
        id: '123',
        key: 'imageKey',
        slug: 'device-image',
      };

      const expectedKeyPath = `company/${user.company}/jobs/${imageJobParamDto.id}/${imageJobParamDto.slug}/${imageJobParamDto.key}`;

      // Call the method being tested
      const result = await jobsService.getImageJob(user, imageJobParamDto);

      // Verify that the correct S3 key path is generated
      expect(result).toBe(expectedKeyPath);
    });

    it('should generate the correct S3 key path and call uploadFileByPresignedUrl for uploadImageJob', async () => {
      // Mock input data
      const user = { company: 'CompanyY' } as WithUserContext;
      const imageJobParamDto: ImageJobParamDto = {
        id: '456',
        key: 'imageKey2',
        slug: 'device-image',
      };
      const expectedKeyPath = `company/${user.company}/jobs/${imageJobParamDto.id}/${imageJobParamDto.slug}/${imageJobParamDto.key}`;

      // Call the method being tested
      const result = await jobsService.uploadImageJob(
        'testFile' as any,
        user,
        imageJobParamDto,
      );

      // Verify that the correct S3 key path is generated and uploadFileByPresignedUrl is called
      expect(result).toBe(expectedKeyPath);
    });
  });

  describe('Prepare Verified Job', () => {
    it('should prepare verified job', () => {
      const user = { userKey: 'user123' } as any;

      const type: CustomerInfoType = CustomerInfoType.DIP_CHIP;

      // Call the method being tested
      const result = jobsService.prepareVerifiedJob(user, type, null);

      // Assertions
      expect(result).toBeInstanceOf(JobEntity);
      expect(result.updatedBy).toBe(user.userKey);
    });

    it('should throw an error when job has no contract and type is not DIP_CHIP', () => {
      const user = { userKey: 'user123' } as any;
      const type = CustomerInfoType.IDENTITY_VERIFICATION;

      try {
        jobsService.prepareVerifiedJob(
          user,
          type,
          null,
          JobStatus.IDENTITY_REJECTED,
        );
        // If it doesn't throw an error, fail the test
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.INVALID_INPUT_FOR_UPDATE_JOB.code,
        );
        expect((error as BaseExceptionService).data).toBe(
          `Invalid input for update job - The status must be: 20_IDENTITY_REQUESTED. Current status is: ${JobStatus.IDENTITY_REJECTED}`,
        );
      }
    });

    it('should pass when job has a contract and the customer info type matches the specified type', () => {
      const user = { userKey: 'user123' } as any;

      const type: CustomerInfoType = CustomerInfoType.IDENTITY_VERIFICATION;

      expect(() =>
        jobsService.prepareVerifiedJob(user, type, {
          customerInfo: {
            type: CustomerInfoType.IDENTITY_VERIFICATION,
          },
        } as ContractEntity),
      ).not.toThrow();
    });

    it('should throw an error when job has a contract and the customer info type does not match the specified type', () => {
      const user = { userKey: 'user123' } as any;

      const type: CustomerInfoType = CustomerInfoType.IDENTITY_VERIFICATION;
      try {
        jobsService.prepareVerifiedJob(user, type, {
          customerInfo: {
            type: CustomerInfoType.DIP_CHIP,
          },
        } as ContractEntity);
        // If it doesn't throw an error, fail the test
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.INVALID_INPUT_FOR_UPDATE_JOB.code,
        );
        expect((error as BaseExceptionService).data).toBe(
          'Invalid customer info type - Requires IDENTITY_VERIFICATION type',
        );
      }
    });
  });

  describe('transformOrderBy', () => {
    // Define test cases using it.each
    it.each`
      input           | result
      ${'brand ASC'}  | ${'modelIdentifiers brand ASC'}
      ${'model DESC'} | ${'modelIdentifiers model DESC'}
      ${'rom ASC'}    | ${'modelIdentifiers rom ASC'}
      ${'price DESC'} | ${'price DESC'}
      ${undefined}    | ${undefined}
    `('should transform orderBy correctly', ({ input, result }) => {
      const transformed = jobsService.transformOrderBy(input);
      expect(transformed).toBe(result);
    });
  });

  describe('checkJobPermission', () => {
    const mockJob: JobEntity = {
      shopUserKey: 'shopUserKey',
      branchId: 'branchId',
    } as JobEntity;

    const mockUser: WithUserContext = {
      userKey: 'userKey',
      roles: [
        { role: ['Manager', 'Sale'], branchId: 'branchId' },
        { role: ['Manager', 'Sale'], branchId: '80000011' },
        { role: ['Sale'], branchId: '80000400' },
      ],
    } as WithUserContext;

    const mockBranch: WithBranchContext = {
      branch: 'branchId',
    };

    it('should not throw an exception when the user has permission', () => {
      // Mock a user who is a manager and has the correct job ownership and branch

      mockJob.shopUserKey = mockUser.userKey;
      mockJob.branchId = mockBranch.branch;

      expect(() =>
        jobsService.checkJobPermission(mockJob, mockUser, mockBranch),
      ).not.toThrow();
    });

    it('should throw an exception when the user does not have permission', () => {
      // Mock a user who is not a manager and has incorrect job ownership or branch

      mockJob.shopUserKey = 'differentUserKey';
      mockJob.branchId = 'differentBranchId';

      try {
        jobsService.checkJobPermission(mockJob, mockUser, mockBranch);
        // If it doesn't throw an error, fail the test
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.INVALID_JOB_PERMISSION.code,
        );
        expect((error as BaseExceptionService).data).toBe(
          'User does not have permission to access this job',
        );
      }
    });
  });

  describe('insertJobActivities', () => {
    it('inserts a new job activity into the repository', async () => {
      const mockJobEntity: JobEntity = {
        jobId: 'mockJobId',
        companyId: 'mockCompanyId',
      } as JobEntity;

      const mockActivityType: JobActivitiesType = JobActivitiesType.COMMENT;

      const mockDetail: JobActivityDetail = {
        summary: 'mockSummary',
      } as JobActivityDetail;

      const mockUser: WithUserContext = {
        userKey: 'mockUserKey',
      } as WithUserContext;

      // Call the function with mock data
      await jobsService.insertJobActivities(
        mockJobEntity,
        mockActivityType,
        mockDetail,
        mockUser,
      );

      // Check if the save method was called with the correct arguments
      expect(jobActivitiesRepository.save).toHaveBeenCalledWith(
        expect.objectContaining({
          jobId: mockJobEntity.jobId,
          companyId: mockJobEntity.companyId,
          type: mockActivityType,
        }),
      );
    });
  });

  describe('verifyByDipChip', () => {
    it('verifies by DipChip and provisions contract job', async () => {
      const mockUser: WithUserContext = {
        userKey: 'mockUserKey',
      } as WithUserContext;

      const mockJobId = 'mockJobId';

      const mockUpdateCustomerInfoJobDto = {
        type: CustomerInfoType.DIP_CHIP,
      } as any;

      const mockContractData = {
        contractId: 'mockContractId',
      } as ContractEntity;

      jest.spyOn(contractRepo, 'findOne').mockResolvedValue(mockContractData);

      jest.spyOn(jobsService, 'prepareVerifiedJob').mockImplementation(() => {
        return {
          jobId: 'mockJobId',
          companyId: 'mockCompanyId',
        } as JobEntity;
      });

      jest.spyOn(jobsService, 'provisionContractJob').mockImplementation(() => {
        return Promise.resolve({
          jobId: 'mockJobId',
          companyId: 'mockCompanyId',
        } as JobEntity);
      });

      // Call the verifyByDipChip method
      await jobsService.verifyByDipChip(
        mockUser,
        mockJobId,
        mockUpdateCustomerInfoJobDto,
      );

      expect(contractRepo.findOne).toHaveBeenCalledWith({
        where: { jobId: mockJobId },
      });

      expect(jobsService.prepareVerifiedJob).toHaveBeenCalledWith(
        mockUser,
        CustomerInfoType.DIP_CHIP,
        mockContractData,
      );

      expect(jobsService.provisionContractJob).toHaveBeenCalledWith(
        mockJobId,
        {
          jobId: 'mockJobId',
          companyId: 'mockCompanyId',
        } as JobEntity,
        { ...mockUpdateCustomerInfoJobDto, type: CustomerInfoType.DIP_CHIP },
        {
          userKey: 'mockUserKey',
        },
        mockContractData,
      );
    });
  });

  describe('identityReviewRequest', () => {
    it('requests identity review and provisions contract job', async () => {
      const mockUser: WithUserContext = {
        userKey: 'mockUserKey',
        permissions: [
          {
            branchId: 'mockBranchId',
            permission: ['mockPermission'],
          },
        ],
      } as WithUserContext;

      const mockJobId = 'mockJobId';

      const mockUpdateCustomerInfoJobDto = {
        type: CustomerInfoType.DIP_CHIP,
      } as any;

      const mockContractData = {
        contractId: 'mockContractId',
      } as ContractEntity;

      jest.spyOn(contractRepo, 'findOne').mockResolvedValue(mockContractData);

      jest
        .spyOn(jobsService, 'defaultPrepareJob')
        .mockImplementation((options) => {
          return {
            jobId: mockJobId,
            companyId: 'mockCompanyId',
            ...options,
          } as JobEntity;
        });

      jest.spyOn(jobsService, 'provisionContractJob').mockImplementation(() => {
        return Promise.resolve({
          jobId: 'mockJobId',
          branchId: 'mockBranchId',
          companyId: 'mockCompanyId',
        } as JobEntity);
      });

      await jobsService.identityReviewRequest(
        mockUser,
        mockJobId,
        mockUpdateCustomerInfoJobDto,
      );

      expect(jobsService.defaultPrepareJob).toHaveBeenCalledWith({
        updatedBy: mockUser.userKey,
        status: JobStatus.IDENTITY_REQUESTED,
      });

      expect(contractRepo.findOne).toHaveBeenCalledWith({
        where: { jobId: mockJobId },
      });

      expect(jobsService.provisionContractJob).toHaveBeenCalledWith(
        mockJobId,
        {
          jobId: 'mockJobId',
          companyId: 'mockCompanyId',
          status: '20_IDENTITY_REQUESTED',
          updatedBy: 'mockUserKey',
        } as JobEntity,
        {
          ...mockUpdateCustomerInfoJobDto,
          type: CustomerInfoType.IDENTITY_VERIFICATION,
        },
        mockUser,
        mockContractData,
      );
    });
  });

  describe('updateJob', () => {
    const mockJobId = 'mockJobId';
    const mockCompanyId = 'mockCompanyId';
    const mockPartialJobEntity = {
      status: JobStatus.QUOTE_REQUESTED,
    };

    const mockJobEntity = {
      jobId: mockJobId,
      companyId: mockCompanyId,
      status: JobStatus.DRAFT,
    } as JobEntity;

    it('updates job successfully', async () => {
      jest
        .spyOn(jobsService, 'sanitizeInputBody')
        .mockReturnValue(mockPartialJobEntity);
      jest
        .spyOn(jobsService, 'computeUpdatePayload')
        .mockReturnValue(mockPartialJobEntity);

      jest
        .spyOn(mockEntityManager, 'findOne')
        .mockResolvedValueOnce(mockJobEntity);

      jest
        .spyOn(mockEntityManager, 'save')
        .mockResolvedValueOnce(mockJobEntity);

      const result = await jobsService.updateJob(
        mockJobId,
        mockCompanyId,
        mockEntityManager,
        mockPartialJobEntity,
      );

      expect(jobsService.sanitizeInputBody).toHaveBeenCalledWith(
        mockPartialJobEntity,
      );
      expect(jobsService.computeUpdatePayload).toHaveBeenCalledWith(
        mockJobEntity,
        mockPartialJobEntity,
      );

      expect(result).toEqual(mockJobEntity);
    });

    it('throws exception when job is not found', async () => {
      jest.spyOn(mockEntityManager, 'findOne').mockResolvedValueOnce(null);

      try {
        await jobsService.updateJob(
          mockJobId,
          mockCompanyId,
          mockEntityManager,
          mockPartialJobEntity,
        );
        // If it doesn't throw an error, fail the test
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.NOT_FOUND_DATA.code,
        );
        expect((error as BaseExceptionService).data).toBe('Job not found');
      }
    });
  });

  describe('provisionContractJob', () => {
    const mockJobId = 'mockJobId';
    const mockCompanyId = 'mockCompanyId';
    const mockJobPayload = { status: JobStatus.QUOTE_REQUESTED };
    const mockBody = { type: CustomerInfoType.IDENTITY_VERIFICATION } as any;
    const mockExistingContract = {
      contractId: 'mockContractId',
    } as ContractEntity;

    const mockJobEntity = {
      jobId: 'mockJobId',
      status: JobStatus.DRAFT,
    } as JobEntity;
    const mockContractData = { contractId: 'mockContractId' } as ContractEntity;

    // Mock the queryRunner and connection
    const mockQueryRunner = {
      connect: jest.fn().mockResolvedValue(undefined),
      startTransaction: jest.fn().mockResolvedValue(undefined),
      commitTransaction: jest.fn().mockResolvedValue(undefined),
      rollbackTransaction: jest.fn().mockResolvedValue(undefined),
      release: jest.fn().mockResolvedValue(undefined),
    } as any;

    const mockConnection = {
      createQueryRunner: jest.fn().mockReturnValue(mockQueryRunner),
    } as any;

    const mockManager = { connection: mockConnection } as any;

    it('provisions contract job successfully', async () => {
      jest.spyOn(jobsService, 'updateJob').mockImplementation(() => {
        return Promise.resolve(mockJobEntity);
      });

      jest
        .spyOn(contractsService, 'provisionContract')
        .mockImplementation(() => {
          return Promise.resolve(mockContractData);
        });

      Object.defineProperty(jobsRepository, 'manager', {
        value: mockManager,
      });
      Object.defineProperty(mockQueryRunner, 'manager', {
        value: mockManager,
      });

      // Call the provisionContractJob method
      const result = await jobsService.provisionContractJob(
        mockJobId,
        mockJobPayload,
        mockBody,
        {
          company: mockCompanyId,
        } as any,
        mockExistingContract,
      );

      expect(jobsService.updateJob).toHaveBeenCalledWith(
        mockJobId,
        mockCompanyId,
        mockManager,
        mockJobPayload,
      );
      expect(contractsService.provisionContract).toHaveBeenCalledWith(
        mockManager,
        mockBody,
        expect.anything(),
        mockExistingContract,
      );

      // Check the result
      expect(result).toEqual({
        contract: {
          contractId: 'mockContractId',
        },
        jobId: 'mockJobId',
        status: '00_DRAFT',
      });
    });

    it('rolls back transaction and throws exception on error', async () => {
      // Mock the updateJob and provisionContract methods to throw an exception
      jest.spyOn(jobsService, 'updateJob').mockImplementation(() => {
        throw new Error('Mock error');
      });

      jest
        .spyOn(contractsService, 'provisionContract')
        .mockImplementation(() => {
          throw new Error('Mock error');
        });

      Object.defineProperty(jobsRepository, 'manager', {
        value: mockManager,
      });

      try {
        await jobsService.provisionContractJob(
          mockJobId,
          mockJobPayload,
          mockBody,
          {} as any,
          mockExistingContract,
        );
        // If it doesn't throw an error, fail the test
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(Error);
        expect((error as Error).message).toBe('Mock error');
      }
    });
  });

  describe('identityReject', () => {
    // Mocks
    const mockUser: WithUserContext = {
      userKey: 'mockUserKey',
      company: 'mockCompanyId',
    } as WithUserContext;
    const mockJobId = 'mockJobId';
    const mockBody = { reason: 'Reject Reason' } as any;
    const mockContract = {
      customerInfo: {
        type: CustomerInfoType.IDENTITY_VERIFICATION,
      } as CustomerInfo,
    } as any;

    // Mock the queryRunner and connection
    const mockQueryRunner = {
      connect: jest.fn().mockResolvedValue(undefined),
      startTransaction: jest.fn().mockResolvedValue(undefined),
      commitTransaction: jest.fn().mockResolvedValue(undefined),
      rollbackTransaction: jest.fn().mockResolvedValue(undefined),
      release: jest.fn().mockResolvedValue(undefined),
    } as any;

    const mockConnection = {
      createQueryRunner: jest.fn().mockReturnValue(mockQueryRunner),
    } as any;

    const mockManager = {
      connection: mockConnection,
      findOne: jest.fn().mockResolvedValue(mockContract),
      save: jest.fn().mockResolvedValue({} as ContractEntity),
    } as any;

    it('should reject identity successfully', async () => {
      // Mock the necessary dependencies and methods
      Object.defineProperty(jobsRepository, 'manager', {
        value: mockManager,
      });
      Object.defineProperty(mockQueryRunner, 'manager', {
        value: mockManager,
      });

      // Spy on the necessary methods
      jest.spyOn(jobsService, 'updateJob').mockResolvedValue({} as JobEntity);
      jest.spyOn(mockManager, 'save').mockResolvedValue({} as ContractEntity);
      jest
        .spyOn(mockQueryRunner, 'commitTransaction')
        .mockResolvedValue(undefined);

      // Call the method
      await jobsService.identityReject(mockUser, mockJobId, mockBody);

      // Assertions
      expect(jobsService.updateJob).toHaveBeenCalledWith(
        mockJobId,
        'mockCompanyId',
        mockManager,
        expect.anything(),
      );
      expect(mockManager.save).toHaveBeenCalledWith(
        expect.objectContaining({
          customerInfo: {
            rejectReason: 'Reject Reason',
            type: 'IDENTITY_VERIFICATION',
          },
        }),
      );
      expect(mockQueryRunner.commitTransaction).toHaveBeenCalled();
    });

    it('should handle errors and roll back transaction', async () => {
      // Mock the necessary dependencies and methods
      Object.defineProperty(jobsRepository, 'manager', {
        value: mockManager,
      });
      Object.defineProperty(mockQueryRunner, 'manager', {
        value: mockManager,
      });

      jest.spyOn(jobsService, 'updateJob').mockResolvedValue({} as JobEntity);
      jest.spyOn(mockManager, 'findOne').mockImplementation(() => null);

      try {
        await jobsService.identityReject(mockUser, mockJobId, mockBody);
        // If it doesn't throw an error, fail the test
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).data).toBe('Contract not found');
      }
    });
  });

  describe('confirmPurchasedJob', () => {
    // Mocks
    const mockUser: WithUserContext = {
      userKey: 'mockUserKey',
      company: 'mockCompanyId',
    } as WithUserContext;
    const mockJobId = 'mockJobId';
    const mockBody = { purchasedPrice: 1000 } as any;
    const mockJobEntity = {} as JobEntity;
    const mockContract = {} as ContractEntity;

    // Mock the queryRunner and connection
    const mockQueryRunner = {
      connect: jest.fn().mockResolvedValue(undefined),
      startTransaction: jest.fn().mockResolvedValue(undefined),
      commitTransaction: jest.fn().mockResolvedValue(undefined),
      rollbackTransaction: jest.fn().mockResolvedValue(undefined),
      release: jest.fn().mockResolvedValue(undefined),
    } as any;

    const mockConnection = {
      createQueryRunner: jest.fn().mockReturnValue(mockQueryRunner),
    } as any;

    const mockManager = {
      connection: mockConnection,
      findOne: jest.fn().mockResolvedValue(mockContract),
      save: jest.fn().mockResolvedValue({} as ContractEntity),
    } as any;

    it('should confirm purchased job successfully', async () => {
      // Mock the necessary dependencies and methods
      jest.spyOn(jobsRepository, 'findOne').mockResolvedValue({
        status: JobStatus.CAMPAIGN_SELECTED,
      } as JobEntity);

      jest
        .spyOn(contractsService, 'confirmCampaignRedemptionCode')
        .mockResolvedValue([{} as CampaignRedemptionCodeEntity]);
      jest
        .spyOn(contractsService, 'confirmContract')
        .mockResolvedValue(mockContract);
      jest.spyOn(jobsService, 'updateJob').mockResolvedValue(mockJobEntity);

      jest
        .spyOn(contractsService, 'sendContractToEmail')
        .mockResolvedValue(true);

      Object.defineProperty(jobsRepository, 'manager', {
        value: mockManager,
      });
      Object.defineProperty(mockQueryRunner, 'manager', {
        value: mockManager,
      });

      // Call the method
      await jobsService.confirmPurchasedJob(mockUser, mockJobId, mockBody);

      // Assertions
      expect(jobsService.updateJob).toHaveBeenCalledWith(
        mockJobId,
        'mockCompanyId',
        mockManager,
        expect.anything(),
      );
      expect(contractsService.sendContractToEmail).toHaveBeenCalledWith(
        expect.anything(),
        expect.anything(),
      );
      expect(contractsService.confirmContract).toHaveBeenCalledWith(
        mockManager,
        expect.anything(),
        mockBody,
      );
    });
    it('should error job not found', async () => {
      jest.spyOn(jobsRepository, 'findOne').mockResolvedValue(null);
      try {
        await jobsService.confirmPurchasedJob(mockUser, mockJobId, mockBody);
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.NOT_FOUND_DATA.code,
        );
      }
    });
    it('should just return job', async () => {
      const mockJobPurchased = { status: JobStatus.PURCHASED } as JobEntity;
      jest.spyOn(jobsRepository, 'findOne').mockResolvedValue(mockJobPurchased);

      const result = await jobsService.confirmPurchasedJob(
        mockUser,
        mockJobId,
        mockBody,
      );
      expect(result).toEqual(mockJobPurchased);
    });
    it('should error CAMPAIGN_REDEMPTION_CODE_NOT_FOUND clear campaign and set status back to verify', async () => {
      // Mock the necessary dependencies and methods
      jest.spyOn(jobsRepository, 'findOne').mockResolvedValue({
        status: JobStatus.CAMPAIGN_SELECTED,
      } as JobEntity);
      jest
        .spyOn(contractsService, 'confirmCampaignRedemptionCode')
        .mockImplementationOnce(() => {
          throw new BaseExceptionService().exception(
            'CAMPAIGN_REDEMPTION_CODE_NOT_FOUND',
          );
        });

      Object.defineProperty(jobsRepository, 'manager', {
        value: mockManager,
      });
      Object.defineProperty(mockQueryRunner, 'manager', {
        value: mockManager,
      });

      try {
        await jobsService.confirmPurchasedJob(mockUser, mockJobId, mockBody);
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.CAMPAIGN_REDEMPTION_CODE_NOT_FOUND.code,
        );
        expect(mockQueryRunner.manager.save).toHaveBeenCalled();
      }
    });

    it('should handle errors and roll back transaction', async () => {
      // Mock the necessary dependencies and methods
      jest.spyOn(contractsService, 'confirmContract').mockImplementation(() => {
        throw new Error('Mock error');
      });
      jest.spyOn(jobsRepository, 'findOne').mockResolvedValue({
        status: JobStatus.CAMPAIGN_SELECTED,
      } as JobEntity);
      Object.defineProperty(jobsRepository, 'manager', {
        value: mockManager,
      });
      Object.defineProperty(mockQueryRunner, 'manager', {
        value: mockManager,
      });

      jest.spyOn(jobsService, 'updateJob').mockResolvedValue(mockJobEntity);

      try {
        await jobsService.confirmPurchasedJob(mockUser, mockJobId, mockBody);
        // If it doesn't throw an error, fail the test
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(Error);
        expect((error as Error).message).toBe('Mock error');
      }
    });

    it.each([[JobStatus.REJECT_BY_SHOP], [JobStatus.INSPECTION_FAILED]])(
      'should throw an error when job status is not JobStatus.PURCHASED or JobStatus.CAMPAIGN_SELECTED',
      async (status) => {
        const mockJob = {
          status,
        } as JobEntity;

        jest.spyOn(jobsRepository, 'findOne').mockResolvedValue(mockJob);

        try {
          await jobsService.confirmPurchasedJob(mockUser, mockJobId, mockBody);
          // If it doesn't throw an error, fail the test
          fail('Expected method to throw an error');
        } catch (error) {
          expect(error).toBeInstanceOf(BaseExceptionService);
          expect((error as BaseExceptionService).code).toBe(
            BASE_EXCEPTIONS.INVALID_INPUT_FOR_UPDATE_JOB.code,
          );
          expect((error as BaseExceptionService).data).toBe(
            `Invalid input for update job - The status must be: ${JobStatus.CAMPAIGN_SELECTED}. Current status is: ${mockJob.status}`,
          );
        }
      },
    );
  });

  describe('sendEmailContract', () => {
    // Mocks
    const mockUser: WithUserContext = {
      userKey: 'mockUserKey',
      company: 'mockCompanyId',
    } as WithUserContext;
    const mockJobId = 'mockJobId';
    const mockJobData: JobEntity = {
      jobId: 'mockJobId',
      companyId: 'mockCompanyId',
    } as JobEntity;
    const mockContractData = {
      contractId: 'mockContractId',
    } as ContractEntity;
    // const mockManager = {
    //   findOne: jest.fn().mockResolvedValue(mockContract),
    // } as any;

    it('should confirm send email successfully', async () => {
      // Call the method
      await jobsService.sendEmailContract({ jobId: mockJobId, user: mockUser });

      // Assertions
      expect(contractsService.sendContractToEmail).toHaveBeenCalled();
    });

    it.each([
      { jobId: undefined, user: mockUser },
      { jobId: mockJobId, user: undefined },
    ])('should %s handle errors BODY_PAYLOAD_INVALID', async (testCase) => {
      const { jobId, user } = testCase;
      try {
        await jobsService.sendEmailContract({
          jobId: jobId as string,
          user: user as WithUserContext,
        });
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.BODY_PAYLOAD_INVALID.code,
        );
      }
    });

    it.each([
      { mockJobReturn: null, mockContractReturn: mockContractData },
      { mockJobReturn: mockJobData, mockContractReturn: null },
    ])('should %s handle errors NOT_FOUND_DATA', async (testCase) => {
      const { mockJobReturn, mockContractReturn } = testCase;
      jest.spyOn(jobsRepository, 'findOne').mockResolvedValue(mockJobReturn);
      jest.spyOn(contractRepo, 'findOne').mockResolvedValue(mockContractReturn);

      try {
        await jobsService.sendEmailContract({
          jobId: mockJobId,
          user: mockUser,
        });
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.NOT_FOUND_DATA.code,
        );
      }
    });
  });

  describe('get draft contract', () => {
    it('should get draft successfully', async () => {
      const mockJob = {
        status: JobStatus.CAMPAIGN_SELECTED,
        branchId: 'branchId',
        companyId: 'companyId',
      } as JobEntity;
      // Call the method
      jest.spyOn(branchRepository, 'findOne').mockResolvedValueOnce({
        branchId: 'branchId',
        title: 'branchTitle',
      } as BranchEntity);

      jest.spyOn(companyRepository, 'findOne').mockResolvedValueOnce({
        companyId: 'companyId',
        logoPath: 'logoPath',
      } as CompanyEntity);

      const result = await jobsService.getDraftContract(mockJob);
      expect(typeof result).toBe('string');
    });

    it('should handle error invalid input for get draft contract', async () => {
      const mockJob = {
        status: JobStatus.DRAFT,
        branchId: 'branchId',
        companyId: 'companyId',
      } as JobEntity;

      try {
        jest.spyOn(branchRepository, 'findOne').mockResolvedValueOnce({
          branchId: 'branchId',
          title: 'branchTitle',
        } as BranchEntity);

        jest.spyOn(companyRepository, 'findOne').mockResolvedValueOnce({
          companyId: 'companyId',
          logoPath: 'logoPath',
        } as CompanyEntity);

        await jobsService.getDraftContract(mockJob);
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.INVALID_INPUT_FOR_UPDATE_JOB.code,
        );
      }
    });

    it('should handle error Invalid input for get draft contract', async () => {
      const mockJob = {
        status: JobStatus.DRAFT,
        branchId: 'branchId',
        companyId: 'companyId',
      } as JobEntity;

      try {
        jest.spyOn(branchRepository, 'findOne').mockResolvedValueOnce({
          branchId: 'branchId',
          title: 'branchTitle',
        } as BranchEntity);

        jest.spyOn(companyRepository, 'findOne').mockResolvedValueOnce({
          companyId: 'companyId',
          logoPath: 'logoPath',
        } as CompanyEntity);

        await jobsService.getDraftContract(mockJob);
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.INVALID_INPUT_FOR_UPDATE_JOB.code,
        );
      }
    });
  });

  describe('get sign contract', () => {
    it('should get draft successfully', async () => {
      const mockJob = {
        status: JobStatus.CAMPAIGN_SELECTED,
        branchId: 'branchId',
        companyId: 'companyId',
      } as JobEntity;

      const contractSigned = await jobsService.getSignContract(
        { signature: mockBase64Png } as SignContractDto,
        mockJob,
      );

      expect(typeof contractSigned).toBe('string');
    });

    it('should handle error Invalid input for sign contract', async () => {
      try {
        const mockJob = {
          status: JobStatus.DRAFT,
          branchId: 'branchId',
          companyId: 'companyId',
        } as JobEntity;

        await jobsService.getSignContract(
          { signature: mockBase64Png } as SignContractDto,
          mockJob,
        );
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.INVALID_INPUT_FOR_UPDATE_JOB.code,
        );
      }
    });
  });

  describe('get sign deposit contract', () => {
    it('should get draft successfully', async () => {
      const mockCompanyId = 'companyId';
      const mockBodySigned = {
        sign: mockBase64Png,
        firstName: 'firstName',
        lastName: 'lastName',
      } as SignDepositContractDto;

      const contractSigned = await jobsService.getSignDepositContract({
        company: mockCompanyId,
        body: mockBodySigned,
      });

      expect(typeof contractSigned).toBe('string');
    });
  });

  describe('get estimation activities', () => {
    it('should get estimation activities successfully', async () => {
      const mockEstimationActivity = {
        id: 'test-1',
        companyId: 'WW',
        modelKey: 'apple|iphone 14|512gb',
        deviceId: 'DEVICE-1',
        estimatedPrice: 1000,
        firstName: 'name1',
        lastName: 'lastname1',
        modelMasterGradeSnapshot: [
          {
            grade: 'A',
            purchasePrice: '20000.00',
            lastPurchasedOn: 'ISO8601',
            lastPurchasedPrice: 'Grade Info',
          },
          {
            grade: 'B',
            purchasePrice: '15000.00',
            lastPurchasedOn: 'ISO8601',
            lastPurchasedPrice: 'Grade Info',
          },
          {
            grade: 'C',
            purchasePrice: '10000.00',
            lastPurchasedOn: 'ISO8601',
            lastPurchasedPrice: 'Grade Info',
          },
          {
            grade: 'D',
            purchasePrice: '8000.00',
            lastPurchasedOn: 'ISO8601',
            lastPurchasedPrice: 'Grade Info',
          },
        ],
        modelChecklistResult: {},
        createdAt: new Date(),
      } as EstimationActivitiesEntity;
      jest
        .spyOn(estimateActivitiesRepository, 'findOne')
        .mockResolvedValueOnce(mockEstimationActivity);

      const result = await jobsService.getEstimationActivities('test-1', 'WW');
      expect(result).toMatchObject({
        firstName: 'name1',
        lastName: 'lastname1',
      });
    });
    it('should throw error NOT_FOUND_DATA', async () => {
      jest
        .spyOn(estimateActivitiesRepository, 'findOne')
        .mockResolvedValueOnce(null);

      try {
        await jobsService.getEstimationActivities('test-2', 'WW');
        // If it doesn't throw an error, fail the test
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        console.log(error);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.NOT_FOUND_DATA.code,
        );
        expect((error as BaseExceptionService).message).toBe(
          BASE_EXCEPTIONS.NOT_FOUND_DATA.message,
        );
      }
    });
    it('should throw error UNAVAILABLE_ESTIMATION_ACTIVITY', async () => {
      const mockEstimationActivity = {
        id: 'test-1',
        companyId: 'WW',
        modelKey: 'apple|iphone 14|512gb',
        deviceId: 'DEVICE-1',
        estimatedPrice: 1000,
        firstName: 'name1',
        lastName: 'lastname1',
        modelMasterGradeSnapshot: [
          {
            grade: 'A',
            purchasePrice: '20000.00',
            lastPurchasedOn: 'ISO8601',
            lastPurchasedPrice: 'Grade Info',
          },
          {
            grade: 'B',
            purchasePrice: '15000.00',
            lastPurchasedOn: 'ISO8601',
            lastPurchasedPrice: 'Grade Info',
          },
          {
            grade: 'C',
            purchasePrice: '10000.00',
            lastPurchasedOn: 'ISO8601',
            lastPurchasedPrice: 'Grade Info',
          },
          {
            grade: 'D',
            purchasePrice: '8000.00',
            lastPurchasedOn: 'ISO8601',
            lastPurchasedPrice: 'Grade Info',
          },
        ],
        modelChecklistResult: {},
        createdAt: new Date(),
        jobId: 'job-id-1',
      } as EstimationActivitiesEntity;
      jest
        .spyOn(estimateActivitiesRepository, 'findOne')
        .mockResolvedValueOnce(mockEstimationActivity);

      try {
        await jobsService.getEstimationActivities('test-1', 'WW');
        // If it doesn't throw an error, fail the test
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.UNAVAILABLE_ESTIMATION_ACTIVITY.code,
        );
        expect((error as BaseExceptionService).message).toBe(
          BASE_EXCEPTIONS.UNAVAILABLE_ESTIMATION_ACTIVITY.message,
        );
      }
    });
    it('should throw error EXPIRED_ESTIMATION_ACTIVITY', async () => {
      const nowDatePastMinusSixHour = new Date();
      nowDatePastMinusSixHour.setHours(nowDatePastMinusSixHour.getHours() - 6);
      const mockEstimationActivity = {
        id: 'test-1',
        companyId: 'WW',
        modelKey: 'apple|iphone 14|512gb',
        deviceId: 'DEVICE-1',
        estimatedPrice: 1000,
        firstName: 'name1',
        lastName: 'lastname1',
        modelMasterGradeSnapshot: [
          {
            grade: 'A',
            purchasePrice: '20000.00',
            lastPurchasedOn: 'ISO8601',
            lastPurchasedPrice: 'Grade Info',
          },
          {
            grade: 'B',
            purchasePrice: '15000.00',
            lastPurchasedOn: 'ISO8601',
            lastPurchasedPrice: 'Grade Info',
          },
          {
            grade: 'C',
            purchasePrice: '10000.00',
            lastPurchasedOn: 'ISO8601',
            lastPurchasedPrice: 'Grade Info',
          },
          {
            grade: 'D',
            purchasePrice: '8000.00',
            lastPurchasedOn: 'ISO8601',
            lastPurchasedPrice: 'Grade Info',
          },
        ],
        modelChecklistResult: {},
        createdAt: nowDatePastMinusSixHour,
      } as EstimationActivitiesEntity;
      jest
        .spyOn(estimateActivitiesRepository, 'findOne')
        .mockResolvedValueOnce(mockEstimationActivity);

      try {
        await jobsService.getEstimationActivities('test-1', 'WW');
        // If it doesn't throw an error, fail the test
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.EXPIRED_ESTIMATION_ACTIVITY.code,
        );
        expect((error as BaseExceptionService).message).toBe(
          BASE_EXCEPTIONS.EXPIRED_ESTIMATION_ACTIVITY.message,
        );
      }
    });
  });
  describe('createFromDraft', () => {
    // Mocks
    const mockJobId = 'mockJobId';
    const mockCompanyId = 'mockCompanyId';
    const mockJobEntity = {
      jobId: mockJobId,
      companyId: mockCompanyId,
      status: JobStatus.DRAFT,
      jobVendor: 'MASS',
      checkList: [
        {
          slug: 'product_information',
          title: 'รายละเอียดสินค้า',
          survey_form: {
            pages: [
              {
                name: 'page1',
                elements: [
                  {
                    name: 'accessories_check',
                    type: 'question_option',
                    title: {
                      en: 'Accessories Check',
                      th: 'อุปกรณ์เสริมที่คุณไม่มี',
                    },
                    choices: [
                      {
                        text: {
                          en: 'Box Case',
                          th: 'กล่อง',
                        },
                        value: 'box',
                      },
                      {
                        text: {
                          en: 'Headphone',
                          th: 'หูฟัง',
                        },
                        value: 'headphone',
                      },
                      {
                        text: {
                          en: 'Charging cable',
                          th: 'สายชาร์จ',
                        },
                        value: 'charging_cable',
                      },
                      {
                        text: {
                          en: 'Charging head',
                          th: 'หัวชาร์จ',
                        },
                        value: 'charger_head',
                      },
                    ],
                    tooltip: {
                      en: null,
                      th: null,
                    },
                    colCount: 4,
                    isRequired: true,
                    description: {
                      en: 'Please select accessories that not existed.',
                      th: 'โปรดเลือกเฉพาะรายการที่คุณไม่มี',
                    },
                    placeholder: {
                      en: 'Complete',
                      th: 'อุปกรณ์ครบ',
                    },
                    requiredErrorText: {
                      en: null,
                      th: 'กรุณาเลือกคำตอบอย่างน้อย 1 ข้อ',
                    },
                  },
                  {
                    name: 'country_of_purchase',
                    type: 'question_selection',
                    title: {
                      en: 'Purchase Country',
                      th: 'ประเทศที่ซื้อโทรศัพท์',
                    },
                    choices: [
                      {
                        text: {
                          en: 'Thai',
                          th: 'เครื่องไทย',
                        },
                        value: 'th',
                        status: 'positive',
                      },
                      {
                        text: {
                          en: 'Foreign device',
                          th: 'เครื่องนอก',
                        },
                        value: 'etc',
                        status: 'negative',
                      },
                    ],
                    tooltip: {
                      en: null,
                      th: null,
                    },
                    colCount: 4,
                    isRequired: true,
                    description: {
                      en: 'The country that sells this product.',
                      th: 'ประเทศร้านที่ออกจำหน่ายเครื่อง',
                    },
                    placeholder: {
                      en: null,
                      th: null,
                    },
                    requiredErrorText: {
                      en: null,
                      th: null,
                    },
                  },
                ],
              },
            ],
            logoPosition: 'right',
            questionDescriptionLocation: 'underInput',
          },
          title_detail: 'รายละเอียดสินค้า',
          fillSpaceColumnCount: 3,
        },
      ],
      checkListValues: {},
      draftEstimationId: '20240101-XXXXX',
    } as JobEntity;

    const mockUser: WithUserContext = {
      userKey: 'mockUserKey',
      company: 'WW',
    } as WithUserContext;

    const mockRequest = {
      estimationId: '20240101-XXXXX',
      deviceKey: '123456789010111',
      deviceKey2: '123456789010112',
      colorId: '0001',
      phoneNumber: '0123456789',
      answers: {
        product_information: {
          country_of_purchase: 'th',
          accessories_check: ['box'],
        },
        product_images: {},
      },
    };

    const mockModelMaster = {
      companyId: 'WW',
      modelKey: 'apple|iphone 14|128gb',
      modelMasterColors: [
        {
          id: '0001',
          companyId: 'WW',
          nameTh: 'ขาว',
          nameEn: 'WHITE',
        },
        {
          id: '0002',
          companyId: 'WW',
          nameTh: 'ดำ',
          nameEn: 'BLACK',
        },
      ],
    } as ModelMasterEntity;

    it('should create successfully (with colorId)', async () => {
      jest.spyOn(jobsRepository, 'findOne').mockResolvedValueOnce(null);

      jest
        .spyOn(jobsRepository, 'findOne')
        .mockResolvedValueOnce(mockJobEntity);

      jest
        .spyOn(modelMastersRepository, 'findOne')
        .mockResolvedValueOnce(mockModelMaster);

      jest.spyOn(jobsRepository, 'save').mockResolvedValueOnce(mockJobEntity);

      const result = await jobsService.createFromDraft(
        mockUser,
        mockJobId,
        mockRequest,
      );

      expect(result).toBe(null);
    });

    it('should create successfully (without colorId)', async () => {
      const mockRequestWithoutColorId = {
        ...mockRequest,
        colorId: undefined,
      };

      jest.spyOn(jobsRepository, 'findOne').mockResolvedValueOnce(null);

      jest
        .spyOn(jobsRepository, 'findOne')
        .mockResolvedValueOnce(mockJobEntity);

      jest
        .spyOn(modelMastersRepository, 'findOne')
        .mockResolvedValueOnce(mockModelMaster);

      jest.spyOn(jobsRepository, 'save').mockResolvedValueOnce(mockJobEntity);

      const result = await jobsService.createFromDraft(
        mockUser,
        mockJobId,
        mockRequestWithoutColorId,
      );

      expect(result).toBe(null);
    });

    it('should error job not found', async () => {
      jest.spyOn(jobsRepository, 'findOne').mockResolvedValueOnce(null);
      jest.spyOn(jobsRepository, 'findOne').mockResolvedValueOnce(null);

      try {
        await jobsService.createFromDraft(mockUser, mockJobId, mockRequest);
        // If it doesn't throw an error, fail the test
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.NOT_FOUND_DATA.code,
        );
        expect((error as BaseExceptionService).data).toBe('Job not found');
      }
    });

    it('should error imei not sold', async () => {
      jest
        .spyOn(jobsRepository, 'findOne')
        .mockResolvedValueOnce(mockJobEntity);

      try {
        await jobsService.createFromDraft(mockUser, mockJobId, mockRequest);
        // If it doesn't throw an error, fail the test
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.IMEI_NOT_SOLD.code,
        );
      }
    });

    it('should error checklist not found', async () => {
      const mockErrorJob = {
        ...mockJobEntity,
        checkList: [] as any,
      } as JobEntity;

      jest.spyOn(jobsRepository, 'findOne').mockResolvedValueOnce(null);

      jest.spyOn(jobsRepository, 'findOne').mockResolvedValueOnce(mockErrorJob);
      jest
        .spyOn(modelMastersRepository, 'findOne')
        .mockResolvedValueOnce(mockModelMaster);
      try {
        await jobsService.createFromDraft(mockUser, mockJobId, mockRequest);
        // If it doesn't throw an error, fail the test
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.NOT_FOUND_DATA.code,
        );
      }
    });

    it('should error draftEAId Invalid', async () => {
      const mockInvalidEAId = {
        ...mockRequest,
        estimationId: '20240101-YYYYY',
      };

      jest.spyOn(jobsRepository, 'findOne').mockResolvedValueOnce(null);

      jest
        .spyOn(jobsRepository, 'findOne')
        .mockResolvedValueOnce(mockJobEntity);
      jest
        .spyOn(estimateActivitiesRepository, 'findOne')
        .mockResolvedValueOnce(null);
      try {
        await jobsService.createFromDraft(mockUser, mockJobId, mockInvalidEAId);
        // If it doesn't throw an error, fail the test
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.INVALID_ESTIMATION_ACTIVITY.code,
        );
      }
    });

    it('should error ea not found', async () => {
      jest.spyOn(jobsRepository, 'findOne').mockResolvedValueOnce(null);

      jest
        .spyOn(jobsRepository, 'findOne')
        .mockResolvedValueOnce(mockJobEntity);
      jest
        .spyOn(estimateActivitiesRepository, 'findOne')
        .mockResolvedValueOnce(null);
      try {
        await jobsService.createFromDraft(mockUser, mockJobId, mockRequest);
        // If it doesn't throw an error, fail the test
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.NOT_FOUND_DATA.code,
        );
      }
    });

    it('should error ea already has job_id', async () => {
      const mockEAWithJobId = {
        ...mockEstimationActivity,
        jobId: mockJobEntity.jobId,
      };
      jest.spyOn(jobsRepository, 'findOne').mockResolvedValueOnce(null);

      jest
        .spyOn(jobsRepository, 'findOne')
        .mockResolvedValueOnce(mockJobEntity);
      jest
        .spyOn(estimateActivitiesRepository, 'findOne')
        .mockResolvedValueOnce(mockEAWithJobId);
      try {
        await jobsService.createFromDraft(mockUser, mockJobId, mockRequest);
        // If it doesn't throw an error, fail the test
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.UNAVAILABLE_ESTIMATION_ACTIVITY.code,
        );
      }
    });

    it('should error invalid colorId', async () => {
      const mockRequestColorInvalid = {
        ...mockRequest,
        colorId: '1020',
      };

      jest.spyOn(jobsRepository, 'findOne').mockResolvedValueOnce(null);

      jest
        .spyOn(jobsRepository, 'findOne')
        .mockResolvedValueOnce(mockJobEntity);

      jest
        .spyOn(modelMastersRepository, 'findOne')
        .mockResolvedValueOnce(mockModelMaster);

      jest.spyOn(jobsRepository, 'save').mockResolvedValueOnce(mockJobEntity);

      try {
        await jobsService.createFromDraft(
          mockUser,
          mockJobId,
          mockRequestColorInvalid,
        );
        // If it doesn't throw an error, fail the test
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.BODY_PAYLOAD_INVALID.code,
        );
        expect((error as BaseExceptionService).data).toBe(`Invalid colorId`);
      }
    });

    it('should error answer invalid (SELECTION TYPE)', async () => {
      const mockRequestSelectionInvalid = {
        ...mockRequest,
        answers: {
          product_information: {
            country_of_purchase: 'thx',
            accessories_check: ['box'],
          },
          product_images: {},
        },
      };

      jest.spyOn(jobsRepository, 'findOne').mockResolvedValueOnce(null);

      jest
        .spyOn(jobsRepository, 'findOne')
        .mockResolvedValueOnce(mockJobEntity);

      jest
        .spyOn(modelMastersRepository, 'findOne')
        .mockResolvedValueOnce(mockModelMaster);

      jest.spyOn(jobsRepository, 'save').mockResolvedValueOnce(mockJobEntity);

      try {
        await jobsService.createFromDraft(
          mockUser,
          mockJobId,
          mockRequestSelectionInvalid,
        );
        // If it doesn't throw an error, fail the test
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.BODY_PAYLOAD_INVALID.code,
        );
        expect((error as BaseExceptionService).data).toBe(
          'Body payload invalid, answers invalid',
        );
      }
    });

    it('should error answer invalid (OPTION TYPE 1)', async () => {
      const mockRequestSelectionInvalid = {
        ...mockRequest,
        answers: {
          product_information: {
            country_of_purchase: 'th',
            accessories_check: ['box', 'xxx'],
          },
          product_images: {},
        },
      };

      jest.spyOn(jobsRepository, 'findOne').mockResolvedValueOnce(null);

      jest
        .spyOn(jobsRepository, 'findOne')
        .mockResolvedValueOnce(mockJobEntity);

      jest
        .spyOn(modelMastersRepository, 'findOne')
        .mockResolvedValueOnce(mockModelMaster);

      jest.spyOn(jobsRepository, 'save').mockResolvedValueOnce(mockJobEntity);

      try {
        await jobsService.createFromDraft(
          mockUser,
          mockJobId,
          mockRequestSelectionInvalid,
        );
        // If it doesn't throw an error, fail the test
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.BODY_PAYLOAD_INVALID.code,
        );
        expect((error as BaseExceptionService).data).toBe(
          'Body payload invalid, answers invalid',
        );
      }
    });

    it('should error answer invalid (OPTION TYPE 2)', async () => {
      const mockRequestSelectionInvalid = {
        ...mockRequest,
        answers: {
          product_information: {
            country_of_purchase: 'th',
            accessories_check: 'BOX',
          },
          product_images: {},
        },
      };

      jest.spyOn(jobsRepository, 'findOne').mockResolvedValueOnce(null);

      jest
        .spyOn(jobsRepository, 'findOne')
        .mockResolvedValueOnce(mockJobEntity);

      jest
        .spyOn(modelMastersRepository, 'findOne')
        .mockResolvedValueOnce(mockModelMaster);

      jest.spyOn(jobsRepository, 'save').mockResolvedValueOnce(mockJobEntity);

      try {
        await jobsService.createFromDraft(
          mockUser,
          mockJobId,
          mockRequestSelectionInvalid,
        );
        // If it doesn't throw an error, fail the test
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.BODY_PAYLOAD_INVALID.code,
        );
        expect((error as BaseExceptionService).data).toBe(
          'Body payload invalid, answers invalid',
        );
      }
    });

    it('should error question not completed', async () => {
      const mockRequestSelectionInvalid = {
        ...mockRequest,
        answers: {
          product_information: {
            country_of_purchase: 'th',
          },
          product_images: {},
        },
      };

      jest.spyOn(jobsRepository, 'findOne').mockResolvedValueOnce(null);

      jest
        .spyOn(jobsRepository, 'findOne')
        .mockResolvedValueOnce(mockJobEntity);

      jest
        .spyOn(modelMastersRepository, 'findOne')
        .mockResolvedValueOnce(mockModelMaster);

      jest.spyOn(jobsRepository, 'save').mockResolvedValueOnce(mockJobEntity);

      try {
        await jobsService.createFromDraft(
          mockUser,
          mockJobId,
          mockRequestSelectionInvalid,
        );
        // If it doesn't throw an error, fail the test
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.BODY_PAYLOAD_INVALID.code,
        );
        expect((error as BaseExceptionService).data).toBe(
          'Body payload invalid, questions not complete',
        );
      }
    });
  });

  describe('prepareJobDraftSign', () => {
    const body: CreateJobDraftSignDto = {
      estimationId: mockOrderId,
      contractKey: 'abc123',
    };
    const user = {
      company: mockCompanyId,
      userKey: 'user123',
    } as WithUserContext;
    const branch = { branch: 'BranchA' };

    const mockModelMasterWithFn = {
      ...mockModelMaster,
      modelMasterFunction:
        mockModelMasterFunctionsForGetFunctionFromModelMasterFn,
      templateId: 'mock-template-id',
    } as ModelMasterEntity;

    const mockJobResult = {
      jobId: '2401-0000-0001',
      companyId: mockCompanyId,
      deviceKey: mockEstimationActivity.imei1,
      branchId: branch.branch,
      modelKey: 'modelKey',
      createdBy: user.userKey,
      updatedBy: user.userKey,
      shopUserKey: user.userKey,
      shopUserName: 'name',
      status: JobStatus.DRAFT,
      checkListValues: {} as any,
    } as JobEntity;

    const mockEA = {
      ...mockEstimationActivity,
      modelChecklistResult: { ...mockEstimationActivity.modelChecklistResult },
    };

    mockEA.modelChecklistResult.QUESTION = mockEAQuestionsJob;

    const mockTemplate = [
      {
        slug: 'remobie_check_list',
        title: null,
        survey_form: {
          pages: [
            {
              name: 'page1',
              elements: null,
            },
          ],
        },
      },
      {
        slug: 'product_information',
        title: 'รายละเอียดอื่นๆเกี่ยวกับตัวเครื่อง',
        survey_form: {
          pages: [
            {
              name: 'page1',
              elements: null,
            },
          ],
        },
      },
      {
        slug: 'product_images',
        title: 'รูปภาพสินค้า',
        title_detail: 'รูปภาพตัวสินค้า',
        fillSpaceColumn: [
          { path: 'product_images_desktop', count: 4 },
          { path: 'product_images_tablet', count: 4 },
        ],
        survey_form: {
          logoPosition: 'right',
          pages: [
            {
              name: 'page1',
              elements: [],
            },
          ],
          questionDescriptionLocation: 'underInput',
        },
      },
    ];

    beforeEach(() => {
      const { modelChecklistResult } = mockEA;
      const newModule = modelChecklistResult.MODULE.map((module) => {
        if (module.modelChecklistId === 'module-3') {
          return {
            ...module,
            isRequired: true,
            isSkip: false,
            moduleStatus: 'pass',
          };
        }
        return module;
      });

      const eaMock = {
        ...mockEA,
        modelChecklistResult: { ...modelChecklistResult, MODULE: newModule },
      } as EstimationActivitiesEntity;

      jest
        .spyOn(estimateActivitiesRepository, 'findOne')
        .mockResolvedValue(eaMock);

      jest
        .mocked(getFunctionFromModelMasterFn)
        .mockImplementation(
          () => mockResultGetFunctionFromModelMasterFn() as any,
        );

      jest
        .mocked(generateProductImageSlugForTemplate)
        .mockImplementation(() => []);

      jest
        .spyOn(modelMastersRepository, 'findOne')
        .mockResolvedValue({ ...mockModelMasterWithFn });

      jest
        .spyOn(penaltiesViewRepository, 'findOne')
        .mockResolvedValue(mockPenalties as PenaltiesView);

      jest
        .spyOn(jobTemplatesRepository, 'findOne')
        .mockResolvedValue({ template: mockTemplate } as any);
    });

    afterEach(() => {
      jest.resetAllMocks();
    });

    it('should prepare a job draft with valid model identifiers and penalties (normal flow)', async () => {
      const mockQueryRunner = {
        connect: jest.fn().mockResolvedValue(undefined),
        startTransaction: jest.fn().mockResolvedValue(undefined),
        commitTransaction: jest.fn().mockResolvedValue(undefined),
        rollbackTransaction: jest.fn().mockResolvedValue(undefined),
        release: jest.fn().mockResolvedValue(undefined),
      } as any;

      const mockConnection = {
        createQueryRunner: jest.fn().mockReturnValue(mockQueryRunner),
      } as any;

      const mockManager = {
        connection: mockConnection,
        getRepository: jest.fn().mockReturnThis(),
        createQueryBuilder: jest.fn().mockReturnThis(),
        insert: jest.fn().mockReturnThis(),
        orIgnore: jest.fn().mockReturnThis(),
        execute: jest.fn().mockResolvedValue({ raw: [mockJobResult] }),
        values: jest.fn().mockReturnThis(),
        save: jest.fn().mockResolvedValueOnce(mockJobResult),
      } as any;

      Object.defineProperty(jobsRepository, 'manager', {
        value: mockManager,
      });
      Object.defineProperty(mockQueryRunner, 'manager', {
        value: mockManager,
      });

      jest.spyOn(jobsRepository, 'createQueryBuilder').mockReturnValueOnce({
        select: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        getOne: jest.fn().mockResolvedValue(null),
      } as any);

      // Call the method being tested
      const result = await jobsService.prepareJobDraftSign(body, user, branch);

      // Assertions
      expect(result).toBeInstanceOf(JobEntity);
      expect(result.companyId).toBe(user.company);
      expect(result.branchId).toBe(branch.branch);
      expect(result.deviceKey).toBe(mockEstimationActivity.imei1);
      expect(result.status).toBe(JobStatus.DRAFT);
      expect(result.estimationActivities).toBe(undefined);
      expect(result.draftEstimationId).toBe(mockOrderId);
    });

    it('should regenerate id', async () => {
      const mockQueryRunner = {
        connect: jest.fn().mockResolvedValue(undefined),
        startTransaction: jest.fn().mockResolvedValue(undefined),
        commitTransaction: jest.fn().mockResolvedValue(undefined),
        rollbackTransaction: jest.fn().mockResolvedValue(undefined),
        release: jest.fn().mockResolvedValue(undefined),
      } as any;

      const mockConnection = {
        createQueryRunner: jest.fn().mockReturnValue(mockQueryRunner),
      } as any;

      const mockManager = {
        connection: mockConnection,
        getRepository: jest.fn().mockReturnThis(),
        createQueryBuilder: jest.fn().mockReturnThis(),
        insert: jest.fn().mockReturnThis(),
        orIgnore: jest.fn().mockReturnThis(),
        execute: jest.fn().mockResolvedValue({ raw: [] }),
        values: jest.fn().mockReturnThis(),
        save: jest.fn().mockResolvedValueOnce(mockJobResult),
      } as any;

      Object.defineProperty(jobsRepository, 'manager', {
        value: mockManager,
      });
      Object.defineProperty(mockQueryRunner, 'manager', {
        value: mockManager,
      });

      jest.spyOn(jobsRepository, 'createQueryBuilder').mockReturnValueOnce({
        select: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        getOne: jest.fn().mockResolvedValue(null),
      } as any);

      // Call the method being tested
      const result = await jobsService.prepareJobDraftSign(body, user, branch);

      // Assertions
      expect(result).toBeInstanceOf(JobEntity);
      expect(result.companyId).toBe(user.company);
      expect(result.branchId).toBe(branch.branch);
      expect(result.deviceKey).toBe(mockEstimationActivity.imei1);
      expect(result.status).toBe(JobStatus.DRAFT);
      expect(result.estimationActivities).toBe(undefined);
      expect(result.draftEstimationId).toBe(mockOrderId);
    });

    it('should throw error EA notfound', async () => {
      jest
        .spyOn(estimateActivitiesRepository, 'findOne')
        .mockImplementation(() => Promise.resolve(null));

      try {
        await jobsService.prepareJobDraftSign(body, user, branch);
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.NOT_FOUND_DATA.code,
        );
      }
    });

    it('should throw error EA used by other', async () => {
      jest
        .spyOn(estimateActivitiesRepository, 'findOne')
        .mockImplementation(() => Promise.resolve({ ...mockEA, jobId: '123' }));

      try {
        await jobsService.prepareJobDraftSign(body, user, branch);

        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.UNAVAILABLE_ESTIMATION_ACTIVITY.code,
        );
      }
    });

    it('should throw error EA expire', async () => {
      jest
        .spyOn(estimateActivitiesRepository, 'findOne')
        .mockImplementation(() =>
          Promise.resolve({
            ...mockEA,
            createdAt: new Date(new Date().getTime() - 60 * 60 * 1000 * 7),
          }),
        );

      try {
        await jobsService.prepareJobDraftSign(body, user, branch);
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.EXPIRED_ESTIMATION_ACTIVITY.code,
        );
      }
    });

    it('should throw error invalid model identifier(not found moodel)', async () => {
      jest
        .spyOn(modelMastersRepository, 'findOne')
        .mockImplementation(() => Promise.resolve(null));

      try {
        await jobsService.prepareJobDraftSign(body, user, branch);
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.INVALID_MODEL_IDENTIFIERS.code,
        );
        expect((error as BaseExceptionService).data).toBe(
          'Target Model not found',
        );
      }
    });

    it('should throw error invalid model identifier(not found model master or templateid)', async () => {
      jest
        .spyOn(modelMastersRepository, 'findOne')
        .mockImplementation(() => Promise.resolve({ ...mockModelMaster }));

      try {
        await jobsService.prepareJobDraftSign(body, user, branch);
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.INVALID_MODEL_IDENTIFIERS.code,
        );
        expect((error as BaseExceptionService).data).toBe(
          'ModelMasterFunction or templateId not found',
        );
      }
    });

    it('should throw error invalid model identifier(not found template)', async () => {
      jest
        .spyOn(jobTemplatesRepository, 'findOne')
        .mockImplementation(() => Promise.resolve(null));

      try {
        await jobsService.prepareJobDraftSign(body, user, branch);
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.INVALID_MODEL_IDENTIFIERS.code,
        );
        expect((error as BaseExceptionService).data).toBe(
          'Not found defaultJobTemplate',
        );
      }
    });

    it('should throw error invalid model identifier(not found template)', async () => {
      console.log(mockEA.modelChecklistResult.MODULE);
      jest
        .spyOn(estimateActivitiesRepository, 'findOne')
        .mockImplementation(() => Promise.resolve(mockEA));

      try {
        await jobsService.prepareJobDraftSign(body, user, branch);
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.INVALID_ESTIMATION_ACTIVITY.code,
        );
      }
    });
  });

  describe('OCR', () => {
    const user = {
      company: 'WW',
    } as WithUserContext;
    it('should return value', async () => {
      const result = await jobsService.getOcrResult(user, 'test-1', {
        image: mockBase64Png,
        key: 'test-key.png',
      });

      expect(result).toHaveProperty('imagePath');
      expect(result).toHaveProperty('masterAddress');
    });
    it('should return error', async () => {
      jest
        .spyOn(ocrService, 'getOcrResult')
        .mockImplementationOnce(async () => {
          throw new Error();
        });
      try {
        await jobsService.getOcrResult(user, 'test-1', {
          image: mockBase64Png,
          key: 'test-key.png',
        });
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
      }
    });
  });

  describe('Get PresignedUrl', () => {
    it('should successfully get PresignedUrl', async () => {
      const request = {
        jobId: 'jobid-0001',
        key: 'test_video_111',
      };

      const user = {
        company: 'CompanyX',
        userKey: 'user123',
      } as WithUserContext;

      const result = await jobsService.getPresigned(
        request.jobId,
        request.key,
        user,
      );

      const expectedUrlPath = getS3JobUrlPath(
        user.company,
        request.jobId,
        request.key,
      );

      // Assertions
      expect(result.url).toContain(request.jobId);
      expect(result.url).toContain(request.key);
      expect(result.path).toContain(expectedUrlPath);
    });
  });

  describe('Get Job Media Url', () => {
    //case 1: job with AOShippingStatus.LOST, incompleteAOListValue has 1 item
    //case 2: job with AOShippingStatus.LOST, incompleteAOListValue has 2 items
    //case 3: job with AOShippingStatus.NOT_SCANNED, incompleteAOListValue has 1 item
    //case 4: job with AOShippingStatus.LOST, incompleteAOListValue is empty
    //case 5: job with AOShippingStatus.LOST, incompleteAOListValue is undefined

    const mockJobPresign = {
      case1: {
        ...mockJobMediUrl,
        aoShippingStatus: AOShippingStatus.LOST,
        incompleteAOListValue: [
          {
            type: AOShippingStatus.LOST,
            remark: 'test remark',
            userKey: '<EMAIL>',
            userName: 'nemo02',
            confirmedAt: new Date(),
            allocationOrderId: 'AO-2024090024',
            videoPath: 'video-save-path',
          },
        ],
      } as JobEntity,
      case2: {
        ...mockJobMediUrl,
        aoShippingStatus: AOShippingStatus.LOST,
        incompleteAOListValue: [
          {
            type: AOShippingStatus.NOT_SCANNED,
            remark: 'test remark',
            userKey: '<EMAIL>',
            userName: 'nemo02',
            confirmedAt: new Date(),
            allocationOrderId: 'AO-2024090024',
          },
          {
            type: AOShippingStatus.LOST,
            remark: 'test remark',
            userKey: '<EMAIL>',
            userName: 'nemo02',
            confirmedAt: new Date(),
            allocationOrderId: 'AO-2024090024',
            videoPath: 'video-save-path',
          },
        ],
      } as JobEntity,
      case3: {
        ...mockJobMediUrl,
        aoShippingStatus: AOShippingStatus.NOT_SCANNED,
        incompleteAOListValue: [
          {
            type: AOShippingStatus.NOT_SCANNED,
            remark: 'test remark',
            userKey: '<EMAIL>',
            userName: 'nemo02',
            confirmedAt: new Date(),
            allocationOrderId: 'AO-2024090024',
          },
        ],
      } as JobEntity,
      case4: {
        ...mockJobMediUrl,
        aoShippingStatus: AOShippingStatus.LOST,
        incompleteAOListValue: [],
      } as JobEntity,
      case5: {
        ...mockJobMediUrl,
        aoShippingStatus: AOShippingStatus.LOST,
        incompleteAOListValue: undefined,
      } as JobEntity,
    };

    const mockResultList = {
      case1: {
        videoPath: 'url-video-2',
      },
      case2: {
        videoPath: 'url-video-2',
      },
      case3: {
        videoPath: undefined,
      },
      case4: {
        videoPath: undefined,
      },
      case5: {
        videoPath: undefined,
      },
    };

    it('should return job with media_url', async () => {
      jest
        .spyOn(s3Service, 'getFileWithSignedUrl')
        .mockResolvedValue('url-video-1');

      const result = await jobsService.getMediaUrl(mockJobMediUrl);
      // Assertions
      expect(result.checkListValues).toHaveProperty('product_information');
      expect(result.checkListValues['product_information']).toHaveProperty(
        'media_url',
      );
      expect(
        result.checkListValues['product_information']['media_url'],
      ).toHaveProperty('dead_pixel');
      expect(
        result.checkListValues['product_information']['media_url']['dead_pixel']
          .length,
      ).toBe(
        result.checkListValues.media['product_information']['dead_pixel']
          .length,
      );

      expect(
        result.checkListValues['product_information']['media_url'],
      ).toHaveProperty('dead_pixel2');
      expect(
        result.checkListValues['product_information']['media_url'][
          'dead_pixel2'
        ].length,
      ).toBe(
        result.checkListValues.media['product_information']['dead_pixel2']
          .length,
      );

      expect(result.checkListValues).toHaveProperty(
        'additional_product_information',
      );
      expect(
        result.checkListValues['additional_product_information'],
      ).toHaveProperty('media_url');
      expect(
        result.checkListValues['additional_product_information']['media_url'],
      ).toHaveProperty('test1');
      expect(
        result.checkListValues['additional_product_information']['media_url'][
          'test1'
        ].length,
      ).toBe(
        result.checkListValues.media['additional_product_information']['test1']
          .length,
      );
    });

    it.each(['case1', 'case2', 'case3', 'case4', 'case5'])(
      'should return job with presignUrl',
      async (testCase) => {
        const mockJob = mockJobPresign[testCase] as JobEntity;
        const mockResult = mockResultList[testCase];
        jest
          .spyOn(s3Service, 'getFileWithSignedUrl')
          .mockResolvedValue('url-video-1');
        jest
          .spyOn(s3Service, 'getFileWithSignedUrl')
          .mockResolvedValue('url-video-2');

        const result = await jobsService.getMediaUrl(mockJob);

        if (result.incompleteAOListValue) {
          const resultAOListValue =
            result.incompleteAOListValue[
              result.incompleteAOListValue.length - 1
            ];
          expect(resultAOListValue?.videoPath).toBe(mockResult.videoPath);
        }
      },
    );
  });
  describe('Get Media Url from path', () => {
    it('should return url path', async () => {
      jest
        .spyOn(s3Service, 'getFileWithSignedUrl')
        .mockResolvedValue('url-video-1');

      const result = await jobsService.getMediaUrlFromPath({
        path: 'path-to-file',
      });
      // Assertions
      expect(result).toEqual({ url: 'url-video-1' });
    });

    it('should not return url path', async () => {
      jest
        .spyOn(s3Service, 'getFileWithSignedUrl')
        .mockRejectedValue(new Error('error'));
      // Assertions
      try {
        await jobsService.getMediaUrlFromPath({
          path: 'path-to-file',
        });

        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.BODY_PAYLOAD_INVALID.code,
        );
      }
    });
  });
  describe('Update Job Select Campaign', () => {
    it('return error not found data job', async () => {
      const userMock = {
        company: 'CompanyX',
        userKey: 'userTest1',
      } as WithUserContext;
      const bodyMock = {
        campaignCodes: ['campaign1', 'campaign2'],
      };
      jest.spyOn(jobsRepository, 'findOne').mockResolvedValue(null);

      try {
        await jobsService.updateSelectCampaign(
          userMock,
          'jobid-0001',
          bodyMock,
        );
        // If it doesn't throw an error, fail the test
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.NOT_FOUND_DATA.code,
        );
      }
    });
    it('return error invalid job permission', async () => {
      const userMock = {
        company: 'CompanyX',
        userKey: 'userTest1',
      } as WithUserContext;
      const bodyMock = {
        campaignCodes: ['campaign1', 'campaign2'],
      };
      jest
        .spyOn(jobsRepository, 'findOne')
        .mockResolvedValue(mockJobCampaignSelectInvalidUser);

      try {
        await jobsService.updateSelectCampaign(
          userMock,
          'jobid-0001',
          bodyMock,
        );
        // If it doesn't throw an error, fail the test
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.INVALID_JOB_PERMISSION.code,
        );
      }
    });
    it('return error invalid job status', async () => {
      const userMock = {
        company: 'CompanyX',
        userKey: 'userTest1',
      } as WithUserContext;
      const bodyMock = {
        campaignCodes: ['campaign1', 'campaign2'],
      };
      jest
        .spyOn(jobsRepository, 'findOne')
        .mockResolvedValue(mockJobCampaignSelectInvalidStatus);

      try {
        await jobsService.updateSelectCampaign(
          userMock,
          'jobid-0001',
          bodyMock,
        );
        // If it doesn't throw an error, fail the test
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.INVALID_JOB_FOR_ACTION.code,
        );
      }
    });
    it('return error campaign not found', async () => {
      const userMock = {
        company: 'CompanyX',
        userKey: 'userTest1',
      } as WithUserContext;
      const bodyMock = {
        campaignCodes: ['campaign1', 'campaign2'],
      };
      jest
        .spyOn(jobsRepository, 'findOne')
        .mockResolvedValue(mockJobCampaignSelect);
      jest.spyOn(campaignRepo, 'findOne').mockResolvedValue(null);

      try {
        await jobsService.updateSelectCampaign(
          userMock,
          'jobid-0001',
          bodyMock,
        );
        // If it doesn't throw an error, fail the test
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.CAMPAIGN_NOT_FOUND.code,
        );
      }
    });
    it('return error campaign redemption code not found', async () => {
      const userMock = {
        company: 'CompanyX',
        userKey: 'userTest1',
      } as WithUserContext;
      const bodyMock = {
        campaignCodes: ['campaign1', 'campaign2'],
      };
      jest
        .spyOn(jobsRepository, 'findOne')
        .mockResolvedValue(mockJobCampaignSelect);
      jest.spyOn(campaignRepo, 'findOne').mockResolvedValue(mockCampaign);

      const mockQueryRunner = {
        connect: jest.fn().mockResolvedValue(undefined),
        startTransaction: jest.fn().mockResolvedValue(undefined),
        commitTransaction: jest.fn().mockResolvedValue(undefined),
        rollbackTransaction: jest.fn().mockResolvedValue(undefined),
        release: jest.fn().mockResolvedValue(undefined),
      } as any;

      const mockConnection = {
        createQueryRunner: jest.fn().mockReturnValue(mockQueryRunner),
      } as any;

      const mockManager = {
        connection: mockConnection,
        getRepository: jest.fn().mockReturnThis(),
        createQueryBuilder: jest.fn().mockReturnThis(),
        setLock: jest.fn().mockReturnThis(),
        setOnLocked: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        getOne: jest.fn().mockResolvedValue(null),
        insert: jest.fn().mockReturnThis(),
        orIgnore: jest.fn().mockReturnThis(),
        execute: jest.fn().mockResolvedValue({ raw: [] }),
        values: jest.fn().mockReturnThis(),
        save: jest.fn().mockResolvedValueOnce(mockCampaignRedemptionCodeList),
      } as any;

      Object.defineProperty(campaignRedemptionCodeRepo, 'manager', {
        value: mockManager,
      });
      Object.defineProperty(mockQueryRunner, 'manager', {
        value: mockManager,
      });

      jest
        .spyOn(campaignRedemptionCodeRepo, 'createQueryBuilder')
        .mockReturnValueOnce({
          select: jest.fn().mockReturnThis(),
          setLock: jest.fn().mockReturnThis(),
          setOnLocked: jest.fn().mockReturnThis(),
          where: jest.fn().mockReturnThis(),
          orderBy: jest.fn().mockReturnThis(),
          getOne: jest.fn().mockResolvedValue(null),
        } as any);

      try {
        await jobsService.updateSelectCampaign(
          userMock,
          'jobid-0001',
          bodyMock,
        );
        // If it doesn't throw an error, fail the test
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.CAMPAIGN_REDEMPTION_CODE_NOT_FOUND.code,
        );
      }
    });
    it('should successful update job select campaign (campaign is null)', async () => {
      const userMock = {
        company: 'CompanyX',
        userKey: 'userTest1',
      } as WithUserContext;
      const bodyMock = {
        campaignCodes: null,
      };
      jest
        .spyOn(jobsRepository, 'findOne')
        .mockResolvedValue(mockJobCampaignSelect2);

      const result = await jobsService.updateSelectCampaign(
        userMock,
        'jobid-0001',
        bodyMock,
      );
      //expect result not null
      expect(result).not.toBeNull();
      expect(result.status).toBe(JobStatus.CAMPAIGN_SELECTED);
    });
    it('should successful update job select campaign', async () => {
      const userMock = {
        company: 'CompanyX',
        userKey: 'userTest1',
      } as WithUserContext;
      const bodyMock = {
        campaignCodes: ['campaign1', 'campaign2'],
      };
      jest
        .spyOn(jobsRepository, 'findOne')
        .mockResolvedValue(mockJobCampaignSelect);
      jest.spyOn(campaignRepo, 'findOne').mockResolvedValue(mockCampaign);

      const mockQueryRunner = {
        connect: jest.fn().mockResolvedValue(undefined),
        startTransaction: jest.fn().mockResolvedValue(undefined),
        commitTransaction: jest.fn().mockResolvedValue(undefined),
        rollbackTransaction: jest.fn().mockResolvedValue(undefined),
        release: jest.fn().mockResolvedValue(undefined),
      } as any;

      const mockConnection = {
        createQueryRunner: jest.fn().mockReturnValue(mockQueryRunner),
      } as any;

      const mockManager = {
        connection: mockConnection,
        getRepository: jest.fn().mockReturnThis(),
        createQueryBuilder: jest.fn().mockReturnThis(),
        setLock: jest.fn().mockReturnThis(),
        setOnLocked: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        getOne: jest.fn().mockResolvedValue(mockCampaignRedemptionCode),
        insert: jest.fn().mockReturnThis(),
        orIgnore: jest.fn().mockReturnThis(),
        execute: jest.fn().mockResolvedValue({ raw: [] }),
        values: jest.fn().mockReturnThis(),
        save: jest.fn().mockResolvedValueOnce(mockCampaignRedemptionCodeList),
      } as any;

      Object.defineProperty(campaignRedemptionCodeRepo, 'manager', {
        value: mockManager,
      });
      Object.defineProperty(mockQueryRunner, 'manager', {
        value: mockManager,
      });

      jest
        .spyOn(campaignRedemptionCodeRepo, 'createQueryBuilder')
        .mockReturnValueOnce({
          select: jest.fn().mockReturnThis(),
          setLock: jest.fn().mockReturnThis(),
          setOnLocked: jest.fn().mockReturnThis(),
          where: jest.fn().mockReturnThis(),
          orderBy: jest.fn().mockReturnThis(),
          getOne: jest.fn().mockResolvedValue(mockCampaignRedemptionCode),
        } as any);

      const result = await jobsService.updateSelectCampaign(
        userMock,
        'jobid-0001',
        bodyMock,
      );
      //expect result not null
      expect(result).not.toBeNull();
      expect(result.status).toBe(JobStatus.CAMPAIGN_SELECTED);
    });
  });

  describe('Get Job CompaignInfo', () => {
    it('return error not found data job', async () => {
      jest.spyOn(jobsRepository, 'findOne').mockResolvedValue(null);

      try {
        await jobsService.getCampaignInformation(mockJob);
        // If it doesn't throw an error, fail the test
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.NOT_FOUND_DATA.code,
        );
      }
    });

    it('return error not found data job', async () => {
      const mockCampaign = {
        campaignCode: 'campaignCode1',
        campaignName: 'campaignName1',
        companyId: 'CompanyX',
        description: 'description',
        remark: 'remark',
        startDate: new Date(),
        endDate: new Date(),
        maxRedemptionCode: 2,
      } as CampaignEntity;

      const mockWithCampaign = {
        ...mockJob,
        campaigns: [mockCampaign],
      } as JobEntity;

      jest.spyOn(jobsRepository, 'findOne').mockResolvedValue(mockWithCampaign);
      const result = await jobsService.getCampaignInformation(mockJob);

      expect(result.campaigns?.length).toBe(1);
    });
  });
});
