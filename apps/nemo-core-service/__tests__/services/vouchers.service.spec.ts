import { Test } from '@nestjs/testing';
import { BaseExceptionModule } from '../../src/base-exceptions/base-exceptions.module';

import { VouchersService } from '../../src/shop/vouchers/vouchers.service';
import { Repository, SelectQueryBuilder } from 'typeorm';
import {
  BranchEntity,
  CustomerInfo,
  CustomerInfoType,
  RemoteActivationStatus,
  UserEntity,
  VerificationStatus,
  VoucherEntity,
  ConfigActivitiesEntity,
} from '../../src/entities';
import { Request } from 'express';
import { getRepositoryToken } from '@nestjs/typeorm';
import { AES128MessageService } from '../../src/encrypt-decrypt-message/encrypt-decrypt.service';
import { BaseExceptionService } from '../../src/exceptions';
import { BASE_EXCEPTIONS } from '../../src/config';
import { TsmService } from '../../src/tsm/tsm.service';

describe('VouchersService', () => {
  let vouchersService: VouchersService;
  let tsmService: TsmService;
  let vouchersRepository: Repository<VoucherEntity>;
  let userRepository: Repository<UserEntity>;
  let branchRepository: Repository<BranchEntity>;

  beforeEach(async () => {
    const module = await Test.createTestingModule({
      imports: [BaseExceptionModule.register()],
      providers: [
        VouchersService,
        {
          provide: getRepositoryToken(VoucherEntity),
          useValue: {
            findOne: jest.fn().mockReturnThis(),
            manager: jest.fn().mockReturnThis(),
            save: jest.fn().mockReturnThis(),
          },
        },
        {
          provide: getRepositoryToken(UserEntity),
          useValue: {
            findOne: jest.fn().mockReturnThis(),
            findOneOrFail: jest.fn().mockReturnThis(),
          },
        },
        {
          provide: getRepositoryToken(BranchEntity),
          useValue: {
            findOne: jest.fn().mockReturnThis(),
            findOneOrFail: jest.fn().mockReturnThis(),
          },
        },
        {
          provide: getRepositoryToken(ConfigActivitiesEntity),
          useValue: {
            findOne: jest.fn().mockReturnThis(),
          },
        },
        {
          provide: AES128MessageService,
          useValue: {
            encrypt: jest.fn(() => 'encrypted'),
            decrypt: jest.fn(() => 'decrypted'),
          },
        },
        {
          provide: TsmService,
          useValue: {
            couponRedemption: jest.fn(),
          },
        },
      ],
    }).compile();

    vouchersService = module.get<VouchersService>(VouchersService);
    tsmService = module.get<TsmService>(TsmService);
    vouchersRepository = module.get<Repository<VoucherEntity>>(
      getRepositoryToken(VoucherEntity),
    );
    userRepository = module.get<Repository<UserEntity>>(
      getRepositoryToken(UserEntity),
    );
    branchRepository = module.get<Repository<BranchEntity>>(
      getRepositoryToken(BranchEntity),
    );
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('Build Search Query', () => {
    it('should construct search query conditions based on provided parameters', () => {
      const mockRequest = {
        query: {
          branchId: 'branchId',
          status: ['status1', 'status2'],
          lastUpdatePeriod: '2000',
        },
      } as unknown as Request;

      const mockListQuery: SelectQueryBuilder<VoucherEntity> = {
        andWhere: jest.fn(),
      } as unknown as SelectQueryBuilder<VoucherEntity>;

      const result = vouchersService.buildSearchQuery(
        mockRequest,
        mockListQuery,
      );

      expect(result.andWhere).toHaveBeenCalledWith(
        expect.stringContaining(
          "verificationBranch.branchId = 'branchId' AND r.verificationStatus IN ('status1','status2') AND r.updatedAt >=",
        ),
      );
    });
  });

  describe('Sanitize Input Body', () => {
    it('should omit voucherId field if isCreated is null', () => {
      const mockData: Partial<VoucherEntity> = {
        voucherId: '1',
      };
      const result = vouchersService.sanitizeInputBody(mockData);

      expect(result).not.toHaveProperty('voucherId');
    });
    it('should omit voucherId field if isCreated is true', () => {
      const mockData: Partial<VoucherEntity> = {
        voucherId: '1',
      };
      const result = vouchersService.sanitizeInputBody(mockData, true);

      expect(result).toHaveProperty('voucherId');
    });
  });

  describe('Compute Update Payload', () => {
    it.each`
      initialStatus                   | updatedStatus
      ${VerificationStatus.IDLE}      | ${VerificationStatus.REQUESTED}
      ${VerificationStatus.REQUESTED} | ${VerificationStatus.IDLE}
    `(
      'should allow updating $initialStatus to $updatedStatus',
      ({ initialStatus, updatedStatus }) => {
        const rawVoucher: VoucherEntity = {
          verificationStatus: initialStatus,
          remoteActivationStatus: RemoteActivationStatus.DEACTIVATE,
        } as VoucherEntity;

        const result = vouchersService.computeUpdatePayload(rawVoucher, {
          verificationStatus: updatedStatus,
        });

        expect(result).toEqual({
          verificationStatus: updatedStatus,
        });
      },
    );

    it.each`
      initialStatus                        | updatedStatus
      ${RemoteActivationStatus.DEACTIVATE} | ${RemoteActivationStatus.ACTIVATE}
    `(
      'should allow updating $initialStatus to $updatedStatus',
      ({ initialStatus, updatedStatus }) => {
        const rawVoucher: VoucherEntity = {
          remoteActivationStatus: initialStatus,
          verificationStatus: VerificationStatus.IDLE,
        } as VoucherEntity;

        const result = vouchersService.computeUpdatePayload(rawVoucher, {
          remoteActivationStatus: updatedStatus,
        });

        expect(result).toEqual({
          remoteActivationStatus: updatedStatus,
        });
      },
    );

    it.each([
      {
        initialStatus: VerificationStatus.REJECTED,
        status: VerificationStatus.REJECTED,
        expectedErrorMessage: `Invalid input for update voucher - The verificationStatus must be: ${VerificationStatus.REQUESTED}. Current verificationStatus is: ${VerificationStatus.REJECTED}`,
      },
      {
        initialStatus: VerificationStatus.REJECTED,
        status: 'invalidStatus',
        expectedErrorMessage: `Invalid input for update voucher - The verificationStatus must be: requested or rejected or verified or idle. Current verificationStatus is: ${VerificationStatus.REJECTED}`,
      },
    ])('should throw an error when %s', (testCase) => {
      const rawVoucher: VoucherEntity = {
        verificationStatus: testCase.initialStatus,
      } as VoucherEntity;

      try {
        vouchersService.computeUpdatePayload(rawVoucher, {
          verificationStatus: testCase.status as any,
        });
        // If it doesn't throw an error, fail the test
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.INVALID_INPUT_FOR_UPDATE_VOUCHER.code,
        );
        expect((error as BaseExceptionService).data).toBe(
          testCase.expectedErrorMessage,
        );
      }
    });

    it.each([
      {
        initialStatus: RemoteActivationStatus.DEACTIVATE,
        status: RemoteActivationStatus.DEACTIVATE,
        expectedErrorMessage: `Invalid input for update voucher - The remoteActivationStatus must be: ${RemoteActivationStatus.ACTIVATE}. Current remoteActivationStatus is: ${RemoteActivationStatus.DEACTIVATE}`,
      },
      {
        initialStatus: RemoteActivationStatus.DEACTIVATE,
        status: 'invalidStatus',
        expectedErrorMessage: `Invalid input for update voucher - The remoteActivationStatus must be: activate or deactivate or used. Current remoteActivationStatus is: ${RemoteActivationStatus.DEACTIVATE}`,
      },
    ])('should throw an error when %s', (testCase) => {
      const rawVoucher: VoucherEntity = {
        verificationStatus: VerificationStatus.VERIFIED,
        remoteActivationStatus: testCase.initialStatus,
      } as VoucherEntity;

      try {
        vouchersService.computeUpdatePayload(rawVoucher, {
          remoteActivationStatus: testCase.status as any,
        });
        // If it doesn't throw an error, fail the test
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.INVALID_INPUT_FOR_UPDATE_VOUCHER.code,
        );
        expect((error as BaseExceptionService).data).toBe(
          testCase.expectedErrorMessage,
        );
      }
    });
  });

  describe('Default Prepare Voucher', () => {
    it('should prepare an comment voucher', () => {
      // Call the method being tested
      const result = vouchersService.defaultPrepareVoucher({
        verificationStatus: VerificationStatus.IDLE,
      });

      // Assertions
      expect(result).toBeInstanceOf(VoucherEntity);

      expect(result.verificationStatus).toEqual(VerificationStatus.IDLE);
    });
  });

  describe('Prepare Verified Voucher', () => {
    it('should prepare verified voucher', () => {
      const type: CustomerInfoType = CustomerInfoType.IDENTITY_VERIFICATION;

      // Call the method being tested
      const result = vouchersService.prepareVerifiedVoucher(type, {
        type: CustomerInfoType.IDENTITY_VERIFICATION,
      } as CustomerInfo);

      // Assertions
      expect(result).toBeInstanceOf(VoucherEntity);
    });

    it('should throw an error when customer info invalid ', () => {
      const type = CustomerInfoType.IDENTITY_VERIFICATION;

      try {
        vouchersService.prepareVerifiedVoucher(type, {} as CustomerInfo);
        // If it doesn't throw an error, fail the test
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.INVALID_INPUT_FOR_UPDATE_VOUCHER.code,
        );
        expect((error as BaseExceptionService).data).toBe(
          `Invalid customer info - Requires to request customer info ${type} type`,
        );
      }
    });
  });

  describe('Prepare Identity Review', () => {
    it('should return a VoucherEntity with the correct properties', async () => {
      // Mock input data
      const body = {
        reason: 'mockedReason',
      } as any;
      const userKey = 'mockedUserKey';
      const branchId = 'mockedBranchId';

      jest.spyOn(userRepository, 'findOneOrFail').mockResolvedValueOnce({
        userKey,
      } as UserEntity);

      jest.spyOn(branchRepository, 'findOneOrFail').mockResolvedValueOnce({
        branchId,
      } as BranchEntity);

      // Call the method
      const result: VoucherEntity = await vouchersService.prepareIdentityReview(
        body,
        { userKey } as any,
        branchId,
      );

      // Assertions
      expect(result).toBeDefined();
      expect(result.customerInfo).toEqual(
        expect.objectContaining({ type: 'IDENTITY_VERIFICATION' }),
      );
      expect(result.verificationStatus).toBe('requested');
    });
  });

  describe('Coupon Redemption', () => {
    it('should redeem a voucher and return the updated voucher entity', async () => {
      const status = RemoteActivationStatus.ACTIVATE;
      const voucherId = 'voucher-id';
      const voucherEntity: VoucherEntity = {
        voucherId,
        company: {
          companyId: 'company-id',
        },
        remoteActivationStatus: RemoteActivationStatus.DEACTIVATE,
        verificationStatus: VerificationStatus.VERIFIED,
      } as VoucherEntity;
      jest
        .spyOn(vouchersRepository, 'findOne')
        .mockResolvedValue(voucherEntity);

      jest.spyOn(vouchersRepository, 'save').mockResolvedValue({
        voucherId,
        company: {
          companyId: 'company-id',
        },
        remoteActivationStatus: status,
      } as VoucherEntity);

      jest.spyOn(vouchersService, 'tsmRedeemCoupon').mockResolvedValue({
        code: '0',
        description: 'Success',
      });

      const result = await vouchersService.couponRedemption(status, voucherId);

      expect(vouchersRepository.findOne).toHaveBeenCalledWith({
        where: { voucherId },
        relations: ['company', 'contract'],
      });
      expect(vouchersService.tsmRedeemCoupon).toHaveBeenCalledWith(
        status,
        voucherEntity,
      );

      expect(result).toEqual({
        voucherId,
        company: {
          companyId: 'company-id',
        },
        remoteActivationStatus: status,
      });
    });

    it('should throw error voucher not found', async () => {
      const status = RemoteActivationStatus.ACTIVATE;
      const voucherId = 'voucher-id';

      jest.spyOn(vouchersRepository, 'findOne').mockResolvedValue(null);

      try {
        await vouchersService.couponRedemption(status, voucherId);
        // If it doesn't throw an error, fail the test
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.NOT_FOUND_DATA.code,
        );
        expect((error as BaseExceptionService).data).toBe(`Voucher not found`);
      }
    });

    it('should throw error redemption failed', async () => {
      const status = RemoteActivationStatus.ACTIVATE;
      const voucherId = 'voucher-id';

      const voucherEntity: VoucherEntity = {
        voucherId,
        company: {
          companyId: 'company-id',
        },
        remoteActivationStatus: RemoteActivationStatus.DEACTIVATE,
        verificationStatus: VerificationStatus.VERIFIED,
      } as VoucherEntity;
      jest
        .spyOn(vouchersRepository, 'findOne')
        .mockResolvedValue(voucherEntity);

      jest.spyOn(vouchersService, 'tsmRedeemCoupon').mockResolvedValue({
        code: '2',
        description: 'Duplicate coupon',
      });

      try {
        await vouchersService.couponRedemption(status, voucherId);
        // If it doesn't throw an error, fail the test
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.REDEMPTION_FAILED.code,
        );
        expect((error as BaseExceptionService).data).toBe(
          `Redemption failed - Duplicate coupon`,
        );
      }
    });

    it('should throw error voucher already used', async () => {
      const status = RemoteActivationStatus.ACTIVATE;
      const voucherId = 'voucher-id';

      const voucherEntity: VoucherEntity = {
        voucherId,
        company: {
          companyId: 'company-id',
        },
        remoteActivationStatus: RemoteActivationStatus.DEACTIVATE,
        verificationStatus: VerificationStatus.VERIFIED,
      } as VoucherEntity;
      jest
        .spyOn(vouchersRepository, 'findOne')
        .mockResolvedValue(voucherEntity);

      jest.spyOn(vouchersService, 'tsmRedeemCoupon').mockResolvedValue({
        code: '4',
        description: 'Coupon already used',
      });

      try {
        await vouchersService.couponRedemption(status, voucherId);
        // If it doesn't throw an error, fail the test
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.VOUCHER_ALREADY_USED.code,
        );
        expect((error as BaseExceptionService).data).toBe(
          `Voucher is already used`,
        );
      }
    });
  });

  describe('tsmRedeemCoupon', () => {
    it('should call the TSM service with the correct parameters', async () => {
      // Arrange
      const status = RemoteActivationStatus.ACTIVATE;
      const voucherId = 'voucher-id';
      const voucher: VoucherEntity = {
        voucherId,
        company: {
          companyId: 'company-id',
        },
        contract: {
          customerKey: 'contract-key',
        },
        remoteActivationStatus: RemoteActivationStatus.DEACTIVATE,
        verificationStatus: VerificationStatus.VERIFIED,
      } as VoucherEntity;
      const expectedPayload = {
        otherPaymentCode: voucher.company.companyId,
        couponSerial: voucher.redemptionCode,
        cancel: 'N',
        thaiId: voucher.contract.customerKey,
        value: voucher.voucherValue,
      };

      jest.spyOn(tsmService, 'couponRedemption').mockResolvedValue({
        code: '0',
        description: 'Success',
      });

      await vouchersService.tsmRedeemCoupon(status, voucher);

      expect(tsmService.couponRedemption).toHaveBeenCalledWith(
        expect.objectContaining(expectedPayload),
      );
    });
  });
});
