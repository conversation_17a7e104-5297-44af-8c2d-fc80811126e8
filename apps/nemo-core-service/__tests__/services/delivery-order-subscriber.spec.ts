import {
  DataSource,
  EntityManager,
  EntityMetadata,
  QueryRunner,
} from 'typeorm';
import {
  JobEntity,
  DeliveryOrderEntity,
  DeliveryOrderStatus,
} from '../../src/entities';
import { Test } from '@nestjs/testing';
import { DeliveryOrderEntitySubscriber } from '../../src/subscriber';
import { FirebaseService } from '../../src/firebase/firebase.service';
import { BaseExceptionModule } from '../../src/base-exceptions/base-exceptions.module';
import { ColumnMetadata } from 'typeorm/metadata/ColumnMetadata';

describe('Delivery Order Subscriber', () => {
  let doSubscriber: DeliveryOrderEntitySubscriber;
  let entityManager: EntityManager;
  let firebaseService: FirebaseService;

  const deliveryOrderEntity = new DeliveryOrderEntity();

  const mockedEvent = {
    entity: deliveryOrderEntity,
    databaseEntity: {} as DeliveryOrderEnti<PERSON>,
    manager: {} as EntityManager,
    updatedColumns: [],
    connection: {} as DataSource,
    queryRunner: {} as QueryRunner,
    metadata: {} as EntityMetadata,
    updatedRelations: [],
  };

  const mockSave = jest.fn().mockReturnThis();

  beforeAll(async () => {
    const module = await Test.createTestingModule({
      imports: [BaseExceptionModule.register()],
      providers: [
        {
          provide: DataSource,
          useValue: {
            subscribers: {
              push: jest.fn().mockReturnThis(),
            },
          },
        },
        {
          provide: FirebaseService,
          useValue: {
            setData: jest.fn().mockReturnThis(),
            addData: jest.fn().mockReturnThis(),
          },
        },
        {
          provide: EntityManager,
          useValue: {
            getRepository: jest.fn().mockImplementation(() => {
              return { save: mockSave };
            }),
            findOne: jest.fn(() => {
              return {
                roles: [
                  {
                    branchId: 'test',
                    role: ['Sale', 'Manager'],
                  },
                ],
              };
            }),
          },
        },
        DeliveryOrderEntitySubscriber,
      ],
    }).compile();

    doSubscriber = module.get<DeliveryOrderEntitySubscriber>(
      DeliveryOrderEntitySubscriber,
    );
    entityManager = module.get<EntityManager>(EntityManager);
    firebaseService = module.get<FirebaseService>(FirebaseService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('After insert doSubscriber', () => {
    it('afterInsert', async () => {
      await doSubscriber.afterInsert({
        ...mockedEvent,
        manager: entityManager as EntityManager,
      });
      expect(firebaseService.setData).toHaveBeenCalled();
      expect(firebaseService.addData).toHaveBeenCalledTimes(2);
      expect(entityManager.getRepository).toHaveBeenCalled();
      expect(mockSave).toHaveBeenCalled();
    });
    it('listenTo', async () => {
      const result = doSubscriber.listenTo();
      expect(result).toBe(DeliveryOrderEntity);
    });
  });

  describe('After update doSubscriber', () => {
    it.each([
      [DeliveryOrderStatus.IN_TRANSIT, DeliveryOrderStatus.PARTIAL_RECEIVED],
      [
        DeliveryOrderStatus.APPOINTMENT_CONFIRMED,
        DeliveryOrderStatus.IN_TRANSIT,
      ],
      [
        DeliveryOrderStatus.APPOINTMENT_PENDING,
        DeliveryOrderStatus.APPOINTMENT_CONFIRMED,
      ],
      [DeliveryOrderStatus.IN_TRANSIT, DeliveryOrderStatus.DELIVERY_SUCCESSFUL],
    ])('afterUpdate status ( %s )', async (statusFrom, statusTo) => {
      let deliveryOrderEntity = new DeliveryOrderEntity();
      deliveryOrderEntity.status = statusTo;
      deliveryOrderEntity.jobs = [{ jobId: 'test' }] as JobEntity[];
      deliveryOrderEntity.updatedAt = new Date();
      await doSubscriber.afterUpdate({
        ...mockedEvent,
        entity: deliveryOrderEntity,
        databaseEntity: {
          status: statusFrom,
          jobs: [] as JobEntity[],
        } as DeliveryOrderEntity,
        manager: entityManager as EntityManager,
        updatedColumns: [
          { propertyAliasName: 'status' } as ColumnMetadata,
          { propertyAliasName: 'transporterName' } as ColumnMetadata,
        ],
      });
      expect(firebaseService.setData).toHaveBeenCalled();
      expect(firebaseService.addData).toHaveBeenCalledTimes(2);
      expect(entityManager.getRepository).toHaveBeenCalled();
      expect(mockSave).toHaveBeenCalled();
    });
  });
});
