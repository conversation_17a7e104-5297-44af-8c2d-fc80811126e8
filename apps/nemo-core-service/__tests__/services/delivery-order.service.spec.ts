import { getRepositoryToken } from '@nestjs/typeorm';
import { Test } from '@nestjs/testing';
import { BaseExceptionModule } from '../../src/base-exceptions/base-exceptions.module';
import {
  DeliveryOrderEntity,
  DeliveryOrderStatus,
  JobEntity,
  JobShippingStatus,
  JobStatus,
  SystemConfigEntity,
  UserEntity,
  RoleEntity,
  CompanyEntity,
  BranchEntity,
} from '../../src/entities';
import { EntityManager, Repository, SelectQueryBuilder } from 'typeorm';
import { Request } from 'express';
import { DeliveryOrdersService } from '../../src/shop/delivery-orders/delivery-orders.service';
import { mock } from 'ts-mockito';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { CacheManagerService } from '../../src/cache-manager/cache-manager.service';
import {
  CreateDeliveryOrderDto,
  UpdateDeliveryOrderDto,
  ConfirmDeliveryOrderDto,
} from '../../src/shop/delivery-orders/dto';
import { WithBranchContext, WithUserContext } from '../../src/interfaces';
import { mockJob } from '../mock-data/job';
import {
  mockDeliveryOrderEntity,
  mockInputDeliveryOrderEntity,
  validPatchBody,
  validPatchDoId,
} from '../../__tests__/mock-data/delivery-orders';
import { BaseExceptionService } from '../../src/exceptions';
import { BASE_EXCEPTIONS } from '../../src/config';

jest.mock('../../src/utils/dynamic-import', () => ({
  dynamicImport: jest.fn(() => {
    return {
      fileTypeFromBuffer: jest.fn(() => {
        return {
          mime: 'image/png',
        };
      }),
    };
  }),
}));

const mockUserKey = '<EMAIL>';

const mockRequestCreate: CreateDeliveryOrderDto = {
  branchId: 'branchId',
  jobIds: ['jobId1', 'jobId2'],
  deliveryDate: new Date(),
  senderMobileNumber: '1234567890',
  senderUserKey: '12346',
} as CreateDeliveryOrderDto;

const mockRequestUpdate: UpdateDeliveryOrderDto = {
  jobIds: [],
  senderUserKey: mockUserKey,
};

const mockRequestConfrim: ConfirmDeliveryOrderDto = {
  transporterName: 'transport-1',
  transporterMobileNumber: '0999999999',
};

const user = {
  userId: 'userId',
} as unknown as WithUserContext;
const branch = {
  branch: 'branchId',
} as unknown as WithBranchContext;

describe('DeliveryOrderService', () => {
  let deliveryOrderService: DeliveryOrdersService;

  let deliveryOrderRepo: Repository<DeliveryOrderEntity>;
  let jobRepo: Repository<JobEntity>;
  let userRepo: Repository<UserEntity>;
  let mockEntityManager: EntityManager;

  beforeEach(async () => {
    mockEntityManager = mock(EntityManager);
    const module = await Test.createTestingModule({
      imports: [BaseExceptionModule.register()],
      providers: [
        DeliveryOrdersService,
        CacheManagerService,
        {
          provide: getRepositoryToken(JobEntity),
          useValue: {
            findOne: jest.fn().mockReturnThis(),
            createQueryBuilder: jest.fn().mockReturnThis(),
            select: jest.fn().mockReturnThis(),
            where: jest.fn().mockReturnThis(),
            andWhere: jest.fn().mockReturnThis(),
            getOne: jest.fn().mockReturnThis(),
            getMany: jest.fn().mockReturnThis(),
          },
        },
        {
          provide: getRepositoryToken(DeliveryOrderEntity),
          useValue: {
            findOne: jest.fn().mockReturnThis(),
            createQueryBuilder: jest.fn().mockReturnThis(),
            select: jest.fn().mockReturnThis(),
            where: jest.fn().mockReturnThis(),
            orderBy: jest.fn().mockReturnThis(),
            getOne: jest.fn().mockReturnThis(),
            deliveryOrderId: {
              slice: jest.fn().mockReturnValue('1'),
            },
            manager: {
              getRepository: jest.fn().mockReturnThis(),
              connection: {
                createQueryRunner: jest.fn().mockReturnThis(),
                connect: jest.fn().mockReturnThis(),
                startTransaction: jest.fn().mockReturnThis(),
                release: jest.fn().mockReturnThis(),
                rollbackTransaction: jest.fn().mockReturnThis(),
                commitTransaction: jest.fn().mockReturnThis(),
                manager: {
                  getRepository: jest.fn().mockReturnThis(),
                  createQueryBuilder: jest.fn().mockReturnThis(),
                  select: jest.fn().mockReturnThis(),
                  where: jest.fn().mockReturnThis(),
                  andWhere: jest.fn().mockReturnThis(),
                  insert: jest.fn().mockReturnThis(),
                  orIgnore: jest.fn().mockReturnThis(),
                  values: jest.fn().mockReturnThis(),
                  save: jest.fn().mockReturnThis(),
                  execute: jest.fn().mockResolvedValue({ raw: [] }),
                },
              },
            },
          },
        },
        {
          provide: getRepositoryToken(UserEntity),
          useValue: {
            findOne: jest.fn().mockReturnThis(),
          },
        },
        {
          provide: getRepositoryToken(SystemConfigEntity),
          useValue: {
            findOne: jest.fn().mockReturnThis(),
          },
        },
        {
          provide: CACHE_MANAGER,
          useValue: {
            get: jest.fn(),
            set: jest.fn(),
            reset: jest.fn(),
            del: jest.fn(),
            store: {
              client: {
                multi: jest.fn().mockReturnThis(),
                incr: jest.fn().mockReturnThis(),
                expire: jest.fn().mockReturnThis(),
                exec: jest.fn().mockReturnValueOnce([[0]]),
              },
            },
          },
        },
      ],
    }).compile();

    deliveryOrderService = module.get<DeliveryOrdersService>(
      DeliveryOrdersService,
    );
    deliveryOrderRepo = module.get<Repository<DeliveryOrderEntity>>(
      getRepositoryToken(DeliveryOrderEntity),
    );
    jobRepo = module.get<Repository<JobEntity>>(getRepositoryToken(JobEntity));
    userRepo = module.get<Repository<UserEntity>>(
      getRepositoryToken(UserEntity),
    );
    deliveryOrderRepo = module.get<Repository<DeliveryOrderEntity>>(
      getRepositoryToken(DeliveryOrderEntity),
    );
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('Build Search Query', () => {
    it('should construct search query conditions based on provided parameters', () => {
      const mockRequest = {
        query: {
          search: 'searchKeyword',
          status: ['status1', 'status2'],
          branchId: 'branchId',
        },
      } as unknown as Request;

      const mockListQuery: SelectQueryBuilder<DeliveryOrderEntity> = {
        andWhere: jest.fn(),
      } as unknown as SelectQueryBuilder<DeliveryOrderEntity>;

      const result = deliveryOrderService.buildSearchQuery(
        mockRequest,
        mockListQuery,
      );

      expect(result.andWhere).toHaveBeenCalledWith(
        expect.any(String),
        expect.any(Object),
      );
    });

    it('serialize input body', () => {
      const mockDeliveryOrder = {
        deliveryOrderId: 'deliveryOrderId',
        branchId: 'branchId',
        job: {
          jobId: 'jobId',
        },
      } as unknown as DeliveryOrderEntity;

      const result = deliveryOrderService.sanitizeInputBody(mockDeliveryOrder);

      expect(result).toEqual({ branchId: 'branchId', job: { jobId: 'jobId' } });
    });
  });

  describe('Prepare Delivery Create', () => {
    it('should be return error sender role invalid', async () => {
      jest.spyOn(deliveryOrderService, 'prepareJobEntity').mockResolvedValue([
        {
          ...mockJob,
          status: JobStatus.DRAFT,
        },
      ]);

      try {
        await deliveryOrderService.prepareCreateDeliveryOrder(
          mockRequestCreate,
          user,
          branch,
        );
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.INVALID_SENDER_ROLE_IN_BRANCH.code,
        );
      }
    });

    it('should be return error job status invalid', async () => {
      jest.spyOn(jobRepo, 'findOne').mockResolvedValueOnce({
        ...mockJob,
        status: JobStatus.DRAFT,
      });

      jest.spyOn(userRepo, 'findOne').mockResolvedValueOnce({
        userKey: 'userId',
        companyId: 'WW',
        isActive: true,
        company: {
          companyId: 'WW',
          title: 'Test',
          logoPath: '',
          createdAt: new Date(),
          createdBy: 'test',
        },
        createdAt: new Date(),
        roles: [
          {
            branchId: 'branchId',
            role: ['Sale'],
          },
        ],
        userType: 'WW',
      });

      try {
        await deliveryOrderService.prepareCreateDeliveryOrder(
          mockRequestCreate,
          user,
          branch,
        );
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.INVALID_DO_STATUS_TO_CREATE.code,
        );
      }
    });

    it('should be return user not exists', async () => {
      jest.spyOn(deliveryOrderService, 'prepareJobEntity').mockResolvedValue([
        {
          ...mockJob,
        },
      ]);

      jest.spyOn(userRepo, 'findOne').mockResolvedValueOnce(null);

      try {
        await deliveryOrderService.prepareCreateDeliveryOrder(
          mockRequestCreate,
          user,
          branch,
        );
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.BODY_PAYLOAD_INVALID.code,
        );
      }
    });

    it('should be create delivery order success', async () => {
      jest.spyOn(deliveryOrderService, 'prepareJobEntity').mockResolvedValue([
        {
          ...mockJob,
        },
      ]);

      jest.spyOn(userRepo, 'findOne').mockResolvedValueOnce({
        userKey: 'userId',
        companyId: 'WW',
        isActive: true,
        company: {
          companyId: 'WW',
          title: 'Test',
          logoPath: '',
          createdAt: new Date(),
          createdBy: 'test',
        },
        createdAt: new Date(),
        roles: [
          {
            branchId: 'branchId',
            role: ['Sale'],
          },
        ],
        userRoleBranch: [
          {
            company: new CompanyEntity(),
            user: new UserEntity(),
            role: new RoleEntity(),
            branch: new BranchEntity(),
            createdAt: new Date(),
            companyId: 'companyId',
            userKey: 'userId',
            roleId: 'roleId',
            branchId: 'branchId',
          },
        ],
        userType: 'WW',
      });

      const result = await deliveryOrderService.prepareCreateDeliveryOrder(
        mockRequestCreate,
        user,
        branch,
      );

      expect(result).toBeDefined();
    });
  });

  describe('Prepare Delivery Update', () => {
    it('should be return user not exists', async () => {
      jest.spyOn(deliveryOrderService, 'prepareJobEntity').mockResolvedValue([
        {
          ...mockJob,
        },
      ]);

      jest.spyOn(userRepo, 'findOne').mockResolvedValueOnce(null);

      try {
        await deliveryOrderService.prepareUpdateDeliveryOrder(
          'test',
          user,
          mockRequestUpdate,
          branch,
        );
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.BODY_PAYLOAD_INVALID.code,
        );
      }
    });
    it('should be return error sender role invalid', async () => {
      jest.spyOn(deliveryOrderService, 'prepareJobEntity').mockResolvedValue([
        {
          ...mockJob,
        },
      ]);
      jest.spyOn(userRepo, 'findOne').mockResolvedValueOnce({
        userKey: 'userId',
        companyId: 'WW',
        isActive: true,
        company: {
          companyId: 'WW',
          title: 'Test',
          logoPath: '',
          createdAt: new Date(),
          createdBy: 'test',
        },
        createdAt: new Date(),
        roles: [
          {
            branchId: 'branchId',
            role: ['Sale'],
          },
        ],
        userRoleBranch: undefined,
        userType: 'WW',
      });

      try {
        await deliveryOrderService.prepareUpdateDeliveryOrder(
          'test',
          user,
          mockRequestUpdate,
          branch,
        );
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.INVALID_SENDER_ROLE_IN_BRANCH.code,
        );
      }
    });
    it('should be update delivery order success', async () => {
      jest.spyOn(deliveryOrderService, 'prepareJobEntity').mockResolvedValue([
        {
          ...mockJob,
        },
      ]);

      const deliveryOrderEntity = new DeliveryOrderEntity();

      deliveryOrderEntity.deliveryOrderId = 'test#1';
      deliveryOrderEntity.companyId = 'WW';
      deliveryOrderEntity.branchId = '80000430';
      deliveryOrderEntity.quantity = 1;
      deliveryOrderEntity.senderUserKey = mockUserKey;
      deliveryOrderEntity.senderUserCompanyId = 'WW';
      deliveryOrderEntity.senderMobileNumber = '1234567890';
      deliveryOrderEntity.shopUserKey = mockUserKey;

      jest
        .spyOn(deliveryOrderRepo, 'findOne')
        .mockResolvedValueOnce(deliveryOrderEntity);

      jest.spyOn(userRepo, 'findOne').mockResolvedValueOnce({
        userKey: 'userId',
        companyId: 'WW',
        isActive: true,
        company: {
          companyId: 'WW',
          title: 'Test',
          logoPath: '',
          createdAt: new Date(),
          createdBy: 'test',
        },
        createdAt: new Date(),
        roles: [
          {
            branchId: 'branchId',
            role: ['Sale'],
          },
        ],
        userRoleBranch: [
          {
            company: new CompanyEntity(),
            user: new UserEntity(),
            role: new RoleEntity(),
            branch: new BranchEntity(),
            createdAt: new Date(),
            companyId: 'companyId',
            userKey: 'userId',
            roleId: 'roleId',
            branchId: 'branchId',
          },
        ],
        userType: 'WW',
      });

      const result = await deliveryOrderService.prepareUpdateDeliveryOrder(
        'test',
        user,
        mockRequestUpdate,
        branch,
      );

      expect(result).toEqual(deliveryOrderEntity);
    });
  });

  describe('Prepare Confirm Delivery', () => {
    it('should be return error not found data ', async () => {
      const mockQueryRunner = {
        connect: jest.fn().mockResolvedValue(undefined),
        startTransaction: jest.fn().mockResolvedValue(undefined),
        commitTransaction: jest.fn().mockResolvedValue(undefined),
        rollbackTransaction: jest.fn().mockResolvedValue(undefined),
        release: jest.fn().mockResolvedValue(undefined),
      } as any;

      const deliveryOrderEntity = new DeliveryOrderEntity();
      deliveryOrderEntity.deliveryOrderId = 'test#1';
      const mockConnection = {
        createQueryRunner: jest.fn().mockReturnValue(mockQueryRunner),
      } as any;
      const mockManager = {
        connection: mockConnection,
        findOne: jest.fn().mockResolvedValue(null),
      } as any;

      Object.defineProperty(deliveryOrderRepo, 'manager', {
        value: mockManager,
      });
      Object.defineProperty(mockQueryRunner, 'manager', {
        value: mockManager,
      });

      try {
        await deliveryOrderService.prepareConfirmDeliveryOrder(
          deliveryOrderEntity.deliveryOrderId,
          mockRequestConfrim,
        );
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.NOT_FOUND_DATA.code,
        );
      }
    });

    it('should be return error invalid status to confirm ', async () => {
      const mockQueryRunner = {
        connect: jest.fn().mockResolvedValue(undefined),
        startTransaction: jest.fn().mockResolvedValue(undefined),
        commitTransaction: jest.fn().mockResolvedValue(undefined),
        rollbackTransaction: jest.fn().mockResolvedValue(undefined),
        release: jest.fn().mockResolvedValue(undefined),
      } as any;

      const deliveryOrderEntity = new DeliveryOrderEntity();
      deliveryOrderEntity.deliveryOrderId = 'test#1';
      deliveryOrderEntity.companyId = 'WW';
      deliveryOrderEntity.branchId = '80000430';
      deliveryOrderEntity.quantity = 1;
      deliveryOrderEntity.senderUserKey = mockUserKey;
      deliveryOrderEntity.senderUserCompanyId = 'WW';
      deliveryOrderEntity.senderMobileNumber = '1234567890';
      deliveryOrderEntity.shopUserKey = mockUserKey;
      deliveryOrderEntity.status = DeliveryOrderStatus.DELIVERY_SUCCESSFUL;

      const mockConnection = {
        createQueryRunner: jest.fn().mockReturnValue(mockQueryRunner),
      } as any;

      const mockManager = {
        connection: mockConnection,
        findOne: jest.fn().mockResolvedValue(deliveryOrderEntity),
      } as any;

      Object.defineProperty(deliveryOrderRepo, 'manager', {
        value: mockManager,
      });
      Object.defineProperty(mockQueryRunner, 'manager', {
        value: mockManager,
      });

      try {
        await deliveryOrderService.prepareConfirmDeliveryOrder(
          deliveryOrderEntity.deliveryOrderId,
          mockRequestConfrim,
        );
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.INVALID_DO_STATUS_TO_CONFIRM.code,
        );
      }
    });

    it('should confirm success (normal flow)', async () => {
      const mockQueryRunner = {
        connect: jest.fn().mockResolvedValue(undefined),
        startTransaction: jest.fn().mockResolvedValue(undefined),
        commitTransaction: jest.fn().mockResolvedValue(undefined),
        rollbackTransaction: jest.fn().mockResolvedValue(undefined),
        release: jest.fn().mockResolvedValue(undefined),
      } as any;

      const deliveryOrderEntity = new DeliveryOrderEntity();
      deliveryOrderEntity.deliveryOrderId = 'test#1';
      deliveryOrderEntity.companyId = 'WW';
      deliveryOrderEntity.branchId = '80000430';
      deliveryOrderEntity.quantity = 1;
      deliveryOrderEntity.senderUserKey = mockUserKey;
      deliveryOrderEntity.senderUserCompanyId = 'WW';
      deliveryOrderEntity.senderMobileNumber = '1234567890';
      deliveryOrderEntity.shopUserKey = mockUserKey;
      deliveryOrderEntity.status = DeliveryOrderStatus.APPOINTMENT_CONFIRMED;

      const job1 = { ...mockJob } as JobEntity;
      job1.deliveryOrder = deliveryOrderEntity;
      job1.jobId = 'job1';
      job1.shippingStatus = undefined;

      const job2 = { ...mockJob } as JobEntity;
      job2.deliveryOrder = deliveryOrderEntity;
      job2.jobId = 'job2';
      job2.shippingStatus = undefined;

      deliveryOrderEntity.jobs = [job1, job2];

      const mockDeliveryOrderResult = {
        ...deliveryOrderEntity,
      } as DeliveryOrderEntity;
      mockDeliveryOrderResult.status = DeliveryOrderStatus.IN_TRANSIT;

      const mockConnection = {
        createQueryRunner: jest.fn().mockReturnValue(mockQueryRunner),
      } as any;

      const mockManager = {
        connection: mockConnection,
        findOne: jest.fn().mockResolvedValue(deliveryOrderEntity),
        save: jest.fn().mockResolvedValue(mockDeliveryOrderResult),
      } as any;

      Object.defineProperty(deliveryOrderRepo, 'manager', {
        value: mockManager,
      });
      Object.defineProperty(mockQueryRunner, 'manager', {
        value: mockManager,
      });

      const result = await deliveryOrderService.prepareConfirmDeliveryOrder(
        deliveryOrderEntity.deliveryOrderId,
        mockRequestConfrim,
      );

      expect(result).toEqual(mockDeliveryOrderResult);
    });

    it('should confirm success (partial flow)', async () => {
      const mockQueryRunner = {
        connect: jest.fn().mockResolvedValue(undefined),
        startTransaction: jest.fn().mockResolvedValue(undefined),
        commitTransaction: jest.fn().mockResolvedValue(undefined),
        rollbackTransaction: jest.fn().mockResolvedValue(undefined),
        release: jest.fn().mockResolvedValue(undefined),
      } as any;

      const deliveryOrderEntity = new DeliveryOrderEntity();
      deliveryOrderEntity.deliveryOrderId = 'test#1';
      deliveryOrderEntity.companyId = 'WW';
      deliveryOrderEntity.branchId = '80000430';
      deliveryOrderEntity.quantity = 1;
      deliveryOrderEntity.senderUserKey = mockUserKey;
      deliveryOrderEntity.senderUserCompanyId = 'WW';
      deliveryOrderEntity.senderMobileNumber = '1234567890';
      deliveryOrderEntity.shopUserKey = mockUserKey;
      deliveryOrderEntity.status = DeliveryOrderStatus.APPOINTMENT_CONFIRMED;

      const job1 = { ...mockJob } as JobEntity;
      job1.deliveryOrder = deliveryOrderEntity;
      job1.jobId = 'job1';
      job1.shippingStatus = undefined;

      const job2 = { ...mockJob } as JobEntity;
      job2.deliveryOrder = deliveryOrderEntity;
      job2.jobId = 'job2';
      job2.shippingStatus = JobShippingStatus.RECEIVED;

      deliveryOrderEntity.jobs = [job1, job2];

      const mockDeliveryOrderResult = {
        ...deliveryOrderEntity,
      } as DeliveryOrderEntity;
      mockDeliveryOrderResult.status = DeliveryOrderStatus.PARTIAL_RECEIVED;

      const mockConnection = {
        createQueryRunner: jest.fn().mockReturnValue(mockQueryRunner),
      } as any;

      const mockManager = {
        connection: mockConnection,
        findOne: jest.fn().mockResolvedValue(deliveryOrderEntity),
        save: jest.fn().mockResolvedValue(mockDeliveryOrderResult),
      } as any;

      Object.defineProperty(deliveryOrderRepo, 'manager', {
        value: mockManager,
      });
      Object.defineProperty(mockQueryRunner, 'manager', {
        value: mockManager,
      });

      const result = await deliveryOrderService.prepareConfirmDeliveryOrder(
        deliveryOrderEntity.deliveryOrderId,
        mockRequestConfrim,
      );

      expect(result).toEqual(mockDeliveryOrderResult);
    });
  });

  describe('validateInputData', () => {
    it('valid case', async () => {
      const deliveryOrderEntity = {
        ...mockInputDeliveryOrderEntity,
      } as DeliveryOrderEntity;

      jest
        .spyOn(deliveryOrderRepo, 'findOne')
        .mockResolvedValueOnce(deliveryOrderEntity);
      expect(
        deliveryOrderService.validateInputData(validPatchBody, validPatchDoId),
      ).resolves.not.toThrow();
    });
    it('invalid doId', async () => {
      jest
        .spyOn(deliveryOrderRepo, 'findOne')
        .mockImplementationOnce(async () => {
          return null;
        });
      try {
        await deliveryOrderService.validateInputData(
          validPatchBody,
          'invalidDoId',
        );
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.NOT_FOUND_DATA.code,
        );
      }
    });
    it.each([
      DeliveryOrderStatus.DELIVERY_SUCCESSFUL,
      DeliveryOrderStatus.IN_TRANSIT,
    ])('invalid do status', async (status) => {
      jest
        .spyOn(deliveryOrderRepo, 'findOne')
        .mockImplementationOnce(async () => {
          return {
            ...mockDeliveryOrderEntity,
            status,
          };
        });
      try {
        await deliveryOrderService.validateInputData(
          validPatchBody,
          validPatchDoId,
        );
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.INVALID_DO_STATUS_TO_UPDATE.code,
        );
      }
    });
    it.each([
      { lastUpdate: '23/12/2040' },
      { appointmentDate: '2024-02-15' },
      { lastUpdate: '23/12/2040', appointmentDate: '2024-02-15' },
      { appointmentDate: '0001-01-01T00:00:00.000Z' },
      { awbNumber: '12345678' },
      { awbNumber: '1234567890' },
    ])('invalid body', async (invalidData) => {
      const deliveryOrderEntity = {
        ...mockInputDeliveryOrderEntity,
      } as DeliveryOrderEntity;

      jest
        .spyOn(deliveryOrderRepo, 'findOne')
        .mockResolvedValueOnce(deliveryOrderEntity);

      const body = { ...validPatchBody, ...invalidData };
      try {
        await deliveryOrderService.validateInputData(body, validPatchDoId);
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.INVALID_INPUT_FOR_UPDATE_DO.code,
        );
      }
    });
  });
  describe('prepareBeforeUpdate', () => {
    it('should return correctly', () => {
      const deliveryOrderEntity = {
        ...mockInputDeliveryOrderEntity,
      } as DeliveryOrderEntity;

      jest
        .spyOn(deliveryOrderRepo, 'findOne')
        .mockResolvedValueOnce(deliveryOrderEntity);
      const result = deliveryOrderService.prepareBeforeUpdate(
        'userKey',
        validPatchBody,
        validPatchDoId,
      );
      expect(result).resolves.toBeInstanceOf(DeliveryOrderEntity);
    });
  });
});
