import { getRepositoryToken } from '@nestjs/typeorm';
import { Test } from '@nestjs/testing';
import {
  BranchEntity,
  UserEntity,
  UserRoleBranchEntity,
} from '../../src/entities';
import { BaseExceptionModule } from '../../src/base-exceptions/base-exceptions.module';
import { UsersService } from '../../src/shop/users/users.service';
import { WithUserContext } from '../../src/interfaces';
import { Repository } from 'typeorm';
import { mockUserShopEntity } from '../mock-data/user';
import { GetMeResponse } from 'contracts';
import { BASE_EXCEPTIONS } from '../../src/config';
import { BaseExceptionService } from '../../src/exceptions';

describe('UsersService', () => {
  let usersService: UsersService;

  let userRepository: Repository<UserEntity>;
  let userRoleBranchRepository: Repository<UserRoleBranchEntity>;

  const mockUserContenxt: WithUserContext = {
    userKey: '<EMAIL>',
    company: 'WW',
    idToken: 'test1234',
  };

  beforeEach(async () => {
    const module = await Test.createTestingModule({
      imports: [BaseExceptionModule.register()],
      providers: [
        UsersService,
        {
          provide: getRepositoryToken(UserEntity),
          useValue: {
            findOne: jest.fn().mockReturnThis(),
            createQueryBuilder: jest.fn().mockReturnThis(),
            select: jest.fn().mockReturnThis(),
            where: jest.fn().mockReturnThis(),
            andWhere: jest.fn().mockReturnThis(),
            getMany: jest.fn().mockReturnThis(),
          },
        },
        {
          provide: getRepositoryToken(BranchEntity),
          useValue: {
            find: jest.fn().mockReturnThis(),
          },
        },
        {
          provide: getRepositoryToken(UserRoleBranchEntity),
          useValue: {
            find: jest.fn().mockReturnThis(),
            manager: {
              find: jest.fn().mockReturnThis(),
            },
            createQueryBuilder: jest.fn().mockReturnThis(),
            select: jest.fn().mockReturnThis(),
            addSelect: jest.fn().mockReturnThis(),
            leftJoin: jest.fn().mockReturnThis(),
            groupBy: jest.fn().mockReturnThis(),
            where: jest.fn().mockReturnThis(),
            getRawMany: jest.fn().mockResolvedValue([
              {
                branchId: 'branchId#1',
                branchName: 'title#1',
                roles: ['test'],
              },
            ]),
          },
        },
      ],
    }).compile();

    usersService = module.get<UsersService>(UsersService);
    userRepository = module.get<Repository<UserEntity>>(
      getRepositoryToken(UserEntity),
    );
    userRoleBranchRepository = module.get<Repository<UserRoleBranchEntity>>(
      getRepositoryToken(UserRoleBranchEntity),
    );
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('Get me', () => {
    const expectedTemplate: GetMeResponse = {
      userKey: 'test',
      name: 'test',
      branchRoles: [
        { branchId: 'branchId#1', branchName: 'title#1', roles: ['test'] },
      ],
      roleConfig: [],
      permissionGroupConfig: [],
      userType: 'WW',
    };
    it('should response profile', async () => {
      const expected = expectedTemplate;

      jest
        .spyOn(userRepository, 'findOne')
        .mockResolvedValueOnce(mockUserShopEntity);

      jest
        .spyOn(userRoleBranchRepository.manager, 'find')
        .mockResolvedValueOnce([]);
      jest
        .spyOn(userRoleBranchRepository.manager, 'find')
        .mockResolvedValueOnce([]);

      const result = await usersService.getMe(mockUserContenxt);

      expect(result).toEqual(expected);
    });

    it('should empty name profile', async () => {
      const expected = {
        ...expectedTemplate,
        name: '',
      };

      jest
        .spyOn(userRepository, 'findOne')
        .mockResolvedValueOnce({ ...mockUserShopEntity, name: '' });

      jest
        .spyOn(userRoleBranchRepository.manager, 'find')
        .mockResolvedValueOnce([]);
      jest
        .spyOn(userRoleBranchRepository.manager, 'find')
        .mockResolvedValueOnce([]);

      const result = await usersService.getMe(mockUserContenxt);

      expect(result).toEqual(expected);
    });

    it('should throw error not found', async () => {
      jest.spyOn(userRepository, 'findOne').mockReturnValueOnce(null as any);

      let error: any;

      try {
        await usersService.getMe(mockUserContenxt);
      } catch (err) {
        error = err;
      }

      expect(error).toBeInstanceOf(BaseExceptionService);

      expect(error.message).toEqual(BASE_EXCEPTIONS.NOT_FOUND_DATA.message);
    });
  });

  describe('Get users in branch', () => {
    it('should return users', async () => {
      jest.spyOn(userRepository, 'createQueryBuilder').mockReturnValueOnce({
        select: jest.fn().mockReturnThis(),
        leftJoin: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        getMany: jest.fn().mockResolvedValueOnce([mockUserShopEntity]),
      } as any);

      // Get user by branch id
      const result = await usersService.getUsersInBranch('test', 'test');

      expect(result).toEqual([mockUserShopEntity]);
    });
  });
});
