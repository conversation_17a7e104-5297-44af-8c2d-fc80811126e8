import { Test } from '@nestjs/testing';
import { BaseExceptionModule } from '../../src/base-exceptions/base-exceptions.module';
import { SmtpService, TemplateEmail } from '../../src/smtp/smtp.service';
import { emailTemplate } from '../../src/utils/email-template';
import { mockJob } from '../mock-data/job';
import {
  CompanyEntity,
  ContractEntity,
  EmailActivitiesType,
  EmailActivitiesEntity,
  EmailActivitiesInfo,
} from '../../src/entities';
import { Repository } from 'typeorm';
import { getRepositoryToken } from '@nestjs/typeorm';
import nodemailer from 'nodemailer';

jest.mock('nodemailer', () => ({
  createTransport: jest.fn(),
}));

describe('SmtpService', () => {
  let smtpService: SmtpService;
  let emailActivitiesRepo: Repository<EmailActivitiesEntity>;
  const transportMock = {
    sendMail: jest.fn(),
    close: jest.fn(),
  };

  beforeEach(async () => {
    (nodemailer.createTransport as jest.Mock).mockReturnValue(transportMock);
    const module = await Test.createTestingModule({
      imports: [BaseExceptionModule.register()],
      providers: [
        SmtpService,
        {
          provide: getRepositoryToken(EmailActivitiesEntity),
          useValue: {
            findOne: jest.fn().mockReturnThis(),
            save: jest.fn().mockReturnThis(),
          },
        },
      ],
    }).compile();

    smtpService = module.get<SmtpService>(SmtpService);

    emailActivitiesRepo = module.get<Repository<EmailActivitiesEntity>>(
      getRepositoryToken(EmailActivitiesEntity),
    );
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('Send Mail', () => {
    const mockEmail = '<EMAIL> ';
    const mockCompany: CompanyEntity = {
      companyId: '123',
      title: 'company title',
      logoUrl: 'company logo url',
      createdBy: 'company created by',
      userKeyClaimFnName: 'company user key claim fn name',
      empUploadMapperFnName: 'company emp upload mapper fn name',
      createdAt: new Date(),
      updatedAt: new Date(),
      logoPath: 'company logo path',
      companyEmail: 'company email',
      contractEmail: 'contract email',
    };
    const mockContract = {
      customerInfo: {
        thaiName: {
          firstName: 'ชื่อจริง',
          lastName: 'นามสกุล',
        },
        engName: {
          firstName: 'firstName',
          lastName: 'lastName',
        },
        birthDate: '11/11/2556',
        email: 'mockEmail',
      },
      contractLink: 'mocklink',
    } as ContractEntity;
    const mockEmailActivity = {
      refId: 'refId',
      companyId: 'companyId',
      type: EmailActivitiesType.CONTRACT,
      detail: {
        contractLink: 'contractLink',
        transactionLink: 'transactionLink',
        sendEmailInfo: {
          from: '<EMAIL>',
          to: '<EMAIL>',
        },
      },
      errorMsg: '',
    } as EmailActivitiesInfo;

    it('should sendMail successfully', async () => {
      transportMock.sendMail.mockImplementation((mailOptions, callback) => {
        callback(null);
      });
      const mockEmailTemplate: TemplateEmail = {
        mailOption: {
          from: '<EMAIL>',
          to: [mockEmail],
          subject: 'mockSubject',
          html: emailTemplate(mockJob, mockCompany, mockContract),
        },
        emailActivity: mockEmailActivity,
      };
      const result = await smtpService.sendMail(mockEmailTemplate);
      expect(result).toBe(true);
    }, 10000);

    it('should fail sendMail', async () => {
      transportMock.sendMail.mockImplementation((mailOptions, callback) => {
        callback(new Error('Failed to send email'));
      });
      const mockEmailTemplate: TemplateEmail = { mailOption: {} };
      const result = await smtpService.sendMail(mockEmailTemplate);
      expect(result).toBe(false);
    });
  });

  describe('insertJobActivities', () => {
    it('inserts a new email activity into the repository', async () => {
      const mockEmailActivity = {
        refId: 'refId',
        companyId: 'companyId',
        type: EmailActivitiesType.CONTRACT,
        detail: {
          contractLink: 'contractLink',
          transactionLink: 'transactionLink',
          sendEmailInfo: {
            from: '<EMAIL>',
            to: '<EMAIL>',
          },
        },
        errorMsg: '',
      } as EmailActivitiesInfo;
      // Call the function with mock data
      await smtpService.insertEmailActivities(mockEmailActivity);
      // Check if the save method was called with the correct arguments
      expect(emailActivitiesRepo.save).toHaveBeenCalledWith(
        expect.objectContaining(mockEmailActivity),
      );
    });
  });
});
