import { Test } from '@nestjs/testing';
import { BaseExceptionModule } from '../../src/base-exceptions/base-exceptions.module';
import { OcrService } from '../../src/ocr/ocr.service';
import { HttpClient } from '../../src/http-client/http-client.service';
import { SystemConfigService } from '../../src/system-config/system-config.service';

jest.mock('google-auth-library', () => ({
  JWT: jest.fn(() => ({
    fetchIdToken: jest.fn(() => 'test'),
  })),
}));

describe('OcrService', () => {
  let ocrService: OcrService;

  beforeEach(async () => {
    const module = await Test.createTestingModule({
      imports: [BaseExceptionModule.register()],
      providers: [
        OcrService,
        {
          provide: HttpClient,
          useValue: {
            init: jest.fn(() => {}),
            post: jest.fn(() => ({
              name: 'test-1',
            })),
          },
        },
        {
          provide: SystemConfigService,
          useValue: {
            getSystemConfig: jest.fn(() => ({})),
          },
        },
      ],
    }).compile();

    ocrService = module.get<OcrService>(OcrService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('get Ocr result', () => {
    it('should return value', async () => {
      const result = await ocrService.getOcrResult('imageString', 'WW');
      expect(result).toEqual({ name: 'test-1' });
    });
  });
});
