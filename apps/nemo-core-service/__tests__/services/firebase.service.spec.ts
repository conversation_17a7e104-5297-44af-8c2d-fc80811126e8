import { Test } from '@nestjs/testing';
import { FirebaseService } from '../../src/firebase/firebase.service';
import { DecodedIdToken } from 'firebase-admin/auth';
import { AES128MessageService } from '../../src/encrypt-decrypt-message/encrypt-decrypt.service';
import { SystemConfigService } from '../../src/system-config/system-config.service';
import { CacheManagerService } from '../../src/cache-manager/cache-manager.service';
import { getRepositoryToken } from '@nestjs/typeorm';
import { getQueueToken } from '@nestjs/bull';
import {
  SystemConfigEntity,
  UserEntity,
  ConfigActivitiesEntity,
  JobEntity,
} from '../../src/entities';
import { BaseExceptionModule } from '../../src/base-exceptions/base-exceptions.module';
import { CACHE_MANAGER } from '@nestjs/cache-manager';

jest.mock('firebase-admin/firestore', () => ({
  getFirestore: jest.fn(() => ({
    settings: jest.fn(),
    doc: jest.fn(() => ({
      set: jest.fn(() => Promise.resolve()),
    })),
  })),
}));

describe('FirebaseService', () => {
  let firebaseService: FirebaseService;

  const mockDecodedToken: DecodedIdToken = {
    uid: 'YGv10t1kRVO4fXeeOQWqBFj9pws1',
    name: 'Testing',
    iss: 'https://securetoken.google.com/axns-nemo-non-prod',
    aud: 'axns-nemo-non-prod',
    auth_time: **********,
    user_id: 'YGv10t1kRVO4fXeeOQWqBFj9pws1',
    sub: 'YGv10t1kRVO4fXeeOQWqBFj9pws1',
    iat: **********,
    exp: **********,
    email: '<EMAIL>',
    email_verified: false,
    firebase: {
      identities: {
        'microsoft.com': ['c23ceb01-**************-bb0024ca2e87'],
        email: ['<EMAIL>'],
      },
      sign_in_provider: 'microsoft.com',
    },
  };

  beforeAll(async () => {
    const module = await Test.createTestingModule({
      imports: [BaseExceptionModule.register()],
      providers: [
        FirebaseService,
        {
          provide: AES128MessageService,
          useFactory: () => {
            // Get aes key
            const aesKey = process.env.AES128_KEY;

            // Get aes salt
            const aesSalt = process.env.AES128_SALT;

            // Prevent key or salt invalid
            if (!aesKey || !aesSalt) {
              throw new Error(
                'AES_KEY and AES_SALT must be defined in .env file',
              );
            }

            // Initial aes service
            return new AES128MessageService(aesKey, aesSalt);
          },
        },
        SystemConfigService,
        CacheManagerService,
        {
          provide: getRepositoryToken(SystemConfigEntity),
          useValue: {
            findOne: jest.fn().mockReturnThis(),
          },
        },
        {
          provide: getRepositoryToken(UserEntity),
          useValue: {
            findOne: jest.fn().mockReturnThis(),
          },
        },
        {
          provide: getRepositoryToken(ConfigActivitiesEntity),
          useValue: {
            findOne: jest.fn().mockReturnThis(),
          },
        },
        {
          provide: getRepositoryToken(JobEntity),
          useValue: {
            findOne: jest.fn().mockReturnThis(),
          },
        },
        {
          provide: getQueueToken('re-calculate-product-queue'),
          useValue: {
            add: jest.fn(),
            process: jest.fn(),
          },
        },
        {
          provide: CACHE_MANAGER,
          useValue: CACHE_MANAGER,
        },
      ],
    }).compile();

    firebaseService = module.get<FirebaseService>(FirebaseService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('Get firebase instance app', () => {
    it('should return firebase instance app', () => {
      jest.spyOn(firebaseService, 'getFirebase').mockReturnValue({} as any);
      const result = firebaseService.getFirebase();
      expect(result).toBeDefined();
    });
  });

  describe('Verify id token', () => {
    it('should return decoded object', async () => {
      jest
        .spyOn(firebaseService, 'verifyIdToken')
        .mockResolvedValueOnce(mockDecodedToken);

      const result = await firebaseService.verifyIdToken('token');

      expect(result).toBe(mockDecodedToken);
    });

    it('should return error', async () => {
      try {
        await firebaseService.verifyIdToken('token');
      } catch (err) {
        expect(err).toBeDefined();
      }
    });
  });

  describe('Set data firestore', () => {
    it('should set data firestore valid', async () => {
      await firebaseService.setData('', {});
    });
  });
});
