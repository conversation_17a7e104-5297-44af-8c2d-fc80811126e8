import { Test } from '@nestjs/testing';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { getQueueToken } from '@nestjs/bull';
import { BaseExceptionModule } from '../../src/base-exceptions/base-exceptions.module';
import {
  CommonService,
  SearchAddressFilter,
  isAddressLovGroupKey,
} from '../../src/common/common.service';
import { getRepositoryToken } from '@nestjs/typeorm';
import {
  BranchEntity,
  CompanyEntity,
  ModelMasterEntity,
  SystemConfigEntity,
  ModelMasterColorEntity,
  ModelMasterFunctionEntity,
  ChecklistType,
  QuestionType,
  ModelChecklistEntity,
  EstimationActivitiesEntity,
  ILegalDocumentType,
  LegalDocumentEntity,
  UserEntity,
  ConfigActivitiesEntity,
  JobEntity,
} from '../../src/entities';
import { MasterAddressEntity } from '../../src/entities/address-master.entity';
import { AES128MessageService } from '../../src/encrypt-decrypt-message/encrypt-decrypt.service';
import { Repository } from 'typeorm';
import {
  BaseExceptionService,
  CommonExceptionService,
} from '../../src/exceptions';
import { BASE_EXCEPTIONS, COMMON_EXCEPTIONS } from '../../src/config';
import { CacheManagerService } from '../../src/cache-manager/cache-manager.service';
import { SystemConfigService } from '../../src/system-config/system-config.service';
import {
  generateModulesFunction,
  generateQuestionOptionFunction,
  generateQuestionSelectionFunction,
  generateQuestionTextFunction,
  mockColor,
  mockColorId,
  mockCompanyId,
  mockCreateOrderBody,
  mockEstimationActivity,
  mockLegalDocId,
  mockModelKey,
  mockModelMaster,
  mockPdpaVersion,
  mockRequestQuestionsSkipByType,
  mockRequestQuestionsnotAnswerByType,
  mockRequestQuestionsnotSelectByType,
  mockStore,
  mockStoreId,
} from '../../__tests__/mock-data/common-order';
import { ILanguage, createRandomStringCharNum } from '../../src/utils/general';
import { convertOrderResponse } from '../../src/utils/common-order';
import { repeat } from 'lodash';

jest.mock('../../src/utils/general', () => {
  const original = jest.requireActual('../../src/utils/general');
  return {
    ...original,
    createRandomStringCharNum: jest.fn(),
  };
});

jest.mock('../../src/utils/common-order', () => {
  const original = jest.requireActual('../../src/utils/common-order');
  return {
    ...original,
    convertOrderResponse: jest.fn(),
  };
});

const manager = {
  getRepository: jest.fn().mockReturnThis(),
  connection: {
    createQueryRunner: jest.fn().mockReturnThis(),
    connect: jest.fn().mockReturnThis(),
    startTransaction: jest.fn().mockReturnThis(),
    release: jest.fn().mockReturnThis(),
    rollbackTransaction: jest.fn().mockReturnThis(),
    commitTransaction: jest.fn().mockReturnThis(),
    manager: {
      getRepository: jest.fn().mockReturnThis(),
      createQueryBuilder: jest.fn().mockReturnThis(),
      insert: jest.fn().mockReturnThis(),
      orIgnore: jest.fn().mockReturnThis(),
      values: jest.fn().mockReturnThis(),
      save: jest.fn().mockReturnThis(),
      execute: jest.fn().mockResolvedValue({ raw: [] }),
    },
  },
};

describe('Common Service', () => {
  let commonsService: CommonService;
  let companyRepository: Repository<CompanyEntity>;
  let systemConfigRepository: Repository<SystemConfigEntity>;
  let masterAddressRepository: Repository<MasterAddressEntity>;
  let modelMasterRepository: Repository<ModelMasterEntity>;
  let modelMasterFunctionRepository: Repository<ModelMasterFunctionEntity>;
  let estimationActivitiesRepository: Repository<EstimationActivitiesEntity>;
  let legalDocumentRepository: Repository<LegalDocumentEntity>;
  let branchRepository: Repository<BranchEntity>;
  let cacheManagerService: CacheManagerService;

  beforeEach(async () => {
    const module = await Test.createTestingModule({
      imports: [BaseExceptionModule.register()],
      providers: [
        CommonService,
        {
          provide: getRepositoryToken(CompanyEntity),
          useValue: {
            findOne: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(SystemConfigEntity),
          useValue: {
            find: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(MasterAddressEntity),
          useValue: {
            createQueryBuilder: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(ModelMasterEntity),
          useValue: {
            find: jest.fn(),
            findOne: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(ModelMasterFunctionEntity),
          useValue: {
            find: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(ModelMasterColorEntity),
          useValue: {
            find: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(EstimationActivitiesEntity),
          useValue: {
            manager,
            find: jest.fn(),
            findOne: jest.fn(),
            update: jest.fn(),
            createQueryBuilder: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(LegalDocumentEntity),
          useValue: {
            manager: { createQueryBuilder: jest.fn() },
            findOne: jest.fn().mockReturnThis(),
          },
        },
        {
          provide: getRepositoryToken(BranchEntity),
          useValue: {
            findOne: jest.fn(),
            find: jest.fn(),
            createQueryBuilder: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(UserEntity),
          useValue: {
            findOne: jest.fn().mockReturnThis(),
          },
        },
        {
          provide: AES128MessageService,
          useValue: {
            encrypt: jest.fn(() => 'encrypted'),
            decrypt: jest.fn(() => JSON.stringify('decryptedData')),
          },
        },
        {
          provide: getRepositoryToken(JobEntity),
          useValue: {
            findOne: jest.fn().mockReturnThis(),
          },
        },
        {
          provide: CACHE_MANAGER,
          useValue: {
            get: jest.fn(),
            set: jest.fn(),
            reset: jest.fn(),
            del: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(ConfigActivitiesEntity),
          useValue: {
            save: jest.fn().mockReturnThis(),
            findOne: jest.fn().mockReturnThis(),
          },
        },
        {
          provide: getQueueToken('re-calculate-product-queue'),
          useValue: {
            add: jest.fn(),
            process: jest.fn(),
          },
        },
        {
          provide: CacheManagerService,
          useValue: {
            incrData: jest.fn(() => Promise.resolve(1)),
            getData: jest.fn(),
            setData: jest.fn(),
          },
        },
        SystemConfigService,
      ],
    }).compile();

    commonsService = module.get<CommonService>(CommonService);
    companyRepository = module.get<Repository<CompanyEntity>>(
      getRepositoryToken(CompanyEntity),
    );
    systemConfigRepository = module.get<Repository<SystemConfigEntity>>(
      getRepositoryToken(SystemConfigEntity),
    );
    masterAddressRepository = module.get<Repository<MasterAddressEntity>>(
      getRepositoryToken(MasterAddressEntity),
    );
    modelMasterRepository = module.get<Repository<ModelMasterEntity>>(
      getRepositoryToken(ModelMasterEntity),
    );
    branchRepository = module.get<Repository<BranchEntity>>(
      getRepositoryToken(BranchEntity),
    );
    modelMasterFunctionRepository = module.get<
      Repository<ModelMasterFunctionEntity>
    >(getRepositoryToken(ModelMasterFunctionEntity));
    estimationActivitiesRepository = module.get<
      Repository<EstimationActivitiesEntity>
    >(getRepositoryToken(EstimationActivitiesEntity));
    legalDocumentRepository = module.get<Repository<LegalDocumentEntity>>(
      getRepositoryToken(LegalDocumentEntity),
    );
    branchRepository = module.get<Repository<BranchEntity>>(
      getRepositoryToken(BranchEntity),
    );
    cacheManagerService = module.get<CacheManagerService>(CacheManagerService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('Is Address Lov Group Key', () => {
    it('should return true for valid AddressLovGroupKeys', () => {
      const validKeys = ['province', 'zipcode', 'district', 'subdistrict'];

      validKeys.forEach((key) => {
        expect(isAddressLovGroupKey(key)).toBe(true);
      });
    });

    it('should return false for invalid AddressLovGroupKeys', () => {
      const invalidKeys = ['city', 'street', 'village', 'country'];

      invalidKeys.forEach((key) => {
        expect(isAddressLovGroupKey(key)).toBe(false);
      });
    });
  });

  describe('getCompanyInfo', () => {
    const mockCompany: CompanyEntity = {
      companyId: '123',
      title: 'company title',
      logoUrl: 'company logo url',
      createdBy: 'company created by',
      userKeyClaimFnName: 'company user key claim fn name',
      empUploadMapperFnName: 'company emp upload mapper fn name',
      createdAt: new Date(),
      updatedAt: new Date(),
      logoPath: 'company logo path',
    };
    const mockFirebaseConfig = {
      companyId: '123',
      configKey: 'firebase_frontend',
      data: ['base64encodeddata'],
      createdAt: new Date(),
    };
    const mockMsConfig = {
      companyId: '123',
      configKey: 'ms',
      data: ['base64encodeddata'],
      createdAt: new Date(),
    };

    it('should return company info with decrypted configs', async () => {
      jest.spyOn(companyRepository, 'findOne').mockResolvedValue(mockCompany);
      jest
        .spyOn(systemConfigRepository, 'find')
        .mockResolvedValue([mockFirebaseConfig, mockMsConfig]);

      // Call the function
      const result = await commonsService.getCompanyInfo('123');

      // Assertions
      expect(result).toEqual({
        ...mockCompany,
        firebaseConfig: 'decryptedData',
        msConfig: 'decryptedData',
        gaConfig: {},
      });
    });

    it('should throw error company not found', async () => {
      jest.spyOn(companyRepository, 'findOne').mockResolvedValue(null);

      try {
        await commonsService.getCompanyInfo('123');
        // If it doesn't throw an error, fail the test
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.NOT_FOUND_DATA.code,
        );
      }
    });

    it('should throw error config not found', async () => {
      jest.spyOn(companyRepository, 'findOne').mockResolvedValue(mockCompany);
      jest.spyOn(systemConfigRepository, 'find').mockResolvedValue(null as any);

      try {
        await commonsService.getCompanyInfo('123');
        // If it doesn't throw an error, fail the test
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.NOT_FOUND_DATA.code,
        );
      }
    });

    it('should throw error config empty', async () => {
      const firebaseConfig = {
        companyId: '123',
        configKey: 'firebase_frontend',
        data: [],
        createdAt: new Date(),
      };
      const msConfig = {
        companyId: '123',
        configKey: 'ms',
        data: [],
        createdAt: new Date(),
      };
      jest.spyOn(companyRepository, 'findOne').mockResolvedValue(mockCompany);
      jest
        .spyOn(systemConfigRepository, 'find')
        .mockResolvedValue([firebaseConfig, msConfig]);

      try {
        await commonsService.getCompanyInfo('123');
        // If it doesn't throw an error, fail the test
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.NOT_FOUND_DATA.code,
        );
      }
    });
  });

  describe('listAddressItem', () => {
    it('should return a list of key-label pairs based on addressFilter', async () => {
      // Mock data for testing
      const mockAddressFilter: Partial<SearchAddressFilter> = {
        provinceCode: '123',
        zipcode: '456',
        districtCode: '789',
        subdistrictCode: '987',
      };

      const mockRawData = [
        { key: 'key1', label: 'label1' },
        { key: 'key2', label: 'label2' },
      ];

      // Mock repository method
      jest
        .spyOn(masterAddressRepository, 'createQueryBuilder')
        .mockReturnValue({
          select: jest.fn().mockReturnThis(),
          distinct: jest.fn().mockReturnThis(),
          addSelect: jest.fn().mockReturnThis(),
          where: jest.fn().mockReturnThis(),
          orderBy: jest.fn().mockReturnThis(),
          cache: jest.fn().mockReturnThis(),
          getRawMany: jest.fn().mockResolvedValue(mockRawData),
        } as any);

      // Call the function
      const result = await commonsService.listAddressItem(
        'province',
        mockAddressFilter,
      );

      // Assertions
      expect(result).toEqual(mockRawData);

      // Verify that the repository method was called with the correct parameters
      expect(masterAddressRepository.createQueryBuilder).toHaveBeenCalled();
      expect(
        masterAddressRepository.createQueryBuilder().select,
      ).toHaveBeenCalledWith('provinceCode', 'key');
      expect(
        masterAddressRepository.createQueryBuilder().addSelect,
      ).toHaveBeenCalledWith("province::jsonb -> 'th'", 'label');
      expect(
        masterAddressRepository.createQueryBuilder().where,
      ).toHaveBeenCalledWith({
        provinceCode: '123',
        zipcode: '456',
        districtCode: '789',
        subdistrictCode: '987',
      });
    });
  });

  describe('Encrypt Decrypt', () => {
    it('should return encrypt data with string data', () => {
      const result = commonsService.encryptData('test');
      expect(result).not.toEqual(undefined);
    });

    it('should return encrypt data with object data', () => {
      const result = commonsService.encryptData({ test: 'test' });
      expect(result).not.toEqual(undefined);
    });

    it('should return encrypt data with other data', () => {
      const result = commonsService.encryptData(12345);
      expect(result).not.toEqual(undefined);
    });

    it('should return decrypt data', () => {
      const result = commonsService.decryptData('test');
      expect(result).not.toEqual(undefined);
    });
  });

  describe('getDeviceDetail', () => {
    const mockModelMaster = (rom: string) => {
      return {
        systemCode: 'systemCode#1',
        modelImageUrl: 'image#1',
        modelIdentifiers: {
          brand: 'brand#1',
          model: 'model#1',
          rom,
        },
        systemCodeList: ['systemCode#1'],
      };
    };
    it('should return correct device detail(1 sku)', async () => {
      jest
        .spyOn(modelMasterRepository, 'find')
        .mockResolvedValueOnce([
          mockModelMaster('128GB'),
        ] as ModelMasterEntity[]);
      const result = await commonsService.getDeviceDetail(
        'systemCode#1',
        'CompanyId',
        'en',
      );

      // Assertions
      expect(result.skus.length).toEqual(1);
      expect(result.id).toEqual(mockModelMaster('').systemCode);
      expect(result.deviceImageUrl).toEqual(mockModelMaster('').modelImageUrl);
      expect(result.brand).toEqual(mockModelMaster('').modelIdentifiers?.brand);
      expect(result.name).toEqual(mockModelMaster('').modelIdentifiers?.model);
    });
    it('should return correct device detail(many sku)', async () => {
      jest
        .spyOn(modelMasterRepository, 'find')
        .mockResolvedValueOnce([
          mockModelMaster('128GB'),
          mockModelMaster('256GB'),
          mockModelMaster('512GB'),
          mockModelMaster('1TB'),
        ] as ModelMasterEntity[]);
      const result = await commonsService.getDeviceDetail(
        'systemCode#1',
        'CompanyId',
        'en',
      );

      // Assertions
      expect(result.skus.length).toEqual(4);
      expect(result.id).toEqual(mockModelMaster('').systemCode);
      expect(result.deviceImageUrl).toEqual(mockModelMaster('').modelImageUrl);
      expect(result.brand).toEqual(mockModelMaster('').modelIdentifiers?.brand);
      expect(result.name).toEqual(mockModelMaster('').modelIdentifiers?.model);
    });

    it('should return correct device detail(many sku) 2', async () => {
      jest
        .spyOn(modelMasterRepository, 'find')
        .mockResolvedValueOnce([
          mockModelMaster('128GB'),
          mockModelMaster('256GB'),
          mockModelMaster('512GB'),
          mockModelMaster('1TB'),
          mockModelMaster('0GB'),
        ] as ModelMasterEntity[]);
      const result = await commonsService.getDeviceDetail(
        'systemCode#1',
        'CompanyId',
        'en',
      );

      // Assertions
      expect(result.skus.length).toEqual(5);
      expect(result.id).toEqual(mockModelMaster('').systemCode);
      expect(result.deviceImageUrl).toEqual(mockModelMaster('').modelImageUrl);
      expect(result.brand).toEqual(mockModelMaster('').modelIdentifiers?.brand);
      expect(result.name).toEqual(mockModelMaster('').modelIdentifiers?.model);
    });

    it('should return correct device detail(many sku) 3', async () => {
      jest
        .spyOn(modelMasterRepository, 'find')
        .mockResolvedValueOnce([
          mockModelMaster('0GB'),
          mockModelMaster('128GB'),
          mockModelMaster('256GB'),
          mockModelMaster('512GB'),
          mockModelMaster('1TB'),
        ] as ModelMasterEntity[]);
      const result = await commonsService.getDeviceDetail(
        'systemCode#1',
        'CompanyId',
        'en',
      );

      // Assertions
      expect(result.skus.length).toEqual(5);
      expect(result.id).toEqual(mockModelMaster('').systemCode);
      expect(result.deviceImageUrl).toEqual(mockModelMaster('').modelImageUrl);
      expect(result.brand).toEqual(mockModelMaster('').modelIdentifiers?.brand);
      expect(result.name).toEqual(mockModelMaster('').modelIdentifiers?.model);
    });
    it('should return default sku', async () => {
      jest
        .spyOn(modelMasterRepository, 'find')
        .mockResolvedValueOnce([] as ModelMasterEntity[]);
      const result = await commonsService.getDeviceDetail(
        'notFoundCode',
        'CompanyId',
        'en',
      );

      // Assertions
      expect(result.skus.length).toEqual(1);
      expect(result.skus[0].id).toEqual('default');
    });
    // it('have to throw error when dont have this system code(default lang)', async () => {
    //   jest
    //     .spyOn(modelMasterRepository, 'find')
    //     .mockResolvedValueOnce([] as ModelMasterEntity[]);

    //   try {
    //     const result = await commonsService.getDeviceDetail(
    //       'systemCode#1',
    //       'CompanyId',
    //       'en',
    //     );
    //     fail('Expected method to throw an error');
    //   } catch (error) {
    //     expect(error).toBeInstanceOf(CommonExceptionService);
    //     expect((error as CommonExceptionService).message).toBe(
    //       'Model not found',
    //     );
    //     expect((error as CommonExceptionService).error?.title).toBe(
    //       'Unable to purchase your device.',
    //     );
    //   }

    //   // Assertions
    // });
    // it('have to throw error when dont have this system code(thai lang)', async () => {
    //   jest
    //     .spyOn(modelMasterRepository, 'find')
    //     .mockResolvedValueOnce([] as ModelMasterEntity[]);

    //   try {
    //     const result = await commonsService.getDeviceDetail(
    //       'systemCode#1',
    //       'CompanyId',
    //       'th',
    //     );
    //     fail('Expected method to throw an error');
    //   } catch (error) {
    //     expect(error).toBeInstanceOf(CommonExceptionService);
    //     expect((error as CommonExceptionService).message).toBe(
    //       'Model not found',
    //     );
    //     expect((error as CommonExceptionService).error?.title).toBe(
    //       'ไม่สามารถรับฝากอุปกรณ์ของคุณได้',
    //     );
    //   }
    // });
  });

  describe('getAppraisal', () => {
    const mockModelMaster = (rom: string) => {
      return {
        systemCode: 'systemCode#1',
        modelImageUrl: 'image#1',
        modelIdentifiers: {
          brand: 'brand#1',
          model: 'model#1',
          rom,
        },
        modelMasterColors: [
          {
            id: '0001',
            companyId: 'WW',
            nameTh: 'ขาว',
            nameEn: 'WHITE',
          },
          {
            id: '0002',
            companyId: 'WW',
            nameTh: 'ดำ',
            nameEn: 'BLACK',
          },
        ] as ModelMasterColorEntity[],
      };
    };

    const mockModelMasterFunction: Partial<ModelMasterFunctionEntity>[] = [
      {
        companyId: 'WW',
        modelKey: 'apple|iphone 14|128gb',
        functionKeyCond: 'product_information.screen_display=abnormal',
        penalties: '-5000.00',
        checkListId: '2473C14UGV628ZWAF',
        modelChecklist: {
          id: '2473C14UGV628ZWAF',
          companyId: 'WW',
          functionKey: 'screen_display',
          functionSection: 'product_information',
          isRequired: true,
          checklistType: ChecklistType.QUESTION,
          checklistNameTh: 'การแสดงภาพหน้าจอ',
          checklistDescriptionTh:
            'ตรวจสอบสภาพหน้าจอสามารถแสดงภาพได้ปกติชัดเจนหรือไม่',
          checklistNameEn: 'การแสดงภาพหน้าจอ',
          checklistDescriptionEn: null,
          iconImageUrl: null,
          moduleCode: null,
          questionType: QuestionType.SELECTION,
          questionChoices: [
            {
              id: 'normal',
              answerTh: 'ปกติ',
              iconImageUrl:
                'https://ww-remobile.axonstech.com/asset/company/WW/icons/screen_display-normal.png',
            },
            {
              id: 'abnormal',
              answerTh: 'ไม่ปกติ',
              iconImageUrl:
                'https://ww-remobile.axonstech.com/asset/company/WW/icons/screen_display-abnormal.png',
            },
          ],
        } as ModelChecklistEntity,
      },
      {
        companyId: 'WW',
        modelKey: 'apple|iphone 14|128gb',
        functionKeyCond: 'remobie_check_list.wifi=non_functional',
        penalties: '-1000.00',
        checkListId: '2473C14UGW53CHN98',
        modelChecklist: {
          id: '2473C14UGW53CHN98',
          companyId: 'WW',
          functionKey: 'wifi',
          functionSection: 'remobie_check_list',
          isRequired: true,
          checklistType: ChecklistType.MODULE,
          checklistNameTh: 'WIFI',
          checklistDescriptionTh: null,
          checklistNameEn: 'WIFI',
          checklistDescriptionEn: null,
          iconImageUrl: null,
          moduleCode: 'WIFI',
          questionType: null,
          questionChoices: null,
        } as ModelChecklistEntity,
      },
      {
        companyId: 'WW',
        modelKey: 'apple|iphone 14|128gb',
        functionKeyCond: 'remobie_check_list.xxx=non_functional',
        penalties: '-1000.00',
        checkListId: '2473C14UGW53CHN98',
        modelChecklist: {
          id: '2473C14UGW53CHN98',
          companyId: 'WW',
          functionKey: 'wifi',
          functionSection: 'remobie_check_list',
          isRequired: true,
          checklistType: ChecklistType.QUESTION,
          questionType: QuestionType.TEXT,
          questionChoices: null,
        } as ModelChecklistEntity,
      },
      {
        companyId: 'WW',
        modelKey: 'apple|iphone 14|128gb',
        functionKeyCond: 'remobie_check_list.xxx=non_functional',
        penalties: '-1000.00',
        checkListId: '2473C14UGW53CHN98',
        modelChecklist: {
          id: '2473C14UGW53CHN98',
          companyId: 'WW',
          functionKey: 'wifi',
          functionSection: 'remobie_check_list',
          isRequired: true,
          checklistType: ChecklistType.QUESTION,
          questionType: QuestionType.TEXT,
          questionChoices: null,
        } as ModelChecklistEntity,
      },
    ];

    it('should return correct appraisal (TH)', async () => {
      jest
        .spyOn(modelMasterFunctionRepository, 'find')
        .mockResolvedValueOnce(
          mockModelMasterFunction as ModelMasterFunctionEntity[],
        );

      jest
        .spyOn(modelMasterRepository, 'findOne')
        .mockResolvedValueOnce(mockModelMaster('128GB') as ModelMasterEntity);
      const result = await commonsService.getAppraisal(
        'SkuId#1',
        'CompanyId',
        'th',
      );

      // Assertions
      expect(result.colors.length).toEqual(2);
      expect(result.modules.length).toEqual(1);
      expect(result.questions.length).toEqual(2);
      expect(result.colors[0].name).toEqual(
        mockModelMaster('').modelMasterColors[0].nameTh,
      );
      expect(result.modules[0].name).toEqual(
        mockModelMasterFunction[1].modelChecklist?.checklistNameTh,
      );
      expect(result.questions[0].question).toEqual(
        mockModelMasterFunction[0].modelChecklist?.checklistNameTh,
      );
      expect(result.questions[1].type).toEqual(
        mockModelMasterFunction[2].modelChecklist?.questionType,
      );
      expect(result.questions[1].choices).toEqual(null);
    });

    it('should return correct appraisal (EN)', async () => {
      jest
        .spyOn(modelMasterFunctionRepository, 'find')
        .mockResolvedValueOnce(
          mockModelMasterFunction as ModelMasterFunctionEntity[],
        );

      jest
        .spyOn(modelMasterRepository, 'findOne')
        .mockResolvedValueOnce(mockModelMaster('128GB') as ModelMasterEntity);
      const result = await commonsService.getAppraisal(
        'SkuId#1',
        'CompanyId',
        'en',
      );

      // Assertions
      expect(result.colors.length).toEqual(2);
      expect(result.modules.length).toEqual(1);
      expect(result.questions.length).toEqual(2);
      expect(result.colors[0].name).toEqual(
        mockModelMaster('').modelMasterColors[0].nameEn,
      );
      expect(result.modules[0].name).toEqual(
        mockModelMasterFunction[1].modelChecklist?.checklistNameEn,
      );
      expect(result.questions[0].question).toEqual(
        mockModelMasterFunction[0].modelChecklist?.checklistNameEn,
      );
    });

    it('should return correct appraisal (Default)', async () => {
      jest
        .spyOn(modelMasterFunctionRepository, 'find')
        .mockResolvedValueOnce(
          mockModelMasterFunction as ModelMasterFunctionEntity[],
        );

      jest
        .spyOn(modelMasterRepository, 'findOne')
        .mockResolvedValueOnce(mockModelMaster('128GB') as ModelMasterEntity);
      const result = await commonsService.getAppraisal(
        'SkuId#1',
        'CompanyId',
        'en',
      );

      // Assertions
      expect(result.colors.length).toEqual(2);
      expect(result.modules.length).toEqual(1);
      expect(result.questions.length).toEqual(2);
      expect(result.colors[0].name).toEqual(
        mockModelMaster('').modelMasterColors[0].nameEn,
      );
      expect(result.modules[0].name).toEqual(
        mockModelMasterFunction[1].modelChecklist?.checklistNameEn,
      );
      expect(result.questions[0].question).toEqual(
        mockModelMasterFunction[0].modelChecklist?.checklistNameEn,
      );
    });

    it('have to throw error when dont have this SkuID (default lang)', async () => {
      jest
        .spyOn(modelMasterFunctionRepository, 'find')
        .mockResolvedValueOnce([] as ModelMasterFunctionEntity[]);

      try {
        await commonsService.getAppraisal('SkuId#1', 'CompanyId', 'en');
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(CommonExceptionService);
        expect((error as CommonExceptionService).message).toBe('SKU not found');
        expect((error as CommonExceptionService).error?.title).toBe(
          'Something went wrong.',
        );
        expect((error as CommonExceptionService).error?.description).toBe(
          'No information found for the specified device.',
        );
      }

      // Assertions
    });

    it('have to throw error when dont have this system code(thai lang)', async () => {
      jest
        .spyOn(modelMasterFunctionRepository, 'find')
        .mockResolvedValueOnce([] as ModelMasterFunctionEntity[]);

      try {
        await commonsService.getAppraisal('SkuId#1', 'CompanyId', 'th');
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(CommonExceptionService);
        expect((error as CommonExceptionService).message).toBe('SKU not found');
        expect((error as CommonExceptionService).error?.title).toBe(
          'เกิดข้อผิดพลาด',
        );
        expect((error as CommonExceptionService).error?.description).toBe(
          'ไม่พบข้อมูล อุปกรณ์ดังกล่าว',
        );
      }
    });
  });

  describe('updateOrder', () => {
    const mockEstimationActivity = (id: string) => {
      return {
        id,
        companyId: 'CompanyId',
        modelKey: 'modelKey#1',
        branchId: 'branchId#1',
        deviceId: 'deviceId#1',
        firstName: 'firstName#1',
        lastName: 'lastName#1',
        phoneNumber: '0866327163',
        email: '<EMAIL>',
        isAcceptPDPA: false,
        PDPAVersion: 1.0,
      };
    };
    const mockBranch = (branchId: string) => {
      return {
        branchId,
        title: 'titleTh_' + branchId,
        titleEn: 'titleEn_' + branchId,
        addressTh: 'addressTh',
        addressEn: 'addressEn',
        subDistrictTh: 'subDistrictTh',
        subDistrictEn: 'subDistrictEn',
        districtTh: 'districtTh',
        districtEn: 'districtEn',
        provinceTh: 'provinceTh',
        provinceEn: 'provinceEn',
        zipCode: '00000',
        latitude: null,
        longitude: null,
        imageUrl: 'url',
      };
    };
    const updateData = {
      customerInfo: {
        firstname: 'zmm3',
        lastname: 'aut2',
        email: '<EMAIL>',
        phoneNumber: '0874213956',
      },
      storeId: '80000411',
      pdpaVersion: 2.0,
      isAcceptPDPA: true,
    };
    const updateDataNotAcceptPDPA = {
      customerInfo: {
        firstname: 'zmm3',
        lastname: 'aut2',
        email: '<EMAIL>',
        phoneNumber: '0874213956',
      },
      storeId: '80000411',
      pdpaVersion: 2.0,
      isAcceptPDPA: false,
    };
    const updateDataNull = {
      customerInfo: {
        firstname: null,
        lastname: null,
        email: null,
        phoneNumber: null,
      },
      storeId: null,
      pdpaVersion: 2.0,
      isAcceptPDPA: true,
    };
    const updateDataBothPDPANull = {
      customerInfo: {
        firstname: 'zmm3',
        lastname: 'aut2',
        email: '<EMAIL>',
        phoneNumber: '0874213956',
      },
      storeId: '80000411',
      pdpaVersion: null,
      isAcceptPDPA: null,
    };
    const updateDataPDPAVersionNull = {
      customerInfo: {
        firstname: 'zmm3',
        lastname: 'aut2',
        email: '<EMAIL>',
        phoneNumber: '0874213956',
      },
      storeId: '80000411',
      pdpaVersion: null,
      isAcceptPDPA: true,
    };
    const updateDataIsAcceptPDPANull = {
      customerInfo: {
        firstname: 'zmm3',
        lastname: 'aut2',
        email: '<EMAIL>',
        phoneNumber: '0874213956',
      },
      storeId: '80000411',
      pdpaVersion: 2.0,
      isAcceptPDPA: null,
    };

    it('update order success', async () => {
      jest
        .spyOn(estimationActivitiesRepository, 'findOne')
        .mockResolvedValueOnce(
          mockEstimationActivity('test-1') as EstimationActivitiesEntity,
        );
      jest
        .spyOn(branchRepository, 'findOne')
        .mockResolvedValueOnce(mockBranch('80000411') as BranchEntity);
      await commonsService.updateEstimationActivity(
        'test-1',
        'CompanyId',
        'en',
        updateData,
      );

      expect(estimationActivitiesRepository.update).toHaveBeenCalledWith(
        { companyId: 'CompanyId', id: 'test-1' },
        expect.objectContaining({
          firstName: updateData.customerInfo.firstname,
          lastName: updateData.customerInfo.lastname,
          phoneNumber: updateData.customerInfo.phoneNumber,
          email: updateData.customerInfo.email,
          isAcceptPDPA: updateData.isAcceptPDPA,
          PDPAVersion: updateData.pdpaVersion,
          branchId: updateData.storeId,
        }),
      );
    });

    it('orderId not found', async () => {
      jest
        .spyOn(estimationActivitiesRepository, 'findOne')
        .mockResolvedValueOnce(null);
      jest
        .spyOn(branchRepository, 'findOne')
        .mockResolvedValueOnce(mockBranch('80000411') as BranchEntity);
      try {
        await commonsService.updateEstimationActivity(
          'test-1',
          'CompanyId',
          'en',
          updateData,
        );
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(CommonExceptionService);
        expect(error).toMatchObject({
          code: '2000',
          message: 'Order Id not found',
        });
      }
    });
    it('isAcceptPDPA is false', async () => {
      jest
        .spyOn(estimationActivitiesRepository, 'findOne')
        .mockResolvedValueOnce(
          mockEstimationActivity('test-1') as EstimationActivitiesEntity,
        );
      jest
        .spyOn(branchRepository, 'findOne')
        .mockResolvedValueOnce(mockBranch('80000411') as BranchEntity);
      try {
        await commonsService.updateEstimationActivity(
          'test-1',
          'CompanyId',
          'en',
          updateDataNotAcceptPDPA,
        );
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(CommonExceptionService);
        expect(error).toMatchObject({
          code: '4002',
          message:
            'Request body error: PDPA accept need when request with PDPA version',
        });
      }
    });
    it('update order set data null (except pdpaVersion and isAcceptPDPA)', async () => {
      jest
        .spyOn(estimationActivitiesRepository, 'findOne')
        .mockResolvedValueOnce(
          mockEstimationActivity('test-1') as EstimationActivitiesEntity,
        );
      jest
        .spyOn(branchRepository, 'findOne')
        .mockResolvedValueOnce(mockBranch('80000411') as BranchEntity);
      await commonsService.updateEstimationActivity(
        'test-1',
        'CompanyId',
        'en',
        updateDataNull,
      );

      expect(estimationActivitiesRepository.update).toHaveBeenCalledWith(
        { companyId: 'CompanyId', id: 'test-1' },
        expect.objectContaining({
          firstName: null,
          lastName: null,
          phoneNumber: null,
          email: null,
          isAcceptPDPA: updateDataNull.isAcceptPDPA,
          PDPAVersion: updateDataNull.pdpaVersion,
          branchId: null,
        }),
      );
    });
    it('update order set pdpaVersion and isAcceptPDPA null', async () => {
      jest
        .spyOn(estimationActivitiesRepository, 'findOne')
        .mockResolvedValueOnce(
          mockEstimationActivity('test-1') as EstimationActivitiesEntity,
        );
      jest
        .spyOn(branchRepository, 'findOne')
        .mockResolvedValueOnce(mockBranch('80000411') as BranchEntity);
      await commonsService.updateEstimationActivity(
        'test-1',
        'CompanyId',
        'en',
        updateDataBothPDPANull,
      );

      expect(estimationActivitiesRepository.update).toHaveBeenCalledWith(
        { companyId: 'CompanyId', id: 'test-1' },
        expect.objectContaining({
          firstName: updateData.customerInfo.firstname,
          lastName: updateData.customerInfo.lastname,
          phoneNumber: updateData.customerInfo.phoneNumber,
          email: updateData.customerInfo.email,
          isAcceptPDPA: null,
          PDPAVersion: null,
          branchId: updateData.storeId,
        }),
      );
    });
    it('update order set pdpaVersion null', async () => {
      jest
        .spyOn(estimationActivitiesRepository, 'findOne')
        .mockResolvedValueOnce(
          mockEstimationActivity('test-1') as EstimationActivitiesEntity,
        );
      jest
        .spyOn(branchRepository, 'findOne')
        .mockResolvedValueOnce(mockBranch('80000411') as BranchEntity);
      try {
        await commonsService.updateEstimationActivity(
          'test-1',
          'CompanyId',
          'en',
          updateDataPDPAVersionNull,
        );
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(CommonExceptionService);
        expect(error).toMatchObject({
          code: '4002',
          message: 'Request body error: PDPA version need when PDPA accept',
        });
      }
    });
    it('update order set isAcceptPDPA null', async () => {
      jest
        .spyOn(estimationActivitiesRepository, 'findOne')
        .mockResolvedValueOnce(
          mockEstimationActivity('test-1') as EstimationActivitiesEntity,
        );
      jest
        .spyOn(branchRepository, 'findOne')
        .mockResolvedValueOnce(mockBranch('80000411') as BranchEntity);
      try {
        await commonsService.updateEstimationActivity(
          'test-1',
          'CompanyId',
          'en',
          updateDataIsAcceptPDPANull,
        );
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(CommonExceptionService);
        expect(error).toMatchObject({
          code: '4002',
          message:
            'Request body error: PDPA accept need when request with PDPA version',
        });
      }
    });
  });

  describe('getLegalDocument', () => {
    const mockDocument = (type: ILegalDocumentType) => {
      return {
        id: 'DocumentId#1',
        company_id: 'company',
        version: '1',
        type,
        content_url_th: `${type}.th.v1`,
        content_url_en: `${type}.en.v1`,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };
    };
    beforeEach(() => {
      jest
        .spyOn(legalDocumentRepository.manager, 'createQueryBuilder')
        .mockReturnValue({
          select: jest.fn().mockReturnThis(),
          from: jest.fn().mockReturnThis(),
          where: jest.fn().mockReturnThis(),
          getRawMany: jest
            .fn()
            .mockResolvedValue([
              mockDocument(ILegalDocumentType.PDPA),
              mockDocument(ILegalDocumentType.TERM),
            ]),
        } as any);
    });
    it('will return thai url', async () => {
      const result = await commonsService.getLegalDocument('company', 'th');

      expect(result.length).toBe(2);
      result.forEach((doc) => {
        expect(doc.version).toEqual(expect.any(Number));
        expect(doc.content_url).toEqual(expect.stringContaining('th'));
      });
    });
    it.each(['', 'en'])('will return eng url', async (lang) => {
      const result = await commonsService.getLegalDocument('company', lang);

      expect(result.length).toBe(2);
      result.forEach((doc) => {
        expect(doc.version).toEqual(expect.any(Number));
        expect(doc.content_url).toEqual(expect.stringContaining('en'));
      });
    });
  });

  describe('validateOrderBody', () => {
    describe('all case', () => {
      const mockPhoneNumPass = '0123456789';
      it.each([
        [
          true,
          [true, true, true],
          [true, true],
          [true, mockPdpaVersion, true],
          mockPhoneNumPass,
          '',
          '',
        ],
        [
          true,
          [false, false, false],
          [false, false],
          [false, undefined, false],
          mockPhoneNumPass,
          '',
          '',
        ],
        // fail phoneNumber
        [
          false,
          [true, true, true],
          [true, true],
          [true, mockPdpaVersion, true],
          1234567890,
          'BODY_PAYLOAD_INVALID',
          'Request body with invalid format: phoneNumber',
        ],
        // pdpa
        [
          false,
          [true, true, true],
          [true, true],
          [true, undefined, false],
          mockPhoneNumPass,
          'BODY_PAYLOAD_INVALID',
          'Request body error: PDPA version need when PDPA accept',
        ],
        [
          false,
          [true, true, true],
          [true, true],
          [true, mockPdpaVersion, false],
          mockPhoneNumPass,
          'BODY_PAYLOAD_INVALID',
          'Request body pdpaVersion is not existed in system',
        ],
        [
          false,
          [true, true, true],
          [true, true],
          [false, mockPdpaVersion, false],
          mockPhoneNumPass,
          'BODY_PAYLOAD_INVALID',
          'Request body error: PDPA accept need when request with PDPA version',
        ],
        // store
        [
          false,
          [true, true, true],
          [true, false],
          [true, mockPdpaVersion, true],
          mockPhoneNumPass,
          'BODY_PAYLOAD_INVALID',
          'Request body storeId is not existed in system',
        ],
        // color
        [
          false,
          [true, false, false],
          [true, true],
          [true, mockPdpaVersion, true],
          false,
          'BODY_PAYLOAD_INVALID',
          'Request body colorId is not existed in model',
        ],
        [
          false,
          [true, true, false],
          [true, true],
          [true, mockPdpaVersion, true],
          false,
          'BODY_PAYLOAD_INVALID',
          'Request body colorId is not existed in model',
        ],
      ])(
        `should pass %s if color(hasId, hasModel, found) %s store(hasId, found) %s pdpa(accept, version, found) %s phoneNumber %s [ %s %s ]`,
        async (
          isPass,
          [hasColorId, hasModelMastorColor, foundColor],
          [hasStoreId, foundStore],
          [isAcceptPDPA, pdpaVersion, foundPdpa],
          phoneNumber,
          errorCode,
          errorMsg,
        ) => {
          jest
            .spyOn(branchRepository, 'findOne')
            .mockResolvedValue(foundStore ? mockStore : null);
          jest
            .spyOn(legalDocumentRepository, 'findOne')
            .mockResolvedValue(
              foundPdpa
                ? ({ id: mockLegalDocId } as LegalDocumentEntity)
                : null,
            );
          let bodyColorId: string | undefined = undefined;
          if (hasColorId) {
            bodyColorId = foundColor ? mockColorId : 'not-in-db-color';
          }

          const body = {
            customerInfo: { phoneNumber },
            isAcceptPDPA,
            pdpaVersion,
            colorId: bodyColorId,
            storeId: !hasStoreId ? undefined : mockStoreId,
          };

          const modelMasterColors = [{ ...mockColor }];

          const expectResult = {
            legalDocumentId:
              pdpaVersion || isAcceptPDPA ? mockLegalDocId : undefined,
            color: hasColorId ? mockColor : undefined,
            store: hasStoreId ? mockStore : undefined,
          };

          const payload = {
            language: 'en' as ILanguage,
            body,
            companyId: mockCompanyId,
            modelMasterColors: hasModelMastorColor
              ? modelMasterColors
              : undefined,
          };

          if (isPass) {
            const result = await commonsService.validateOrderBody(payload);

            expect(result).toEqual(expectResult);
          } else {
            try {
              await commonsService.validateOrderBody(payload);
              fail('Expected method to throw an error');
            } catch (error) {
              expect(error).toBeInstanceOf(CommonExceptionService);
              expect((error as CommonExceptionService).code).toBe(
                COMMON_EXCEPTIONS[errorCode].code,
              );
              expect((error as CommonExceptionService).message).toEqual(
                errorMsg,
              );
            }
          }
        },
      );
    });
  });

  describe('generateEstimateActivitiesIdWithoutRunningNumber', () => {
    it('Should return true id', async () => {
      jest
        .mocked(createRandomStringCharNum)
        .mockImplementation((length: number) => repeat('A', length));
      const currentDate = 'mock-current-date';
      const expectResult = `${currentDate}-AAAAA`;
      const result =
        await commonsService.generateEstimateActivitiesIdWithoutRunningNumber(
          currentDate,
        );
      expect(result).toBe(expectResult);
    });
  });

  describe('generateEstimateActivitiesId', () => {
    it('Should return true id', async () => {
      const currentDate = 'mock-current-date';
      jest.spyOn(cacheManagerService, 'incrData').mockResolvedValue(3);
      const expectResult = `${currentDate}-0000003`;
      const result =
        await commonsService.generateEstimateActivitiesId(currentDate);
      expect(result).toBe(expectResult);
    });
  });

  describe('fallBackGenerateEAId', () => {
    const currentDate = 'mock-current-date';
    it.each([
      [null, `${currentDate}-0000001`, 1],
      [`${currentDate}-0000007`, `${currentDate}-0000008`, 8],
    ])(
      'Should pass if prev id %s resultId %s set DB count %s',
      async (prev, current, countToDB) => {
        const mockCreateQuery = prev ? { id: prev } : undefined;
        jest
          .spyOn(estimationActivitiesRepository, 'createQueryBuilder')
          .mockReturnValue({
            estimationActivities: jest.fn().mockReturnThis(),
            select: jest.fn().mockReturnThis(),
            where: jest.fn().mockReturnThis(),
            orderBy: jest.fn().mockReturnThis(),
            getOne: jest.fn().mockResolvedValue(mockCreateQuery),
          } as any);
        const result = await commonsService.fallBackGenerateEAId(currentDate);
        expect(result).toEqual({ eaId: current, eaCount: countToDB });
      },
    );
  });

  describe('createOrder', () => {
    beforeEach(() => {
      const mockvalidateOrderBodyValue = {
        legalDocumentId: mockLegalDocId,
        color: mockColor,
        store: mockStore,
      };
      jest.mocked(convertOrderResponse).mockImplementation(() => true as any);
      jest
        .spyOn(commonsService, 'validateOrderBody')
        .mockResolvedValue(mockvalidateOrderBodyValue);
      jest
        .spyOn(commonsService, 'fallBackGenerateEAId')
        .mockResolvedValue(new Date().toISOString() as any);
      jest
        .spyOn(
          commonsService,
          'generateEstimateActivitiesIdWithoutRunningNumber',
        )
        .mockResolvedValue(new Date().toISOString() as any);
    });

    describe('main and payload case', () => {
      it.each([
        [true, [true, true], [], [], [], '', ''],
        // option not select
        [true, [true, true], [], [true], [], '', ''],
        // text not answer
        [
          true,
          [true, true],
          [],
          [false, false, true],
          [],
          'BODY_PAYLOAD_INVALID',
          'Request body error: not skip selection type question must have 1 answer',
        ],
        // error sku not found
        [false, [], [], [], [], 'NOT_FOUND_DATA', 'SKU not existed in system'],
        [
          false,
          [true, false],
          [],
          [],
          [],
          'NOT_FOUND_DATA',
          'SKU not existed in system',
        ],
        // no select answer in choices
        [
          false,
          [true, true],
          [true],
          [],
          [],
          'BODY_PAYLOAD_INVALID',
          'Request body error: answer not in choices',
        ],
        [
          false,
          [true, true],
          [false, true],
          [],
          [],
          'BODY_PAYLOAD_INVALID',
          'Request body error: answer not in choices',
        ],
        // selection not select
        [
          false,
          [true, true],
          [],
          [false, true],
          [],
          'BODY_PAYLOAD_INVALID',
          'Request body error: not skip selection type question must have 1 answer',
        ],
        // require but skip
        [
          false,
          [true, true],
          [],
          [],
          [true],
          'BODY_PAYLOAD_INVALID',
          'Request body error: checklist with required answer cannot skip',
        ],
        [
          false,
          [true, true],
          [],
          [],
          [false, true],
          'BODY_PAYLOAD_INVALID',
          'Request body error: checklist with required answer cannot skip',
        ],
        [
          false,
          [true, true],
          [],
          [],
          [false, false, true],
          'BODY_PAYLOAD_INVALID',
          'Request body error: checklist with required answer cannot skip',
        ],
        [
          false,
          [true, true],
          [],
          [],
          [false, false, false, true],
          'BODY_PAYLOAD_INVALID',
          'Request body error: checklist with required answer cannot skip',
        ],
      ])(
        `should pass %s if found(model, fn) %s noSelectedChoice(option, select) %s notSelect(option, select, text not answer) %s  errorSkip(module, option, select, text) %s [%s %s]`,
        async (
          isPass,
          [foundModel, foundModelFunction],
          [noSelectedChoiceOption, noSelectedChoiceSelect],
          [notSelectOption, notSelectSelect, notAnswerText],
          [
            errorSkipModule,
            errorSkipOption,
            errorSkipSelection,
            errorSkipFreetext,
          ],
          errorCode,
          errorMsg,
        ) => {
          const mockModelMasterFunctions = [
            ...generateModulesFunction('1'),
            ...generateModulesFunction('2'),
            ...generateModulesFunction('3', !errorSkipModule),
            ...generateQuestionOptionFunction(
              '1',
              noSelectedChoiceOption ? 2 : 1,
            ),
            ...generateQuestionSelectionFunction(
              '1',
              noSelectedChoiceSelect ? 2 : 1,
            ),
            ...generateQuestionTextFunction('1'),
          ];

          const mockModelFound = foundModelFunction
            ? {
                ...mockModelMaster,
                modelMasterFunction: mockModelMasterFunctions,
              }
            : { ...mockModelMaster };

          jest
            .spyOn(modelMasterRepository, 'findOne')
            .mockResolvedValue(foundModel ? mockModelFound : null);

          const body = { ...mockCreateOrderBody };

          if (notSelectOption) {
            body.questions = mockRequestQuestionsnotSelectByType(1); // 0:selection 1:option 2:text
          }
          if (notSelectSelect) {
            body.questions = mockRequestQuestionsnotSelectByType(0); // 0:selection 1:option 2:text
          }
          if (notAnswerText) {
            body.questions = mockRequestQuestionsnotAnswerByType(2); // 0:selection 1:option 2:text
          }
          if (errorSkipOption) {
            body.questions = mockRequestQuestionsSkipByType(2); // 0:selection 1:option 2:text
          }
          if (errorSkipSelection) {
            body.questions = mockRequestQuestionsSkipByType(1); // 0:selection 1:option 2:text
          }
          if (errorSkipFreetext) {
            body.questions = mockRequestQuestionsSkipByType(2); // 0:selection 1:option 2:text
          }

          const payload = {
            skuId: mockModelKey,
            headerData: {
              companyId: mockCompanyId,
              language: 'en' as ILanguage,
            },
            body,
          };

          if (isPass) {
            const result = await commonsService.createOrder(payload);

            expect(result).toEqual(true);
          } else {
            try {
              await commonsService.createOrder(payload);
              fail('Expected method to throw an error');
            } catch (error) {
              expect(error).toBeInstanceOf(CommonExceptionService);
              expect((error as CommonExceptionService).code).toBe(
                COMMON_EXCEPTIONS[errorCode].code,
              );

              expect((error as CommonExceptionService).message).toEqual(
                errorMsg,
              );
            }
          }
        },
      );
    });

    describe('db relate case and require field', () => {
      it.each([
        [true, [], [], '', ''],
        // checklist not found
        [
          false,
          [true],
          [],
          'BODY_PAYLOAD_INVALID',
          'Some request body moduleId is not existed in system',
        ],
        [
          false,
          [false, true],
          [],
          'BODY_PAYLOAD_INVALID',
          'Some request body questionsId not found in system',
        ],
        // missing require checklist
        [
          false,
          [],
          [true],
          'BODY_PAYLOAD_INVALID',
          'Required checklist is missing',
        ],
        [
          false,
          [],
          [false, true],
          'BODY_PAYLOAD_INVALID',
          'Required checklist is missing',
        ],
      ])(
        `should pass %s if noChecklist(module, question) %s requireChecklistMissing(module, question) %s`,
        async (
          isPass,
          [noChecklistModule, noChecklistQuestion],
          [requireModuleMissing, requireQuestionMissing],
          errorCode,
          errorMsg,
        ) => {
          let mockModelMasterFunctions = [
            ...generateModulesFunction('2'),
            ...generateModulesFunction('3', true),
            ...generateQuestionSelectionFunction('1'),
            ...generateQuestionTextFunction('1'),
          ];
          if (!noChecklistModule) {
            mockModelMasterFunctions = [
              ...mockModelMasterFunctions,
              ...generateModulesFunction('1'),
            ];
          }
          if (!noChecklistQuestion) {
            mockModelMasterFunctions = [
              ...mockModelMasterFunctions,
              ...generateQuestionOptionFunction('1'),
            ];
          }
          if (requireModuleMissing) {
            mockModelMasterFunctions = [
              ...mockModelMasterFunctions,
              ...generateModulesFunction('99'),
            ];
          }
          if (requireQuestionMissing) {
            mockModelMasterFunctions = [
              ...mockModelMasterFunctions,
              ...generateQuestionOptionFunction('99'),
            ];
          }

          jest.spyOn(modelMasterRepository, 'findOne').mockResolvedValue({
            ...mockModelMaster,
            modelMasterFunction: mockModelMasterFunctions,
          });

          const body = { ...mockCreateOrderBody };

          const payload = {
            skuId: mockModelKey,
            headerData: {
              companyId: mockCompanyId,
              language: 'en' as ILanguage,
            },
            body,
          };

          if (isPass) {
            const result = await commonsService.createOrder(payload);

            expect(result).toEqual(true);
          } else {
            try {
              await commonsService.createOrder(payload);
              fail('Expected method to throw an error');
            } catch (error) {
              expect(error).toBeInstanceOf(CommonExceptionService);
              expect((error as CommonExceptionService).code).toBe(
                COMMON_EXCEPTIONS[errorCode].code,
              );

              expect((error as CommonExceptionService).message).toEqual(
                errorMsg,
              );
            }
          }
        },
      );
    });
  });

  describe('getStoreListData', () => {
    const mockBranch = (
      branchId: string,
      latitude: string | null,
      longitude: string | null,
    ) => {
      return {
        branch_branch_id: branchId,
        branch_title_en: 'titleTh_' + branchId,
        branch_title: 'titleEn_' + branchId,
        branch_address_th: 'addressTh',
        branch_address_en: 'addressEn',
        branch_sub_district_th: 'subDistrictTh',
        branch_sub_district_en: 'subDistrictEn',
        branch_district_th: 'districtTh',
        branch_district_en: 'districtEn',
        branch_province_th: 'provinceTh',
        branch_province_en: 'provinceEn',
        branch_zip_code: '00000',
        branch_latitude: latitude ? Number(latitude) : latitude,
        branch_longitude: longitude ? Number(longitude) : longitude,
        branch_image_url: 'url',
      };
    };
    // beforeEach(() => {
    //   jest
    //     .spyOn(companyRepository, 'findOne')
    //     .mockResolvedValue({ companyId: 'CompanyId' } as CompanyEntity);
    // });
    it('should return store list sort by distance asc', async () => {
      jest.spyOn(branchRepository, 'createQueryBuilder').mockReturnValue({
        where: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        getRawMany: jest.fn().mockResolvedValue([
          mockBranch('branch2', '13.6982317', '100.5375343'),
          mockBranch('branch3', null, null), // must be index last
          mockBranch('branch1', '13.7589248', '100.566164'), //must be index 0
        ]),
      } as any);
      const result = await commonsService.getStoreList('CompanyId', 'en', {
        lat: '13.7548965',
        long: '100.5637128',
      });

      expect(result.length).toEqual(mockBranch.length);
      expect(result[0].id).toEqual(
        mockBranch('branch1', '13.7589248', '100.566164').branch_branch_id,
      );
      expect(result[result.length - 1].id).toEqual(
        mockBranch('branch3', null, null).branch_branch_id,
      );
    });
    it('request latitude longitude is null return store list order by title asc', async () => {
      jest.spyOn(branchRepository, 'createQueryBuilder').mockReturnValue({
        where: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        getRawMany: jest.fn().mockResolvedValue([
          mockBranch('branch1', '13.7589248', '100.566164'), //must be index 0
          mockBranch('branch2', '13.6982317', '100.5375343'),
          mockBranch('branch3', null, null), // must be index last
        ]),
      } as any);
      const result = await commonsService.getStoreList('CompanyId', 'th', {
        lat: '',
        long: '',
      });

      expect(result.length).toEqual(mockBranch.length);
      expect(result[0].id).toEqual(
        mockBranch('branch1', '13.7589248', '100.566164').branch_branch_id,
      );
      expect(result[result.length - 1].id).toEqual(
        mockBranch('branch3', null, null).branch_branch_id,
      );
    });
  });

  describe('getOrderList', () => {
    it.each(['th', 'en'])('will return 0 length', async (lang) => {
      jest
        .spyOn(estimationActivitiesRepository, 'find')
        .mockResolvedValueOnce([]);

      const result = await commonsService.getActivitiesByDeviceId(
        'deviceId#1',
        'company#1',
        lang as ILanguage,
      );
      expect(estimationActivitiesRepository.find).toHaveBeenCalledWith({
        where: { companyId: 'company#1', deviceId: 'deviceId#1' },
        relations: ['modelMaster', 'modelMasterColor', 'branch'],
        order: { updatedAt: 'DESC' },
      });
      expect(result.length).toEqual(0);
    });

    it.each(['th', 'en'])('will return >0 length', async (lang) => {
      jest.spyOn(estimationActivitiesRepository, 'find').mockResolvedValueOnce([
        { ...mockEstimationActivity, id: 'test1' },
        { ...mockEstimationActivity, id: 'test2' },
        { ...mockEstimationActivity, id: 'test3' },
      ]);

      jest.mocked(convertOrderResponse).mockImplementation(() => true as any);
      const result = await commonsService.getActivitiesByDeviceId(
        'deviceId#1',
        'company#1',
        lang as ILanguage,
      );

      expect(estimationActivitiesRepository.find).toHaveBeenCalledWith({
        where: { companyId: 'company#1', deviceId: 'deviceId#1' },
        relations: ['modelMaster', 'modelMasterColor', 'branch'],
        order: { updatedAt: 'DESC' },
      });
      expect(result.length).toEqual(3);
    });
  });
});
