import { Test } from '@nestjs/testing';
import { BaseExceptionModule } from '../../src/base-exceptions/base-exceptions.module';
import {
  ExcelManagerService,
  IConvertToType,
  INumTypeValidate,
} from '../../src/excel/excel-manager.service';
import { ExcelManagerModule } from '../../src/excel/excel-manager.module';
import { BaseExceptionService } from '../../src/exceptions';
import { BASE_EXCEPTIONS } from '../../src/config';
import {
  mockSuccessColumn,
  mockRowData,
  expectedSuccessResult,
  mockMissingColumn,
  mockCheckListForExcel,
  mockHeaderExcelV2,
  mockRowExcelV2,
  expectedSuccessResultOfExcelv2,
  mockHeaderExcelV2BadRequired,
  mockAnswer,
  mockmasterQuestions,
} from '../mock-data/excel-manager';
import * as ExcelJS from 'exceljs';
import { EncryptDecryptModule } from '../../src/encrypt-decrypt-message/encrypt-decrypt.module';
import { JobEntity } from 'src/entities';
import { SelectQueryBuilder } from 'typeorm';
import { PassThrough, Readable } from 'stream';
import { ReadStream } from 'typeorm/platform/PlatformTools';

describe('ExcelManagerService', () => {
  let excelManagerService: ExcelManagerService;

  beforeEach(async () => {
    const module = await Test.createTestingModule({
      imports: [
        EncryptDecryptModule.register(),
        BaseExceptionModule.register(),
        ExcelManagerModule.register({
          headers: {
            PRODUCT_ID: {
              keyName: 'modelKey',
              type: IConvertToType.string,
              isRequired: true,
              subHeader: 'ID',
            },
            MATERIAL_ID: {
              keyName: 'matCode',
              type: IConvertToType.string,
              isRequired: true,
              subHeader: 'MatID',
            },
            BRAND: {
              keyName: 'brand',
              type: IConvertToType.string,
              isRequired: true,
              subHeader: 'ยี่ห้อ',
            },
            MODEL: {
              keyName: 'model',
              type: IConvertToType.string,
              isRequired: true,
              subHeader: 'รุ่น',
            },
            CAPACITY: {
              keyName: 'rom',
              type: IConvertToType.string,
              isRequired: true,
              subHeader: 'ความจุ',
            },
            MODEL_PRICE: {
              keyName: 'referencePrice',
              type: IConvertToType.numString,
              isRequired: false,
              subHeader: 'ราคาเครื่อง',
              options: {
                decimal: 0,
              },
            },
            RELEASE_YEAR: {
              keyName: 'modelYear',
              type: IConvertToType.numString,
              isRequired: false,
              subHeader: 'ปี',
            },
            PERCENT_PURCHASE: {
              keyName: 'purchasedRatio',
              type: IConvertToType.number,
              isRequired: false,
              subHeader: '% รับซื้อ',
            },
            MAXIMUM_PURCHASE_PRICE_GRADE_A: {
              keyName: 'gradeA',
              type: IConvertToType.numString,
              isRequired: true,
              subHeader: 'ราคารับซื้อสูงสุดของเกรด A',
              options: {
                decimal: 2,
                min: { referenceField: 'MAXIMUM_PURCHASE_PRICE_GRADE_B' },
              },
            },
            MAXIMUM_PURCHASE_PRICE_GRADE_B: {
              keyName: 'gradeB',
              type: IConvertToType.numString,
              isRequired: true,
              subHeader: 'ราคารับซื้อสูงสุดของเกรด B',
              options: {
                decimal: 2,
                max: { referenceField: 'MAXIMUM_PURCHASE_PRICE_GRADE_A' },
                min: { referenceField: 'MAXIMUM_PURCHASE_PRICE_GRADE_C' },
              },
            },
            MAXIMUM_PURCHASE_PRICE_GRADE_C: {
              keyName: 'gradeC',
              type: IConvertToType.numString,
              isRequired: true,
              subHeader: 'ราคารับซื้อสูงสุดของเกรด C',
              options: {
                decimal: 2,
                max: { referenceField: 'MAXIMUM_PURCHASE_PRICE_GRADE_B' },
                min: { referenceField: 'MAXIMUM_PURCHASE_PRICE_GRADE_D' },
              },
            },
            MAXIMUM_PURCHASE_PRICE_GRADE_D: {
              keyName: 'gradeD',
              type: IConvertToType.numString,
              isRequired: true,
              subHeader: 'ราคารับซื้อสูงสุดของเกรด D',
              options: {
                decimal: 2,
                max: { referenceField: 'MAXIMUM_PURCHASE_PRICE_GRADE_C' },
                min: { value: 0 },
              },
            },
          },
        }),
      ],
    }).compile();

    excelManagerService = module.get<ExcelManagerService>(ExcelManagerService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('readExcelFile', () => {
    it('should match success result', async () => {
      const workbook = new ExcelJS.Workbook();
      workbook.addWorksheet('sheet1');
      const worksheet = workbook.worksheets[0];

      worksheet.columns = mockSuccessColumn;

      for (let i = 0; i < mockRowData.length; i++) {
        worksheet.addRow(mockRowData[i]);
      }

      const mockBuffer = (await workbook.xlsx.writeBuffer()) as Buffer;
      const result = await excelManagerService.readExcelFile(
        mockBuffer,
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      );

      expect(result).toEqual(expectedSuccessResult);
    });

    it('should return empty array data', async () => {
      const workbook = new ExcelJS.Workbook();
      workbook.addWorksheet('sheet1');
      const worksheet = workbook.worksheets[0];

      worksheet.columns = mockSuccessColumn;

      const mockBuffer = (await workbook.xlsx.writeBuffer()) as Buffer;
      const result = await excelManagerService.readExcelFile(
        mockBuffer,
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'sheet1',
      );

      expect(result).toEqual([]);
    });

    it('should required field incomplete (missing some field)', async () => {
      const workbook = new ExcelJS.Workbook();
      const sheet = workbook.addWorksheet('sheet1');
      const worksheet = workbook.worksheets[0];

      worksheet.columns = mockMissingColumn;

      for (let i = 0; i < mockRowData.length; i++) {
        worksheet.addRow(mockRowData[i]);
      }

      const mockBuffer = (await workbook.xlsx.writeBuffer()) as Buffer;
      try {
        await excelManagerService.readExcelFile(
          mockBuffer,
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          'sheet1',
        );
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.REQUIRED_FIELD_INCOMPLETE.code,
        );
        expect((error as BaseExceptionService).message).toBe(
          'Required field incomplete',
        );
      }
    });

    it('should required field incomplete (blank sheet)', async () => {
      const workbook = new ExcelJS.Workbook();
      const sheet = workbook.addWorksheet('sheet1');
      const worksheet = workbook.worksheets[0];

      const mockBuffer = (await workbook.xlsx.writeBuffer()) as Buffer;
      try {
        await excelManagerService.readExcelFile(
          mockBuffer,
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          'sheet1',
        );
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.REQUIRED_FIELD_INCOMPLETE.code,
        );
        expect((error as BaseExceptionService).message).toBe(
          'Required field incomplete',
        );
      }
    });

    it('should invalid file type', async () => {
      const workbook = new ExcelJS.Workbook();

      const mockBuffer = (await workbook.xlsx.writeBuffer()) as Buffer;
      try {
        await excelManagerService.readExcelFile(mockBuffer, 'excel');
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.INVALID_FILE_TYPE.code,
        );
        expect((error as BaseExceptionService).message).toBe(
          'Invalid file type',
        );
      }
    });

    it('should invalid file size', async () => {
      const workbook = new ExcelJS.Workbook();
      const sheet = workbook.addWorksheet('sheet1');
      const worksheet = workbook.worksheets[0];

      worksheet.columns = mockSuccessColumn;

      for (let i = 0; i < mockRowData.length; i++) {
        worksheet.addRow(mockRowData[i]);
      }

      const mockBuffer = (await workbook.xlsx.writeBuffer()) as Buffer;
      try {
        await excelManagerService.readExcelFile(
          mockBuffer,
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          'sheet1',
          1,
        );
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.INVALID_FILE_SIZE.code,
        );
        expect((error as BaseExceptionService).message).toBe(
          'Invalid file size',
        );
      }
    });

    it('should invalid sheet name', async () => {
      const workbook = new ExcelJS.Workbook();
      const sheet = workbook.addWorksheet('sheet1');
      const worksheet = workbook.worksheets[0];

      worksheet.columns = mockSuccessColumn;

      for (let i = 0; i < mockRowData.length; i++) {
        worksheet.addRow(mockRowData[i]);
      }

      const mockBuffer = (await workbook.xlsx.writeBuffer()) as Buffer;

      try {
        await excelManagerService.readExcelFile(
          mockBuffer,
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          'sheet2',
        );
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.INVALID_SHEET_NAME.code,
        );
        expect((error as BaseExceptionService).message).toBe(
          'Invalid excel sheet name',
        );
      }
    });

    it('should row data exceed limit', async () => {
      const module2 = await Test.createTestingModule({
        imports: [
          EncryptDecryptModule.register(),
          BaseExceptionModule.register(),
          ExcelManagerModule.register({
            headers: {
              PRODUCT_ID: {
                keyName: 'modelKey',
                type: IConvertToType.string,
                isRequired: true,
                subHeader: 'ID',
              },
            },
            maxRows: 1,
            headerRowsCount: 2,
          }),
        ],
        providers: [],
      }).compile();
      const excelManagerService2 =
        module2.get<ExcelManagerService>(ExcelManagerService);

      const workbook = new ExcelJS.Workbook();
      workbook.addWorksheet('sheet1');
      const worksheet = workbook.worksheets[0];

      worksheet.columns = mockSuccessColumn;

      for (let i = 0; i < mockRowData.length; i++) {
        worksheet.addRow(mockRowData[i]);
      }

      const mockBuffer = (await workbook.xlsx.writeBuffer()) as Buffer;
      try {
        await excelManagerService2.readExcelFile(
          mockBuffer,
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          'sheet1',
        );
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.DATA_ROW_EXCEED_LIMIT.code,
        );
      }
    });
  });

  describe('readExcelFileV2', () => {
    it('shoud be invalid mime type', async () => {
      const workbook = new ExcelJS.Workbook();
      workbook.addWorksheet('MasterPriceFunction_26092024_154042');
      const worksheet = workbook.worksheets[0];

      worksheet.columns = mockHeaderExcelV2;

      for (const mockitem of mockRowExcelV2) {
        worksheet.addRow(mockitem);
      }

      // Write the workbook to a buffer
      const buffer = await workbook.xlsx.writeBuffer();

      const masterfile = {
        fieldname: 'file',
        originalname:
          'undefined.vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        encoding: '7bit',
        mimetype: 'invalid',
        buffer: buffer,
        size: 10734,
      } as Express.Multer.File;
      try {
        await excelManagerService.readExcelFileV2(
          masterfile,
          mockCheckListForExcel as any[],
        );
        fail('Expected method to throw an error');
      } catch (error) {
        expect((error as BaseExceptionService).message).toBe(
          'Invalid file type',
        );
      }
    });

    it('shoud be invalid file size', async () => {
      const workbook = new ExcelJS.Workbook();
      workbook.addWorksheet('MasterPriceFunction_26092024_154042');
      const worksheet = workbook.worksheets[0];

      worksheet.columns = mockHeaderExcelV2;

      for (const mockitem of mockRowExcelV2) {
        worksheet.addRow(mockitem);
      }

      // Write the workbook to a buffer
      const buffer = await workbook.xlsx.writeBuffer();

      const masterfile = {
        fieldname: 'file',
        originalname:
          'undefined.vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        encoding: '7bit',
        mimetype:
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        buffer: buffer,
        size: 99999910734,
      } as Express.Multer.File;

      try {
        await excelManagerService.readExcelFileV2(
          masterfile,
          mockCheckListForExcel as any[],
          'sheet1',
          1,
        );
        fail('Expected method to throw an error');
      } catch (error) {
        expect((error as BaseExceptionService).message).toBe(
          'Invalid file size',
        );
      }
    });

    it('shoud be invalid file name', async () => {
      const workbook = new ExcelJS.Workbook();
      workbook.addWorksheet('helloworld');
      const worksheet = workbook.worksheets[0];

      worksheet.columns = mockHeaderExcelV2;

      for (const mockitem of mockRowExcelV2) {
        worksheet.addRow(mockitem);
      }
      // Write the workbook to a buffer
      const buffer = await workbook.xlsx.writeBuffer();

      const masterfile = {
        fieldname: 'file',
        originalname:
          'undefined.vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        encoding: '7bit',
        mimetype:
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        buffer: buffer,
        size: 10734,
      } as Express.Multer.File;

      try {
        await excelManagerService.readExcelFileV2(
          masterfile,
          mockCheckListForExcel as any[],
          'sheet2',
        );
        fail('Expected method to throw an error');
      } catch (error) {
        expect((error as BaseExceptionService).message).toBe(
          'Invalid excel sheet name',
        );
      }
    });

    it('shoud be invalid required header column', async () => {
      const workbook = new ExcelJS.Workbook();
      workbook.addWorksheet('helloworld');
      const worksheet = workbook.worksheets[0];

      worksheet.columns = mockHeaderExcelV2BadRequired;

      for (const mockitem of mockRowExcelV2) {
        worksheet.addRow(mockitem);
      }

      // Write the workbook to a buffer
      const buffer = await workbook.xlsx.writeBuffer();

      const masterfile = {
        fieldname: 'file',
        originalname:
          'undefined.vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        encoding: '7bit',
        mimetype:
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        buffer: buffer,
        size: 10734,
      } as Express.Multer.File;

      try {
        await excelManagerService.readExcelFileV2(
          masterfile,
          mockCheckListForExcel as any[],
        );
        fail('Expected method to throw an error');
      } catch (error) {
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.REQUIRED_FIELD_INCOMPLETE.code,
        );
      }
    });

    it('shoud be success read a file excel master funtion', async () => {
      const workbook = new ExcelJS.Workbook();
      workbook.addWorksheet('MasterPriceFunction_26092024_154042');
      const worksheet = workbook.worksheets[0];

      worksheet.columns = mockHeaderExcelV2;

      for (const mockitem of mockRowExcelV2) {
        worksheet.addRow(mockitem);
      }

      // Write the workbook to a buffer
      const buffer = await workbook.xlsx.writeBuffer();

      const masterfile = {
        fieldname: 'file',
        originalname:
          'undefined.vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        encoding: '7bit',
        mimetype:
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        buffer: buffer,
        size: 10734,
      } as Express.Multer.File;

      const result = await excelManagerService.readExcelFileV2(
        masterfile,
        mockCheckListForExcel as any[],
      );

      expect(result).toEqual(expectedSuccessResultOfExcelv2);
    });
  });

  describe('Convert Value To Known Type', () => {
    it('should convert null of STRING type to blankTo', () => {
      const result = excelManagerService.convertValueToKnownType({
        value: null,
        toType: IConvertToType.string,
      });

      expect(result).toEqual(null);
    });

    it('should convert undefined of STRING type to blankTo', () => {
      const result = excelManagerService.convertValueToKnownType({
        value: undefined,
        toType: IConvertToType.string,
      });

      expect(result).toEqual(null);
    });

    it('should convert null of NUMBER type to blankTo', () => {
      const result = excelManagerService.convertValueToKnownType({
        value: null,
        toType: IConvertToType.number,
      });

      expect(result).toEqual(null);
    });

    it('should convert undefined of NUMBER type to blankTo', () => {
      const result = excelManagerService.convertValueToKnownType({
        value: undefined,
        toType: IConvertToType.number,
      });

      expect(result).toEqual(null);
    });

    it('should convert empty string of STRING type to blankTo', () => {
      const result = excelManagerService.convertValueToKnownType({
        value: '',
        toType: IConvertToType.string,
      });

      expect(result).toEqual(null);
    });

    it('should convert empty string of NUMBER type to blankTo', () => {
      const result = excelManagerService.convertValueToKnownType({
        value: '',
        toType: IConvertToType.number,
      });

      expect(result).toEqual(null);
    });

    it('should convert number to STRING type', () => {
      const result = excelManagerService.convertValueToKnownType({
        value: '123.45',
        toType: IConvertToType.string,
      });

      expect(result).toEqual('123.45');
    });

    it('should convert number with whitespace to STRING type', () => {
      const result = excelManagerService.convertValueToKnownType({
        value: '123.45  ',
        toType: IConvertToType.string,
      });

      expect(result).toEqual('123.45');
    });

    it('should convert number to NUMBER type', () => {
      const result = excelManagerService.convertValueToKnownType({
        value: '123.45',
        toType: IConvertToType.number,
      });

      expect(result).toEqual(123.45);
    });

    it('should convert number with whitespace to NUMBER type', () => {
      const result = excelManagerService.convertValueToKnownType({
        value: '123.45  ',
        toType: IConvertToType.number,
      });

      expect(result).toEqual(123.45);
    });

    it('should convert number to STRING_OF_NUMBER type', () => {
      const result = excelManagerService.convertValueToKnownType({
        value: '123.45',
        toType: IConvertToType.numString,
      });

      expect(result).toEqual('123.45');
    });

    it('should convert number with whitespace to STRING_OF_NUMBER type', () => {
      const result = excelManagerService.convertValueToKnownType({
        value: '123.45  ',
        toType: IConvertToType.numString,
      });

      expect(result).toEqual('123.45');
    });

    it('should convert NAN of NUMBER type to nanTo', () => {
      const result = excelManagerService.convertValueToKnownType({
        value: 'nan',
        toType: IConvertToType.number,
      });

      expect(result).toEqual('ERROR_NAN');
    });

    it('should convert NAN of NUMBER type with whitespace to nanTo', () => {
      const result = excelManagerService.convertValueToKnownType({
        value: 'nan with whitespace ',
        toType: IConvertToType.number,
      });

      expect(result).toEqual('ERROR_NAN');
    });

    it('should convert NAN of STRING_OF_NUMBER type to nanTo', () => {
      const result = excelManagerService.convertValueToKnownType({
        value: 'nan',
        toType: IConvertToType.numString,
      });

      expect(result).toEqual('ERROR_NAN');
    });

    it('should convert NAN of STRING_OF_NUMBER with whitespace to nanTo', () => {
      const result = excelManagerService.convertValueToKnownType({
        value: 'nan with whitespace ',
        toType: IConvertToType.numString,
      });

      expect(result).toEqual('ERROR_NAN');
    });
  });

  describe('validate number value', () => {
    it('should return true for IS_INTEGER when the value is an integer', () => {
      const result = excelManagerService.validateNumberValue({
        value: 10,
        typeValidate: INumTypeValidate.isInteger,
      });

      expect(result).toBe(true);
    });

    it('should return false for IS_INTEGER when the value is not an integer', () => {
      const result = excelManagerService.validateNumberValue({
        value: 10.1,
        typeValidate: INumTypeValidate.isInteger,
      });

      expect(result).toBe(false);
    });

    it('should return true for MAX_DECIMAL when the value has decimals equal to validateOption', () => {
      const result = excelManagerService.validateNumberValue({
        value: 10.12,
        typeValidate: INumTypeValidate.maxDecimal,
        validateOption: 2,
      });

      expect(result).toBe(true);
    });

    it('should return false for MAX_DECIMAL when the value has decimals not equal to validateOption', () => {
      const result = excelManagerService.validateNumberValue({
        value: 10.123,
        typeValidate: INumTypeValidate.maxDecimal,
        validateOption: 2,
      });

      expect(result).toBe(false);
    });

    it('should return true for MAX_VALUE when the value is less than to validateOption', () => {
      const result = excelManagerService.validateNumberValue({
        value: 15000,
        typeValidate: INumTypeValidate.maxValue,
        validateOption: 20000,
      });

      expect(result).toBe(true);
    });

    it('should return false for MAX_VALUE when the value is greater than validateOption', () => {
      const result = excelManagerService.validateNumberValue({
        value: 15000,
        typeValidate: INumTypeValidate.maxValue,
        validateOption: 10000,
      });

      expect(result).toBe(false);
    });

    it('should return false for MAX_VALUE when the value is equal to validateOption', () => {
      const result = excelManagerService.validateNumberValue({
        value: 15000,
        typeValidate: INumTypeValidate.maxValue,
        validateOption: 15000,
      });

      expect(result).toBe(false);
    });

    it('should return true for MIN_VALUE when the value is greater than to validateOption', () => {
      const result = excelManagerService.validateNumberValue({
        value: 15000,
        typeValidate: INumTypeValidate.minValue,
        validateOption: 10000,
      });

      expect(result).toBe(true);
    });

    it('should return false for MIN_VALUE when the value is less than to validateOption', () => {
      const result = excelManagerService.validateNumberValue({
        value: 5000,
        typeValidate: INumTypeValidate.minValue,
        validateOption: 10000,
      });

      expect(result).toBe(false);
    });

    it('should return false for MIN_VALUE when the value is equal to validateOption', () => {
      const result = excelManagerService.validateNumberValue({
        value: 10000,
        typeValidate: INumTypeValidate.minValue,
        validateOption: 10000,
      });

      expect(result).toBe(false);
    });
  });

  describe('Validate Row Data', () => {
    it('should throw an error when INVALID_DATA_TYPE', () => {
      const mockRowData = {
        PRODUCT_ID: '001',
        MATERIAL_ID: 'TEST',
        MODEL_PRICE: 'nan',
        BRAND: 'TEST',
        MODEL: 'TEST',
        CAPACITY: 'TEST',
        MAXIMUM_PURCHASE_PRICE_GRADE_A: '20000.00',
        MAXIMUM_PURCHASE_PRICE_GRADE_B: '15000.00',
        MAXIMUM_PURCHASE_PRICE_GRADE_C: '20000.00',
        MAXIMUM_PURCHASE_PRICE_GRADE_D: '15000.00',
      } as any;

      try {
        excelManagerService.validate(mockRowData);
        // If it doesn't throw an error, fail the test
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.INVALID_DATA_TYPE.code,
        );
        expect((error as BaseExceptionService).message).toBe(
          'Invalid data type',
        );
      }
    });

    it('should throw an error when REQUIRED_FIELD_INCOMPLETE and header is required', () => {
      const mockRowData = {
        PRODUCT_ID: '001',
        MODEL_PRICE: '',
        MAXIMUM_PURCHASE_PRICE_GRADE_A: '20000.00',
        MAXIMUM_PURCHASE_PRICE_GRADE_B: '15000.00',
      } as any;

      try {
        excelManagerService.validate(mockRowData);
        // If it doesn't throw an error, fail the test
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.REQUIRED_FIELD_INCOMPLETE.code,
        );
        expect((error as BaseExceptionService).message).toBe(
          'Required field incomplete',
        );
      }
    });

    it('should throw an error when row data value is undefined or null and header is required', () => {
      const mockRowData = {} as any;

      try {
        excelManagerService.validate(mockRowData);
        // If it doesn't throw an error, fail the test
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.REQUIRED_FIELD_INCOMPLETE.code,
        );
        expect((error as BaseExceptionService).message).toBe(
          'Required field incomplete',
        );
      }
    });
  });

  describe('validate numeric value', () => {
    it('should throw an error when validate decimal is false', () => {
      const mockNumericValue = 10.123;

      try {
        excelManagerService.validateNumericValue(
          mockNumericValue,
          'MODEL_PRICE',
        );
        // If it doesn't throw an error, fail the test
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.INVALID_DATA_FORMAT.code,
        );
        expect((error as BaseExceptionService).message).toBe(
          'Invalid data format',
        );
      }
    });

    it('should throw an error when validate max is false', () => {
      const mockNumericValue = 40000;

      try {
        excelManagerService.validateNumericValue(
          mockNumericValue,
          'MAXIMUM_PURCHASE_PRICE_GRADE_B',
          {
            MAXIMUM_PURCHASE_PRICE_GRADE_A: 30000,
            MAXIMUM_PURCHASE_PRICE_GRADE_C: 10000,
          },
        );
        // If it doesn't throw an error, fail the test
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.INVALID_RANGE.code,
        );
        expect((error as BaseExceptionService).message).toBe('Invalid range');
      }
    });

    it('should throw an error when validate min is false', () => {
      const mockNumericValue = -1;

      try {
        excelManagerService.validateNumericValue(
          mockNumericValue,
          'MAXIMUM_PURCHASE_PRICE_GRADE_A',
          {
            MAXIMUM_PURCHASE_PRICE_GRADE_B: 20000,
          },
        );
        // If it doesn't throw an error, fail the test
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.INVALID_RANGE.code,
        );
        expect((error as BaseExceptionService).message).toBe('Invalid range');
      }
    });
  });

  describe('export file excel v2', () => {
    it('shoude be generate success', async () => {
      const result = await excelManagerService.generateTemplateExcelFileV2(
        mockmasterQuestions as any,
        mockAnswer,
      );

      expect(result).toBeInstanceOf(Buffer);
    });
  });

  describe('generate excel stream', () => {
    it('should be generate success (SAP)', async () => {
      const queryStream = {
        where: jest.fn().mockReturnThis(),
        getMany: jest.fn(),
        stream: jest.fn(),
      } as unknown as jest.Mocked<SelectQueryBuilder<JobEntity>>;

      const mockStream = new Readable({
        objectMode: true,
        read() {
          this.push({
            r_job_id: 1,
            cost_center: 1111111111,
            r_branch_id: 90022222,
            redemption_code:
              'MTIzNDU2Nzg5MDEyLo5Fgm5Vc3LCkMPnbFF0rvwK2E+MPh9a9WRYP3TUUSBSz70G2YKz0Jo=',
            r_model_identifiers: {
              rom: '64',
              brand: 'Apple',
              model: 'iPhone 11 Pro Max',
            },
            status: '81_INSPECTION_AUTO_COMPLETED',
          });
          this.push({
            r_job_id: 2,
            cost_center: 1111111111,
            r_branch_id: 90022222,
            redemption_code: null,
            r_model_identifiers: {
              rom: '64',
              brand: 'Apple',
              model: 'iPhone 11 Pro Max',
            },
            status: '81_INSPECTION_AUTO_COMPLETED',
          });
          this.push(null); // End of stream
        },
      }) as unknown as ReadStream;

      // Mock the `stream` method to return the mock Readable stream
      queryStream.stream.mockReturnValue(
        new Promise((resolve) => {
          return resolve(mockStream);
        }),
      );
      excelManagerService.options.headers = {
        purchasedAt: {
          keyName: 'test',
          subHeader: 'test',
          type: IConvertToType.string,
        },
        receivedAt: {
          keyName: 'test',
          subHeader: 'test',
          type: IConvertToType.string,
        },
        jobId: {
          keyName: 'test',
          subHeader: 'test',
          type: IConvertToType.string,
        },
        model: {
          keyName: 'test',
          subHeader: 'test',
          type: IConvertToType.string,
        },
      };

      const resultStream =
        await excelManagerService.generateExcelFileWithStream(
          [],
          'fileName',
          queryStream,
          {
            userMappedTable: {},
            branchMappedTable: {},
          },
          'SAP',
          {},
        );

      const data: any[] = await new Promise((resolve, reject) => {
        const chunks: any[] = [];
        resultStream.on('data', (chunk) => {
          chunks.push(chunk.toString());
        });
        resultStream.on('end', () => resolve(chunks));
        resultStream.on('error', (err) => reject(err));
      });

      expect(resultStream).toBeInstanceOf(PassThrough);
    });

    it('should be generate success (REPORT)', async () => {
      const queryStream = {
        where: jest.fn().mockReturnThis(),
        getMany: jest.fn(),
        stream: jest.fn(),
      } as unknown as jest.Mocked<SelectQueryBuilder<JobEntity>>;

      const mockStream = new Readable({
        objectMode: true,
        read() {
          this.push({
            r_job_id: 1,
            cost_center: 1111111111,
            r_branch_id: 90022222,
            redemption_code:
              'MTIzNDU2Nzg5MDEyLo5Fgm5Vc3LCkMPnbFF0rvwK2E+MPh9a9WRYP3TUUSBSz70G2YKz0Jo=',
            r_model_identifiers: {
              rom: '64',
              brand: 'Apple',
              model: 'iPhone 11 Pro Max',
            },
            r_status: '81_INSPECTION_AUTO_COMPLETED',
          });

          this.push(null); // End of stream
        },
      }) as unknown as ReadStream;

      // Mock the `stream` method to return the mock Readable stream
      queryStream.stream.mockReturnValue(
        new Promise((resolve) => {
          return resolve(mockStream);
        }),
      );
      excelManagerService.options.headers = {
        purchasedAt: {
          keyName: 'test',
          subHeader: 'test',
          type: IConvertToType.string,
        },
        receivedAt: {
          keyName: 'test',
          subHeader: 'test',
          type: IConvertToType.string,
        },
        jobId: {
          keyName: 'test',
          subHeader: 'test',
          type: IConvertToType.string,
        },
        model: {
          keyName: 'test',
          subHeader: 'test',
          type: IConvertToType.string,
        },
      };

      const resultStream =
        await excelManagerService.generateExcelFileWithStream(
          [],
          'fileName',
          queryStream,
          {
            userMappedTable: {},
            branchMappedTable: {},
          },
          'REPORT',
          { jobId: `XXX1111` },
        );

      const data: any[] = await new Promise((resolve, reject) => {
        const chunks: any[] = [];
        resultStream.on('data', (chunk) => {
          chunks.push(chunk.toString());
        });
        resultStream.on('end', () => resolve(chunks));
        resultStream.on('error', (err) => reject(err));
      });

      expect(resultStream).toBeInstanceOf(PassThrough);
    });

    it('should be generate success (PRODUCT)', async () => {
      const queryStream = {
        where: jest.fn().mockReturnThis(),
        getMany: jest.fn(),
        stream: jest.fn(),
      } as unknown as jest.Mocked<SelectQueryBuilder<JobEntity>>;

      const mockStream = new Readable({
        objectMode: true,
        read() {
          this.push({
            r_job_id: 1,
            cost_center: 1111111111,
            r_branch_id: 90022222,
            redemption_code:
              'MTIzNDU2Nzg5MDEyLo5Fgm5Vc3LCkMPnbFF0rvwK2E+MPh9a9WRYP3TUUSBSz70G2YKz0Jo=',
            r_model_identifiers: {
              rom: '64',
              brand: 'Apple',
              model: 'iPhone 11 Pro Max',
            },
            r_status: '80_INSPECTION_COMPLETED',
            r_qc_status: 'SCRAP',
          });

          this.push(null); // End of stream
        },
      }) as unknown as ReadStream;

      // Mock the `stream` method to return the mock Readable stream
      queryStream.stream.mockReturnValue(
        new Promise((resolve) => {
          return resolve(mockStream);
        }),
      );
      excelManagerService.options.headers = {
        purchasedAt: {
          keyName: 'test',
          subHeader: 'test',
          type: IConvertToType.string,
        },
        receivedAt: {
          keyName: 'test',
          subHeader: 'test',
          type: IConvertToType.string,
        },
        jobId: {
          keyName: 'test',
          subHeader: 'test',
          type: IConvertToType.string,
        },
        model: {
          keyName: 'test',
          subHeader: 'test',
          type: IConvertToType.string,
        },
      };

      const resultStream =
        await excelManagerService.generateExcelFileWithStream(
          [],
          'fileName',
          queryStream,
          {
            userMappedTable: {},
            branchMappedTable: {},
          },
          'REPORT',
          { jobId: `XXX1111` },
        );

      const data: any[] = await new Promise((resolve, reject) => {
        const chunks: any[] = [];
        resultStream.on('data', (chunk) => {
          chunks.push(chunk.toString());
        });
        resultStream.on('end', () => resolve(chunks));
        resultStream.on('error', (err) => reject(err));
      });

      expect(resultStream).toBeInstanceOf(PassThrough);
    });
  });

  describe('validateMaxLengthString', () => {
    it('should not throw if string length is within maxLength', () => {
      excelManagerService.options.headers = {
        TEST: {
          keyName: 'test',
          type: IConvertToType.string,
          options: { maxLength: 5 },
        },
      };
      expect(() =>
        excelManagerService.validateMaxLengthString('12345', 'TEST'),
      ).not.toThrow();
    });

    it('should throw error if string length exceeds maxLength', () => {
      excelManagerService.options.headers = {
        TEST: {
          keyName: 'test',
          type: IConvertToType.string,
          options: { maxLength: 5 },
        },
      };
      expect(() =>
        excelManagerService.validateMaxLengthString('123456', 'TEST'),
      ).toThrow();
    });
  });

  describe('convertValueToKnownType', () => {
    it('should call validateStringKey and return uppercase string for stringKey type', () => {
      excelManagerService.options.headers = {
        TEST: {
          keyName: 'test',
          type: IConvertToType.stringKey,
          options: { maxLength: 5 },
        },
      };
      const spy = jest.spyOn(excelManagerService, 'validateStringKey');
      const result = excelManagerService.convertValueToKnownType({
        value: 'abc12',
        toType: IConvertToType.stringKey,
        headerKey: 'TEST',
      });
      expect(result).toBe('ABC12');
      expect(spy).toHaveBeenCalledWith('abc12', 'TEST');
    });

    it('should throw error for invalid stringKey value', () => {
      excelManagerService.options.headers = {
        TEST: {
          keyName: 'test',
          type: IConvertToType.stringKey,
          options: { maxLength: 5 },
        },
      };
      expect(() =>
        excelManagerService.convertValueToKnownType({
          value: 'abc-12',
          toType: IConvertToType.stringKey,
          headerKey: 'TEST',
        }),
      ).toThrow();
    });
  });

  describe('validateStringKey', () => {
    it('should return uppercase string if valid', () => {
      excelManagerService.options.headers = {
        TEST: {
          keyName: 'test',
          type: IConvertToType.stringKey,
          options: { maxLength: 5 },
        },
      };
      const result = excelManagerService.validateStringKey('abc12', 'TEST');
      expect(result).toBe('ABC12');
    });

    it('should throw error if string contains invalid characters', () => {
      excelManagerService.options.headers = {
        TEST: {
          keyName: 'test',
          type: IConvertToType.stringKey,
          options: { maxLength: 5 },
        },
      };
      expect(() =>
        excelManagerService.validateStringKey('abc-12', 'TEST'),
      ).toThrow();
    });

    it('should throw error if string exceeds maxLength', () => {
      excelManagerService.options.headers = {
        TEST: {
          keyName: 'test',
          type: IConvertToType.stringKey,
          options: { maxLength: 5 },
        },
      };
      expect(() =>
        excelManagerService.validateStringKey('abc123', 'TEST'),
      ).toThrow();
    });
  });
});
