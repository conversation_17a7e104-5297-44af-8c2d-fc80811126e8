import { getRepositoryToken } from '@nestjs/typeorm';
import { Test } from '@nestjs/testing';
import {
  CampaignEntity,
  CampaignRedemptionCodeEntity,
} from '../../src/entities';
import { BaseExceptionModule } from '../../src/base-exceptions/base-exceptions.module';
import { Repository } from 'typeorm';
import { CampaignService } from '../../src/shop/campaign/campaign.service';
import { mockCampaignRedemptionCodeHaveJobId } from '../../__tests__/mock-data/campaign-redemption-code';
import { WithUserContext } from '../../src/interfaces';

describe('CampaignsService', () => {
  let campaignService: CampaignService;
  let campaignRepository: Repository<CampaignEntity>;
  let campaignRedemptionCodeRepository: Repository<CampaignRedemptionCodeEntity>;

  beforeEach(async () => {
    const module = await Test.createTestingModule({
      imports: [BaseExceptionModule.register()],
      providers: [
        CampaignService,
        {
          provide: getRepositoryToken(CampaignEntity),
          useValue: {
            find: jest.fn().mockReturnThis(),
          },
        },
        {
          provide: getRepositoryToken(CampaignRedemptionCodeEntity),
          useValue: {
            createQueryBuilder: jest.fn().mockReturnThis(),
            select: jest.fn().mockReturnThis(),
            addSelect: jest.fn().mockReturnThis(),
            where: jest.fn().mockReturnThis(),
            groupBy: jest.fn().mockReturnThis(),
            getRawMany: jest.fn().mockReturnThis(),
            find: jest.fn().mockReturnThis(),
            save: jest.fn().mockReturnThis(),
          },
        },
      ],
    }).compile();

    campaignService = module.get<CampaignService>(CampaignService);
    campaignRepository = module.get<Repository<CampaignEntity>>(
      getRepositoryToken(CampaignEntity),
    );
    campaignRedemptionCodeRepository = module.get<
      Repository<CampaignRedemptionCodeEntity>
    >(getRepositoryToken(CampaignRedemptionCodeEntity));
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('Get Campaign', () => {
    it('should sucessfully result', async () => {
      const userMock = {
        company: 'WW',
        userKey: 'userTest1',
      } as WithUserContext;
      const mockCampaign = [
        {
          companyId: 'WW',
          campaignCode: 'CAMP-001',
          campaignName: 'TEST001',
          description: 'Desc001',
          remark: 'Remark001',
          maxRedemptionCode: 2,
          startDate: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000),
          endDate: new Date(),
          createdBy: 'system',
          updatedBy: 'system',
        },
      ] as CampaignEntity[];
      const mockCampaignRedemptionCode = [
        {
          campaignCode: 'CAMP-001',
          order: 1,
          count: 2,
        },
        {
          campaignCode: 'CAMP-001',
          order: 2,
          count: 2,
        },
      ];
      jest
        .spyOn(campaignRepository, 'find')
        .mockResolvedValueOnce(mockCampaign);
      jest
        .spyOn(campaignRedemptionCodeRepository, 'createQueryBuilder')
        .mockReturnValueOnce({
          select: jest.fn().mockReturnThis(),
          addSelect: jest.fn().mockReturnThis(),
          where: jest.fn().mockReturnThis(),
          groupBy: jest.fn().mockReturnThis(),
          getRawMany: jest
            .fn()
            .mockResolvedValue(mockCampaignRedemptionCode as any[]),
        } as any);
      const query = {
        modelKey: 'test',
        grade: 'test',
      };

      const result = await campaignService.getCampaigns(query, userMock);
      expect(result).not.toBeNull();
      expect(result.length).toEqual(1);
      expect(result[0].campaignCode).toEqual('CAMP-001');
    });
    it('should result [] case campaign not found', async () => {
      const userMock = {
        company: 'WW',
        userKey: 'userTest1',
      } as WithUserContext;
      const mockCampaign = [] as CampaignEntity[];

      jest
        .spyOn(campaignRepository, 'find')
        .mockResolvedValueOnce(mockCampaign);

      const query = {
        modelKey: 'test',
        grade: 'test',
      };

      const result = await campaignService.getCampaigns(query, userMock);
      expect(result).not.toBeNull();
      expect(result.length).toEqual(0);
    });
  });

  describe('Remove Job Id in Selected Campaign', () => {
    it('should successfully remove job id in selected campaign', async () => {
      jest
        .spyOn(campaignRedemptionCodeRepository, 'find')
        .mockResolvedValue([mockCampaignRedemptionCodeHaveJobId]);
      const result =
        await campaignService.removeJobIdInSelectedCampaign('jobId1');
      expect(result).toBeUndefined();
    });
  });
});
