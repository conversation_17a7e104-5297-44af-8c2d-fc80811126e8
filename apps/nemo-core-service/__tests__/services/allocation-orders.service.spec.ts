import { getRepositoryToken } from '@nestjs/typeorm';
import { Test } from '@nestjs/testing';
import {
  AllocationOrderEntity,
  JobEntity,
  AllocationOrderStatus,
  AllocationOrderType,
} from '../../src/entities';
import { BaseExceptionModule } from '../../src/base-exceptions/base-exceptions.module';
import { AllocationOrdersService } from '../../src/shop/allocation-orders/allocation-orders.service';
import { S3Service } from '../../src/storage/s3.service';
import { BaseExceptionService } from '../../src/exceptions';
import {
  BASE_EXCEPTIONS,
  getS3AllocationOrdersUrlPath,
} from '../../src/config';

import { Repository } from 'typeorm';
import { mockAO } from '../mock-data/allocation-order';
import { mockBranchEntity } from '../mock-data/branches';
import { mockUserEntity } from '../mock-data/user';
import {
  mockJobAOConfirm,
  mockProductJobWithConfirmPrice,
  mockJobAOConfirmPartial,
} from '../mock-data/job';
import { AllocationOrdersConfirmBodyDto } from '../../src/shop/allocation-orders/dto';
import { WithUserContext } from '../../src/interfaces';

jest.mock('../../src/utils/general', () => {
  const original = jest.requireActual('../../src/utils/general');
  return {
    ...original,
    getDateFromToday: jest.fn(),
  };
});

const manager = {
  getRepository: jest.fn().mockReturnThis(),
  connection: {
    createQueryRunner: jest.fn().mockReturnThis(),
    connect: jest.fn().mockReturnThis(),
    startTransaction: jest.fn().mockReturnThis(),
    release: jest.fn().mockReturnThis(),
    rollbackTransaction: jest.fn().mockReturnThis(),
    commitTransaction: jest.fn().mockReturnThis(),
    manager: {
      getRepository: jest.fn().mockReturnThis(),
      createQueryBuilder: jest.fn().mockReturnThis(),
      select: jest.fn().mockReturnThis(),
      where: jest.fn().mockReturnThis(),
      andWhere: jest.fn().mockReturnThis(),
      insert: jest.fn().mockReturnThis(),
      orIgnore: jest.fn().mockReturnThis(),
      values: jest.fn().mockReturnThis(),
      save: jest.fn().mockReturnThis(),
      execute: jest.fn().mockResolvedValue({ raw: [] }),
    },
  },
};

describe('AllocationOrdersService', () => {
  let allocationOrdersService: AllocationOrdersService;
  let allocationOrdersRepository: Repository<AllocationOrderEntity>;

  beforeEach(async () => {
    const module = await Test.createTestingModule({
      imports: [BaseExceptionModule.register()],
      providers: [
        AllocationOrdersService,
        {
          provide: getRepositoryToken(AllocationOrderEntity),
          useValue: {
            findOne: jest.fn().mockReturnThis(),
            insert: jest.fn().mockReturnThis(),
            save: jest.fn().mockReturnThis(),
            manager,
          },
        },
        {
          provide: getRepositoryToken(JobEntity),
          useValue: {
            find: jest.fn().mockReturnThis(),
            manager,
          },
        },
        {
          provide: S3Service,
          useValue: {
            getUploadFilePreSignedUrl: jest.fn(
              () =>
                `company/company-name/allocation-orders/aoid-0001/video/test_video_2221111`,
            ),
            getFileWithSignedUrl: jest.fn(() => 'signed-url'),
          },
        },
      ],
    }).compile();
    allocationOrdersService = module.get<AllocationOrdersService>(
      AllocationOrdersService,
    );
    allocationOrdersRepository = module.get<Repository<AllocationOrderEntity>>(
      getRepositoryToken(AllocationOrderEntity),
    );
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('buildSearchQuery', () => {
    it.each`
      status        | search       | branchId     | type         | expectResult
      ${['status']} | ${'aoId'}    | ${'0001'}    | ${'type'}    | ${`r.status IN ('${'status'}') AND (r.allocationOrderId = '${'aoId'}' or r.awbNumber = '${'aoId'}') AND r.toBranchId = '0001' AND r.allocationOrderType = 'type'`}
      ${['status']} | ${undefined} | ${undefined} | ${undefined} | ${`r.status IN ('${'status'}')`}
      ${undefined}  | ${'aoId'}    | ${undefined} | ${undefined} | ${`(r.allocationOrderId = '${'aoId'}' or r.awbNumber = '${'aoId'}')`}
      ${undefined}  | ${undefined} | ${'0001'}    | ${undefined} | ${`r.toBranchId = '0001'`}
      ${undefined}  | ${undefined} | ${undefined} | ${'type'}    | ${`r.allocationOrderType = 'type'`}
      ${['status']} | ${'aoId'}    | ${undefined} | ${undefined} | ${`r.status IN ('${'status'}') AND (r.allocationOrderId = '${'aoId'}' or r.awbNumber = '${'aoId'}')`}
      ${['status']} | ${undefined} | ${'0001'}    | ${undefined} | ${`r.status IN ('${'status'}') AND r.toBranchId = '0001'`}
      ${['status']} | ${undefined} | ${undefined} | ${'type'}    | ${`r.status IN ('${'status'}') AND r.allocationOrderType = 'type'`}
      ${undefined}  | ${'aoId'}    | ${'0001'}    | ${undefined} | ${`(r.allocationOrderId = '${'aoId'}' or r.awbNumber = '${'aoId'}') AND r.toBranchId = '0001'`}
      ${undefined}  | ${'aoId'}    | ${undefined} | ${'type'}    | ${`(r.allocationOrderId = '${'aoId'}' or r.awbNumber = '${'aoId'}') AND r.allocationOrderType = 'type'`}
      ${undefined}  | ${undefined} | ${'0001'}    | ${'type'}    | ${`r.toBranchId = '0001' AND r.allocationOrderType = 'type'`}
      ${''}         | ${''}        | ${''}        | ${''}        | ${''}
    `(
      'buildSearchQuery',
      async ({ status, search, branchId, type, expectResult }) => {
        const mockQueryBuilder = {
          andWhere: jest.fn(),
        } as unknown as any;
        const mockRequest = {
          query: {
            status,
            search,
            type,
          },
          withBranchContext: { branch: branchId },
        } as unknown as any;
        const result = allocationOrdersService.buildSearchQuery(
          mockRequest,
          mockQueryBuilder,
        );
        if (status || search || branchId) {
          expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(expectResult);
        }
        expect(result).toBeDefined();
      },
    );
  });

  describe('Get Allocation Order by Id', () => {
    it('should successfully get AO', async () => {
      const mockSuccessAO = {
        ...mockAO[0],
        toBranch: mockBranchEntity,
        fromBranch: mockBranchEntity,
        createdUser: mockUserEntity,
      } as AllocationOrderEntity;

      jest
        .spyOn(allocationOrdersRepository, 'findOne')
        .mockResolvedValueOnce(mockSuccessAO);

      const result = await allocationOrdersService.getAOById(
        mockSuccessAO.allocationOrderId,
        mockBranchEntity.branchId,
      );

      // Assertions
      expect(result.allocationOrderId).toBe(mockSuccessAO.allocationOrderId);
      expect(result.status).toBe(mockSuccessAO.status);
      expect(result.fromBranch).toBe(mockBranchEntity);
      expect(result.toBranch).toBe(mockBranchEntity);
      expect(result.createdUser).toBe(mockUserEntity);
    });

    it('should error not found', async () => {
      const mockFailAO = {
        ...mockAO[0],
        allocationOrderId: 'xxxxx',
        toBranchId: mockBranchEntity.branchId,
      } as AllocationOrderEntity;

      jest
        .spyOn(allocationOrdersRepository, 'findOne')
        .mockResolvedValueOnce(null);

      try {
        await allocationOrdersService.getAOById(
          mockFailAO.allocationOrderId,
          mockBranchEntity.branchId,
        );
        // If it doesn't throw an error, fail the test
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.NOT_FOUND_DATA.code,
        );
      }
    });

    it('should return with videoUrl', async () => {
      const mockSuccessAO = {
        ...mockAO[0],
        toBranch: mockBranchEntity,
        fromBranch: mockBranchEntity,
        createdUser: mockUserEntity,
        jobsSnapshot: [
          {
            jobId: '0001',
            videoPath: 'test_video_1',
          },
          {
            jobId: '0002',
            videoPath: null,
          },
          {
            jobId: '0003',
            videoPath: 'test_video_2',
          },
        ],
      } as AllocationOrderEntity;

      jest
        .spyOn(allocationOrdersRepository, 'findOne')
        .mockResolvedValueOnce(mockSuccessAO);

      const result = await allocationOrdersService.getAOById(
        mockSuccessAO.allocationOrderId,
        mockBranchEntity.branchId,
      );

      // Assertions
      console.log(result.jobsSnapshot);
      expect(result.jobsSnapshot).toEqual([
        {
          jobId: '0001',
          videoPath: 'signed-url',
        },
        {
          jobId: '0002',
          videoPath: null,
        },
        {
          jobId: '0003',
          videoPath: 'signed-url',
        },
      ]);
    });
  });

  describe('Confirm Allocation Order', () => {
    it('confirm AO success case (DELIVERY_SUCCESSFUL)', async () => {
      const mockQueryRunner = {
        connect: jest.fn().mockResolvedValue(undefined),
        startTransaction: jest.fn().mockResolvedValue(undefined),
        commitTransaction: jest.fn().mockResolvedValue(undefined),
        rollbackTransaction: jest.fn().mockResolvedValue(undefined),
        release: jest.fn().mockResolvedValue(undefined),
      } as any;

      const allocationOrderEntity = new AllocationOrderEntity();
      allocationOrderEntity.allocationOrderId = 'test-1';
      allocationOrderEntity.companyId = 'WW';
      allocationOrderEntity.fromBranchId = 'branch1';
      allocationOrderEntity.toBranchId = 'branch2';
      allocationOrderEntity.quantity = 3;
      allocationOrderEntity.status = AllocationOrderStatus.IN_TRANSIT;
      allocationOrderEntity.allocationOrderType = AllocationOrderType.RETAIL;
      allocationOrderEntity.jobs = mockJobAOConfirm;
      const mockConnection = {
        createQueryRunner: jest.fn().mockReturnValue(mockQueryRunner),
      } as any;

      const mockAOResult = {
        ...allocationOrderEntity,
      } as AllocationOrderEntity;
      mockAOResult.status = AllocationOrderStatus.DELIVERY_SUCCESSFUL;

      const mockManager = {
        connection: mockConnection,
        findOne: jest.fn().mockResolvedValue(allocationOrderEntity),
        save: jest.fn().mockResolvedValue(mockAOResult),
      } as any;

      Object.defineProperty(allocationOrdersRepository, 'manager', {
        value: mockManager,
      });
      Object.defineProperty(mockQueryRunner, 'manager', {
        value: mockManager,
      });
      const mockBody = {
        jobs: ['00001', '00002', '00003'],
      } as AllocationOrdersConfirmBodyDto;
      const user = {
        company: 'WW',
        userKey: 'user123',
        roles: [
          {
            branchId: 'branch2',
            role: ['Sale', 'Manager'],
          },
        ],
        permissions: [
          {
            branchId: 'branch2',
            permission: ['PS-0019_VIEW', 'PS-0019_UPDATE'],
          },
        ],
      } as WithUserContext;

      const result = await allocationOrdersService.confirmAO(
        'test-1',
        mockBody,
        user,
      );

      // Assertions
      expect(result.allocationOrderId).toBe(
        allocationOrderEntity.allocationOrderId,
      );
      expect(result.status).toBe(mockAOResult.status);
      expect(result.jobsSnapshot).not.toBe(null);
      expect(result.videoPath).toBe(undefined);
      expect(result.remark).toBe(undefined);
    });

    it('confirm AO success case (PARTIAL_RECEIVED)', async () => {
      const mockQueryRunner = {
        connect: jest.fn().mockResolvedValue(undefined),
        startTransaction: jest.fn().mockResolvedValue(undefined),
        commitTransaction: jest.fn().mockResolvedValue(undefined),
        rollbackTransaction: jest.fn().mockResolvedValue(undefined),
        release: jest.fn().mockResolvedValue(undefined),
      } as any;

      const allocationOrderEntity = new AllocationOrderEntity();
      allocationOrderEntity.allocationOrderId = 'test-1';
      allocationOrderEntity.companyId = 'WW';
      allocationOrderEntity.fromBranchId = 'branch1';
      allocationOrderEntity.toBranchId = 'branch2';
      allocationOrderEntity.quantity = 3;
      allocationOrderEntity.status = AllocationOrderStatus.IN_TRANSIT;
      allocationOrderEntity.allocationOrderType = AllocationOrderType.RETAIL;
      allocationOrderEntity.jobs = mockJobAOConfirmPartial;

      const mockConnection = {
        createQueryRunner: jest.fn().mockReturnValue(mockQueryRunner),
      } as any;

      const mockAOResult = {
        ...allocationOrderEntity,
      } as AllocationOrderEntity;
      mockAOResult.status = AllocationOrderStatus.PARTIAL_RECEIVED;
      mockAOResult.remark = 'test-remark';
      mockAOResult.videoPath = 'video-path';

      const mockManager = {
        connection: mockConnection,
        findOne: jest.fn().mockResolvedValue(allocationOrderEntity),
        save: jest.fn().mockResolvedValue(mockAOResult),
      } as any;

      Object.defineProperty(allocationOrdersRepository, 'manager', {
        value: mockManager,
      });
      Object.defineProperty(mockQueryRunner, 'manager', {
        value: mockManager,
      });
      const mockBody = {
        jobs: ['00001'],
        videoPath: 'video',
        remark: 'rm',
      } as AllocationOrdersConfirmBodyDto;
      const user = {
        company: 'WW',
        userKey: 'user123',
        roles: [
          {
            branchId: 'branch2',
            role: ['Sale', 'Manager'],
          },
        ],
        permissions: [
          {
            branchId: 'branch2',
            permission: ['PS-0019_VIEW', 'PS-0019_UPDATE'],
          },
        ],
      } as WithUserContext;

      const result = await allocationOrdersService.confirmAO(
        'test-1',
        mockBody,
        user,
      );

      // Assertions
      expect(result.allocationOrderId).toBe(
        allocationOrderEntity.allocationOrderId,
      );
      expect(result.status).toBe(mockAOResult.status);
      expect(result.jobsSnapshot).not.toBe(null);
      expect(result.videoPath).not.toBe(null);
      expect(result.remark).not.toBe(null);
    });

    it('should error invalid branch', async () => {
      const mockQueryRunner = {
        connect: jest.fn().mockResolvedValue(undefined),
        startTransaction: jest.fn().mockResolvedValue(undefined),
        commitTransaction: jest.fn().mockResolvedValue(undefined),
        rollbackTransaction: jest.fn().mockResolvedValue(undefined),
        release: jest.fn().mockResolvedValue(undefined),
      } as any;

      const allocationOrderEntity = new AllocationOrderEntity();
      allocationOrderEntity.allocationOrderId = 'test-1';
      allocationOrderEntity.companyId = 'WW';
      allocationOrderEntity.fromBranchId = 'branch1';
      allocationOrderEntity.toBranchId = 'branch2';
      allocationOrderEntity.quantity = 3;
      allocationOrderEntity.status = AllocationOrderStatus.IN_TRANSIT;
      allocationOrderEntity.allocationOrderType = AllocationOrderType.RETAIL;
      allocationOrderEntity.jobs = mockJobAOConfirm;
      const mockConnection = {
        createQueryRunner: jest.fn().mockReturnValue(mockQueryRunner),
      } as any;

      const mockAOResult = {
        ...allocationOrderEntity,
      } as AllocationOrderEntity;
      mockAOResult.status = AllocationOrderStatus.PARTIAL_RECEIVED;
      mockAOResult.remark = 'test-remark';
      mockAOResult.videoPath = 'video-path';

      const mockManager = {
        connection: mockConnection,
        findOne: jest.fn().mockResolvedValue(allocationOrderEntity),
        save: jest.fn().mockResolvedValue(mockAOResult),
      } as any;

      Object.defineProperty(allocationOrdersRepository, 'manager', {
        value: mockManager,
      });
      Object.defineProperty(mockQueryRunner, 'manager', {
        value: mockManager,
      });
      const mockBody = {
        jobs: ['00001', '00002', '00003'],
      } as AllocationOrdersConfirmBodyDto;
      const user = {
        company: 'WW',
        userKey: 'user123',
        roles: [
          {
            branchId: 'branch1',
            role: ['Sale', 'Manager'],
          },
        ],
      } as WithUserContext;

      try {
        await allocationOrdersService.confirmAO('test-1', mockBody, user);
        // If it doesn't throw an error, fail the test
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.BRANCH_INVALID.code,
        );
      }
    });

    it('should error ao not found', async () => {
      const mockQueryRunner = {
        connect: jest.fn().mockResolvedValue(undefined),
        startTransaction: jest.fn().mockResolvedValue(undefined),
        commitTransaction: jest.fn().mockResolvedValue(undefined),
        rollbackTransaction: jest.fn().mockResolvedValue(undefined),
        release: jest.fn().mockResolvedValue(undefined),
      } as any;

      const mockConnection = {
        createQueryRunner: jest.fn().mockReturnValue(mockQueryRunner),
      } as any;

      const mockManager = {
        connection: mockConnection,
        findOne: jest.fn().mockResolvedValue(undefined),
        save: jest.fn().mockResolvedValue(undefined),
      } as any;

      Object.defineProperty(allocationOrdersRepository, 'manager', {
        value: mockManager,
      });
      Object.defineProperty(mockQueryRunner, 'manager', {
        value: mockManager,
      });
      const mockBody = {
        jobs: ['00001', '00002', '00003'],
      } as AllocationOrdersConfirmBodyDto;
      const user = {
        company: 'WW',
        userKey: 'user123',
        roles: [
          {
            branchId: 'branch1',
            role: ['Sale', 'Manager'],
          },
        ],
      } as WithUserContext;
      try {
        await allocationOrdersService.confirmAO('test-1', mockBody, user);
        // If it doesn't throw an error, fail the test
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.NOT_FOUND_DATA.code,
        );
      }
    });

    it('should error job not in ao', async () => {
      const mockQueryRunner = {
        connect: jest.fn().mockResolvedValue(undefined),
        startTransaction: jest.fn().mockResolvedValue(undefined),
        commitTransaction: jest.fn().mockResolvedValue(undefined),
        rollbackTransaction: jest.fn().mockResolvedValue(undefined),
        release: jest.fn().mockResolvedValue(undefined),
      } as any;

      const allocationOrderEntity = new AllocationOrderEntity();
      allocationOrderEntity.allocationOrderId = 'test-1';
      allocationOrderEntity.companyId = 'WW';
      allocationOrderEntity.fromBranchId = 'branch1';
      allocationOrderEntity.toBranchId = 'branch2';
      allocationOrderEntity.quantity = 3;
      allocationOrderEntity.status = AllocationOrderStatus.IN_TRANSIT;
      allocationOrderEntity.allocationOrderType = AllocationOrderType.RETAIL;
      allocationOrderEntity.jobs = mockJobAOConfirm;
      const mockConnection = {
        createQueryRunner: jest.fn().mockReturnValue(mockQueryRunner),
      } as any;

      const mockManager = {
        connection: mockConnection,
        findOne: jest.fn().mockResolvedValue(allocationOrderEntity),
        save: jest.fn().mockResolvedValue(undefined),
      } as any;

      Object.defineProperty(allocationOrdersRepository, 'manager', {
        value: mockManager,
      });
      Object.defineProperty(mockQueryRunner, 'manager', {
        value: mockManager,
      });
      const mockBody = {
        jobs: ['00004'],
      } as AllocationOrdersConfirmBodyDto;
      const user = {
        company: 'WW',
        userKey: 'user123',
        roles: [
          {
            branchId: 'branch2',
            role: ['Sale', 'Manager'],
          },
        ],
        permissions: [
          {
            branchId: 'branch2',
            permission: ['PS-0019_VIEW', 'PS-0019_UPDATE'],
          },
        ],
      } as WithUserContext;
      try {
        await allocationOrdersService.confirmAO('test-1', mockBody, user);
        // If it doesn't throw an error, fail the test
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.NOT_FOUND_DATA.code,
        );
        expect((error as BaseExceptionService).data).toBe(
          'Job is not in this allocation order',
        );
      }
    });

    it('should error no videoPath in status partial', async () => {
      const mockQueryRunner = {
        connect: jest.fn().mockResolvedValue(undefined),
        startTransaction: jest.fn().mockResolvedValue(undefined),
        commitTransaction: jest.fn().mockResolvedValue(undefined),
        rollbackTransaction: jest.fn().mockResolvedValue(undefined),
        release: jest.fn().mockResolvedValue(undefined),
      } as any;

      const allocationOrderEntity = new AllocationOrderEntity();
      allocationOrderEntity.allocationOrderId = 'test-1';
      allocationOrderEntity.companyId = 'WW';
      allocationOrderEntity.fromBranchId = 'branch1';
      allocationOrderEntity.toBranchId = 'branch2';
      allocationOrderEntity.quantity = 3;
      allocationOrderEntity.status = AllocationOrderStatus.IN_TRANSIT;
      allocationOrderEntity.allocationOrderType = AllocationOrderType.RETAIL;
      allocationOrderEntity.jobs = mockJobAOConfirmPartial;
      const mockConnection = {
        createQueryRunner: jest.fn().mockReturnValue(mockQueryRunner),
      } as any;

      const mockManager = {
        connection: mockConnection,
        findOne: jest.fn().mockResolvedValue(allocationOrderEntity),
        save: jest.fn().mockResolvedValue(undefined),
      } as any;

      Object.defineProperty(allocationOrdersRepository, 'manager', {
        value: mockManager,
      });
      Object.defineProperty(mockQueryRunner, 'manager', {
        value: mockManager,
      });
      const mockBody = {
        jobs: ['00001'],
      } as AllocationOrdersConfirmBodyDto;
      const user = {
        company: 'WW',
        userKey: 'user123',
        roles: [
          {
            branchId: 'branch2',
            role: ['Sale', 'Manager'],
          },
        ],
        permissions: [
          {
            branchId: 'branch2',
            permission: ['PS-0019_VIEW', 'PS-0019_UPDATE'],
          },
        ],
      } as WithUserContext;
      try {
        await allocationOrdersService.confirmAO('test-1', mockBody, user);
        // If it doesn't throw an error, fail the test
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.BODY_PAYLOAD_INVALID.code,
        );
        expect((error as BaseExceptionService).data).toBe(
          'videoPath is required for partial job submit',
        );
      }
    });
  });

  describe('Get PresignedUrl', () => {
    it('should successfully get PresignedUrl', async () => {
      const request = {
        aoId: 'aoid-0001',
        key: 'test_video_2221111',
      };

      const user = {
        company: 'CompanyX',
        userKey: 'user123',
      } as WithUserContext;

      const result = await allocationOrdersService.getPresigned(
        request.aoId,
        request.key,
        user,
      );

      const expectedUrlPath = getS3AllocationOrdersUrlPath(
        user.company,
        request.aoId,
        request.key,
      );

      // Assertions
      expect(result.url).toContain(request.aoId);
      expect(result.url).toContain(request.key);
      expect(result.path).toContain(expectedUrlPath);
    });
  });

  describe('Patch Allocation Reject', () => {
    const mockAOIntransit = {
      ...mockAO[0],
      allocationOrderId: 'AO-MOCK-00',
      status: AllocationOrderStatus.IN_TRANSIT,
      jobs: [{ ...mockProductJobWithConfirmPrice[0] }],
      toBranchId: 'branchId#1',
      jobsSnapshot: null,
    } as AllocationOrderEntity;

    const body = {
      remark: 'test-remark',
    };

    const user = {
      company: 'CompanyX',
      userKey: 'user123',
      roles: [
        {
          branchId: 'branchId#1',
          role: ['Sale', 'Manager'],
        },
      ],
      permissions: [
        {
          branchId: 'branchId#1',
          permission: ['PS-0019_VIEW', 'PS-0019_UPDATE'],
        },
      ],
    } as WithUserContext;

    it('should successfully patch', async () => {
      jest
        .spyOn(allocationOrdersRepository, 'findOne')
        .mockResolvedValueOnce(mockAOIntransit);

      const result = await allocationOrdersService.rejectAO(
        mockAOIntransit.allocationOrderId,
        body,
        user,
      );

      expect(result.status).toBe(AllocationOrderStatus.REJECT_BY_SHOP);
      expect(result.remark).toBe(body.remark);
      expect(result.receiverUserKey).toBe(user.userKey);
      expect(result.receivedDate).not.toBeNull();
      expect(result.jobsSnapshot).not.toBeNull();
    });

    it('should stamp grade D', async () => {
      const mockAOJob = {
        ...mockAOIntransit,
        status: AllocationOrderStatus.IN_TRANSIT,
        jobs: [{ ...mockProductJobWithConfirmPrice[0], currentGrade: null }],
      } as AllocationOrderEntity;

      jest
        .spyOn(allocationOrdersRepository, 'findOne')
        .mockResolvedValueOnce(mockAOJob);

      const result = await allocationOrdersService.rejectAO(
        mockAOJob.allocationOrderId,
        body,
        user,
      );

      if (result.jobsSnapshot) {
        expect(result.jobsSnapshot[0].currentGrade).toBe('D');
      }
    });

    it('should error ao not found', async () => {
      jest
        .spyOn(allocationOrdersRepository, 'findOne')
        .mockResolvedValueOnce(null);

      try {
        await allocationOrdersService.rejectAO(
          mockAOIntransit.allocationOrderId,
          body,
          user,
        );
        // If it doesn't throw an error, fail the test
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.NOT_FOUND_DATA.code,
        );
      }
    });

    it('should error branch invalid', async () => {
      const userDiffBranch = {
        company: 'CompanyX',
        userKey: 'user123',
        roles: [
          {
            branchId: 'branchId#2',
            role: ['Sale', 'Manager'],
          },
        ],
      } as WithUserContext;

      jest
        .spyOn(allocationOrdersRepository, 'findOne')
        .mockResolvedValueOnce(mockAOIntransit);

      try {
        await allocationOrdersService.rejectAO(
          mockAOIntransit.allocationOrderId,
          body,
          userDiffBranch,
        );
        // If it doesn't throw an error, fail the test
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.BRANCH_INVALID.code,
        );
      }
    });

    it('should error ao status invalid', async () => {
      const mockAOInvalid = {
        ...mockAOIntransit,
        status: AllocationOrderStatus.DELIVERY_SUCCESSFUL,
      } as AllocationOrderEntity;
      jest
        .spyOn(allocationOrdersRepository, 'findOne')
        .mockResolvedValueOnce(mockAOInvalid);

      try {
        await allocationOrdersService.rejectAO(
          mockAOInvalid.allocationOrderId,
          body,
          user,
        );
        // If it doesn't throw an error, fail the test
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.INVALID_AO_STATUS_TO_CONFIRM.code,
        );
      }
    });
  });
});
