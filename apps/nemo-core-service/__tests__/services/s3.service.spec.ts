import { Test } from '@nestjs/testing';
import { BaseExceptionModule } from '../../src/base-exceptions/base-exceptions.module';
import { S3Service } from '../../src/storage/s3.service';
import {
  GetObjectCommand,
  PutObjectCommand,
  S3Client,
} from '@aws-sdk/client-s3';
import { mockClient } from 'aws-sdk-client-mock';
import { Readable } from 'stream';

const mockS3Client = mockClient(S3Client);

mockS3Client.on(PutObjectCommand).resolves({});
mockS3Client.on(GetObjectCommand).resolves({});

describe('S3Service', () => {
  let s3Service: S3Service;

  beforeEach(async () => {
    const module = await Test.createTestingModule({
      imports: [BaseExceptionModule.register()],
      providers: [S3Service],
    }).compile();

    s3Service = module.get<S3Service>(S3Service);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('Upload file', () => {
    it('should return string', async () => {
      const result = await s3Service.uploadFile(Buffer.from('test'), 'test');
      expect(result).toBeDefined();
    });
  });

  describe('Preview file', () => {
    it('should return url', async () => {
      const result = await s3Service.getPreviewUrl('test');
      expect(result).toBeDefined();
    });
  });

  describe('Get file', () => {
    it('should return file', async () => {
      const result = await s3Service.getFile('test');
      expect(result).toBeDefined();
    });
  });

  describe('Get file with signed url', () => {
    it('should return url', async () => {
      const result = await s3Service.getFileWithSignedUrl('test');
      expect(result).toBeDefined();
    });
  });

  describe('Get upload signed url', () => {
    it('should return url', async () => {
      const result = await s3Service.getUploadFilePreSignedUrl('test');
      expect(result).toBeDefined();
    });
  });

  describe('Get Asset file', () => {
    it('should return file', async () => {
      const result = await s3Service.getAssetFile('test');
      expect(result).toBeDefined();
    });
  });

  describe('Get Buffer file', () => {
    it('should return buffer', async () => {
      const result = await s3Service.getBufferFile('test');
      expect(result).toBeUndefined();
    });
  });

  describe('streamToBuffer', () => {
    it('converts a readable stream to Buffer', async () => {
      // Create a mock readable stream with some data
      const testData = 'Hello, Jest!';
      const readableStream = new Readable({
        read() {
          this.push(testData);
          this.push(null); // Signal the end of the stream
        },
      });

      // Call the function and wait for the promise to resolve
      const resultBuffer = await s3Service.streamToBuffer(readableStream);

      // Assert that the result matches the expected Buffer
      expect(resultBuffer).toEqual(Buffer.from(testData));
    });

    it('handles empty stream', async () => {
      // Create a mock readable stream with no data
      const readableStream = new Readable({
        read() {
          this.push(null); // Signal the end of the empty stream
        },
      });

      // Call the function and wait for the promise to resolve
      const resultBuffer = await s3Service.streamToBuffer(readableStream);

      // Assert that the result is an empty Buffer
      expect(resultBuffer).toEqual(Buffer.from(''));
    });

    it('handles stream error', async () => {
      // Create a mock readable stream that emits an error
      const error = new Error('Test error');
      const readableStream = new Readable({
        read() {
          this.emit('error', error);
        },
      });

      // Call the function and expect the promise to be rejected with the error
      await expect(s3Service.streamToBuffer(readableStream)).rejects.toEqual(
        error,
      );
    });
  });
});
