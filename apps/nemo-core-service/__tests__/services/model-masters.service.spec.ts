import { getRepositoryToken } from '@nestjs/typeorm';
import { Test } from '@nestjs/testing';
import { ModelMasterColorEntity, ModelMasterEntity } from '../../src/entities';
import { BaseExceptionModule } from '../../src/base-exceptions/base-exceptions.module';
import { Repository, SelectQueryBuilder } from 'typeorm';
import { ModelMastersService } from '../../src/shop/model-masters/model-masters.service';
import { Request } from 'express';
import { mockModelMasters } from '../mock-data/model-master';
import { BaseExceptionService } from '../../src/exceptions';
import { BASE_EXCEPTIONS } from '../../src/config';

describe('ModelMastersService', () => {
  let modelMastersService: ModelMastersService;
  let modelMastersRepository: Repository<ModelMasterEntity>;

  beforeEach(async () => {
    const module = await Test.createTestingModule({
      imports: [BaseExceptionModule.register()],
      providers: [
        ModelMastersService,
        {
          provide: getRepositoryToken(ModelMasterEntity),
          useValue: {
            findOne: jest.fn().mockReturnThis(),
          },
        },
      ],
    }).compile();

    modelMastersRepository = module.get<Repository<ModelMasterEntity>>(
      getRepositoryToken(ModelMasterEntity),
    );

    modelMastersService = module.get<ModelMastersService>(ModelMastersService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('Get Model Masters', () => {
    describe('buildSearchQuery', () => {
      it('should construct search query conditions based on provided parameters', () => {
        const mockQueryBuilder = {
          andWhere: jest.fn(),
        } as unknown as SelectQueryBuilder<ModelMasterEntity>;

        const mockRequest = {
          query: {
            brand: 'iPhone',
            model: '12',
            rom: '256GB',
          },
        } as unknown as Request;

        const result = modelMastersService.buildSearchQuery(
          mockRequest,
          mockQueryBuilder,
        );

        expect(result.andWhere).toHaveBeenCalledWith(
          "r.modelIdentifiers ->> 'brand' = 'iPhone' AND r.modelIdentifiers ->> 'model' = '12' AND r.modelIdentifiers ->> 'rom' = '256GB'",
        );
      });

      it('should not add conditions if parameters are not provided', () => {
        const mockQueryBuilder = {
          andWhere: jest.fn(),
        } as unknown as SelectQueryBuilder<ModelMasterEntity>;

        const mockRequest = {
          query: {},
        } as unknown as Request;

        const result = modelMastersService.buildSearchQuery(
          mockRequest,
          mockQueryBuilder,
        );

        expect(result.andWhere).not.toHaveBeenCalled();
      });
    });

    describe('afterLoad', () => {
      it('should transform data correctly based on provided parameters', () => {
        const mockRequest = {
          params: {
            key: 'brand',
          },
          query: {
            info: 'true',
          },
        } as unknown as Request;

        const result = modelMastersService.afterLoad(mockRequest, [
          mockModelMasters,
        ]);

        expect(result).toEqual([
          {
            key: 'brand',
            label: 'Apple',
          },
        ]);
      });

      it('should handle cases when "info" parameter is not "true"', () => {
        const mockRequest = {
          params: {
            key: 'brand',
          },
          query: {
            info: 'false', // or undefined, null, or absent
          },
        } as unknown as Request;

        const result = modelMastersService.afterLoad(mockRequest, [
          mockModelMasters,
        ]);

        expect(result).toEqual([
          {
            key: 'brand',
            label: 'Apple',
          },
        ]);
      });
    });

    describe('getModelMaster', () => {
      it('should return model master based on provided parameters', async () => {
        jest
          .spyOn(modelMastersRepository, 'findOne')
          .mockResolvedValueOnce(mockModelMasters);

        const mockQuery = {
          rom: '128GB',
          brand: 'Apple',
          model: 'iPhone 14 Plus',
        };

        const result = await modelMastersService.getModelMaster(
          mockQuery.brand,
          mockQuery.model,
          mockQuery.rom,
          {} as any,
        );

        expect(result).toEqual(mockModelMasters);
      });
      it('should throw error if model master is not found', async () => {
        jest
          .spyOn(modelMastersRepository, 'findOne')
          .mockResolvedValueOnce(null);

        const mockQuery = {
          rom: '128GB',
          brand: 'Apple',
          model: 'iPhone 11 Plus',
        };

        try {
          await modelMastersService.getModelMaster(
            mockQuery.brand,
            mockQuery.model,
            mockQuery.rom,
            {} as any,
          );
        } catch (error) {
          expect(error).toBeInstanceOf(BaseExceptionService);
          expect((error as BaseExceptionService).code).toBe(
            BASE_EXCEPTIONS.INVALID_MODEL_IDENTIFIERS.code,
          );
        }
      });
    });
    describe('getColorByModelKey', () => {
      it('should return color list of modelKey', async () => {
        jest.spyOn(modelMastersRepository, 'findOne').mockResolvedValueOnce({
          ...mockModelMasters,
          modelMasterColors: [
            { nameTh: 'TH1', nameEn: 'EN1', id: '1' },
            { nameTh: 'TH2', nameEn: 'EN2', id: '2' },
            { nameTh: 'TH3', nameEn: 'EN3', id: '3' },
          ] as ModelMasterColorEntity[],
        });

        const mockQuery = {
          modelKey: 'ModelKey1',
        };

        const result = await modelMastersService.getColorByModelKey(
          'companyA',
          mockQuery,
        );

        expect(result?.length).toEqual(3);
      });
      it('should throw error if not send the modelKey', async () => {
        try {
          await modelMastersService.getColorByModelKey('companyA', {});
        } catch (error) {
          expect(error).toBeInstanceOf(BaseExceptionService);
          expect((error as BaseExceptionService).code).toBe(
            BASE_EXCEPTIONS.BODY_PAYLOAD_INVALID.code,
          );
        }
      });
    });
  });
});
