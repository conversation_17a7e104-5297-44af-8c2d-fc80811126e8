import { getRepositoryToken } from '@nestjs/typeorm';
import { Test } from '@nestjs/testing';
import {
  JobEntity,
  UserEntity,
  CompanyEntity,
  IssueReportEntity,
  ContractEntity,
  EmailActivitiesEntity,
  SystemConfigEntity,
} from '../../src/entities';

import { BaseExceptionModule } from '../../src/base-exceptions/base-exceptions.module';
import { BaseExceptionService } from '../../src/exceptions';
import { BASE_EXCEPTIONS } from '../../src/config';
import { Repository } from 'typeorm';

import { IssueReportsService } from '../../src/shop/issue-reports/issue-reports.service';
import { WithUserContext } from '../../src/interfaces';
import { DateTime } from 'luxon';
import { mockPurchasedJob } from '../mock-data/job';
import { getQueueToken } from '@nestjs/bull';

jest.mock('../../src/utils/general', () => {
  const original = jest.requireActual('../../src/utils/general');
  return {
    ...original,
    getDateFromToday: jest.fn(),
  };
});
const createQueryBuilder = jest.fn(() => ({
  leftJoinAndSelect: createQueryBuilder,
  where: createQueryBuilder,
  andWhere: createQueryBuilder,
  getOne: createQueryBuilder,
}));

describe('IssueReportsService', () => {
  let issueReportsService: IssueReportsService;
  let issueReportRepository: Repository<IssueReportEntity>;
  let jobRepository: Repository<JobEntity>;
  let systemConfigRepository: Repository<SystemConfigEntity>;

  beforeEach(async () => {
    const module = await Test.createTestingModule({
      imports: [BaseExceptionModule.register()],
      providers: [
        IssueReportsService,
        {
          provide: getRepositoryToken(IssueReportEntity),
          useValue: {
            findOne: jest.fn().mockReturnThis(),
            findOneBy: jest.fn().mockReturnThis(),
            insert: jest.fn().mockReturnThis(),
          },
        },
        {
          provide: getRepositoryToken(UserEntity),
          useValue: {
            findOne: jest.fn().mockReturnThis(),
            findOneBy: jest.fn().mockReturnThis(),
          },
        },
        {
          provide: getRepositoryToken(JobEntity),
          useValue: {
            findOne: jest.fn().mockReturnThis(),
            findOneBy: jest.fn().mockReturnThis(),
            createQueryBuilder: createQueryBuilder,
          },
        },
        {
          provide: getRepositoryToken(CompanyEntity),
          useValue: {
            findOne: jest.fn().mockReturnThis(),
            findOneBy: jest.fn().mockReturnThis(),
          },
        },
        {
          provide: getRepositoryToken(EmailActivitiesEntity),
          useValue: {
            findOne: jest.fn().mockReturnThis(),
            findOneBy: jest.fn().mockReturnThis(),
          },
        },
        {
          provide: getRepositoryToken(SystemConfigEntity),
          useValue: {
            findOne: jest.fn().mockReturnThis(),
          },
        },
        {
          provide: getQueueToken('email-queue'),
          useValue: {
            add: jest.fn(),
            process: jest.fn(),
          },
        },
      ],
    }).compile();

    issueReportsService = module.get<IssueReportsService>(IssueReportsService);

    issueReportRepository = module.get<Repository<IssueReportEntity>>(
      getRepositoryToken(IssueReportEntity),
    );

    jobRepository = module.get<Repository<JobEntity>>(
      getRepositoryToken(JobEntity),
    );

    systemConfigRepository = module.get<Repository<SystemConfigEntity>>(
      getRepositoryToken(SystemConfigEntity),
    );
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('buildSearchQuery', () => {
    it.each`
      status        | issueType      | searchId      | expectResult
      ${['status']} | ${'issueType'} | ${'searchId'} | ${`r.branchId = 'branchId' AND r.status IN ('${'status'}') AND r.issueReportType = 'issueType' AND (r.jobId LIKE '%${'searchId'}%' OR r.issueReportId LIKE '%${'searchId'}%')`}
      ${['status']} | ${undefined}   | ${undefined}  | ${`r.branchId = 'branchId' AND r.status IN ('${'status'}')`}
      ${undefined}  | ${'issueType'} | ${undefined}  | ${`r.branchId = 'branchId' AND r.issueReportType = 'issueType'`}
      ${undefined}  | ${undefined}   | ${'searchId'} | ${`r.branchId = 'branchId' AND (r.jobId LIKE '%${'searchId'}%' OR r.issueReportId LIKE '%${'searchId'}%')`}
      ${['status']} | ${'issueType'} | ${undefined}  | ${`r.branchId = 'branchId' AND r.status IN ('${'status'}') AND r.issueReportType = 'issueType'`}
      ${['status']} | ${undefined}   | ${'searchId'} | ${`r.branchId = 'branchId' AND r.status IN ('${'status'}') AND (r.jobId LIKE '%${'searchId'}%' OR r.issueReportId LIKE '%${'searchId'}%')`}
      ${undefined}  | ${'issueType'} | ${'searchId'} | ${`r.branchId = 'branchId' AND r.issueReportType = 'issueType' AND (r.jobId LIKE '%${'searchId'}%' OR r.issueReportId LIKE '%${'searchId'}%')`}
      ${''}         | ${''}          | ${''}         | ${`r.branchId = 'branchId'`}
    `(
      'buildSearchQuery',
      async ({ status, searchId, issueType, expectResult }) => {
        const mockQueryBuilder = {
          andWhere: jest.fn(),
        } as unknown as any;
        const branchId = 'branchId';
        const mockRequest = {
          query: {
            status,
            searchId,
            issueType,
            branchId,
          },
        } as unknown as any;
        const result = issueReportsService.buildSearchQuery(
          mockRequest,
          mockQueryBuilder,
        );
        if (status || searchId || issueType) {
          expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(expectResult);
        }
        expect(result).toBeDefined();
      },
    );
  });

  describe('create Issue Report Email Change', () => {
    const mockUser: WithUserContext = {
      userKey: 'userKey',
      company: 'companyId',
    } as WithUserContext;

    const body = {
      newEmail: 'newEmail',
      imageIdCardUrlPath: 'imageIdCardUrlPath',
      imageEmailCaptureUrlPath: 'imageEmailCaptureUrlPath',
      jobId: '01020202',
      creatorPhoneNumber: '0999992222',
      remark: 'remark',
      customerPhoneNumber: '0888882211',
    };

    const mockMonthYear = DateTime.now().toFormat('yyMM');
    const mockIssueReport = {
      issueReportId: `0000000111-${mockMonthYear}-0001`,
      companyId: 'comId',
    } as IssueReportEntity;

    const contract = {
      customerInfo: {
        thaiName: {
          firstName: 'firstName',
          lastName: 'lastName',
        },
        mobileNumber: 'mobileNumber',
        email: 'email',
      },
    } as ContractEntity;

    const mockJob = {
      ...mockPurchasedJob,
      contract,
    };

    const mockRCCConfig = {
      companyId: 'comId',
      configKey: 'rccEmail',
      data: {
        rccEmail: {
          cc: ['<EMAIL>'],
          to: '<EMAIL>',
        },
      },
    } as SystemConfigEntity;

    const mockBaseUrl = {
      companyId: 'comId',
      configKey: 'baseUrl',
      data: {
        cms: 'cms',
      },
    } as SystemConfigEntity;

    it('should error job not found', async () => {
      jest.spyOn(jobRepository, 'createQueryBuilder').mockReturnValueOnce({
        leftJoinAndSelect: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        getOne: jest.fn().mockResolvedValue(null),
      } as any);
      try {
        await issueReportsService.createEmailChange(body, mockUser, '0000111');
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.NOT_FOUND_DATA.code,
        );
        expect((error as BaseExceptionService).message).toBe(
          BASE_EXCEPTIONS.NOT_FOUND_DATA.message,
        );
        expect((error as BaseExceptionService).data).toBe('Job not found');
      }
    });

    it('should success save', async () => {
      const createdAt = DateTime.now().toISO();
      const insertValue = {
        identifiers: [
          {
            issueReportId: mockIssueReport.issueReportId,
            companyId: mockIssueReport.companyId,
          },
        ],
        generatedMaps: [
          {
            createdAt: createdAt,
            updatedAt: createdAt,
          },
        ],
        raw: [
          {
            created_at: createdAt,
            updated_at: createdAt,
          },
        ],
      };
      jest.spyOn(jobRepository, 'createQueryBuilder').mockReturnValueOnce({
        leftJoinAndSelect: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        getOne: jest.fn().mockResolvedValue(mockPurchasedJob),
      } as any);

      jest
        .spyOn(issueReportRepository, 'findOne')
        .mockResolvedValue(mockIssueReport);

      jest
        .spyOn(issueReportRepository, 'insert')
        .mockResolvedValue(insertValue);

      jest
        .spyOn(systemConfigRepository, 'findOne')
        .mockResolvedValueOnce(mockRCCConfig);
      jest
        .spyOn(systemConfigRepository, 'findOne')
        .mockResolvedValueOnce(mockBaseUrl);
      const result = await issueReportsService.createEmailChange(
        body,
        mockUser,
        '0000111',
      );

      expect(result?.issueReportId).toBe(mockIssueReport.issueReportId);
      expect(result?.companyId).toBe(mockIssueReport.companyId);
    });

    it('should error after save', async () => {
      jest.spyOn(jobRepository, 'createQueryBuilder').mockReturnValueOnce({
        leftJoinAndSelect: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        getOne: jest.fn().mockResolvedValue(mockJob),
      } as any);

      jest
        .spyOn(issueReportRepository, 'findOne')
        .mockResolvedValue(mockIssueReport);

      jest
        .spyOn(issueReportRepository, 'insert')
        .mockRejectedValue(new Error('error'));

      try {
        await issueReportsService.createEmailChange(body, mockUser, '0000111');
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(Error);
      }
    });

    it('create issue report id (not existed)', async () => {
      jest.spyOn(issueReportRepository, 'findOne').mockResolvedValue(null);
      const id = await issueReportsService.getIssueReportId('000111');
      const expectedId = `0000000111-${DateTime.now().toFormat('yyMM')}-0001`;
      expect(id).toBe(expectedId);
    });

    it('create issue report id (existed, different month)', async () => {
      const mockIssueReport = {
        issueReportId: '0000000111-2201-0001',
        companyId: 'comId',
      } as IssueReportEntity;
      jest
        .spyOn(issueReportRepository, 'findOne')
        .mockResolvedValue(mockIssueReport);
      const id = await issueReportsService.getIssueReportId('000111');
      const expectedId = `0000000111-${DateTime.now().toFormat('yyMM')}-0001`;
      expect(id).toBe(expectedId);
    });

    it('create issue report id (existed, same month)', async () => {
      jest
        .spyOn(issueReportRepository, 'findOne')
        .mockResolvedValue(mockIssueReport);
      const id = await issueReportsService.getIssueReportId('000111');
      const expectedId = `0000000111-${mockMonthYear}-0002`;
      expect(id).toBe(expectedId);
    });

    it('send email (no config)', async () => {
      jest.spyOn(systemConfigRepository, 'findOne').mockResolvedValue(null);
      jest.spyOn(systemConfigRepository, 'findOne').mockResolvedValue(null);

      const result = await issueReportsService.sendEmailNoti('000111', 'comId');

      expect(result).toBe(true);
    });
  });
});
