import { Test } from '@nestjs/testing';
import { AES128MessageService } from '../../src/encrypt-decrypt-message/encrypt-decrypt.service';

describe('Aes128MessageService', () => {
  // Setups
  let aes128MessageService: AES128MessageService;

  beforeEach(async () => {
    const module = await Test.createTestingModule({
      providers: [
        {
          provide: AES128MessageService,
          useFactory: () => {
            // Get aes key
            const aesKey = process.env.AES128_KEY;

            // Get aes salt
            const aesSalt = process.env.AES128_SALT;

            // Prevent key or salt invalid
            if (!aesKey || !aesSalt) {
              throw new Error(
                'AES_KEY and AES_SALT must be defined in .env file',
              );
            }

            // Initial aes service
            return new AES128MessageService(aesKey, aesSalt);
          },
        },
      ],
    }).compile();

    aes128MessageService =
      module.get<AES128MessageService>(AES128MessageService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('AES128MessagePipe', () => {
    it('should return encrypt data', () => {
      const result = aes128MessageService.encrypt('test').toString('base64');
      expect(result).not.toEqual(undefined);
    });

    it('should return decrypt data', () => {
      const result = aes128MessageService.decrypt(
        Buffer.from('06juEVp62yVLY7wQ9sxH+6mVUu+0N/M1vAKUmxkRMi0=', 'base64'),
      );
      expect(result).toEqual('test');
    });
  });
});
