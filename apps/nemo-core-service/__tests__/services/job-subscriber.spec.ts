import {
  DataSource,
  EntityManager,
  EntityMetadata,
  QueryRunner,
} from 'typeorm';
import { JobStatus, JobEntity, JobActivitiesEntity } from '../../src/entities';
import { Test } from '@nestjs/testing';
import {
  <PERSON>ChangeSlug,
  JobActivitiesEntitySubscriber,
  JobEntitySubscriber,
} from '../../src/subscriber';
import { getRepositoryToken } from '@nestjs/typeorm';
import { FirebaseService } from '../../src/firebase/firebase.service';
import { BaseExceptionModule } from '../../src/base-exceptions/base-exceptions.module';
import { ColumnMetadata } from 'typeorm/metadata/ColumnMetadata';

describe('Job Subscriber', () => {
  let jobSubscriber: JobEntitySubscriber;
  let jobActivitiesSubscriber: JobActivitiesEntitySubscriber;
  let entityManager: EntityManager;
  let firebaseService: FirebaseService;

  const jobEntity = new JobEntity();
  const jobActivitiesEntity = new JobActivitiesEntity();

  const mockedEvent = {
    entity: jobEntity,
    databaseEntity: {} as JobEntity,
    manager: {} as EntityManager,
    updatedColumns: [],
    connection: {} as DataSource,
    queryRunner: {} as QueryRunner,
    metadata: {} as EntityMetadata,
    updatedRelations: [],
  };

  const mockedJobActivitiesEvent = {
    ...mockedEvent,
    entity: jobActivitiesEntity,
    databaseEntity: {} as JobActivitiesEntity,
  };

  const mockSave = jest.fn().mockReturnThis();

  beforeAll(async () => {
    const module = await Test.createTestingModule({
      imports: [BaseExceptionModule.register()],
      providers: [
        {
          provide: DataSource,
          useValue: {
            subscribers: {
              push: jest.fn().mockReturnThis(),
            },
          },
        },
        {
          provide: FirebaseService,
          useValue: {
            setData: jest.fn().mockReturnThis(),
            addData: jest.fn().mockReturnThis(),
          },
        },
        {
          provide: EntityManager,
          useValue: {
            getRepository: jest.fn().mockImplementation(() => {
              return { save: mockSave };
            }),
            findOne: jest.fn(() => {
              return {
                roles: [
                  {
                    branchId: 'test',
                    role: ['Sale', 'Manager'],
                  },
                ],
              };
            }),
          },
        },
        {
          provide: getRepositoryToken(JobActivitiesEntity),
          useValue: {
            findOne: jest.fn().mockReturnThis(),
          },
        },
        JobEntitySubscriber,
        JobActivitiesEntitySubscriber,
      ],
    }).compile();

    jobSubscriber = module.get<JobEntitySubscriber>(JobEntitySubscriber);
    jobActivitiesSubscriber = module.get<JobActivitiesEntitySubscriber>(
      JobActivitiesEntitySubscriber,
    );
    entityManager = module.get<EntityManager>(EntityManager);
    firebaseService = module.get<FirebaseService>(FirebaseService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('Before update jobSubscriber', () => {
    it('Quote Requested', async () => {
      let jobEntity = new JobEntity();
      jobEntity.status = JobStatus.QUOTE_REQUESTED;
      jobEntity.requestedAt = new Date();
      jobEntity.checkListValues = {
        product_images: {
          new: [{}],
        },
      };
      await jobSubscriber.beforeUpdate({
        ...mockedEvent,
        entity: jobEntity,
        databaseEntity: {
          requestedAt: new Date('1996'),
        } as JobEntity,
        manager: entityManager as EntityManager,
        updatedColumns: [
          { propertyAliasName: 'checkListValues' } as ColumnMetadata,
        ],
      });
      expect(entityManager.getRepository).toHaveBeenCalled();
      expect(mockSave).toHaveBeenCalled();
    });
    it.each([undefined, 'test'])(
      `Price Processing shopUserName: ( %s )`,
      async (shopUserName) => {
        let jobEntity = new JobEntity();
        jobEntity.status = JobStatus.ESTIMATE_PRICE_PROCESSING;
        jobEntity.checkListValues = {
          product_images: {
            new: [{}],
          },
          product_additional_images: {
            new: [{}],
          },
        };
        jobEntity.shopUserName = shopUserName;
        await jobSubscriber.beforeUpdate({
          ...mockedEvent,
          entity: jobEntity,
          databaseEntity: {
            checkListValues: {
              product_images: {
                old: [{}],
              },
            },
          } as JobEntity,
          manager: entityManager as EntityManager,
          updatedColumns: [
            { propertyAliasName: 'checkListValues' } as ColumnMetadata,
          ],
        });
        expect(entityManager.getRepository).toHaveBeenCalled();
        expect(mockSave).toHaveBeenCalled();
      },
    );
    it('Assigned', async () => {
      let jobEntity = new JobEntity();
      jobEntity.status = JobStatus.ESTIMATE_PRICE_PROCESSING;
      jobEntity.assignedAt = new Date();
      await jobSubscriber.beforeUpdate({
        ...mockedEvent,
        entity: jobEntity,
        databaseEntity: {
          assignedAt: new Date('1996'),
        } as JobEntity,
        manager: entityManager as EntityManager,
        updatedColumns: [
          { propertyAliasName: 'checkListValues' } as ColumnMetadata,
        ],
      });
      expect(entityManager.getRepository).toHaveBeenCalled();
      expect(mockSave).toHaveBeenCalled();
    });
    it('Commented', async () => {
      let jobEntity = new JobEntity();
      jobEntity.status = JobStatus.ESTIMATE_PRICE_PROCESSING;
      jobEntity.isAdditionalCheckList = true;
      await jobSubscriber.beforeUpdate({
        ...mockedEvent,
        entity: jobEntity,
        databaseEntity: {
          isAdditionalCheckList: false,
        } as JobEntity,
        manager: entityManager as EntityManager,
        updatedColumns: [
          { propertyAliasName: 'checkListValues' } as ColumnMetadata,
        ],
      });
      expect(mockSave).not.toHaveBeenCalled();
    });
    it.each([
      [JobStatus.PRICE_ESTIMATED, JobStatus.IDENTITY_REQUESTED],
      [JobStatus.PRICE_ESTIMATED, JobStatus.IDENTITY_VERIFIED],
      [JobStatus.IDENTITY_REQUESTED, JobStatus.IDENTITY_VERIFIED],
      [JobStatus.IDENTITY_REQUESTED, JobStatus.IDENTITY_REJECTED],
    ])(`Should status to ( %s ) classified`, async (statusFrom, statusTo) => {
      let jobEntity = new JobEntity();
      jobEntity.status = statusTo;
      jobEntity.isAdditionalCheckList = true;
      await jobSubscriber.beforeUpdate({
        ...mockedEvent,
        entity: jobEntity,
        databaseEntity: {
          status: statusFrom,
        } as JobEntity,
        manager: entityManager as EntityManager,
      });
      expect(entityManager.getRepository).toHaveBeenCalled();
      expect(mockSave).toHaveBeenCalled();
    });
  });

  describe('After update + listenTo jobSubscriber', () => {
    it('afterUpdate', async () => {
      let jobEntity = new JobEntity();
      await jobSubscriber.afterUpdate({
        ...mockedEvent,
        entity: jobEntity,
        manager: entityManager as EntityManager,
      });
      expect(firebaseService.setData).toHaveBeenCalled();
    });
    it('listenTo', async () => {
      const result = jobSubscriber.listenTo();
      expect(result).toBe(JobEntity);
    });
  });

  describe('After insert jobAcitivitiesSubscriber', () => {
    it.each([
      [DataChangeSlug.IDENTITY_REQUESTED, true],
      [DataChangeSlug.IDENTITY_VERIFIED, true],
      [DataChangeSlug.IDENTITY_REJECTED, true],
      [DataChangeSlug.ADMIN_ASSIGNED, false],
      [DataChangeSlug.COMMENTED, false],
      [DataChangeSlug.ADDITIONAL_IMAGE, false],
      [DataChangeSlug.ADMIN_COMMENTED, false],
      [JobStatus.PRICE_ESTIMATED, false],
      [DataChangeSlug.JOB_REQUESTED, false],
      [DataChangeSlug.IDENTITY_REQUESTED, false],
      [DataChangeSlug.IDENTITY_VERIFIED_DIPCHIP, false],
      [DataChangeSlug.IDENTITY_VERIFIED, false],
      [DataChangeSlug.IDENTITY_REJECTED, false],
    ])(
      `DataChangeSlug or status ( %s )`,
      async (dataChangeSlugOrStatus, isRoleManager) => {
        let jobActivitiesEntity = new JobActivitiesEntity();
        jobActivitiesEntity.detail = {
          branchId: 'test',
          summary: 'test',
          ...(dataChangeSlugOrStatus === JobStatus.PRICE_ESTIMATED
            ? { status: dataChangeSlugOrStatus, dataChangeSlug: null }
            : { status: 'test', dataChangeSlug: dataChangeSlugOrStatus }),
        };
        if (!isRoleManager) {
          jest.spyOn(entityManager, 'findOne').mockResolvedValueOnce(null);
        }
        await jobActivitiesSubscriber.afterInsert({
          ...mockedJobActivitiesEvent,
          entity: jobActivitiesEntity,
          manager: entityManager as EntityManager,
        });
        if (
          !isRoleManager &&
          dataChangeSlugOrStatus !== DataChangeSlug.IDENTITY_VERIFIED_DIPCHIP
        ) {
          expect(firebaseService.addData).toHaveBeenCalled();
        }
      },
    );
    it('listenTo', async () => {
      const result = jobActivitiesSubscriber.listenTo();
      expect(result).toBe(JobActivitiesEntity);
    });
  });
});
