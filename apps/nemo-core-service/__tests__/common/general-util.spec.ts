import {
  convertArrayToSQLString,
  getUTCDate,
  getDateOnlyString,
  getDateUTCFromYMDArray,
  getDateFromToday,
  getDateIntervalByMonth,
  getCountToDateDays,
  convertToThaiDateTime,
  extractStringInCharOpenClose,
  tranformRomToNum,
  getLanguageFromHeader,
  languageConvert,
  validatePhoneNumber,
  validateEmail,
  ILanguage,
  formatErrorLog,
  createRandomStringCharNum,
  getThDateObject,
  removeEnterFromExcelField,
  getStatusNumber,
  convertTextStartToEndDate,
  formatMoney,
  convertToThaiDate,
} from '../../src/utils/general';

describe('General Utils', () => {
  beforeAll(() => {
    jest.useFakeTimers();
    jest.setSystemTime(new Date(2024, 3, 12, 0, 0, 0));
  });

  afterAll(() => {
    jest.useRealTimers();
  });

  describe('languageConvert', () => {
    it.each([
      ['en', 'LANG', 'EN'],
      ['th', 'LANG', 'TH'],
      ['en', 'Lang', 'En'],
      ['th', 'Lang', 'Th'],
    ])(
      `should convert lang %s type %s to %s`,
      (lang: string, type: string, expectResult: string) => {
        const result = languageConvert(
          lang as ILanguage,
          type as 'Lang' | 'LANG',
        );
        expect(result).toEqual(expectResult);
      },
    );
  });

  describe('getLanguageFromHeader', () => {
    it.each([
      ['th', 'th'],
      ['en', 'en'],
      ['ch', 'en'],
    ])(
      `should convert header with accept-language %s to %s`,
      (acceptLanguage: string, expectResult) => {
        const result = getLanguageFromHeader({
          'accept-language': acceptLanguage,
        });
        expect(result).toEqual(expectResult);
      },
    );
  });

  describe('convertArrayToSQLString', () => {
    it.each([
      [['a', 'b', 'c'], `'a', 'b', 'c'`],
      [['a'], `'a'`],
      [[], ''],
    ])(
      `should convert array %s to %s`,
      (arraySource: string[], expectResult) => {
        const result = convertArrayToSQLString(arraySource);
        expect(result).toEqual(expectResult);
      },
    );
  });

  describe('removeEnterFromExcelField', () => {
    it(`should convert correctly`, () => {
      const result = removeEnterFromExcelField('mock test');
      expect(result).toEqual('mock test');
    });
  });
  describe('getStatusNumber', () => {
    it.each([
      ['40_aaa', 40],
      ['04_bbb', 4],
      ['00_ccc', 0],
      ['', 0],
    ])(`should convert %s to %s correctly`, (value, resultExpect) => {
      const result = getStatusNumber(value);
      expect(result).toEqual(resultExpect);
    });
  });

  describe('getUTCDate', () => {
    it.each([
      [2024, 4, 1, '2024-04-01T00:00:00.000Z'],
      [2024, 3, 31, '2024-03-31T00:00:00.000Z'],
    ])(
      `should convert date %s to UTC date %s %s %s`,
      (y, m, d, expectResult) => {
        const result = getUTCDate(y, m - 1, d);
        expect(result.toISOString()).toEqual(expectResult);
      },
    );
  });

  describe('getDateOnlyString', () => {
    it.each([
      ['2024-03-31T17:00:00.000Z', '2024-03-31'],
      ['2024-03-31T00:00:00.000Z', '2024-03-31'],
    ])(`should convert date %s to %s`, (date, expectResult) => {
      const result = getDateOnlyString(new Date(date));
      expect(result).toEqual(expectResult);
    });
  });

  describe('getDateUTCFromYMDArray', () => {
    it.each([
      [[2024, 4, 1], '2024-04-01T00:00:00.000Z'],
      [[2024, 3, 31], '2024-03-31T00:00:00.000Z'],
    ])(`should convert array %s to date utc %s`, (array, dateResult) => {
      const result = getDateUTCFromYMDArray(array as [number, number, number]);
      expect(result.toISOString()).toEqual(dateResult);
    });
  });

  describe('getDateFromToday', () => {
    it.each([
      ['today', undefined, [2024, 4, 12, '2024-04-12']],
      ['tomorrow', 1, [2024, 4, 13, '2024-04-13']],
      ['yesterday', -1, [2024, 4, 11, '2024-04-11']],
      ['13 days before', -13, [2024, 3, 30, '2024-03-30']],
    ])(
      `should get date - %s [ %s ] - correctly as %s`,
      (caseDetail, addDate, resultDate) => {
        const result = getDateFromToday(addDate);
        expect(result).toEqual(resultDate);
      },
    );
  });

  describe('getDateFromToday', () => {
    it.each([
      ['2024-04-01T00:00:00.000Z', '2024-04-01T07:00:00.000+07:00'],
      ['2024-04-01T17:00:00.000Z', '2024-04-02T00:00:00.000+07:00'],
    ])(`should convert date %s to thai date %s`, (dateTarget, resultDate) => {
      const result = convertToThaiDateTime(new Date(dateTarget)).toISO();
      expect(result).toEqual(resultDate);
    });
  });

  describe('getDateIntervalByMonth', () => {
    it.each([
      [2024, 4, '2024-04-01', '2024-04-30'],
      [2023, 2, '2023-02-01', '2023-02-28'],
    ])(`should %s %s get date start %s end %s`, (year, month, start, end) => {
      const result = getDateIntervalByMonth({ year, month });
      expect(result).toEqual([start, end]);
    });
  });

  describe('getThDateObject', () => {
    it(`should convert correctly`, () => {
      const result = getThDateObject(new Date('2024-04-01T00:00:00.000Z'));
      expect(result).toEqual({
        year: '2567',
        dmy: '1 เม.ย. 2567',
        dateMonth: '1 เม.ย.',
      });
    });
  });

  describe('convertToThaiDate', () => {
    it(`should convert correctly`, () => {
      const result = convertToThaiDate(new Date('2024-04-01T00:00:00.000Z'));
      expect(result).toEqual(new Date('2024-04-01T00:00:00.000Z'));
    });
  });

  describe('convertTextStartToEndDate', () => {
    it.each([
      [
        '2024-04-01T00:00:00.000Z',
        '2024-05-01T00:00:00.000Z',
        '1 เม.ย. - 1 พ.ค. 2567',
      ],
      [
        '2024-04-01T00:00:00.000Z',
        '2025-03-01T00:00:00.000Z',
        '1 เม.ย. 2567 - 1 มี.ค. 2568',
      ],
    ])(`should pass start %s end %s result %s`, (start, end, expectResult) => {
      const result = convertTextStartToEndDate(new Date(start), new Date(end));
      expect(expectResult).toEqual(result);
    });
  });

  describe('getCountToDateDays', () => {
    it('should count collectly', () => {
      const result = getCountToDateDays(
        new Date(2024, 2, 1),
        new Date(2024, 2, 31),
      );
      expect(result).toEqual(30);
    });
  });

  describe('tranformRomToNum', () => {
    it('should return rom correctly', () => {
      const result = tranformRomToNum('256GB');
      expect(result).toEqual(256);
    });
    it('should return rom(TB) correctly', () => {
      const result = tranformRomToNum('1TB');
      expect(result).toEqual(1000);
    });
    it('should return rom correctly (lower case)', () => {
      const result = tranformRomToNum('256gb');
      expect(result).toEqual(256);
    });
    it('should return rom(TB) correctly (lower case)', () => {
      const result = tranformRomToNum('1tb');
      expect(result).toEqual(1000);
    });
    it('Invalid rom string should return null', () => {
      const result = tranformRomToNum('1terabyte');
      expect(result).toBeNull();
    });
  });

  describe('extractStringInCharOpenClose', () => {
    it.each([
      ['[nomal case] No bracket', '(', 'ทดสอบจบการทดสอบ', ')', []],
      [
        '[nomal case] 1 bracket',
        '(',
        'ทดสอบ (find 1) จบการทดสอบ',
        ')',
        ['find 1'],
      ],
      [
        '[nomal case] 2 bracket',
        '(',
        'ทดสอบ (find 1)(find 2) จบการทดสอบ',
        ')',
        ['find 1', 'find 2'],
      ],
      [
        '[nomal case] 2 {xxx}',
        '{',
        'ทดสอบ {find 1}{find 2} จบการทดสอบ',
        '}',
        ['find 1', 'find 2'],
      ],
      [
        '[? case] bracket with no close',
        '(',
        'ทดสอบ (find 1 จบการทดสอบ',
        ')',
        [],
      ],
      [
        '[? case] second bracket no open',
        '(',
        'ทดสอบ (find 1) find 2) จบการทดสอบ',
        ')',
        ['find 1'],
      ],
      [
        '[? case] same symbol',
        '|',
        'ทดสอบ |find 1|find 2| จบการทดสอบ',
        '|',
        ['find 1', 'find 2'],
      ],
    ])(
      `should correctly return %s`,
      (caseDetail, open, text, close, resultExpect) => {
        const result = extractStringInCharOpenClose(text, open, close);
        expect(result).toEqual(resultExpect);
      },
    );
  });

  describe('validatePhoneNumber', () => {
    it.each([
      ['0123456789', true],
      [1234567890, false],
      ['+66123456789', true],
    ])(`should phone number %s pass %s`, (phoneNumber, expectResult) => {
      const result = validatePhoneNumber(phoneNumber);
      expect(result).toEqual(expectResult);
    });
  });

  describe('validateEmail', () => {
    it.each([
      ['<EMAIL>', true],
      ['abcd', false],
      ['a1234@hot', false],
    ])(`should email %s pass %s`, (email, exceptResult) => {
      const result = validateEmail(email);
      expect(result).toEqual(exceptResult);
    });
  });

  describe('formatErrorLog', () => {
    it.each([
      [
        400,
        'ERROR',
        100000,
        'Error type: all, status: 400, key: ERROR, code: 100000',
      ],
      [
        '400',
        { test: 'ERROR' },
        '100000',
        'Error type: all, status: 400, key: {"test":"ERROR"}, code: 100000',
      ],
    ])(
      `should correctly return formatted string status %s key %s code %s`,
      (status, key, code, formattedString) => {
        const result = formatErrorLog('all', status, key, code);
        expect(result).toEqual(formattedString);
      },
    );
  });

  describe('createRandomStringCharNum', () => {
    it('should correctly return', () => {
      const result = createRandomStringCharNum(3);
      expect(result.length).toEqual(3);
    });
  });
});

describe('formatMoney', () => {
  it.each([
    [100, 2, '100.00'],
    [1000, 2, '1,000.00'],
    [1000, 0, '1,000'],
  ])(
    `should number %s digit %s translate to %s`,
    (number, digit, expectResult) => {
      const result = formatMoney(number, digit);
      expect(result).toEqual(expectResult);
    },
  );
});
