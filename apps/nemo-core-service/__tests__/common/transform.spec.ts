import { ColumnDecimalTransformer } from '../../src/entities';

describe('Utils', () => {
  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('Transform decimal number', () => {
    it('Transform decimal to function', () => {
      const transformDecimal = new ColumnDecimalTransformer();

      const result = transformDecimal.to(1.1);

      expect(result).toEqual(1.1);
    });

    it('Transform decimal from function', () => {
      const transformDecimal = new ColumnDecimalTransformer();

      const result = transformDecimal.from('1.1');

      expect(result).toEqual(1.1);
    });
  });
});
