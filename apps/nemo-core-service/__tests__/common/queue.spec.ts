import { Test } from '@nestjs/testing';
import { Repository } from 'typeorm';
import { getRepositoryToken } from '@nestjs/typeorm';
import { JobEntity } from '../../src/entities';
import { reCalculateProduct } from '../../src/utils/job/queue';

describe('Queue', () => {
  let jobsRepository: Repository<JobEntity>;

  beforeEach(async () => {
    const module = await Test.createTestingModule({
      imports: [],
      providers: [
        {
          provide: getRepositoryToken(JobEntity),
          useValue: {
            find: jest.fn(),
          },
        },
      ],
    }).compile();

    jobsRepository = module.get<Repository<JobEntity>>(
      getRepositoryToken(JobEntity),
    );
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('re calculate product', () => {
    let mockQueue;
    beforeEach(() => {
      mockQueue = { add: jest.fn(), empty: jest.fn() };
    });
    it.each([
      { jobList: [] },
      { jobList: [{ jobId: 'TestId1' }] },
      { jobList: [{ jobId: 'TestId1' }, { jobId: 'TestId2' }] },
    ])('valid data', async ({ jobList }) => {
      jest
        .spyOn(jobsRepository, 'find')
        .mockResolvedValueOnce(jobList as unknown as JobEntity[]);

      await reCalculateProduct({}, mockQueue, jobsRepository);
      expect(mockQueue.empty).toHaveBeenCalledTimes(1);
      expect(mockQueue.add).toHaveBeenCalledTimes(jobList.length);
      jobList.forEach((job, index) => {
        expect(mockQueue.add).toHaveBeenNthCalledWith(
          index + 1,
          're-calculate-product',
          {
            jobId: job.jobId,
            costConfig: {},
          },
        );
      });
    });
  });
});
