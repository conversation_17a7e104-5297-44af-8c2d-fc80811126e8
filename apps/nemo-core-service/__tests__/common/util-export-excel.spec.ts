import {
  convertSlugToHeaderColumn,
  headerBySlugAllJobStatus,
} from '../../src/utils/job/exportJobReport';

describe('General Utils', () => {
  describe('convertSlugToHeaderColumn', () => {
    const headerBySlugAllJobStatusToArray = Object.keys(
      headerBySlugAllJobStatus,
    ).map((key) => [key, headerBySlugAllJobStatus[key]]);

    // console.log(headerBySlugAllJobStatusToArray);

    const getMockByIndex = (list: number[]) => {
      const slugList: any[] = [];
      const result: any = {};
      list.forEach((ind: number) => {
        slugList.push(headerBySlugAllJobStatusToArray[ind][0]);
        result[headerBySlugAllJobStatusToArray[ind][0]] =
          headerBySlugAllJobStatusToArray[ind][1];
      });
      return { slugList, result };
    };

    const mockCase3First = getMockByIndex([0, 1, 2]);
    const mockCaseIndex2Only = getMockByIndex([2]);
    it.each([
      [mockCase3First.slugList, mockCase3First.result],
      [mockCaseIndex2Only.slugList, mockCaseIndex2Only.result],
      [[], {}],
    ])(`should convert array %s to %s`, (arraySource, expectResult) => {
      const result = convertSlugToHeaderColumn({
        slugList: arraySource,
        tableType: 'ALL_STATUS_JOB',
      });
      expect(result).toEqual(expectResult);
    });
  });
});
