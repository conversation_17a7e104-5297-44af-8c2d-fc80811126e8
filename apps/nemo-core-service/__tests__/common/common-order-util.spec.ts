import {
  getMockCommonOrderResponseByLang,
  mockColor,
  mockEstimationActivity,
  mockModelKey,
  mockModelMaster,
  mockModelMasterNullable,
  mockStore,
  mockStoreId,
  mockStoreNullable,
  mockStoreAddressNullable,
  getMockMergeAddressResponseByLang,
  getMockReponseValue,
  mockStoreAddress,
  generateQuestionSelectionFunction,
  mockModelMasterFunctionsForGetFunctionFromModelMasterFn,
  mockResultGetFunctionFromModelMasterFn,
  mockModelMasterForNotPurchasable,
} from '../mock-data/common-order';
import {
  EstimationActivitiesEntity,
  ModelChecklistEntity,
  ModelMasterFunctionEntity,
} from '../../src/entities';
import {
  convertOrderResponse,
  getQuestionOptionKeyCond,
} from '../../src/utils/common-order';
import * as commonOrderUtil from '../../src/utils/common-order';
import { ILanguage } from '../../src/utils/general';

describe('getQuestionOptionKeyCond', () => {
  it.each([
    [false, ['a'], 'select'],
    [false, ['a', 'b'], 'select'],
    [false, [], 'not_select'],
    [true, ['a'], 'skip'],
    [true, [], 'skip'],
  ])(
    `should pass if isSkip %s answerIds has value %s expect %s`,
    (isSkip: boolean, answerIds: string[], expectResult) => {
      const result = getQuestionOptionKeyCond(isSkip, answerIds);
      expect(result).toEqual(expectResult);
    },
  );
});

describe('case store address validate', () => {
  it.each([
    ['en', false],
    ['th', false],
    ['en', true],
    ['th', true],
  ])(
    `should pass if mock language: %s, nulllAdress: %s`,
    (language, nullAdress) => {
      let estimationActivity = {
        ...mockEstimationActivity,
      };
      const acceptLanguage = language as ILanguage;
      let commonOrderResponse: any = {
        ...getMockCommonOrderResponseByLang(acceptLanguage),
      };

      commonOrderResponse.selectedStore = {
        id: mockStoreId,
        name: null,
        imageUrl: null,
        latitude: null,
        longitude: null,
        distance: null,
      };

      commonOrderResponse.selectedStore.address = nullAdress
        ? null
        : getMockMergeAddressResponseByLang(acceptLanguage);
      commonOrderResponse.selectedStore.imageUrl = getMockReponseValue(
        acceptLanguage,
        'imageUrl',
        nullAdress,
      );
      commonOrderResponse.selectedStore.latitude = getMockReponseValue(
        acceptLanguage,
        'lat',
        nullAdress,
      );
      commonOrderResponse.selectedStore.longitude = getMockReponseValue(
        acceptLanguage,
        'long',
        nullAdress,
      );
      commonOrderResponse.selectedStore.name = getMockReponseValue(
        acceptLanguage,
        'name',
        nullAdress,
      );

      const result = convertOrderResponse({
        estimationActivity: estimationActivity,
        language: acceptLanguage,
        modelMaster: mockModelMaster,
        color: mockColor,
        store: nullAdress ? mockStoreAddressNullable : mockStoreAddress,
      });

      expect(result).toEqual(commonOrderResponse);
    },
  );
});

describe('convertOrderResponse', () => {
  beforeEach(() => {
    jest
      .spyOn(commonOrderUtil, 'mergeAddress')
      .mockReturnValue('mock-address-merge');
  });
  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('case all in estimateActivity except price validate', () => {
    // fn นี้ไม่ได้เอา มีไหม id vs entity store/color ที่ส่งเข้ามา มาเช็คให้ว่าถูกไหม เพียงแปลงสิ่งที่เข้ามาโดยเชื่อว่าส่ง query มาถูก
    it.each([
      ['en', true, true, false, null],
      ['th', true, true, false, null],
      ['th', true, true, true, null],
      ['th', false, true, true, null],
      ['th', true, false, true, null],
      ['th', true, true, true, 'firstName'],
      ['th', true, true, true, 'phoneNumber'],
    ])(
      `should pass if language %s has color %s has store %s clear all optional %s except clear %s`,
      (language, hasColor, hasStore, clearOptional, exceptClear) => {
        let estimationActivity = { ...mockEstimationActivity };

        let commonOrderResponse: any = {
          ...getMockCommonOrderResponseByLang(language as ILanguage),
        };

        if (!hasColor) {
          commonOrderResponse.color = null;
        }
        if (!hasStore) {
          commonOrderResponse.selectedStore = null;
        } else {
          commonOrderResponse.selectedStore.address = 'mock-address-merge';
        }
        if (clearOptional) {
          estimationActivity = {
            ...mockEstimationActivity,
            deviceId: '',
            imei1: '',
            imei2: '',
            firstName: '',
            lastName: '',
            email: '',
            phoneNumber: '',
          };
          if (exceptClear) {
            estimationActivity[exceptClear] =
              mockEstimationActivity[exceptClear];
          }
          commonOrderResponse = {
            ...commonOrderResponse,
            deviceId: null,
            imei1: null,
            imei2: null,
          };
          if (
            exceptClear &&
            ['phoneNumber', 'email', 'firstName', 'lastName'].includes(
              exceptClear,
            )
          ) {
            const exceptclearProp = {
              firstName: 'firstname',
              lastName: 'lastname',
              email: 'email',
              phoneNumber: 'phoneNumber',
            };
            commonOrderResponse.customerInfo = {
              firstname: null,
              lastname: null,
              email: null,
              phoneNumber: null,
              [exceptclearProp[exceptClear]]:
                mockEstimationActivity[exceptClear],
            };
          } else {
            commonOrderResponse.customerInfo = null;
          }
        }

        const result = convertOrderResponse({
          estimationActivity: estimationActivity,
          language: language as ILanguage,
          modelMaster: mockModelMaster,
          color: hasColor ? mockColor : undefined,
          store: hasStore ? mockStore : undefined,
        });
        expect(result).toEqual(commonOrderResponse);
      },
    );
  });

  describe('case estimatePrice validate', () => {
    it.each([
      [1500, 1500],
      [0, 0],
      [null, null],
      [undefined, null],
    ])(
      `should pass if estimatePrice %s currentNet expect %s`,
      (price, expectPrice) => {
        let estimationActivity = {
          ...mockEstimationActivity,
          estimatedPrice: price,
        } as EstimationActivitiesEntity;

        let commonOrderResponse: any = {
          ...getMockCommonOrderResponseByLang('en'),
          currentNet: expectPrice,
        };

        commonOrderResponse.selectedStore.address = 'mock-address-merge';

        const result = convertOrderResponse({
          estimationActivity: estimationActivity,
          language: 'en',
          modelMaster: mockModelMaster,
          color: mockColor,
          store: mockStore,
        });

        expect(result).toEqual(commonOrderResponse);
      },
    );
  });

  describe('case modelMaster validate', () => {
    it.each([[true], [false]])(
      `should pass if mock optional modelMaster null %s `,
      (optionalModelMaster) => {
        let estimationActivity = {
          ...mockEstimationActivity,
        };

        let commonOrderResponse: any = {
          ...getMockCommonOrderResponseByLang('en'),
        };

        if (optionalModelMaster) {
          commonOrderResponse.device = {
            id: null,
            deviceImageUrl: null,
            brand: null,
            name: null,
            sku: {
              id: mockModelKey,
              storage: null,
            },
            isPurchasable: true,
          };

          commonOrderResponse.selectedStore.address = 'mock-address-merge';

          const result = convertOrderResponse({
            estimationActivity: estimationActivity,
            language: 'en',
            modelMaster: optionalModelMaster
              ? mockModelMasterNullable
              : mockModelMaster,
            color: mockColor,
            store: mockStore,
          });

          expect(result).toEqual(commonOrderResponse);
        }
      },
    );
  });

  describe('case store validate', () => {
    it.each([[true], [false]])(
      `should pass if mock optional store null %s `,
      (optionalStore) => {
        let estimationActivity = {
          ...mockEstimationActivity,
        };

        let commonOrderResponse: any = {
          ...getMockCommonOrderResponseByLang('en'),
        };

        if (optionalStore) {
          commonOrderResponse.selectedStore = {
            id: mockStoreId,
            name: null,
            imageUrl: null,
            latitude: null,
            longitude: null,
            distance: null,
          };

          commonOrderResponse.selectedStore.address = 'mock-address-merge';

          const result = convertOrderResponse({
            estimationActivity: estimationActivity,
            language: 'en',
            modelMaster: mockModelMaster,
            color: mockColor,
            store: optionalStore ? mockStoreNullable : mockStore,
          });

          expect(result).toEqual(commonOrderResponse);
        }
      },
    );
  });

  describe('case all in estimateActivity except price validate', () => {
    // fn นี้ไม่ได้เอา มีไหม id vs entity store/color ที่ส่งเข้ามา มาเช็คให้ว่าถูกไหม เพียงแปลงสิ่งที่เข้ามาโดยเชื่อว่าส่ง query มาถูก
    it.each([
      [false, true],
      [true, false],
    ])(
      `should pass if modelKey %s isPurchasable %s`,
      (defaultModelKey, isPurchasable) => {
        let estimationActivity = { ...mockEstimationActivity };

        let commonOrderResponse: any = {
          ...getMockCommonOrderResponseByLang('en'),
        };

        if (defaultModelKey) {
          const device = {
            ...commonOrderResponse.device,
            brand: '',
            name: '',
            sku: { ...commonOrderResponse.device.sku, id: 'default' },
            isPurchasable: false,
          };
          commonOrderResponse.device = device;
        }

        commonOrderResponse.selectedStore.address = 'mock-address-merge';

        const result = convertOrderResponse({
          estimationActivity: estimationActivity,
          language: 'en',
          modelMaster: isPurchasable
            ? mockModelMaster
            : mockModelMasterForNotPurchasable,
          color: mockColor,
          store: mockStore,
        });
        expect(result).toEqual(commonOrderResponse);
      },
    );
  });
});

describe('getFunctionFromModelMasterFn', () => {
  describe('shoud correct translate', () => {
    it(`shoud correct translate`, () => {
      const result = commonOrderUtil.getFunctionFromModelMasterFn(
        mockModelMasterFunctionsForGetFunctionFromModelMasterFn,
      );

      expect(result.moduleMasterFunctionByFunction).toEqual(
        mockResultGetFunctionFromModelMasterFn().moduleMasterFunctionByFunction,
      );
      expect(result.questionMasterFunctionByFunction).toEqual(
        mockResultGetFunctionFromModelMasterFn()
          .questionMasterFunctionByFunction,
      );
      expect(result.allModuleChecklistIds).toEqual(
        mockResultGetFunctionFromModelMasterFn().allModuleChecklistIds,
      );
      expect(result.requireModuleChecklistIds).toEqual(
        mockResultGetFunctionFromModelMasterFn().requireModuleChecklistIds,
      );
      expect(result.allQuestionChecklistIds).toEqual(
        mockResultGetFunctionFromModelMasterFn().allQuestionChecklistIds,
      );
      expect(result.requireQuestionChecklistIds).toEqual(
        mockResultGetFunctionFromModelMasterFn().requireQuestionChecklistIds,
      );
      expect(result.questionMasterFunctionPointer).toEqual(
        mockResultGetFunctionFromModelMasterFn().questionMasterFunctionPointer,
      );
      expect(result.moduleMasterFunctionPointer).toEqual(
        mockResultGetFunctionFromModelMasterFn().moduleMasterFunctionPointer,
      );
    });
  });

  describe('shoud correct translate (will not occur case)', () => {
    it(`shoud correct translate`, () => {
      const selectionIsRequiredUndefined =
        generateQuestionSelectionFunction('2')[0];

      const { isRequired, ...newChecklist } =
        selectionIsRequiredUndefined.modelChecklist || {};
      selectionIsRequiredUndefined.modelChecklist =
        newChecklist as ModelChecklistEntity;

      const mockModelMasterFunctions = [
        selectionIsRequiredUndefined,
        {} as ModelMasterFunctionEntity,
      ];

      const result = commonOrderUtil.getFunctionFromModelMasterFn(
        mockModelMasterFunctions,
      );

      expect(result.allModuleChecklistIds).toEqual([]);
      expect(result.requireModuleChecklistIds).toEqual(new Set([]));
      expect(result.allQuestionChecklistIds).toEqual(['question-selection-2']);
      expect(result.requireQuestionChecklistIds).toEqual(new Set([]));
    });
  });
});
