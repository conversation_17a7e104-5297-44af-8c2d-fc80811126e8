import { JobEntity, QCStatus, RepairHx } from '../../src/entities';
import {
  calculateProductPrices,
  getCostPrice,
  getMargin,
  getMarketingCost,
  getOperationCost,
  getRepairCost,
  isNumber,
} from '../../src/utils/job/calculateProduct';

describe('Calculate Product Utils', () => {
  describe('isNumber', () => {
    it('should return true when receive number', () => {
      expect(isNumber(0)).toBeTruthy();
      expect(isNumber(10000000)).toBeTruthy();
      expect(isNumber(100.0)).toBeTruthy();
    });
    it('should return false when not receive number', () => {
      expect(isNumber('0')).toBeFalsy();
      expect(isNumber('')).toBeFalsy();
      expect(isNumber('a')).toBeFalsy();
    });
  });
  describe('getMargin', () => {
    it('normal', () => {
      const result = getMargin(100, 200);
      expect(result.baht).toBe(100);
      expect(result.percent).toBe(100);
    });
    it('negative', () => {
      const result = getMargin(200, 100);
      expect(result.baht).toBe(-100);
      expect(result.percent).toBe(-50);
    });
    it('zero', () => {
      const result = getMargin(100, 100);
      expect(result.baht).toBe(0);
      expect(result.percent).toBe(0);
    });
    it('exactly 2 decimal place', () => {
      const result1 = getMargin(30, 40);
      expect(result1.baht).toBe(10);
      expect(result1.percent).toBe(33.33);
      const result2 = getMargin(30, 20);
      expect(result2.baht).toBe(-10);
      expect(result2.percent).toBe(-33.33);
    });
  });

  describe('getRepairCost', () => {
    it('should return the correct total cost for a given list of repairs', () => {
      const repairList = [{ cost: 100 }, { cost: 150 }, { cost: 50 }];
      expect(getRepairCost(repairList)).toBe(300.0);
    });

    it('should handle repair items without a cost or with a cost of 0', () => {
      const repairList = [{ cost: 100 }, {}, { cost: 0 }];
      expect(getRepairCost(repairList)).toBe(100.0);
    });

    it('should return a value with exactly two decimal places', () => {
      const repairList = [{ cost: 33.333 }, { cost: 33.335 }];
      expect(getRepairCost(repairList)).toBe(66.67);
    });

    it('should handle an empty repair list', () => {
      const repairList: any[] = [];
      expect(getRepairCost(repairList)).toBe(0.0);
    });

    it('should handle edge cases with large numbers', () => {
      const repairList = [{ cost: 1e12 }, { cost: 1e12 }];
      expect(getRepairCost(repairList)).toBe(2e12);
    });
  });

  // getOperationCost.test.ts

  describe('getOperationCost', () => {
    it('should return the correct total cost for a given operation cost configuration', () => {
      const operationCostConfig = {
        cost1: 100,
        cost2: 150,
        cost3: 50,
      };
      expect(getOperationCost(operationCostConfig)).toBe(300.0);
    });

    it('should handle operation costs that are strings', () => {
      const operationCostConfig = {
        cost1: '100',
        cost2: '150',
        cost3: '50',
      };
      expect(getOperationCost(operationCostConfig)).toBe(300.0);
    });

    it('should handle invalid operationCostConfig', () => {
      expect(getOperationCost({})).toBe(0);
      expect(getOperationCost(null as any)).toBe(0);
      expect(getOperationCost(undefined as any)).toBe(0);
    });

    it('should handle a mix of string and number costs', () => {
      const operationCostConfig = {
        cost1: '100',
        cost2: 150,
        cost3: '50',
      };
      expect(getOperationCost(operationCostConfig)).toBe(300.0);
    });

    it('should return a value with exactly two decimal places', () => {
      const operationCostConfig = {
        cost1: 33.333,
        cost2: 33.335,
      };
      expect(getOperationCost(operationCostConfig)).toBe(66.67);
    });

    it('should handle an empty operation cost configuration', () => {
      const operationCostConfig: { [key: string]: string | number } = {};
      expect(getOperationCost(operationCostConfig)).toBe(0.0);
    });

    it('should handle non-numeric values gracefully', () => {
      const operationCostConfig = {
        cost1: 100,
        cost2: 'invalid',
        cost3: 50,
      };
      expect(getOperationCost(operationCostConfig)).toBe(150.0);
    });

    it('should handle edge cases with large numbers', () => {
      const operationCostConfig = {
        cost1: 1e12,
        cost2: 1e12,
      };
      expect(getOperationCost(operationCostConfig)).toBe(2e12);
    });
  });

  describe('getMarketingCost', () => {
    it('should return the correct marketing cost for valid inputs', () => {
      expect(getMarketingCost(10, 1000, 200, 300)).toBe(150.0);
      expect(getMarketingCost('10', 1000, 200, 300)).toBe(150.0);
      expect(getMarketingCost(20, 1000, 200, 300)).toBe(300.0);
      expect(getMarketingCost('20', 1000, 200, 300)).toBe(300.0);
    });

    it('should handle zero marketing percent', () => {
      expect(getMarketingCost(0, 1000, 200, 300)).toBe(0.0);
      expect(getMarketingCost('0', 1000, 200, 300)).toBe(0.0);
    });

    it('should return a value with exactly two decimal places', () => {
      expect(getMarketingCost(10.234, 1000, 200, 300)).toBe(153.51);
      expect(getMarketingCost('10.567', 1000, 200, 300)).toBe(158.51);
    });

    it('should handle non-numeric marketing percent gracefully', () => {
      expect(getMarketingCost('invalid', 1000, 200, 300)).toBe(0.0);
    });

    it('should handle negative values correctly', () => {
      expect(getMarketingCost(-10, 1000, 200, 300)).toBe(-150.0);
      expect(getMarketingCost('-10', 1000, 200, 300)).toBe(-150.0);
    });

    it('should handle edge cases with large numbers', () => {
      expect(getMarketingCost(10, 1e12, 1e12, 1e12)).toBe(3e11);
    });

    it('should handle all costs being zero', () => {
      expect(getMarketingCost(10, 0, 0, 0)).toBe(0.0);
    });
  });

  describe('getCostPrice', () => {
    it('should return the correct total cost for valid inputs', () => {
      expect(getCostPrice(1000, 200, 300, 150, 50)).toBe(1700.0);
      expect(getCostPrice(500, 100, 200, 50, 25)).toBe(875.0);
    });

    it('should handle zero values for all costs', () => {
      expect(getCostPrice(0, 0, 0, 0, 0)).toBe(0.0);
    });

    it('should return a value with exactly two decimal places', () => {
      expect(getCostPrice(1000.123, 200.456, 300.789, 150.987, 50.654)).toBe(
        1703.01,
      );
      expect(getCostPrice(500.1, 100.1, 200.1, 50.1, 25.1)).toBe(875.5);
    });

    it('should handle negative values correctly', () => {
      expect(getCostPrice(1000, -200, 300, -150, 50)).toBe(1000.0);
      expect(getCostPrice(500, -100, 200, -50, 25)).toBe(575.0);
    });

    it('should handle edge cases with large numbers', () => {
      expect(getCostPrice(1e12, 1e12, 1e12, 1e12, 1e12)).toBe(5e12);
    });

    it('should handle mixed positive and negative values', () => {
      expect(getCostPrice(1000, -200, 300, -150, -50)).toBe(900.0);
    });
  });

  describe('calculateProductPrices', () => {
    const validFixCost = {
      operationCost: {
        logistic: '7.00',
        warehouseRental: '1.00',
        productPackaging: '15.00',
      },
      marketing: '2.00',
    };
    const validJobEntity = {
      repairListValue: [{ cost: 100 }, { cost: 200 }, {}],
      purchasedPrice: 15000,
      modelMaster: {
        insuranceCost: 150,
        averageRetailCost: { AA: '18000.00', AD: '9000.00', DD: '8000.00' },
        averageWholeSaleCost: { AA: '16000.00', AD: '7000.00', DD: '6000.00' },
      },
      qcStatus: QCStatus.FIX,
      estimatedGrade: 'A',
      currentGrade: 'A',
    } as unknown as JobEntity;

    it.each([
      { qcStatus: QCStatus.REFURBISH, estimatedGrade: 'A', currentGrade: 'A' },
      { qcStatus: QCStatus.SCRAP, estimatedGrade: 'A', currentGrade: 'A' },
      { qcStatus: QCStatus.FIX, estimatedGrade: 'A', currentGrade: 'D' },
    ])('Valid Input', async ({ qcStatus, estimatedGrade, currentGrade }) => {
      const result = calculateProductPrices(
        {
          ...validJobEntity,
          qcStatus,
          estimatedGrade,
          currentGrade,
        } as JobEntity,
        validFixCost,
      );

      expect(result.costPrice).toBeGreaterThanOrEqual(
        validJobEntity.purchasedPrice ?? 10000000,
      );
      expect(result.retailPrice).toEqual(
        qcStatus === QCStatus.SCRAP
          ? Number(validJobEntity.modelMaster.averageRetailCost['DD'])
          : Number(
              validJobEntity.modelMaster.averageRetailCost[
                `${estimatedGrade}${currentGrade}`
              ],
            ),
      );
      expect(result.retailMargin).toEqual(expect.any(Number));
      expect(result.retailMargin).not.toBeNaN();
      expect(result.marginRetailBaht).toEqual(expect.any(Number));
      expect(result.marginRetailBaht).not.toBeNaN();
      expect(result.wholeSalePrice).toEqual(
        qcStatus === QCStatus.SCRAP
          ? Number(validJobEntity.modelMaster.averageWholeSaleCost['DD'])
          : Number(
              validJobEntity.modelMaster.averageWholeSaleCost[
                `${estimatedGrade}${currentGrade}`
              ],
            ),
      );
      expect(result.wholeSaleMargin).toEqual(expect.any(Number));
      expect(result.wholeSaleMargin).not.toBeNaN();
      expect(result.marginWholeSaleBaht).toEqual(expect.any(Number));
      expect(result.marginWholeSaleBaht).not.toBeNaN();
    });
    describe('Invalid JobEntity', () => {
      it.each([null, undefined, []])(
        'Invalid repair cost',
        async (repairListValue) => {
          const invalidJob = {
            ...validJobEntity,
            repairListValue: repairListValue as RepairHx[],
          };
          const result = calculateProductPrices(
            invalidJob as JobEntity,
            validFixCost,
          );

          expect(result.costPrice).toBeGreaterThanOrEqual(
            validJobEntity.purchasedPrice ?? 10000000,
          );
          expect(result.retailPrice).toEqual(
            Number(validJobEntity.modelMaster.averageRetailCost['AA']),
          );
          expect(result.retailMargin).toEqual(expect.any(Number));
          expect(result.retailMargin).not.toBeNaN();
          expect(result.wholeSalePrice).toEqual(
            Number(validJobEntity.modelMaster.averageWholeSaleCost['AA']),
          );
          expect(result.wholeSaleMargin).toEqual(expect.any(Number));
          expect(result.wholeSaleMargin).not.toBeNaN();

          expect(result.marginRetailBaht).toEqual(expect.any(Number));
          expect(result.marginRetailBaht).not.toBeNaN();
          expect(result.marginWholeSaleBaht).toEqual(expect.any(Number));
          expect(result.marginWholeSaleBaht).not.toBeNaN();
        },
      );
      it.each([null, undefined, 0])(
        'Invalid purchase price',
        async (purchasedPrice) => {
          const invalidJob = {
            ...validJobEntity,
            purchasedPrice,
          };
          const result = calculateProductPrices(
            invalidJob as JobEntity,
            validFixCost,
          );

          expect(result.costPrice).toBeGreaterThanOrEqual(
            invalidJob.purchasedPrice || 0,
          );
          expect(result.retailPrice).toEqual(
            Number(invalidJob.modelMaster.averageRetailCost['AA']),
          );
          expect(result.retailMargin).toEqual(expect.any(Number));
          expect(result.retailMargin).not.toBeNaN();
          expect(result.wholeSalePrice).toEqual(
            Number(invalidJob.modelMaster.averageWholeSaleCost['AA']),
          );
          expect(result.wholeSaleMargin).toEqual(expect.any(Number));
          expect(result.wholeSaleMargin).not.toBeNaN();

          expect(result.marginRetailBaht).toEqual(expect.any(Number));
          expect(result.marginRetailBaht).not.toBeNaN();
          expect(result.marginWholeSaleBaht).toEqual(expect.any(Number));
          expect(result.marginWholeSaleBaht).not.toBeNaN();
        },
      );
      it.each([
        { estimatedGrade: null },
        { currentGrade: null },
        { estimatedGrade: null, currentGrade: null },
        { estimatedGrade: undefined },
        { currentGrade: undefined },
        { estimatedGrade: undefined, currentGrade: undefined },
        { estimatedGrade: 'X' },
        { currentGrade: 'X' },
        { estimatedGrade: 'X', currentGrade: 'X' },
        { estimatedGrade: '' },
        { currentGrade: '' },
        { estimatedGrade: '', currentGrade: '' },
      ])('Invalid grade', async (invalidGrade) => {
        const invalidJob = {
          ...validJobEntity,
          ...invalidGrade,
        };
        const result = calculateProductPrices(
          invalidJob as JobEntity,
          validFixCost,
        );

        expect(result.costPrice).toBeGreaterThanOrEqual(
          validJobEntity.purchasedPrice || 100000000,
        );
        expect(result.retailPrice).toBeNull();
        expect(result.retailMargin).toBeNull();
        expect(result.wholeSalePrice).toBeNull();
        expect(result.wholeSaleMargin).toBeNull();
        expect(result.marginRetailBaht).toBeNull();
        expect(result.marginWholeSaleBaht).toBeNull();
      });
      it.each([null, undefined, 0])(
        'Invalid model master insurance cost',
        async (insuranceCost) => {
          const modelMaster = {
            ...validJobEntity.modelMaster,
            insuranceCost,
          };
          const invalidJob = { ...validJobEntity, modelMaster };
          const result = calculateProductPrices(
            invalidJob as JobEntity,
            validFixCost,
          );

          expect(result.costPrice).toBeGreaterThanOrEqual(
            invalidJob.purchasedPrice || 10000000,
          );
          expect(result.retailPrice).toEqual(
            Number(invalidJob.modelMaster.averageRetailCost['AA']),
          );
          expect(result.retailMargin).toEqual(expect.any(Number));
          expect(result.retailMargin).not.toBeNaN();
          expect(result.wholeSalePrice).toEqual(
            Number(invalidJob.modelMaster.averageWholeSaleCost['AA']),
          );
          expect(result.wholeSaleMargin).toEqual(expect.any(Number));
          expect(result.wholeSaleMargin).not.toBeNaN();
          expect(result.marginRetailBaht).toEqual(expect.any(Number));
          expect(result.marginRetailBaht).not.toBeNaN();
          expect(result.marginWholeSaleBaht).toEqual(expect.any(Number));
          expect(result.marginWholeSaleBaht).not.toBeNaN();
        },
      );
      it.each([
        { averageRetailCost: {} },
        { averageWholeSaleCost: {} },
        { averageRetailCost: {}, averageWholeSaleCost: {} },
        { averageRetailCost: undefined },
        { averageWholeSaleCost: undefined },
        { averageRetailCost: undefined, averageWholeSaleCost: undefined },
        { averageRetailCost: null },
        { averageWholeSaleCost: null },
        { averageRetailCost: null, averageWholeSaleCost: null },
      ])('Invalid model master avg cost', async (invalidModelMaster) => {
        const modelMaster = {
          ...validJobEntity.modelMaster,
          ...invalidModelMaster,
        };
        const invalidJob = { ...validJobEntity, modelMaster };
        const result = calculateProductPrices(
          invalidJob as JobEntity,
          validFixCost,
        );
        expect(result.costPrice).toBeGreaterThanOrEqual(
          validJobEntity.purchasedPrice ?? 10000000,
        );
        if (
          Object.keys(invalidModelMaster).includes('averageRetailCost') &&
          Object.keys(invalidModelMaster).includes('averageWholeSaleCost')
        ) {
          expect(result.retailPrice).toBeNull();
          expect(result.retailMargin).toBeNull();
          expect(result.wholeSalePrice).toBeNull();
          expect(result.wholeSaleMargin).toBeNull();
        } else if (
          Object.keys(invalidModelMaster).includes('averageRetailCost')
        ) {
          expect(result.retailPrice).toBeNull();
          expect(result.retailMargin).toBeNull();
          expect(result.marginRetailBaht).toBeNull();
          expect(result.wholeSalePrice).toEqual(
            Number(validJobEntity.modelMaster.averageWholeSaleCost['AA']),
          );
          expect(result.wholeSaleMargin).toEqual(expect.any(Number));
          expect(result.wholeSaleMargin).not.toBeNaN();
          expect(result.marginWholeSaleBaht).toEqual(expect.any(Number));
          expect(result.marginWholeSaleBaht).not.toBeNaN();
        } else if (
          Object.keys(invalidModelMaster).includes('averageWholeSaleCost')
        ) {
          expect(result.retailPrice).toEqual(
            Number(validJobEntity.modelMaster.averageRetailCost['AA']),
          );
          expect(result.retailMargin).toEqual(expect.any(Number));
          expect(result.retailMargin).not.toBeNaN();
          expect(result.marginRetailBaht).toEqual(expect.any(Number));
          expect(result.marginRetailBaht).not.toBeNaN();
          expect(result.wholeSalePrice).toBeNull();
          expect(result.wholeSaleMargin).toBeNull();
          expect(result.marginWholeSaleBaht).toBeNull();
        }
      });
    });

    it.each([{}, { logistic: 'a' }, { logistic: '' }])(
      'Invalid operation cost',
      async (operationCost) => {
        const invalidOperationCost = { ...validFixCost, operationCost };
        const result = calculateProductPrices(
          validJobEntity,
          invalidOperationCost,
        );

        expect(result.costPrice).toBeGreaterThanOrEqual(
          validJobEntity.purchasedPrice ?? 10000000,
        );
        expect(result.retailPrice).toEqual(
          Number(validJobEntity.modelMaster.averageRetailCost['AA']),
        );
        expect(result.retailMargin).toEqual(expect.any(Number));
        expect(result.retailMargin).not.toBeNaN();
        expect(result.wholeSalePrice).toEqual(
          Number(validJobEntity.modelMaster.averageWholeSaleCost['AA']),
        );
        expect(result.wholeSaleMargin).toEqual(expect.any(Number));
        expect(result.wholeSaleMargin).not.toBeNaN();

        expect(result.marginRetailBaht).toEqual(expect.any(Number));
        expect(result.marginRetailBaht).not.toBeNaN();
        expect(result.marginWholeSaleBaht).toEqual(expect.any(Number));
        expect(result.marginWholeSaleBaht).not.toBeNaN();
      },
    );
    it.each(['', undefined, null])('Invalid marketing', async (marketing) => {
      const invalidMarketing = { ...validFixCost, marketing };
      const result = calculateProductPrices(validJobEntity, invalidMarketing);

      expect(result.costPrice).toBeGreaterThanOrEqual(
        validJobEntity.purchasedPrice ?? 10000000,
      );
      expect(result.retailPrice).toEqual(
        Number(validJobEntity.modelMaster.averageRetailCost['AA']),
      );
      expect(result.retailMargin).toEqual(expect.any(Number));
      expect(result.retailMargin).not.toBeNaN();
      expect(result.wholeSalePrice).toEqual(
        Number(validJobEntity.modelMaster.averageWholeSaleCost['AA']),
      );
      expect(result.wholeSaleMargin).toEqual(expect.any(Number));
      expect(result.wholeSaleMargin).not.toBeNaN();

      expect(result.marginRetailBaht).toEqual(expect.any(Number));
      expect(result.marginRetailBaht).not.toBeNaN();
      expect(result.marginWholeSaleBaht).toEqual(expect.any(Number));
      expect(result.marginWholeSaleBaht).not.toBeNaN();
    });
  });
});
