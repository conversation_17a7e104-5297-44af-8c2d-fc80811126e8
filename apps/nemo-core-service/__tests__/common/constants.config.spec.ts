import {
  mappingUrlWithCompanyId,
  getPermssionAll,
  PermissionAction,
  FilterRole,
} from '../../src/config';

describe('Constants', () => {
  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('Mapping url with company id', () => {
    it('should return mapping company id', () => {
      const result = mappingUrlWithCompanyId('WW');
      expect(result).toEqual('WW');
    });

    it('should return mapping company id', () => {
      const result = mappingUrlWithCompanyId('test');
      expect(result).toEqual(null);
    });
  });
  //test function getPermssionAll
  describe('Get permission all', () => {
    it('should return permission all', () => {
      const result = getPermssionAll(PermissionAction.VIEW);
      expect(result.length).not.toEqual(0);
    });
    it('should rturn permission CMS', () => {
      const result = getPermssionAll(PermissionAction.VIEW, FilterRole.CMS);
      expect(result.length).not.toEqual(0);
    });
    it('should rturn permission SHOP', () => {
      const result = getPermssionAll(PermissionAction.VIEW, FilterRole.SHOP);
      expect(result.length).not.toEqual(0);
    });
  });
});
