import { JobEntity } from '../../src/entities';
import { prepareJobsSnapshot } from '../../src/utils/job/jobsSnapshot';
import { mockJob } from '../mock-data/job';
import { WithUserContext } from '../../src/interfaces';

describe('Jobs Snapshot', () => {
  describe('prepare jobsSnapshot', () => {
    const mockSnapshotJob = {
      ...mockJob,
      currentGrade: 'A',
    } as JobEntity;
    const user = {
      company: 'CompanyX',
      userKey: 'user123',
      name: 'test user',
      roles: [
        {
          branchId: 'branchId#1',
          role: ['Sale', 'Manager'],
        },
      ],
    } as WithUserContext;

    const aoIncompleteInspectedAt = new Date();
    const remark = 'test-remakr';
    const videoPath = 'test-url';

    it('should return all valid data', () => {
      const result = prepareJobsSnapshot({
        job: mockSnapshotJob,
        user: user,
        aoIncompleteInspectedAt,
        remark: remark,
        videoPath: videoPath,
      });

      expect(result.jobId).toBe(mockSnapshotJob.jobId);
      expect(result.remark).toBe(remark);
      expect(result.videoPath).toBe(videoPath);
      expect(result.aoIncompleteInspectedUserKey).toBe(user.userKey);
      expect(result.aoIncompleteInspectedUserName).toBe(user.name);
      expect(result.aoIncompleteInspectedAt).toBe(aoIncompleteInspectedAt);
    });

    it('should return grade D', () => {
      const result = prepareJobsSnapshot({
        job: mockJob,
        user,
        aoIncompleteInspectedAt,
        remark,
        videoPath,
      });

      expect(result.currentGrade).toBe('D');
    });

    it('should return remark = null', () => {
      const result = prepareJobsSnapshot({
        job: mockJob,
        user,
        aoIncompleteInspectedAt,
      });

      expect(result.remark).toBeNull();
      expect(result.videoPath).toBeNull();
    });

    it('should return videoPath = null', () => {
      const result = prepareJobsSnapshot({
        job: mockJob,
        user,
        aoIncompleteInspectedAt,
        remark,
      });

      expect(result.videoPath).toBeNull();
    });
  });
});
