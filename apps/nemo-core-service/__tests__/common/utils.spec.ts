import {
  deepDifference,
  isJsonString,
  recordsFromCsvFile,
  recordsFromTsvFile,
  generateHMACSHA512,
  getDistanceFromCoordinate,
} from '../../src/utils';
import {
  mockDataFromCSV,
  mockDataFromTSV,
  resultTSVData,
} from '../mock-data/utils';

jest.mock('csvtojson', () => ({
  __esModule: true,
  default: jest.fn(() => ({
    fromFile: jest.fn(() => mockDataFromCSV),
  })),
}));

jest.mock('fs', () => ({
  promises: {
    readFile: jest.fn(() => mockDataFromTSV),
  },
}));

describe('Utils', () => {
  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('Check json string', () => {
    it('should return valid json string', () => {
      const result = isJsonString('{"test": "test"}');
      expect(result).toBeTruthy();
    });

    it('should return invalid json string', () => {
      const result = isJsonString('test');
      expect(result).toBeFalsy();
    });
  });

  describe('Deep Difference', () => {
    it('should return diff object', () => {
      const result = deepDifference(
        {
          test: 'test',
        },
        {
          test: 'test#1',
        },
      );

      expect(result).toStrictEqual({ test: { from: 'test', to: 'test#1' } });
    });
  });

  describe('GenerateHMACSHA512', () => {
    it('should return encrypt data with hmac512', () => {
      const result = generateHMACSHA512('test', 'test');
      expect(result).not.toEqual(undefined);
    });
  });

  describe('Record from csv file', () => {
    it('should return result array', async () => {
      const result = await recordsFromCsvFile('');

      expect(result).toBeDefined();
      expect(result).toStrictEqual(mockDataFromCSV);
    });

    it('should return result with transform array', async () => {
      const transform = (key: string) => {
        switch (key) {
          case 'id':
            return mockDataFromCSV[0].id;
          case 'name':
            return mockDataFromCSV[0].name;
        }
      };

      const result = await recordsFromCsvFile('', transform);

      expect(result).toBeDefined();
      expect(result).toStrictEqual(mockDataFromCSV);
    });
  });

  describe('Record from tsv file', () => {
    it('should return result array', async () => {
      const result = await recordsFromTsvFile('');

      expect(result).toBeDefined();
      expect(result).toStrictEqual(resultTSVData);
    });

    it('should return result array with headerKeys', async () => {
      const result = await recordsFromTsvFile('', {
        TEST1: 'test1Custom',
      });

      expect(result).toBeDefined();
      expect(Object.keys(result[0])[0]).toBe('test1Custom');
    });
  });

  describe('Get Distance From Coordinate', () => {
    it('should return type number', () => {
      const result = getDistanceFromCoordinate(
        13.7548965,
        100.5637128,
        13.7589248,
        100.566164,
      );
      expect(typeof result).toBe('number');
    });
  });
});
