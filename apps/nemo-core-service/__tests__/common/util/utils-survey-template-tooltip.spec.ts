import {
  ITooltipConfig,
  convertTooltipConfigToTooltipHTML,
} from '../../../src/utils/survey-template/tooltip';

describe('Utils SJS Template - Tooltip', () => {
  describe('convertTooltipConfigToTooltipHTML', () => {
    const fullOption = {
      images: ['img_url1', 'img_url2'],
      title: 'test title',
      sections: [
        { description: 'desc 1' },
        {
          description: 'desc 2',
          bullets: ['desc 2, bullet 1', 'desc 2, bullet 2'],
        },
        {
          description: 'desc 3',
          bullets: ['desc 3, bullet 1'],
          color: 'text-warning-500',
        },
      ],
    } as ITooltipConfig;

    const fullOptionResult = `<div class="grid grid-cols-2 gap-x-6 gap-y-4 place-items-center"><img class="h-56" src="img_url1" /><img class="h-56" src="img_url2" /></div><div class="text-t4-semi-bold text-base-25 mt-4 mb-4"><p>test title</p></div><div class="text-b5-regular text-base-25 mb-4"><p>desc 1</p></div><div class="text-b5-regular text-base-25 mb-4"><p>desc 2</p><div class="bullet-point"><div style="width: 5%">&#8226;</div><div style="width: 95%">desc 2, bullet 1</div></div><div class="bullet-point"><div style="width: 5%">&#8226;</div><div style="width: 95%">desc 2, bullet 2</div></div></div><div class="text-b5-regular text-warning-500 "><p>desc 3</p><div class="bullet-point"><div style="width: 5%">&#8226;</div><div style="width: 95%">desc 3, bullet 1</div></div></div>`;

    const titleOnly = {
      title: 'test title',
    } as ITooltipConfig;

    const titleOnlyResult = `<div class="text-t4-semi-bold text-base-25 mb-4"><p>test title</p></div>`;

    const imageOnly = {
      images: ['img_url1'],
    } as ITooltipConfig;

    const imageOnlyResult = `<div class="grid grid-cols-1 gap-x-6 gap-y-4 place-items-center"><img class="h-56" src="img_url1" /></div>`;

    const sectionOnly = {
      sections: [
        {
          description: 'desc 2',
          bullets: ['desc 2, bullet 1', 'desc 2, bullet 2'],
          color: 'text-warning-500',
        },
      ],
    } as ITooltipConfig;

    const sectionOnlyResult = `<div class="text-b5-regular text-warning-500 "><p>desc 2</p><div class="bullet-point"><div style="width: 5%">&#8226;</div><div style="width: 95%">desc 2, bullet 1</div></div><div class="bullet-point"><div style="width: 5%">&#8226;</div><div style="width: 95%">desc 2, bullet 2</div></div></div>`;

    const bulletOnly = {
      sections: [
        {
          bullets: ['desc 2, bullet 1', 'desc 2, bullet 2'],
        },
      ],
    } as ITooltipConfig;

    const bulletOnlyResult = `<div class="text-b5-regular text-base-25 "><p></p><div class="bullet-point"><div style="width: 5%">&#8226;</div><div style="width: 95%">desc 2, bullet 1</div></div><div class="bullet-point"><div style="width: 5%">&#8226;</div><div style="width: 95%">desc 2, bullet 2</div></div></div>`;

    const descriptionOnly = {
      sections: [
        {
          description: 'desc 2',
        },
      ],
    } as ITooltipConfig;

    const descriptionOnlyResult = `<div class="text-b5-regular text-base-25 "><p>desc 2</p></div>`;

    const nullValue = null;
    const nullValueResult = '';
    const testList = [
      nullValue,
      fullOption,
      titleOnly,
      imageOnly,
      sectionOnly,
      bulletOnly,
      descriptionOnly,
    ];
    const resultList = [
      nullValueResult,
      fullOptionResult,
      titleOnlyResult,
      imageOnlyResult,
      sectionOnlyResult,
      bulletOnlyResult,
      descriptionOnlyResult,
    ];

    testList.forEach((item, index) => {
      it(`should pass`, () => {
        const result = convertTooltipConfigToTooltipHTML(item);
        expect(result).toEqual(resultList[index]);
      });
    });
  });
});
