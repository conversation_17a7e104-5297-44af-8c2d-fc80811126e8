import {
  convertTooltipConfigToTooltipHTML,
  ITooltipConfig,
} from '../../../src/utils/survey-template/tooltip';
import {
  defaultImageSjs,
  generateProductImageSlugForTemplate,
  getProductImageSjs,
  IOverridingObject,
} from '../../../src/utils/survey-template/dynamic-product-images';

jest.mock('../../../src/utils/survey-template/tooltip', () => {
  const original = jest.requireActual(
    '../../../src/utils/survey-template/tooltip',
  );
  return {
    ...original,
    convertTooltipConfigToTooltipHTML: jest.fn(),
  };
});

describe('Utils SJS Template - Dynamic - ProductImage', () => {
  beforeAll(() => {
    jest
      .mocked(convertTooltipConfigToTooltipHTML)
      .mockImplementationOnce(() => 'mock_result_tooltip_HTML');
  });
  afterAll(() => {
    jest.clearAllMocks();
  });

  describe('getProductImageSjs', () => {
    it.each([[true], [false]])(`case has tooltip %s`, (hasTooltip) => {
      const ovObj = { ...mockProductImageSjsOverride };
      if (hasTooltip) {
        ovObj.tooltip = mockConfigTooltip;
      }

      const expectResult = {
        desktop: { ...mockGetProductImageWOTooltipResult.desktop },
        mobile: { ...mockGetProductImageWOTooltipResult.mobile },
      };

      if (hasTooltip) {
        expectResult.desktop.tooltip = {
          html: 'mock_result_tooltip_HTML',
        };
        expectResult.mobile.tooltip = {
          html: 'mock_result_tooltip_HTML',
        };
      }

      const result = getProductImageSjs(ovObj);
      expect(result).toEqual(expectResult);
    });
  });

  describe('generateProductImageSlugForTemplate', () => {
    it('Should return correctly', () => {
      const result = generateProductImageSlugForTemplate({
        config: mockProductImagesConfig,
        itemPerRow: 2,
      });
      expect(result).toEqual(mockProductImageSurveyFormPage0Element);
    });
  });
});

const mockProductImageSjsOverride: IOverridingObject = {
  name: 'name_slug',
  title: 'title_here',
  backgroundImage: 'mock_bg_image',
};

const mockConfigTooltip: ITooltipConfig = {
  title: 'tile_tooltip',
  images: [],
  sections: [],
};

const mockGetProductImageWOTooltipResult = {
  desktop: {
    ...defaultImageSjs,
    ...mockProductImageSjsOverride,
    sourceType: 'file-camera',
  },
  mobile: {
    ...defaultImageSjs,
    ...mockProductImageSjsOverride,
    sourceType: 'file',
  },
};

const mockProductImagesConfig = [
  mockProductImageSjsOverride,
  mockProductImageSjsOverride,
  mockProductImageSjsOverride,
];
const mockDesktopElementEach = {
  ...mockGetProductImageWOTooltipResult.desktop,
};
const mockMobileElementEach = { ...mockGetProductImageWOTooltipResult.mobile };

const mockProductImageSurveyFormPage0Element = [
  {
    type: 'panel',
    name: 'product_images_desktop',
    visibleIf: '{deviceSize} == desktop',
    elements: [
      { ...mockDesktopElementEach, startWithNewLine: true },
      mockDesktopElementEach,
      { ...mockDesktopElementEach, startWithNewLine: true },
    ],
  },
  {
    type: 'panel',
    name: 'product_images_tablet',
    visibleIf: '{deviceSize} == tablet',
    elements: [
      { ...mockMobileElementEach, startWithNewLine: true },
      mockMobileElementEach,
      { ...mockMobileElementEach, startWithNewLine: true },
    ],
  },
];
