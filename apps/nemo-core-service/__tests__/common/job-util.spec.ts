import { JobStatus } from '../../src/entities';
import {
  getStatusJobByType,
  IGetStatusJobByType,
} from '../../src/utils/job/dataJob';

jest.mock('../../src/entities', () => {
  return {
    JobStatus: {
      CAMPAIGN_SELECTED: '35_CAMPAIGN_SELECTED',
      PURCHASED: '40_PURCHASED',
      REJECT_BY_CUSTOMER: '99_REJECT_BY_CUSTOMER',
      REJECT_BY_SHOP: '98_REJECT_BY_SHOP',
    },
  };
});

describe('getStatusJobByType', () => {
  it('should return the correct status for PURCHASED', () => {
    const result = getStatusJobByType(IGetStatusJobByType.PURCHASED);
    expect(result).toEqual([JobStatus.PURCHASED]);
  });
  it('should return the correct status for not defined', () => {
    const result = getStatusJobByType('default' as IGetStatusJobByType);
    expect(result).toEqual([]);
  });
});
