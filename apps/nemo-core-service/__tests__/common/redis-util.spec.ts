import { DateTime } from 'luxon';
import { RedisUtils } from '../../src/utils/redis.util';

describe('Redis Utils', () => {
  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('Get day ttl', () => {
    it('should return day ttl', () => {
      const result = RedisUtils.dayTTL;
      expect(result).toEqual(86400);
    });
  });

  describe('Get effective redis key', () => {
    it('should return key with today date', () => {
      const today = DateTime.fromJSDate(new Date()).toFormat('yyyy-MM-dd');

      const result = RedisUtils.getEffectiveElementRedisKey('test');
      expect(result).toEqual(`test-${today}`);
    });
  });

  describe('Get system config key', () => {
    it('should return config key', () => {
      const result = RedisUtils.getSystemConfigKey('test');
      expect(result).toEqual(`config-test`);
    });
  });

  describe('Get system config key', () => {
    it('should return config key', () => {
      const result = RedisUtils.getUserRoleKey('test');
      expect(result).toEqual(`user-roles-test`);
    });
  });
});
