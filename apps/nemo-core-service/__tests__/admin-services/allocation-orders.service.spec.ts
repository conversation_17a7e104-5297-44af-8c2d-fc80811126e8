import { getRepositoryToken } from '@nestjs/typeorm';
import { Test } from '@nestjs/testing';
import { getQueueToken } from '@nestjs/bull';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import {
  AllocationOrderEntity,
  AllocationOrderStatus,
  AllocationOrderType,
  JobEntity,
  SystemConfigEntity,
  ConfigActivitiesEntity,
  UserEntity,
  ContractEntity,
  VoucherEntity,
  BranchEntity,
  CompanyEntity,
  CompanyRoleEntity,
  branchType,
} from '../../src/entities';
import { BaseExceptionModule } from '../../src/base-exceptions/base-exceptions.module';
import { AdminAllocationOrdersService } from '../../src/admin/allocation-orders/allocation-orders.service';
import { SystemConfigService } from '../../src/system-config/system-config.service';
import { CacheManagerService } from '../../src/cache-manager/cache-manager.service';
import { AES128MessageService } from '../../src/encrypt-decrypt-message/encrypt-decrypt.service';
import { SmtpService } from '../../src/smtp/smtp.service';
import { S3Service } from '../../src/storage/s3.service';
import { ContractsService } from '../../src/shop/contracts/contracts.service';
import { BaseExceptionService } from '../../src/exceptions';
import { BASE_EXCEPTIONS } from '../../src/config';
import { Repository } from 'typeorm';
import { WithUserContext } from '../../src/interfaces';
import {
  mockAO,
  mockAODraft,
  mockAOPending,
  mockCreatedUser,
  mockCreatedUserEntity,
  mockIdAO,
  getMockJobs,
  mockCompany,
} from '../mock-data/allocation-order';
import {
  mockInspectionJob,
  mockBaseJob,
  mockInspectionJobWithUpdate,
  mockInspectionJobNoUpdate,
} from '../mock-data/job';
import { getDateFromToday } from '../../src/utils/general';
import { AOAwbConfirmDto } from '../../src/admin/allocation-orders/dto/awb-confirm.dto';
import { AOPickupConfirmDto } from 'src/admin/allocation-orders/dto/pickup-confirm.dto';

jest.mock('../../src/utils/general', () => {
  const original = jest.requireActual('../../src/utils/general');
  return {
    ...original,
    getDateFromToday: jest.fn(),
  };
});

const manager = {
  getRepository: jest.fn().mockReturnThis(),
  connection: {
    createQueryRunner: jest.fn().mockReturnThis(),
    connect: jest.fn().mockReturnThis(),
    startTransaction: jest.fn().mockReturnThis(),
    release: jest.fn().mockReturnThis(),
    rollbackTransaction: jest.fn().mockReturnThis(),
    commitTransaction: jest.fn().mockReturnThis(),
    manager: {
      getRepository: jest.fn().mockReturnThis(),
      createQueryBuilder: jest.fn().mockReturnThis(),
      select: jest.fn().mockReturnThis(),
      where: jest.fn().mockReturnThis(),
      andWhere: jest.fn().mockReturnThis(),
      insert: jest.fn().mockReturnThis(),
      orIgnore: jest.fn().mockReturnThis(),
      values: jest.fn().mockReturnThis(),
      save: jest.fn().mockReturnThis(),
      execute: jest.fn().mockResolvedValue({ raw: [] }),
    },
  },
};

describe('AdminAllocationOrdersService', () => {
  let adminAllocationOrdersService: AdminAllocationOrdersService;
  let allocationOrdersRepository: Repository<AllocationOrderEntity>;
  let jobsRepository: Repository<JobEntity>;
  let branchRepository: Repository<BranchEntity>;
  let systemConfigService: SystemConfigService<string>;
  let companyRepository: Repository<CompanyEntity>;
  beforeEach(async () => {
    const module = await Test.createTestingModule({
      imports: [BaseExceptionModule.register()],
      providers: [
        AdminAllocationOrdersService,
        SystemConfigService,
        CacheManagerService,
        {
          provide: ContractsService,
          useValue: {
            getTransactionAo: jest.fn(() => 'contract'),
          },
        },
        {
          provide: AES128MessageService,
          useFactory: () => {
            // Get aes key
            const aesKey = process.env.AES128_KEY;

            // Get aes salt
            const aesSalt = process.env.AES128_SALT;

            // Prevent key or salt invalid
            if (!aesKey || !aesSalt) {
              throw new Error(
                'AES_KEY and AES_SALT must be defined in .env file',
              );
            }

            // Initial aes service
            return new AES128MessageService(aesKey, aesSalt);
          },
        },
        {
          provide: getRepositoryToken(AllocationOrderEntity),
          useValue: {
            findOne: jest.fn().mockReturnThis(),
            insert: jest.fn().mockReturnThis(),
            save: jest.fn().mockReturnThis(),
            manager,
          },
        },
        {
          provide: getRepositoryToken(JobEntity),
          useValue: {
            find: jest.fn().mockReturnThis(),
            manager,
          },
        },
        {
          provide: getQueueToken('re-calculate-product-queue'),
          useValue: {
            add: jest.fn(),
            process: jest.fn(),
          },
        },
        {
          provide: CACHE_MANAGER,
          useValue: {
            get: jest.fn(),
            set: jest.fn(),
            reset: jest.fn(),
            del: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(SystemConfigEntity),
          useValue: {
            findOne: jest.fn().mockReturnThis(),
          },
        },
        {
          provide: getRepositoryToken(ConfigActivitiesEntity),
          useValue: {
            save: jest.fn().mockReturnThis(),
            findOne: jest.fn().mockReturnThis(),
          },
        },
        {
          provide: getRepositoryToken(UserEntity),
          useValue: {
            find: jest.fn().mockReturnThis(),
          },
        },
        {
          provide: getRepositoryToken(ContractEntity),
          useValue: {
            findOne: jest.fn().mockReturnThis(),
          },
        },
        {
          provide: getRepositoryToken(VoucherEntity),
          useValue: {
            findOne: jest.fn().mockReturnThis(),
          },
        },
        {
          provide: getRepositoryToken(BranchEntity),
          useValue: {
            find: jest.fn().mockReturnThis(),
            findOne: jest.fn().mockReturnThis(),
          },
        },
        {
          provide: getRepositoryToken(CompanyEntity),
          useValue: {
            find: jest.fn().mockReturnThis(),
            findOne: jest.fn().mockReturnThis(),
          },
        },
        {
          provide: getRepositoryToken(CompanyRoleEntity),
          useValue: {
            find: jest.fn().mockReturnThis(),
          },
        },
        {
          provide: S3Service,
          useValue: {
            getPreviewUrl: jest.fn(),
            uploadFile: jest.fn(),
            getFile: jest.fn(),
            uploadFileByPresignedUrl: jest.fn(),
            getFileWithSignedUrl: jest.fn(() => `presignurl-download`),
          },
        },
        {
          provide: SmtpService,
          useValue: {
            sendMail: jest.fn(),
          },
        },
      ],
    }).compile();

    adminAllocationOrdersService = module.get<AdminAllocationOrdersService>(
      AdminAllocationOrdersService,
    );
    allocationOrdersRepository = module.get<Repository<AllocationOrderEntity>>(
      getRepositoryToken(AllocationOrderEntity),
    );
    jobsRepository = module.get<Repository<JobEntity>>(
      getRepositoryToken(JobEntity),
    );
    systemConfigService =
      module.get<SystemConfigService<string>>(SystemConfigService);
    branchRepository = module.get<Repository<BranchEntity>>(
      getRepositoryToken(BranchEntity),
    );
    companyRepository = module.get<Repository<CompanyEntity>>(
      getRepositoryToken(CompanyEntity),
    );
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('buildSearchQuery', () => {
    it.each`
      status        | allocationOrderId | type         | expectResult
      ${['status']} | ${'aoId'}         | ${'type'}    | ${`r.status IN ('${'status'}') AND r.allocationOrderId ILIKE '%${'aoId'}%' AND r.allocationOrderType = 'type'`}
      ${['status']} | ${undefined}      | ${undefined} | ${`r.status IN ('${'status'}')`}
      ${undefined}  | ${'aoId'}         | ${undefined} | ${`r.allocationOrderId ILIKE '%${'aoId'}%'`}
      ${undefined}  | ${undefined}      | ${'type'}    | ${`r.allocationOrderType = 'type'`}
      ${['status']} | ${'aoId'}         | ${undefined} | ${`r.status IN ('${'status'}') AND r.allocationOrderId ILIKE '%${'aoId'}%'`}
      ${['status']} | ${undefined}      | ${'type'}    | ${`r.status IN ('${'status'}') AND r.allocationOrderType = 'type'`}
      ${undefined}  | ${'aoId'}         | ${'type'}    | ${`r.allocationOrderId ILIKE '%${'aoId'}%' AND r.allocationOrderType = 'type'`}
      ${''}         | ${''}             | ${''}        | ${''}
    `(
      'buildSearchQuery',
      async ({ status, allocationOrderId, type, expectResult }) => {
        const mockQueryBuilder = {
          andWhere: jest.fn(),
        } as unknown as any;
        const mockRequest = {
          query: {
            status,
            allocationOrderId,
            type,
          },
        } as unknown as any;
        const result = adminAllocationOrdersService.buildSearchQuery(
          mockRequest,
          mockQueryBuilder,
        );
        if (status || allocationOrderId || type) {
          expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(expectResult);
        }
        expect(result).toBeDefined();
      },
    );
  });

  describe('delete allocation order', () => {
    const mockUser = {
      company: 'WW',
      userKey: 'test-user-key',
      name: 'test-name',
    } as WithUserContext;

    it('should error not found AO', async () => {
      jest
        .spyOn(allocationOrdersRepository, 'findOne')
        .mockResolvedValueOnce(null);

      jest.spyOn(jobsRepository, 'find').mockResolvedValue(mockInspectionJob);

      try {
        await adminAllocationOrdersService.deleteAllocationOrder(
          mockAOPending.allocationOrderId,
          mockUser,
        );
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.NOT_FOUND_DATA.code,
        );
        expect((error as BaseExceptionService).message).toBe('Not found data');
        expect((error as BaseExceptionService).data).toBe(
          'Allocation order not found',
        );
      }
    });

    it('should error Unauthorized', async () => {
      jest
        .spyOn(allocationOrdersRepository, 'findOne')
        .mockResolvedValueOnce(mockAO[1]);

      try {
        await adminAllocationOrdersService.deleteAllocationOrder(
          mockAOPending.allocationOrderId,
          mockUser,
        );
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.INVALID_AO_OWNER.code,
        );
        expect((error as BaseExceptionService).message).toBe(
          'Unauthorized for update ao',
        );
        expect((error as BaseExceptionService).data).toBe(
          `user (${mockUser.name}) does not have permission to update this AllocationOrder`,
        );
      }
    });

    it('should error Invalid Status', async () => {
      jest
        .spyOn(allocationOrdersRepository, 'findOne')
        .mockResolvedValueOnce(mockAO[0]);

      try {
        await adminAllocationOrdersService.deleteAllocationOrder(
          mockAOPending.allocationOrderId,
          mockUser,
        );
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.INVALID_AO_STATUS_TO_UPDATE.code,
        );
        expect((error as BaseExceptionService).message).toBe(
          'Invalid status for update ao',
        );
        expect((error as BaseExceptionService).data).toBe(
          `Invalid AllocationOrder status. AllocationOrder status must be ${AllocationOrderStatus.APPOINTMENT_PENDING}`,
        );
      }
    });

    it('should success delete', async () => {
      const mockValue = {
        marketing: 3,
        operationCost: {
          logistic: 1102,
          warehouseRental: 1201,
          productPackaging: 1303,
        },
      };

      jest
        .spyOn(allocationOrdersRepository, 'findOne')
        .mockResolvedValueOnce(mockAO[4]);

      jest
        .spyOn(systemConfigService, 'getSystemConfig')
        .mockResolvedValueOnce(mockValue);

      const result = await adminAllocationOrdersService.deleteAllocationOrder(
        mockAO[0].allocationOrderId,
        mockUser,
      );

      expect(result).toBe(null);
    });
  });

  describe('create draft', () => {
    const mockUser = {
      company: 'WW',
      userKey: 'test-user-key',
      name: 'test-name',
    } as WithUserContext;

    it('should success create draft (normal flow)', async () => {
      jest
        .spyOn(allocationOrdersRepository, 'findOne')
        .mockResolvedValue(mockAO[0]);

      const result = await adminAllocationOrdersService.createDraft(mockUser);
      expect(result).toEqual(mockAO[1].allocationOrderId);
    });

    it('should success create draft (empty flow)', async () => {
      jest.spyOn(allocationOrdersRepository, 'findOne').mockResolvedValue(null);

      const result = await adminAllocationOrdersService.createDraft(mockUser);
      expect(result).toEqual(mockAO[0].allocationOrderId);
    });

    it('should success create draft (new month flow)', async () => {
      jest
        .spyOn(allocationOrdersRepository, 'findOne')
        .mockResolvedValue(mockAO[3]);

      const result = await adminAllocationOrdersService.createDraft(mockUser);
      expect(result).toEqual(mockAO[0].allocationOrderId);
    });
  });

  describe('create allocation order', () => {
    const mockUser = {
      company: 'WW',
      userKey: 'test-user-key',
      name: 'test-name',
    } as WithUserContext;

    const mockBranch = {
      companyId: 'WW',
      branchId: 'test',
    } as BranchEntity;
    it('should success create', async () => {
      jest
        .spyOn(allocationOrdersRepository, 'findOne')
        .mockResolvedValueOnce(mockAO[0]);
      jest
        .spyOn(branchRepository, 'findOne')
        .mockResolvedValueOnce({ ...mockBranch, branchType: branchType.SHOP });
      jest
        .spyOn(branchRepository, 'findOne')
        .mockResolvedValueOnce({ ...mockBranch, branchType: branchType.SHOP });
      jest.spyOn(jobsRepository, 'find').mockResolvedValue(mockInspectionJob);

      const result = await adminAllocationOrdersService.createAllocationOrder(
        mockAO[0].allocationOrderId,
        mockUser,
        [mockInspectionJob[0].jobId, mockInspectionJob[1].jobId],
        mockAO[0].fromBranchId!,
        mockAO[0].toBranchId!,
        AllocationOrderType.WHOLESALE,
      );

      // match size
      expect(result.jobList.length).toEqual(mockInspectionJob.length);
      expect(result.unavailableJobList.length).toEqual(0);
      expect(result.updatedJobList.length).toEqual(0);

      //match order
      expect(result.jobList[0]).toEqual(mockInspectionJob[0]);
      expect(result.jobList[1]).toEqual(mockInspectionJob[1]);
    });

    it('should error not found AO', async () => {
      jest
        .spyOn(allocationOrdersRepository, 'findOne')
        .mockResolvedValueOnce(null);

      jest.spyOn(jobsRepository, 'find').mockResolvedValue(mockInspectionJob);

      try {
        await adminAllocationOrdersService.createAllocationOrder(
          mockAOPending.allocationOrderId,
          mockUser,
          [mockInspectionJob[0].jobId, mockInspectionJob[1].jobId],
          mockAO[0].fromBranchId!,
          mockAO[0].toBranchId!,
          AllocationOrderType.WHOLESALE,
        );
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.NOT_FOUND_DATA.code,
        );
        expect((error as BaseExceptionService).message).toBe('Not found data');
        expect((error as BaseExceptionService).data).toBe(
          'Allocation order not found',
        );
      }
    });

    it('should error invalid ao status', async () => {
      jest
        .spyOn(allocationOrdersRepository, 'findOne')
        .mockResolvedValueOnce(mockAO[0]);

      jest.spyOn(jobsRepository, 'find').mockResolvedValue(mockInspectionJob);

      try {
        await adminAllocationOrdersService.createAllocationOrder(
          mockAOPending.allocationOrderId,
          mockUser,
          [mockInspectionJob[0].jobId, mockInspectionJob[1].jobId],
          mockAO[0].fromBranchId!,
          mockAO[0].toBranchId!,
          AllocationOrderType.WHOLESALE,
        );
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.INVALID_AO_STATUS_TO_UPDATE.code,
        );
        expect((error as BaseExceptionService).message).toBe(
          'Invalid status for update ao',
        );
        expect((error as BaseExceptionService).data).toBe(
          `Allocation order status must be ${AllocationOrderStatus.DRAFT}`,
        );
      }
    });

    it('should error not found fromBranch', async () => {
      jest
        .spyOn(allocationOrdersRepository, 'findOne')
        .mockResolvedValueOnce(mockAODraft);

      jest.spyOn(branchRepository, 'findOne').mockResolvedValueOnce(null);
      jest.spyOn(branchRepository, 'findOne').mockResolvedValueOnce(null);

      try {
        await adminAllocationOrdersService.createAllocationOrder(
          mockAOPending.allocationOrderId,
          mockUser,
          [mockInspectionJob[0].jobId, mockInspectionJob[1].jobId],
          mockAO[0].fromBranchId!,
          mockAO[0].toBranchId!,
          AllocationOrderType.WHOLESALE,
        );
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.NOT_FOUND_DATA.code,
        );
        expect((error as BaseExceptionService).message).toBe('Not found data');
        expect((error as BaseExceptionService).data).toBe(`Branch not found`);
      }
    });

    it('should error not found toBranch', async () => {
      jest
        .spyOn(allocationOrdersRepository, 'findOne')
        .mockResolvedValueOnce(mockAODraft);

      jest.spyOn(branchRepository, 'findOne').mockResolvedValueOnce(null);
      jest.spyOn(branchRepository, 'findOne').mockResolvedValueOnce(null);

      try {
        await adminAllocationOrdersService.createAllocationOrder(
          mockAOPending.allocationOrderId,
          mockUser,
          [mockInspectionJob[0].jobId, mockInspectionJob[1].jobId],
          mockAO[0].fromBranchId!,
          mockAO[0].toBranchId!,
          AllocationOrderType.WHOLESALE,
        );
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.NOT_FOUND_DATA.code,
        );
        expect((error as BaseExceptionService).message).toBe('Not found data');
        expect((error as BaseExceptionService).data).toBe(`Branch not found`);
      }
    });

    it('should error not found fromBranch Type', async () => {
      jest
        .spyOn(allocationOrdersRepository, 'findOne')
        .mockResolvedValueOnce(mockAODraft);

      jest.spyOn(branchRepository, 'findOne').mockResolvedValueOnce({
        ...mockBranch,
        branchType: branchType.DEALER,
      });

      try {
        await adminAllocationOrdersService.createAllocationOrder(
          mockAOPending.allocationOrderId,
          mockUser,
          [mockInspectionJob[0].jobId, mockInspectionJob[1].jobId],
          mockAO[0].fromBranchId!,
          mockAO[0].toBranchId!,
          AllocationOrderType.WHOLESALE,
        );
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.INVALID_BRANCH_TYPE.code,
        );
        expect((error as BaseExceptionService).message).toBe(
          'Invalid branchType',
        );
        expect((error as BaseExceptionService).data).toBe(
          `fromBranchType is not ${branchType.SHOP}/${branchType.WAREHOUSE}`,
        );
      }
    });

    it('should error duplicated job', async () => {
      jest
        .spyOn(allocationOrdersRepository, 'findOne')
        .mockResolvedValueOnce(mockAODraft);

      jest.spyOn(branchRepository, 'findOne').mockResolvedValueOnce({
        ...mockBranch,
        branchType: branchType.WAREHOUSE,
      });

      try {
        await adminAllocationOrdersService.createAllocationOrder(
          mockAOPending.allocationOrderId,
          mockUser,
          [
            mockInspectionJob[0].jobId,
            mockInspectionJob[0].jobId,
            mockInspectionJob[1].jobId,
          ],
          mockAO[0].fromBranchId!,
          mockAO[0].toBranchId!,
          AllocationOrderType.WHOLESALE,
        );
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.DUPLICATED_JOB.code,
        );
        expect((error as BaseExceptionService).message).toBe(
          'Duplicated job input',
        );
        expect((error as BaseExceptionService).data).toBe(
          `Input jobList has duplicated data`,
        );
      }
    });

    it('should error invalid job input', async () => {
      jest
        .spyOn(allocationOrdersRepository, 'findOne')
        .mockResolvedValueOnce(mockAODraft);

      jest.spyOn(branchRepository, 'findOne').mockResolvedValueOnce({
        ...mockBranch,
        branchType: branchType.WAREHOUSE,
      });

      try {
        await adminAllocationOrdersService.createAllocationOrder(
          mockAOPending.allocationOrderId,
          mockUser,
          [mockInspectionJob[0].jobId, mockInspectionJob[1].jobId],
          mockAO[0].fromBranchId!,
          mockAO[0].toBranchId!,
          AllocationOrderType.WHOLESALE,
        );
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.INVALID_JOB_ID_INPUT.code,
        );
        expect((error as BaseExceptionService).message).toBe(
          'Invalid jobId from jobList',
        );
        expect((error as BaseExceptionService).data).toBe(`Invalid jobId`);
      }
    });

    it('should error job already change', async () => {
      jest
        .spyOn(allocationOrdersRepository, 'findOne')
        .mockResolvedValueOnce(mockAO[1]);

      jest
        .spyOn(branchRepository, 'findOne')
        .mockResolvedValueOnce({ ...mockBranch, branchType: branchType.SHOP });
      jest
        .spyOn(branchRepository, 'findOne')
        .mockResolvedValueOnce({ ...mockBranch, branchType: branchType.SHOP });
      jest
        .spyOn(jobsRepository, 'find')
        .mockResolvedValue([
          mockBaseJob,
          mockInspectionJobWithUpdate,
          mockInspectionJobNoUpdate,
        ]);

      try {
        await adminAllocationOrdersService.createAllocationOrder(
          mockAO[0].allocationOrderId,
          mockUser,
          [
            mockBaseJob.jobId,
            mockInspectionJobWithUpdate.jobId,
            mockInspectionJobNoUpdate.jobId,
          ],
          mockAO[0].fromBranchId!,
          mockAO[0].toBranchId!,
          AllocationOrderType.WHOLESALE,
        );
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.JOB_ALREADY_CHANGED.code,
        );
        expect((error as BaseExceptionService).message).toBe(
          'Invalid job to create allocation order',
        );
        expect((error as BaseExceptionService).data).toMatchObject({
          jobList: [],
          unavailableJobList: ['test'],
          updatedJobList: ['00001', '00003'],
        });
      }
    });

    it('should error job already change (case not confirm price)', async () => {
      const mockNotConfirmJob = {
        ...mockInspectionJobWithUpdate,
        isConfirmPrice: false,
        updatedAt: new Date('2000-02-14T00:00:00.000Z'),
        isUpdatedToEstimated: () => true,
      };

      jest
        .spyOn(allocationOrdersRepository, 'findOne')
        .mockResolvedValueOnce(mockAO[1]);

      jest
        .spyOn(branchRepository, 'findOne')
        .mockResolvedValueOnce({ ...mockBranch, branchType: branchType.SHOP });
      jest
        .spyOn(branchRepository, 'findOne')
        .mockResolvedValueOnce({ ...mockBranch, branchType: branchType.SHOP });
      jest
        .spyOn(jobsRepository, 'find')
        .mockResolvedValue([
          mockBaseJob,
          mockNotConfirmJob,
          mockInspectionJobNoUpdate,
        ]);

      try {
        await adminAllocationOrdersService.createAllocationOrder(
          mockAO[0].allocationOrderId,
          mockUser,
          [
            mockBaseJob.jobId,
            mockNotConfirmJob.jobId,
            mockInspectionJobNoUpdate.jobId,
          ],
          mockAO[0].fromBranchId!,
          mockAO[0].toBranchId!,
          AllocationOrderType.WHOLESALE,
        );
        fail('Expected method to throw an error');
      } catch (error) {
        console.log('ERROR', (error as BaseExceptionService).data);
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.JOB_ALREADY_CHANGED.code,
        );
        expect((error as BaseExceptionService).message).toBe(
          'Invalid job to create allocation order',
        );
        expect((error as BaseExceptionService).data).toMatchObject({
          jobList: [],
          unavailableJobList: ['test'],
          updatedJobList: ['00001', '00003'],
        });
      }
    });
  });

  describe('Confirm Air way bill', () => {
    const today: [number, number, number, string] = [2024, 4, 16, '2024-04-16'];
    jest.mocked(getDateFromToday).mockImplementation((add) => today);
    const mockDate = new Date('2024-04-17');
    const mockPastDate = new Date('2024-04-15');
    const DEFAULT_AO: AllocationOrderEntity = {
      ...mockAOPending,
      createdBy: mockCreatedUser.userKey,
      createdUser: mockCreatedUserEntity,
      allocationOrderId: mockIdAO,
      allocationOrderType: AllocationOrderType.RETAIL,
      status: AllocationOrderStatus.APPOINTMENT_PENDING,
    };

    it.each([
      [true, true],
      [false, false],
    ])(`should found AO ( %s ) pass ( %s )`, async (foundAO, isPass) => {
      jest
        .spyOn(allocationOrdersRepository, 'findOne')
        .mockResolvedValue(foundAO ? { ...DEFAULT_AO } : null);
      jest
        .spyOn(jobsRepository, 'find')
        .mockResolvedValue(getMockJobs(10, mockIdAO, {}));

      const body: AOAwbConfirmDto = {
        awbNumber: '123456789',
        appointmentDate: mockDate.toISOString(),
        pickupDate: mockDate.toISOString(),
      };

      if (isPass) {
        const result = await adminAllocationOrdersService.confirmAWB({
          allocationOrderId: mockIdAO,
          user: mockCreatedUser,
          body,
        });

        expect(result).toBe(true);
      } else {
        try {
          await adminAllocationOrdersService.confirmAWB({
            allocationOrderId: mockIdAO,
            user: mockCreatedUser,
            body,
          });
          fail('Expected method to throw an error');
        } catch (error) {
          expect(error).toBeInstanceOf(BaseExceptionService);
          expect((error as BaseExceptionService).code).toBe(
            BASE_EXCEPTIONS.NOT_FOUND_DATA.code,
          );
          expect((error as BaseExceptionService).data).toBe(
            'Allocation order ID not found',
          );
        }
      }
    });

    it.each([
      [mockCreatedUser.userKey, true],
      ['not-created-user', false],
    ])(`should userKey ( %s ) pass ( %s )`, async (userKey, isPass) => {
      jest
        .spyOn(allocationOrdersRepository, 'findOne')
        .mockResolvedValue(DEFAULT_AO);
      jest
        .spyOn(jobsRepository, 'find')
        .mockResolvedValue(getMockJobs(10, mockIdAO, {}));

      const body: AOAwbConfirmDto = {
        awbNumber: '123456789',
        appointmentDate: mockDate.toISOString(),
        pickupDate: mockDate.toISOString(),
      };

      if (isPass) {
        const result = await adminAllocationOrdersService.confirmAWB({
          allocationOrderId: mockIdAO,
          user: { ...mockCreatedUser, userKey },
          body,
        });

        expect(result).toBe(true);
      } else {
        try {
          await adminAllocationOrdersService.confirmAWB({
            allocationOrderId: mockIdAO,
            user: { ...mockCreatedUser, userKey },
            body,
          });
          fail('Expected method to throw an error');
        } catch (error) {
          expect(error).toBeInstanceOf(BaseExceptionService);
          expect((error as BaseExceptionService).code).toBe(
            BASE_EXCEPTIONS.INVALID_AO_OWNER.code,
          );
          expect((error as BaseExceptionService).data).toBe(
            'Need permission for update AO',
          );
        }
      }
    });

    /*
     VALIDATE PAYLOAD - STATUS PREV - INPUT
     ไม่ test case ต่อไปนี้
       - !awbNumber - DTO validate
    */
    it.each([
      // Happy case
      [
        AllocationOrderType.RETAIL,
        '123456789',
        mockDate,
        mockDate,
        AllocationOrderStatus.APPOINTMENT_PENDING,
        true,
        undefined,
        undefined,
      ],
      [
        AllocationOrderType.RETAIL,
        '123456789',
        mockDate,
        undefined,
        AllocationOrderStatus.APPOINTMENT_PENDING,
        true,
        undefined,
        undefined,
      ],
      [
        AllocationOrderType.WHOLESALE,
        '123456789',
        undefined,
        mockDate,
        AllocationOrderStatus.APPOINTMENT_PENDING,
        true,
        undefined,
        undefined,
      ],
      // BODY_PAYLOAD_INVALID
      [
        AllocationOrderType.RETAIL,
        '123456789',
        undefined,
        undefined,
        AllocationOrderStatus.APPOINTMENT_PENDING,
        false,
        'BODY_PAYLOAD_INVALID',
        'AppointmentDate need for update AO',
      ],
      [
        AllocationOrderType.RETAIL,
        '123456789',
        undefined,
        mockDate,
        AllocationOrderStatus.APPOINTMENT_PENDING,
        false,
        'BODY_PAYLOAD_INVALID',
        'AppointmentDate need for update AO',
      ],
      [
        AllocationOrderType.WHOLESALE,
        '123456789',
        mockDate,
        undefined,
        AllocationOrderStatus.APPOINTMENT_PENDING,
        false,
        'BODY_PAYLOAD_INVALID',
        'PickupDate need for update AO',
      ],
      // INVALID_AO_STATUS_TO_UPDATE
      [
        AllocationOrderType.RETAIL,
        '123456789',
        mockDate,
        undefined,
        AllocationOrderStatus.DRAFT,
        false,
        'INVALID_AO_STATUS_TO_UPDATE',
        `Previous AO Status must be ${AllocationOrderStatus.APPOINTMENT_PENDING}`,
      ],
      [
        AllocationOrderType.RETAIL,
        '123456789',
        mockDate,
        undefined,
        AllocationOrderStatus.DELETED,
        false,
        'INVALID_AO_STATUS_TO_UPDATE',
        `Previous AO Status must be ${AllocationOrderStatus.APPOINTMENT_PENDING}`,
      ],
      [
        AllocationOrderType.RETAIL,
        '123456789',
        mockDate,
        undefined,
        AllocationOrderStatus.APPOINTMENT_CONFIRMED,
        false,
        'INVALID_AO_STATUS_TO_UPDATE',
        `Previous AO Status must be ${AllocationOrderStatus.APPOINTMENT_PENDING}`,
      ],
      [
        AllocationOrderType.RETAIL,
        '123456789',
        mockDate,
        undefined,
        AllocationOrderStatus.DELIVERY_SUCCESSFUL,
        false,
        'INVALID_AO_STATUS_TO_UPDATE',
        `Previous AO Status must be ${AllocationOrderStatus.APPOINTMENT_PENDING}`,
      ],
      [
        AllocationOrderType.RETAIL,
        '123456789',
        mockDate,
        undefined,
        AllocationOrderStatus.IN_TRANSIT,
        false,
        'INVALID_AO_STATUS_TO_UPDATE',
        `Previous AO Status must be ${AllocationOrderStatus.APPOINTMENT_PENDING}`,
      ],
      [
        AllocationOrderType.RETAIL,
        '123456789',
        mockDate,
        undefined,
        AllocationOrderStatus.PARTIAL_RECEIVED,
        false,
        'INVALID_AO_STATUS_TO_UPDATE',
        `Previous AO Status must be ${AllocationOrderStatus.APPOINTMENT_PENDING}`,
      ],
      // INVALID_INPUT_FOR_UPDATE_AO
      [
        AllocationOrderType.RETAIL,
        '123456789',
        mockPastDate,
        undefined,
        AllocationOrderStatus.APPOINTMENT_PENDING,
        false,
        'INVALID_INPUT_FOR_UPDATE_AO',
        'Appoinment date incorrect. The appointment date cannot be in the past',
      ],
      [
        AllocationOrderType.WHOLESALE,
        '123456789',
        undefined,
        mockPastDate,
        AllocationOrderStatus.APPOINTMENT_PENDING,
        false,
        'INVALID_INPUT_FOR_UPDATE_AO',
        'Pickup date incorrect. The pickup date cannot be in the past',
      ],
      [
        AllocationOrderType.WHOLESALE,
        '12345678',
        undefined,
        mockDate,
        AllocationOrderStatus.APPOINTMENT_PENDING,
        false,
        'INVALID_INPUT_FOR_UPDATE_AO',
        'Air way bill incorrect. The air way bill must have 9 character',
      ],
      [
        AllocationOrderType.WHOLESALE,
        '1234567890',
        undefined,
        mockDate,
        AllocationOrderStatus.APPOINTMENT_PENDING,
        false,
        'INVALID_INPUT_FOR_UPDATE_AO',
        'Air way bill incorrect. The air way bill must have 9 character',
      ],
      [
        AllocationOrderType.WHOLESALE,
        '1234567B9',
        undefined,
        mockDate,
        AllocationOrderStatus.APPOINTMENT_PENDING,
        false,
        'INVALID_INPUT_FOR_UPDATE_AO',
        'Air way bill incorrect. The air way bill must be number',
      ],
    ])(
      `should type ( %s ) awb ( %s ) appointment date ( %s ) pickup date ( %s ) status ( %s ) pass ( %s ) error ( %s ) error message ( %s )`,
      async (
        type,
        awbNumber,
        appointmentDate,
        pickupDate,
        status,
        isPass,
        errorType,
        errorMsg,
      ) => {
        jest
          .spyOn(allocationOrdersRepository, 'findOne')
          .mockResolvedValue(
            type === AllocationOrderType.WHOLESALE
              ? { ...DEFAULT_AO, allocationOrderType: type, status }
              : { ...DEFAULT_AO, status },
          );
        jest
          .spyOn(jobsRepository, 'find')
          .mockResolvedValue(getMockJobs(10, mockIdAO, {}));

        const body: AOAwbConfirmDto = { awbNumber };
        if (appointmentDate) {
          body.appointmentDate = appointmentDate.toISOString();
        }
        if (pickupDate) {
          body.pickupDate = pickupDate.toISOString();
        }

        if (isPass) {
          const result = await adminAllocationOrdersService.confirmAWB({
            allocationOrderId: mockIdAO,
            user: mockCreatedUser,
            body,
          });

          expect(result).toBe(true);
        } else {
          try {
            await adminAllocationOrdersService.confirmAWB({
              allocationOrderId: mockIdAO,
              user: mockCreatedUser,
              body,
            });
            fail('Expected method to throw an error');
          } catch (error) {
            expect(error).toBeInstanceOf(BaseExceptionService);
            expect((error as BaseExceptionService).code).toBe(
              BASE_EXCEPTIONS[errorType as string].code,
            );
            expect((error as BaseExceptionService).data).toBe(errorMsg);
          }
        }
      },
    );
  });

  describe('Confirm Pickup', () => {
    const DEFAULT_AO: AllocationOrderEntity = {
      ...mockAOPending,
      createdBy: mockCreatedUser.userKey,
      createdUser: mockCreatedUserEntity,
      allocationOrderId: mockIdAO,
      allocationOrderType: AllocationOrderType.RETAIL,
      status: AllocationOrderStatus.APPOINTMENT_CONFIRMED,
    };

    it.each([
      [true, true],
      [false, false],
    ])(`should found AO ( %s ) pass ( %s )`, async (foundAO, isPass) => {
      jest
        .spyOn(allocationOrdersRepository, 'findOne')
        .mockResolvedValue(foundAO ? { ...DEFAULT_AO } : null);
      jest
        .spyOn(jobsRepository, 'find')
        .mockResolvedValue(getMockJobs(10, mockIdAO, {}));

      const body: AOPickupConfirmDto = {
        transporterName: 'test Name',
        transporterMobileNumber: '*********9',
      };

      if (isPass) {
        const result = await adminAllocationOrdersService.confirmPickup({
          allocationOrderId: mockIdAO,
          user: mockCreatedUser,
          body,
        });

        expect(result).toBe(true);
      } else {
        try {
          await adminAllocationOrdersService.confirmPickup({
            allocationOrderId: mockIdAO,
            user: mockCreatedUser,
            body,
          });
          fail('Expected method to throw an error');
        } catch (error) {
          expect(error).toBeInstanceOf(BaseExceptionService);
          expect((error as BaseExceptionService).code).toBe(
            BASE_EXCEPTIONS.NOT_FOUND_DATA.code,
          );
          expect((error as BaseExceptionService).data).toBe(
            'Allocation order ID not found',
          );
        }
      }
    });

    it.each([
      [mockCreatedUser.userKey, true],
      ['not-created-user', false],
    ])(`should userKey ( %s ) pass ( %s )`, async (userKey, isPass) => {
      jest
        .spyOn(allocationOrdersRepository, 'findOne')
        .mockResolvedValue({ ...DEFAULT_AO });
      jest
        .spyOn(jobsRepository, 'find')
        .mockResolvedValue(getMockJobs(10, mockIdAO, {}));

      const body: AOPickupConfirmDto = {
        transporterName: 'test Name',
        transporterMobileNumber: '*********9',
      };

      if (isPass) {
        const result = await adminAllocationOrdersService.confirmPickup({
          allocationOrderId: mockIdAO,
          user: { ...mockCreatedUser, userKey },
          body,
        });

        expect(result).toBe(true);
      } else {
        try {
          await adminAllocationOrdersService.confirmPickup({
            allocationOrderId: mockIdAO,
            user: { ...mockCreatedUser, userKey },
            body,
          });
          fail('Expected method to throw an error');
        } catch (error) {
          expect(error).toBeInstanceOf(BaseExceptionService);
          expect((error as BaseExceptionService).code).toBe(
            BASE_EXCEPTIONS.INVALID_AO_OWNER.code,
          );
          expect((error as BaseExceptionService).data).toBe(
            'Need permission for update AO',
          );
        }
      }
    });

    /*
     VALIDATE STATUS PREV - INPUT
     ไม่ test case ต่อไปนี้
       - !transporterName - DTO validate
       - !transporterMobileNumber - DTO validate
    */
    it.each([
      [
        AllocationOrderType.RETAIL,
        'mock name pass',
        '*********9',
        AllocationOrderStatus.APPOINTMENT_CONFIRMED,
        true,
        undefined,
        undefined,
      ],
      // INVALID_AO_STATUS_TO_UPDATE
      [
        AllocationOrderType.RETAIL,
        'mock name pass',
        '*********9',
        AllocationOrderStatus.DRAFT,
        false,
        'INVALID_AO_STATUS_TO_UPDATE',
        `Previous AO Status must be ${AllocationOrderStatus.APPOINTMENT_CONFIRMED}`,
      ],
      [
        AllocationOrderType.RETAIL,
        'mock name pass',
        '*********9',
        AllocationOrderStatus.DELETED,
        false,
        'INVALID_AO_STATUS_TO_UPDATE',
        `Previous AO Status must be ${AllocationOrderStatus.APPOINTMENT_CONFIRMED}`,
      ],
      [
        AllocationOrderType.RETAIL,
        'mock name pass',
        '*********9',
        AllocationOrderStatus.APPOINTMENT_PENDING,
        false,
        'INVALID_AO_STATUS_TO_UPDATE',
        `Previous AO Status must be ${AllocationOrderStatus.APPOINTMENT_CONFIRMED}`,
      ],
      [
        AllocationOrderType.RETAIL,
        'mock name pass',
        '*********9',
        AllocationOrderStatus.IN_TRANSIT,
        false,
        'INVALID_AO_STATUS_TO_UPDATE',
        `Previous AO Status must be ${AllocationOrderStatus.APPOINTMENT_CONFIRMED}`,
      ],
      [
        AllocationOrderType.RETAIL,
        'mock name pass',
        '*********9',
        AllocationOrderStatus.DELIVERY_SUCCESSFUL,
        false,
        'INVALID_AO_STATUS_TO_UPDATE',
        `Previous AO Status must be ${AllocationOrderStatus.APPOINTMENT_CONFIRMED}`,
      ],
      [
        AllocationOrderType.RETAIL,
        'mock name pass',
        '*********9',
        AllocationOrderStatus.PARTIAL_RECEIVED,
        false,
        'INVALID_AO_STATUS_TO_UPDATE',
        `Previous AO Status must be ${AllocationOrderStatus.APPOINTMENT_CONFIRMED}`,
      ],
      [
        AllocationOrderType.WHOLESALE,
        'mock name pass',
        '*********9',
        AllocationOrderStatus.APPOINTMENT_CONFIRMED,
        false,
        'INVALID_AO_STATUS_TO_UPDATE',
        `allocationOrderType must be ${AllocationOrderType.RETAIL}`,
      ],
      // INVALID_INPUT_FOR_UPDATE_AO
      [
        AllocationOrderType.RETAIL,
        'A'.repeat(101),
        '*********9',
        AllocationOrderStatus.APPOINTMENT_CONFIRMED,
        false,
        'INVALID_INPUT_FOR_UPDATE_AO',
        'TransporterName too long',
      ],
      [
        AllocationOrderType.RETAIL,
        'mock name pass',
        '*********',
        AllocationOrderStatus.APPOINTMENT_CONFIRMED,
        false,
        'INVALID_INPUT_FOR_UPDATE_AO',
        'TransporterMobileNumber invalid format',
      ],
      [
        AllocationOrderType.RETAIL,
        'mock name pass',
        '*********90',
        AllocationOrderStatus.APPOINTMENT_CONFIRMED,
        false,
        'INVALID_INPUT_FOR_UPDATE_AO',
        'TransporterMobileNumber invalid format',
      ],
      [
        AllocationOrderType.RETAIL,
        'mock name pass',
        '*********x',
        AllocationOrderStatus.APPOINTMENT_CONFIRMED,
        false,
        'INVALID_INPUT_FOR_UPDATE_AO',
        'TransporterMobileNumber invalid format',
      ],
    ])(
      `should type ( %s ) transporterName( %s ) transporterMobileNumber ( %s ) status ( %s ) pass ( %s ) error ( %s ) error message ( %s )`,
      async (
        type,
        transporterName,
        transporterMobileNumber,
        status,
        isPass,
        errorType,
        errorMsg,
      ) => {
        jest
          .spyOn(allocationOrdersRepository, 'findOne')
          .mockResolvedValue(
            type === AllocationOrderType.WHOLESALE
              ? { ...DEFAULT_AO, allocationOrderType: type, status }
              : { ...DEFAULT_AO, status },
          );
        jest
          .spyOn(jobsRepository, 'find')
          .mockResolvedValue(getMockJobs(10, mockIdAO, {}));

        const body: AOPickupConfirmDto = {
          transporterName,
          transporterMobileNumber,
        };

        if (isPass) {
          const result = await adminAllocationOrdersService.confirmPickup({
            allocationOrderId: mockIdAO,
            user: mockCreatedUser,
            body,
          });

          expect(result).toBe(true);
        } else {
          try {
            await adminAllocationOrdersService.confirmPickup({
              allocationOrderId: mockIdAO,
              user: mockCreatedUser,
              body,
            });
            fail('Expected method to throw an error');
          } catch (error) {
            expect(error).toBeInstanceOf(BaseExceptionService);
            expect((error as BaseExceptionService).code).toBe(
              BASE_EXCEPTIONS[errorType as string].code,
            );
            expect((error as BaseExceptionService).data).toBe(errorMsg);
          }
        }
      },
    );
  });

  describe('get Transaction', () => {
    const today: [number, number, number, string] = [2024, 4, 16, '2024-04-16'];
    jest.mocked(getDateFromToday).mockImplementation((add) => today);
    const mockDate = new Date('2024-04-17');
    const DEFAULT_AO: AllocationOrderEntity = {
      ...mockAOPending,
      createdBy: mockCreatedUser.userKey,
      createdUser: mockCreatedUserEntity,
      allocationOrderId: mockIdAO,
      allocationOrderType: AllocationOrderType.RETAIL,
      status: AllocationOrderStatus.APPOINTMENT_CONFIRMED,
      companyId: 'WW',
      awbNumber: '*********',
      appointmentDate: new Date('2024-04-15'),
      pickupDate: new Date('2024-04-17'),
      fromBranchId: 'from-branch',
      toBranchId: 'to-branch',
    };

    it.each([
      [true, true],
      [false, false],
    ])(`should found AO ( %s ) pass ( %s )`, async (foundAO, isPass) => {
      jest
        .spyOn(allocationOrdersRepository, 'findOne')
        .mockResolvedValue(foundAO ? { ...DEFAULT_AO } : null);
      jest.spyOn(companyRepository, 'findOne').mockResolvedValue(mockCompany);
      jest
        .spyOn(jobsRepository, 'find')
        .mockResolvedValue(getMockJobs(10, mockIdAO, {}));

      if (isPass) {
        const result =
          await adminAllocationOrdersService.getTransaction(mockIdAO);

        expect(result).toBe('contract');
      } else {
        try {
          await adminAllocationOrdersService.getTransaction(mockIdAO);
          fail('Expected method to throw an error');
        } catch (error) {
          expect(error).toBeInstanceOf(BaseExceptionService);
          expect((error as BaseExceptionService).code).toBe(
            BASE_EXCEPTIONS.NOT_FOUND_DATA.code,
          );
          expect((error as BaseExceptionService).data).toBe(
            'Allocation order ID not found',
          );
        }
      }
    });

    it.each([
      [true, true],
      [false, false],
    ])(`should found logo ( %s ) pass ( %s )`, async (hasLogoPath, isPass) => {
      jest
        .spyOn(allocationOrdersRepository, 'findOne')
        .mockResolvedValue({ ...DEFAULT_AO });
      jest.spyOn(companyRepository, 'findOne').mockResolvedValue({
        ...mockCompany,
        logoPath: hasLogoPath ? 'logo-path' : '',
      });
      jest
        .spyOn(jobsRepository, 'find')
        .mockResolvedValue(getMockJobs(10, mockIdAO, {}));

      if (isPass) {
        const result =
          await adminAllocationOrdersService.getTransaction(mockIdAO);

        expect(result).toBe('contract');
      } else {
        try {
          await adminAllocationOrdersService.getTransaction(mockIdAO);
          fail('Expected method to throw an error');
        } catch (error) {
          expect(error).toBeInstanceOf(BaseExceptionService);
          expect((error as BaseExceptionService).code).toBe(
            BASE_EXCEPTIONS.NOT_FOUND_DATA.code,
          );
          expect((error as BaseExceptionService).data).toBe(
            'Company image not found - invalid url',
          );
        }
      }
    });

    it.each([
      // happy
      [
        AllocationOrderType.RETAIL,
        AllocationOrderStatus.APPOINTMENT_CONFIRMED,
        false,
        true,
        undefined,
        undefined,
      ],
      [
        AllocationOrderType.RETAIL,
        AllocationOrderStatus.IN_TRANSIT,
        false,
        true,
        undefined,
        undefined,
      ],
      [
        AllocationOrderType.RETAIL,
        AllocationOrderStatus.PARTIAL_RECEIVED,
        false,
        true,
        undefined,
        undefined,
      ],
      [
        AllocationOrderType.RETAIL,
        AllocationOrderStatus.DELIVERY_SUCCESSFUL,
        false,
        true,
        undefined,
        undefined,
      ],
      [
        AllocationOrderType.RETAIL,
        AllocationOrderStatus.APPOINTMENT_CONFIRMED,
        'pickupDate',
        true,
        undefined,
        undefined,
      ],
      [
        AllocationOrderType.WHOLESALE,
        AllocationOrderStatus.DELIVERY_SUCCESSFUL,
        'appointmentDate',
        true,
        undefined,
        undefined,
      ],
      // case status not match
      [
        AllocationOrderType.RETAIL,
        AllocationOrderStatus.DRAFT,
        'pickupDate',
        false,
        'INVALID_AO_FOR_ACTION',
        'Allocation order status not match.',
      ],
      [
        AllocationOrderType.RETAIL,
        AllocationOrderStatus.APPOINTMENT_PENDING,
        'pickupDate',
        false,
        'INVALID_AO_FOR_ACTION',
        'Allocation order status not match.',
      ],
      [
        AllocationOrderType.RETAIL,
        AllocationOrderStatus.DELETED,
        'pickupDate',
        false,
        'INVALID_AO_FOR_ACTION',
        'Allocation order status not match.',
      ],
      // case previous field
      [
        AllocationOrderType.RETAIL,
        AllocationOrderStatus.APPOINTMENT_CONFIRMED,
        'awbNumber',
        false,
        'INVALID_AO_FOR_ACTION',
        undefined,
      ],
      [
        AllocationOrderType.WHOLESALE,
        AllocationOrderStatus.DELIVERY_SUCCESSFUL,
        'pickupDate',
        false,
        'INVALID_AO_FOR_ACTION',
        undefined,
      ],
      [
        AllocationOrderType.RETAIL,
        AllocationOrderStatus.APPOINTMENT_CONFIRMED,
        'appointmentDate',
        false,
        'INVALID_AO_FOR_ACTION',
        undefined,
      ],
      [
        AllocationOrderType.RETAIL,
        AllocationOrderStatus.APPOINTMENT_CONFIRMED,
        'fromBranchId',
        false,
        'INVALID_AO_FOR_ACTION',
        undefined,
      ],
      [
        AllocationOrderType.RETAIL,
        AllocationOrderStatus.APPOINTMENT_CONFIRMED,
        'toBranchId',
        false,
        'INVALID_AO_FOR_ACTION',
        undefined,
      ],
    ])(
      `should type ( %s ) status ( %s ) undefined fill ( %s ) pass ( %s )`,
      async (
        allocationOrderType,
        status,
        undefinedDataField,
        isPass,
        errorType,
        errorMsg,
      ) => {
        let dataAO = { ...DEFAULT_AO, allocationOrderType, status };
        if (undefinedDataField) {
          dataAO[undefinedDataField as string] = undefined;
        }
        jest
          .spyOn(allocationOrdersRepository, 'findOne')
          .mockResolvedValue(dataAO);
        jest.spyOn(companyRepository, 'findOne').mockResolvedValue(mockCompany);
        jest
          .spyOn(jobsRepository, 'find')
          .mockResolvedValue(getMockJobs(10, mockIdAO, {}));

        if (isPass) {
          const result =
            await adminAllocationOrdersService.getTransaction(mockIdAO);

          expect(result).toBe('contract');
        } else {
          try {
            await adminAllocationOrdersService.getTransaction(mockIdAO);
            fail('Expected method to throw an error');
          } catch (error) {
            expect(error).toBeInstanceOf(BaseExceptionService);
            expect((error as BaseExceptionService).code).toBe(
              BASE_EXCEPTIONS[errorType as string].code,
            );
            if (errorMsg) {
              expect((error as BaseExceptionService).data).toBe(errorMsg);
            }
          }
        }
      },
    );
  });

  describe('Get Presign Download', () => {
    const request = {
      aoId: 'aoid-0001',
      key: 'test_video_2221111',
    };
    it('should successfully get Presign Download', async () => {
      const mockResult = 'presignurl-download';
      const mockAOWithVideoPath = {
        ...mockAO[0],
        videoPath: 'test-video',
      } as AllocationOrderEntity;

      jest
        .spyOn(allocationOrdersRepository, 'findOne')
        .mockResolvedValue(mockAOWithVideoPath);

      const result = await adminAllocationOrdersService.getPresignDownload(
        request.aoId,
      );

      // Assertions
      expect(result.url).toContain(mockResult);
    });

    it('should error aoId not found', async () => {
      jest.spyOn(allocationOrdersRepository, 'findOne').mockResolvedValue(null);

      try {
        await adminAllocationOrdersService.getPresignDownload(request.aoId);
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.NOT_FOUND_DATA.code,
        );
        expect((error as BaseExceptionService).data).toBe(
          'Allocation order ID not found',
        );
      }
    });

    it('should error video path not found', async () => {
      const mockAOWithOutVideoPath = {
        ...mockAO[0],
        videoPath: undefined,
      } as AllocationOrderEntity;

      jest
        .spyOn(allocationOrdersRepository, 'findOne')
        .mockResolvedValue(mockAOWithOutVideoPath);

      try {
        await adminAllocationOrdersService.getPresignDownload(request.aoId);
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.NOT_FOUND_DATA.code,
        );
        expect((error as BaseExceptionService).data).toBe(
          'Video path not found',
        );
      }
    });
  });
});
