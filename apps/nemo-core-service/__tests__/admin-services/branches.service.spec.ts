import { getRepositoryToken } from '@nestjs/typeorm';
import { Test } from '@nestjs/testing';
import { BranchEntity, CompanyEntity } from '../../src/entities';
import { BaseExceptionModule } from '../../src/base-exceptions/base-exceptions.module';
import {
  BranchesService,
  excelBranchesOption,
} from '../../src/admin/branches/branches.service';
import { Request } from 'express';
import { mockBranchEntity } from '../mock-data/branches';
import { Repository, SelectQueryBuilder } from 'typeorm';
import {
  mockGetBranchesRequest,
  mockImporrtError,
} from '../mock-data/branches';
import { WithUserContext } from '../../src/interfaces';
import { ExcelManagerModule } from '../../src/excel/excel-manager.module';
import { ExcelManagerService } from '../../src/excel/excel-manager.service';
import { BaseExceptionService } from '../../src/exceptions';
import { BASE_EXCEPTIONS } from '../../src/config';
import { EncryptDecryptModule } from '../../src/encrypt-decrypt-message/encrypt-decrypt.module';

describe('BranchesService', () => {
  let branchesService: BranchesService;
  let branchRepository: Repository<BranchEntity>;
  let excelManagerService: ExcelManagerService;

  beforeAll(async () => {
    const module = await Test.createTestingModule({
      imports: [
        EncryptDecryptModule.register(),
        BaseExceptionModule.register(),
        ExcelManagerModule.register(excelBranchesOption),
      ],
      providers: [
        BranchesService,
        {
          provide: getRepositoryToken(BranchEntity),
          useValue: {
            manager: {
              transaction: jest.fn().mockReturnThis(),
            },
          },
        },
        {
          provide: getRepositoryToken(CompanyEntity),
          useValue: {
            findOne: jest.fn().mockReturnThis(),
          },
        },
      ],
    }).compile();

    branchesService = module.get<BranchesService>(BranchesService);
    branchRepository = module.get<Repository<BranchEntity>>(
      getRepositoryToken(BranchEntity),
    );
    excelManagerService = module.get<ExcelManagerService>(ExcelManagerService);
  });

  beforeEach(() => {
    jest.clearAllMocks();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('Get Branches', () => {
    // using default build search query
    describe('afterLoad should contain label for display in dropdown', () => {
      it('should return label correctly', () => {
        const mockRequest = {
          query: {
            pagination: 'false',
            orderBy: 'branchId asc',
          },
        } as unknown as Request;

        const result = branchesService.afterLoad(mockRequest, [
          mockBranchEntity,
        ]);

        expect(result).toEqual(
          expect.arrayContaining([
            expect.objectContaining({ label: 'branchId#1 title#1' }),
          ]),
        );
      });
    });
    describe('exportBranches', () => {
      it('should return buffer', async () => {
        const result = await branchesService.exportBranches([
          { branchId: 'branchId1', title: 'title1', branchType: 'Shop' },
        ] as BranchEntity[]);
        expect(result).toBeInstanceOf(Buffer);
      });
    });

    describe('buildSearchQuery', () => {
      const mockQueryBuilder = {
        andWhere: jest.fn(),
      } as unknown as SelectQueryBuilder<BranchEntity>;
      it.each(['branchId', 'title', 'branchType', 'label', 'labelAndBranch'])(
        'query param each case',
        async (testCase) => {
          const mockParam = mockGetBranchesRequest[testCase];
          const mockRequest = mockParam.request as unknown as Request & {
            withUserContext?: WithUserContext;
          };
          const result = branchesService.buildSearchQuery(
            mockRequest,
            mockQueryBuilder,
          );

          expect(result.andWhere).toHaveBeenCalledWith(mockParam.result);
        },
      );
    });
  });

  describe('putBranches', () => {
    const mockImporrtData = [
      {
        branchId: 'branchId1',
        title: 'title1',
        branchType: 'Shop',
        titleEn: 'titleEn',
        addressTh: 'ทดสอบที่อยู่',
        provinceTh: 'ทดสอบจังหวัด',
        districtTh: 'ทดสอบอำเภอ',
        subDistrictTh: 'ทดสอบตำบล',
        addressEn: 'test address',
        provinceEn: 'test province',
        districtEn: 'test district',
        subDistrictEn: 'test subdistrict',
        zipCode: '10000',
        latitude: 50.12567,
        longitude: 150.12367,
      },
      {
        branchId: 'branchId2',
        title: 'title2',
        branchType: 'Shop',
        titleEn: 'titleEn',
        addressTh: 'ทดสอบที่อยู่',
        provinceTh: 'ทดสอบจังหวัด',
        districtTh: 'ทดสอบอำเภอ',
        subDistrictTh: 'ทดสอบตำบล',
        addressEn: 'test address',
        provinceEn: 'test province',
        districtEn: 'test district',
        subDistrictEn: 'test subdistrict',
        zipCode: '10000',
        latitude: 50.1234567,
        longitude: 150.1234567,
      },
      {
        branchId: 'branchId3',
        title: 'title3',
        branchType: 'Shop',
        titleEn: 'titleEn',
        addressTh: 'ทดสอบที่อยู่',
        provinceTh: 'ทดสอบจังหวัด',
        districtTh: 'ทดสอบอำเภอ',
        subDistrictTh: 'ทดสอบตำบล',
        addressEn: 'test address',
        provinceEn: 'test province',
        districtEn: 'test district',
        subDistrictEn: 'test subdistrict',
        zipCode: '10000',
        latitude: 50.1234567,
        longitude: 150.1234567,
      },
    ] as BranchEntity[];
    it('should success put', async () => {
      const excelBuffer = await excelManagerService.generateExcelFile(
        mockImporrtData,
        'Sheet name',
      );
      await branchesService.putBranches('company', {
        buffer: excelBuffer,
        mimetype:
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      } as unknown as Express.Multer.File);
      expect(branchRepository.manager.transaction).toHaveBeenCalledTimes(1);
    });
    it('should return cost center error', async () => {
      const excelBuffer = await excelManagerService.generateExcelFile(
        mockImporrtData.map((val) => {
          return { ...val, costCenter: 'invalid' };
        }),
        'Sheet name',
      );
      let error;
      try {
        await branchesService.putBranches('company', {
          buffer: excelBuffer,
          mimetype:
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        } as unknown as Express.Multer.File);
      } catch (err) {
        error = err;
      }

      expect(error).toBeInstanceOf(BaseExceptionService);
      expect(error.message).toEqual(
        BASE_EXCEPTIONS.INVALID_DATA_FORMAT.message,
      );
      expect(error.data).toEqual(`Invalid cost center format.`);
    });
    it('should return branch type error', async () => {
      const excelBuffer = await excelManagerService.generateExcelFile(
        mockImporrtData.map((val) => {
          return { ...val, branchType: 'invalid' };
        }),
        'Sheet name',
      );
      let error;
      try {
        await branchesService.putBranches('company', {
          buffer: excelBuffer,
          mimetype:
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        } as unknown as Express.Multer.File);
      } catch (err) {
        error = err;
      }

      expect(error).toBeInstanceOf(BaseExceptionService);
      expect(error.message).toEqual(
        BASE_EXCEPTIONS.INVALID_DATA_FORMAT.message,
      );
    });
    it('should return zip code error', async () => {
      const excelBuffer = await excelManagerService.generateExcelFile(
        [mockImporrtError[0]],
        'Sheet name',
      );
      let error;
      try {
        await branchesService.putBranches('company', {
          buffer: excelBuffer,
          mimetype:
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        } as unknown as Express.Multer.File);
      } catch (err) {
        error = err;
      }

      expect(error).toBeInstanceOf(BaseExceptionService);
      expect(error.message).toEqual(
        BASE_EXCEPTIONS.INVALID_DATA_FORMAT.message,
      );
      expect(error.data).toBe('Invalid zip code. Zip code must have 5 digits.');
    });
    it('should return latitude error (range)', async () => {
      const excelBuffer = await excelManagerService.generateExcelFile(
        [mockImporrtError[1]],
        'Sheet name',
      );
      let error;
      try {
        await branchesService.putBranches('company', {
          buffer: excelBuffer,
          mimetype:
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        } as unknown as Express.Multer.File);
      } catch (err) {
        error = err;
      }

      expect(error).toBeInstanceOf(BaseExceptionService);
      expect(error.message).toEqual(BASE_EXCEPTIONS.INVALID_RANGE.message);
      expect(error.data).toBe('LATITUDE is in invalid range');
    });
    it('should return longitude error (range)', async () => {
      const excelBuffer = await excelManagerService.generateExcelFile(
        [mockImporrtError[2]],
        'Sheet name',
      );
      let error;
      try {
        await branchesService.putBranches('company', {
          buffer: excelBuffer,
          mimetype:
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        } as unknown as Express.Multer.File);
      } catch (err) {
        error = err;
      }

      expect(error).toBeInstanceOf(BaseExceptionService);
      expect(error.message).toEqual(BASE_EXCEPTIONS.INVALID_RANGE.message);
      expect(error.data).toBe('LONGITUDE is in invalid range');
    });
    it('should return latitude error (data format)', async () => {
      const excelBuffer = await excelManagerService.generateExcelFile(
        [mockImporrtError[3]],
        'Sheet name',
      );
      let error;
      try {
        await branchesService.putBranches('company', {
          buffer: excelBuffer,
          mimetype:
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        } as unknown as Express.Multer.File);
      } catch (err) {
        error = err;
      }

      expect(error).toBeInstanceOf(BaseExceptionService);
      expect(error.message).toEqual(
        BASE_EXCEPTIONS.INVALID_DATA_FORMAT.message,
      );
      expect(error.data).toBe('LATITUDE is in invalid format');
    });
    it('should return longitude error (data format)', async () => {
      const excelBuffer = await excelManagerService.generateExcelFile(
        [mockImporrtError[4]],
        'Sheet name',
      );
      let error;
      try {
        await branchesService.putBranches('company', {
          buffer: excelBuffer,
          mimetype:
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        } as unknown as Express.Multer.File);
      } catch (err) {
        error = err;
      }

      expect(error).toBeInstanceOf(BaseExceptionService);
      expect(error.message).toEqual(
        BASE_EXCEPTIONS.INVALID_DATA_FORMAT.message,
      );
      expect(error.data).toBe('LONGITUDE is in invalid format');
    });
    it('should throw error if branchType is PARTNER and partnerName is missing', async () => {
      const mockData = [
        {
          branchId: 'branchId1',
          title: 'title1',
          branchType: 'Partner',
          titleEn: 'titleEn',
          addressTh: 'ทดสอบที่อยู่',
          provinceTh: 'ทดสอบจังหวัด',
          districtTh: 'ทดสอบอำเภอ',
          subDistrictTh: 'ทดสอบตำบล',
          addressEn: 'test address',
          provinceEn: 'test province',
          districtEn: 'test district',
          subDistrictEn: 'test subdistrict',
          zipCode: '10000',
          latitude: 50.12567,
          longitude: 150.12367,
          partnerName: undefined, // Missing partnerName
        },
      ];
      const excelBuffer = await excelManagerService.generateExcelFile(
        mockData,
        'Sheet name',
      );
      let error;
      try {
        await branchesService.putBranches('company', {
          buffer: excelBuffer,
          mimetype:
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        } as unknown as Express.Multer.File);
      } catch (err) {
        error = err;
      }
      expect(error).toBeInstanceOf(BaseExceptionService);
      expect(error.message).toEqual(
        BASE_EXCEPTIONS.INVALID_DATA_FORMAT.message,
      );
      expect(error.data).toBe(
        `Partner name is required for branch type Partner.`,
      );
    });
    it('should success if branchType is PARTNER and partnerName is provided', async () => {
      const mockData = [
        {
          branchId: 'branchId1',
          title: 'title1',
          branchType: 'Partner',
          titleEn: 'titleEn',
          addressTh: 'ทดสอบที่อยู่',
          provinceTh: 'ทดสอบจังหวัด',
          districtTh: 'ทดสอบอำเภอ',
          subDistrictTh: 'ทดสอบตำบล',
          addressEn: 'test address',
          provinceEn: 'test province',
          districtEn: 'test district',
          subDistrictEn: 'test subdistrict',
          zipCode: '10000',
          latitude: 50.12567,
          longitude: 150.12367,
          partnerName: 'DTAC', // Provided partnerName
        },
      ];
      const excelBuffer = await excelManagerService.generateExcelFile(
        mockData,
        'Sheet name',
      );
      await expect(
        branchesService.putBranches('company', {
          buffer: excelBuffer,
          mimetype:
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        } as unknown as Express.Multer.File),
      ).resolves.not.toThrow();
      expect(branchRepository.manager.transaction).toHaveBeenCalledTimes(1);
    });
  });
});
