import { Test } from '@nestjs/testing';
import { Repository } from 'typeorm';
import { getRepositoryToken } from '@nestjs/typeorm';
import {
  GeneralActivitiesEntity,
  CampaignRedemptionCodeEntity,
  CampaignEntity,
} from '../../src/entities';
import { BaseExceptionModule } from '../../src/base-exceptions/base-exceptions.module';
import { CampaignRedemptionCodeQueueConsumer } from '../../src/admin/campaigns/campaign-redemption-code-queue.consumer';
import { Job } from 'bull';
import { FirebaseService } from '../../src/firebase/firebase.service';
import { AES128MessageService } from '../../src/encrypt-decrypt-message/encrypt-decrypt.service';
import { BaseExceptionService } from '../../src/exceptions';
import { BASE_EXCEPTIONS } from '../../src/config';
import { generateCampaignCodeExcelDataListMock } from '../mock-data/excel-manager';

describe('Campaign Redemption Code Queue Consumer', () => {
  let campaignRedemptionCodeQueueConsumer: CampaignRedemptionCodeQueueConsumer;
  let campaignRedemptionCodeRepository: Repository<CampaignRedemptionCodeEntity>;
  let campaignRepository: Repository<CampaignEntity>;
  let generalActivitiesRepository: Repository<GeneralActivitiesEntity>;
  let firebaseService: FirebaseService;

  const createQueryBuilder = jest.fn(() => ({
    insert: createQueryBuilder,
    into: createQueryBuilder,
    value: createQueryBuilder,
    execute: createQueryBuilder,
  }));

  beforeEach(async () => {
    const module = await Test.createTestingModule({
      imports: [BaseExceptionModule.register()],
      providers: [
        CampaignRedemptionCodeQueueConsumer,
        {
          provide: getRepositoryToken(CampaignRedemptionCodeEntity),
          useValue: {
            createQueryBuilder: createQueryBuilder,
            save: jest.fn().mockReturnThis(),
            find: jest.fn().mockReturnThis(),
            manager: {
              transaction: jest.fn().mockReturnThis(),
            },
          },
        },
        {
          provide: getRepositoryToken(GeneralActivitiesEntity),
          useValue: {
            save: jest.fn().mockReturnThis(),
            findOne: jest.fn().mockReturnThis(),
          },
        },
        {
          provide: getRepositoryToken(CampaignEntity),
          useValue: {
            find: jest.fn().mockReturnThis(),
          },
        },
        {
          provide: AES128MessageService,
          useValue: {
            decrypt: jest.fn(() => 'decryptedData'),
          },
        },
        {
          provide: FirebaseService,
          useValue: {
            setData: jest.fn().mockReturnThis(),
            addData: jest.fn().mockReturnThis(),
          },
        },
      ],
    }).compile();

    campaignRedemptionCodeQueueConsumer =
      module.get<CampaignRedemptionCodeQueueConsumer>(
        CampaignRedemptionCodeQueueConsumer,
      );
    campaignRedemptionCodeRepository = module.get<
      Repository<CampaignRedemptionCodeEntity>
    >(getRepositoryToken(CampaignRedemptionCodeEntity));
    generalActivitiesRepository = module.get<
      Repository<GeneralActivitiesEntity>
    >(getRepositoryToken(GeneralActivitiesEntity));
    campaignRepository = module.get<Repository<CampaignEntity>>(
      getRepositoryToken(CampaignEntity),
    );
    firebaseService = module.get<FirebaseService>(FirebaseService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('Upload Campaign Redemption Code', () => {
    const currentDate = new Date();
    const tenDaysLater = new Date(currentDate);
    tenDaysLater.setDate(currentDate.getDate() + 10);

    const redemptionCodeData = [
      {
        campaignCode: 'CODE1',
        redemptionCode: 'CODE1',
        campaign: {
          campaignName: 'campaignName',
          companyId: 'companyId',
          isActive: true,
          endDate: tenDaysLater,
        },
      },
      {
        campaignCode: 'CODE2',
        redemptionCode: 'CODE2',
        campaign: {
          campaignName: 'campaignName',
          companyId: 'companyId',
          isActive: true,
          endDate: tenDaysLater,
        },
      },
    ] as CampaignRedemptionCodeEntity[];

    const mockCampiagnData = [
      {
        campaignCode: 'CODE1',
        campaignName: 'campaignName1',
        companyId: 'companyId',
        isActive: true,
        endDate: tenDaysLater,
      },
      {
        campaignCode: 'CODE2',
        campaignName: 'campaignName2',
        companyId: 'companyId',
        isActive: true,
        endDate: tenDaysLater,
      },
    ] as CampaignEntity[];

    it('success', async () => {
      jest.spyOn(campaignRepository, 'find').mockResolvedValueOnce([]);
      jest
        .spyOn(campaignRedemptionCodeRepository, 'find')
        .mockResolvedValueOnce([]);

      await campaignRedemptionCodeQueueConsumer.campaignRedemptionCodeUpload({
        data: {
          fileData: generateCampaignCodeExcelDataListMock(4),
          user: 'user',
          company: 'companyId',
        },
      } as Job);

      expect(
        campaignRedemptionCodeRepository.manager.transaction,
      ).toHaveBeenCalledTimes(1);
      expect(generalActivitiesRepository.save).toHaveBeenCalledTimes(1);
    });

    it('Should fail duplicate code (db)', async () => {
      jest
        .spyOn(campaignRepository, 'find')
        .mockResolvedValueOnce([])
        .mockResolvedValueOnce([]);
      jest
        .spyOn(campaignRedemptionCodeRepository, 'find')
        .mockResolvedValueOnce([])
        .mockResolvedValueOnce([]);

      jest
        .spyOn(campaignRedemptionCodeRepository.manager, 'transaction')
        .mockImplementationOnce(() => {
          throw { driverError: { code: '23505' } };
        })
        .mockImplementationOnce(() => {
          throw { driverError: { code: '0000' } };
        });

      try {
        await campaignRedemptionCodeQueueConsumer.campaignRedemptionCodeUpload({
          data: {
            fileData: generateCampaignCodeExcelDataListMock(1),
            user: 'user1',
            company: 'companyId1',
          },
        } as Job);
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.REDEMPTION_CODE_ALREADY_UPLOADED.code,
        );
      }

      try {
        await campaignRedemptionCodeQueueConsumer.campaignRedemptionCodeUpload({
          data: {
            fileData: generateCampaignCodeExcelDataListMock(2),
            user: 'user2',
            company: 'companyId2',
          },
        } as Job);
      } catch (error) {
        expect(error).not.toBeInstanceOf(BaseExceptionService);
      }
    });

    it('Should fail not found activity', async () => {
      jest
        .spyOn(generalActivitiesRepository, 'findOne')
        .mockResolvedValueOnce(null);
      try {
        await campaignRedemptionCodeQueueConsumer.campaignRedemptionCodeUpload({
          data: {
            fileData: generateCampaignCodeExcelDataListMock(4),
            user: 'user',
            company: 'company',
            id: 'id',
          },
        } as Job);
        fail('Should throw error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.NOT_FOUND_DATA.code,
        );
      }
    });

    it('Should fail campaign code exceed maximum character', async () => {
      const fileData = generateCampaignCodeExcelDataListMock(4);
      fileData[2].redemptionCode = '123456789012345678901234567899';

      try {
        await campaignRedemptionCodeQueueConsumer.campaignRedemptionCodeUpload({
          data: {
            fileData: fileData,
            user: 'user',
            company: 'company',
          },
        } as Job);
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.BODY_PAYLOAD_INVALID.code,
        );
        expect((error as BaseExceptionService).message).toBe(
          'Body payload invalid',
        );
        expect((error as BaseExceptionService).data).toBe(
          `redemptionCode exceed 20 characters`,
        );
      }
    });

    it('Should fail supporter exceed maximum character', async () => {
      const fileData = generateCampaignCodeExcelDataListMock(4);
      fileData[2].supporter =
        'REDEMPTION_CODE_SUPPORTER_EXCEED_MAXIMUM_CHARACTER_TEST';

      try {
        await campaignRedemptionCodeQueueConsumer.campaignRedemptionCodeUpload({
          data: {
            fileData: fileData,
            user: 'user',
            company: 'company',
          },
        } as Job);
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.BODY_PAYLOAD_INVALID.code,
        );
        expect((error as BaseExceptionService).message).toBe(
          'Body payload invalid',
        );
        expect((error as BaseExceptionService).data).toBe(
          `${fileData[2].supporter}: supporter exceed 50 characters`,
        );
      }
    });

    it('Should fail inactive campaign', async () => {
      jest
        .spyOn(campaignRedemptionCodeQueueConsumer, 'checkActiveCampaign')
        .mockImplementationOnce(async () => {
          new BaseExceptionService().exception(
            'CAMPAIGN_NOT_ACTIVE',
            'inactiveCampaignCode',
          );
        });
      jest.spyOn(campaignRepository, 'find').mockResolvedValueOnce([]);
      jest
        .spyOn(campaignRedemptionCodeRepository, 'find')
        .mockResolvedValueOnce([]);

      try {
        await campaignRedemptionCodeQueueConsumer.campaignRedemptionCodeUpload({
          data: {
            fileData: generateCampaignCodeExcelDataListMock(4),
            user: 'user',
            company: 'companyId',
          },
        } as Job);
        fail('Expected method to throw an error');
      } catch (error) {
        expect(firebaseService.addData).toHaveBeenCalledTimes(1);
      }
    });

    it('Should fail campaign not active', async () => {
      jest
        .spyOn(campaignRepository, 'find')
        .mockResolvedValueOnce(mockCampiagnData);

      try {
        await campaignRedemptionCodeQueueConsumer.checkActiveCampaign(
          redemptionCodeData,
          'companyId',
        );
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.CAMPAIGN_NOT_ACTIVE.code,
        );
        expect((error as BaseExceptionService).data).toBe(
          mockCampiagnData[0].campaignCode,
        );
      }
    });

    it('Should fail duplicate redemption code (jobId)', async () => {
      const mockDuplicateData = [
        {
          redemptionCode: 'CODE1',
          campaignCode: 'CODE1',
          companyId: 'companyId',
          jobId: 'xxxxxxx',
          campaign: {
            campaignCode: 'CODE1',
            campaignName: 'campaignName',
            companyId: 'companyId',
            isActive: true,
            endDate: tenDaysLater,
          },
        },
      ] as CampaignRedemptionCodeEntity[];

      jest
        .spyOn(campaignRedemptionCodeRepository, 'find')
        .mockResolvedValueOnce(mockDuplicateData);

      try {
        await campaignRedemptionCodeQueueConsumer.checkDuplicateRedemptionCode(
          redemptionCodeData,
          'companyId',
        );
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.REDEMPTION_CODE_ALREADY_UPLOADED.code,
        );
      }
    });

    it('Should fail duplicate redemption code (inside file)', async () => {
      const mockData = generateCampaignCodeExcelDataListMock(4);
      mockData[0].redemptionCode = 'CODE1';
      mockData[1].redemptionCode = 'CODE1';

      jest.spyOn(campaignRepository, 'find').mockResolvedValueOnce([]);
      jest
        .spyOn(campaignRedemptionCodeRepository, 'find')
        .mockResolvedValueOnce([]);

      try {
        await campaignRedemptionCodeQueueConsumer.campaignRedemptionCodeUpload({
          data: {
            fileData: mockData,
            user: 'user',
            company: 'companyId',
          },
        } as Job);
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.BODY_PAYLOAD_INVALID.code,
        );
        expect((error as BaseExceptionService).data).toBe(
          'Duplicate redemption code in file',
        );
      }
    });

    it('Should fail grade body payload invalid', async () => {
      jest.spyOn(campaignRepository, 'find').mockResolvedValueOnce([]);
      jest
        .spyOn(campaignRedemptionCodeRepository, 'find')
        .mockResolvedValueOnce([]);

      const fileData = generateCampaignCodeExcelDataListMock(1);
      fileData[0].grade = 'X,J,Y';

      try {
        await campaignRedemptionCodeQueueConsumer.campaignRedemptionCodeUpload({
          data: {
            fileData: fileData,
            user: 'user',
            company: 'companyId',
          },
        } as Job);
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.BODY_PAYLOAD_INVALID.code,
        );
      }
    });

    it('Should return grade array', async () => {
      const gradeValue_swap =
        campaignRedemptionCodeQueueConsumer.filterGrade('D,A,C,B');
      const gradeValue_over =
        campaignRedemptionCodeQueueConsumer.filterGrade('X,D,A,C,B,T');
      const gradeValue_dup =
        campaignRedemptionCodeQueueConsumer.filterGrade('A,A,B,B');
      const gradeValue_dup_and_over =
        campaignRedemptionCodeQueueConsumer.filterGrade('C,B,Q,B,C,F,F,F');

      expect(gradeValue_swap).toEqual(['A', 'B', 'C', 'D']);
      expect(gradeValue_over).toEqual(['A', 'B', 'C', 'D']);
      expect(gradeValue_dup).toEqual(['A', 'B']);
      expect(gradeValue_dup_and_over).toEqual(['B', 'C']);
    });

    it('Should return error grade body payload invalid', async () => {
      try {
        campaignRedemptionCodeQueueConsumer.filterGrade('Q,X,T,Y');
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.BODY_PAYLOAD_INVALID.code,
        );
        expect((error as BaseExceptionService).data).toBe(
          'Grade required at least one of A, B, C, D',
        );
      }
    });
  });
});
