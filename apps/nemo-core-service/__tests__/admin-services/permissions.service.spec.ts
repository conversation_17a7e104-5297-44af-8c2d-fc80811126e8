import { getRepositoryToken } from '@nestjs/typeorm';
import { Test } from '@nestjs/testing';
import { PermissionGroupEntity } from '../../src/entities';
import { BaseExceptionModule } from '../../src/base-exceptions/base-exceptions.module';
import { AdminPermissionsService } from '../../src/admin/permissions/permissions.service';
import { EntityManager, Repository } from 'typeorm';
import { mock } from 'ts-mockito';
import { mockPermission } from '../mock-data/permission';

describe('AdminPermissionsService', () => {
  let permissionsService: AdminPermissionsService;
  let mockEntityManager: EntityManager;
  let permissionGroupRepository: Repository<PermissionGroupEntity>;

  beforeEach(async () => {
    mockEntityManager = mock(EntityManager);
    const module = await Test.createTestingModule({
      imports: [BaseExceptionModule.register()],
      providers: [
        AdminPermissionsService,
        {
          provide: getRepositoryToken(PermissionGroupEntity),
          useValue: {
            find: jest.fn().mockReturnThis(),
          },
        },
      ],
    }).compile();

    permissionsService = module.get<AdminPermissionsService>(
      AdminPermissionsService,
    );
    permissionGroupRepository = module.get<Repository<PermissionGroupEntity>>(
      getRepositoryToken(PermissionGroupEntity),
    );
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('Get Permissions Data', () => {
    it('should response permission data', async () => {
      jest
        .spyOn(permissionGroupRepository, 'find')
        .mockResolvedValueOnce(mockPermission);
      const result = await permissionsService.getPermissions('WW');

      expect(result.frontshop).not.toBeNull();
      expect(result.cms).not.toBeNull();
    });

    it('should response empty array ', async () => {
      jest.spyOn(permissionGroupRepository, 'find').mockResolvedValueOnce([]);
      const result = await permissionsService.getPermissions('WW');

      expect(result.frontshop).toStrictEqual([]);
      expect(result.cms).toStrictEqual([]);
    });
  });
});
