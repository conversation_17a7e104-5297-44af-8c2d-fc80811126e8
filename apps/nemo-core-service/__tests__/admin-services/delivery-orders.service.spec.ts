import { getRepositoryToken } from '@nestjs/typeorm';
import { Test } from '@nestjs/testing';
import { DeliveryOrderEntity, DeliveryOrderStatus } from '../../src/entities';
import { BaseExceptionModule } from '../../src/base-exceptions/base-exceptions.module';
import { AdminDeliveryOrdersService } from '../../src/admin/delivery-orders/delivery-orders.service';
import { BaseExceptionService } from '../../src/exceptions';
import { BASE_EXCEPTIONS } from '../../src/config';
import { Repository } from 'typeorm';
import {
  mockDeliveryOrderEntity,
  mockInputDeliveryOrderEntity,
  validPatchBody,
  validPatchDoId,
} from '../../__tests__/mock-data/delivery-orders';
import { mockDO } from '../mock-data/delivery-order';

describe('AdminDeliveryOrdersService', () => {
  let adminDeliveryOrdersService: AdminDeliveryOrdersService;

  let deliveryOrdersRepository: Repository<DeliveryOrderEntity>;

  beforeEach(async () => {
    const module = await Test.createTestingModule({
      imports: [BaseExceptionModule.register()],
      providers: [
        AdminDeliveryOrdersService,
        {
          provide: getRepositoryToken(DeliveryOrderEntity),
          useValue: {
            findOne: jest.fn(() => mockDeliveryOrderEntity),
            findAndCount: jest.fn().mockReturnThis(),
          },
        },
      ],
    }).compile();

    adminDeliveryOrdersService = module.get<AdminDeliveryOrdersService>(
      AdminDeliveryOrdersService,
    );
    deliveryOrdersRepository = module.get<Repository<DeliveryOrderEntity>>(
      getRepositoryToken(DeliveryOrderEntity),
    );
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('buildSearchQuery', () => {
    it.each`
      status        | deliveryOrderId | expectResult
      ${['status']} | ${'doId'}       | ${`r.status IN ('${'status'}') AND r.deliveryOrderId ILIKE '%${'doId'}%'`}
      ${undefined}  | ${'doId'}       | ${`r.deliveryOrderId ILIKE '%${'doId'}%'`}
      ${['status']} | ${undefined}    | ${`r.status IN ('${'status'}')`}
      ${''}         | ${''}           | ${''}
    `('buildSearchQuery', async ({ status, deliveryOrderId, expectResult }) => {
      const mockQueryBuilder = {
        andWhere: jest.fn(),
      } as unknown as any;
      const mockRequest = {
        query: {
          status,
          deliveryOrderId,
        },
      } as unknown as any;
      const result = adminDeliveryOrdersService.buildSearchQuery(
        mockRequest,
        mockQueryBuilder,
      );
      if (status || deliveryOrderId) {
        expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(expectResult);
      }
      expect(result).toBeDefined();
    });
  });
  describe('validateInputData', () => {
    it('valid case', async () => {
      expect(
        adminDeliveryOrdersService.validateInputData(
          validPatchBody,
          validPatchDoId,
        ),
      ).resolves.not.toThrow();
    });
    it('invalid doId', async () => {
      jest
        .spyOn(deliveryOrdersRepository, 'findOne')
        .mockImplementationOnce(async () => {
          return null;
        });
      try {
        await adminDeliveryOrdersService.validateInputData(
          validPatchBody,
          'invalidDoId',
        );
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.NOT_FOUND_DATA.code,
        );
      }
    });
    it.each([
      DeliveryOrderStatus.DELIVERY_SUCCESSFUL,
      DeliveryOrderStatus.IN_TRANSIT,
    ])('invalid do status', async (status) => {
      jest
        .spyOn(deliveryOrdersRepository, 'findOne')
        .mockImplementationOnce(async () => {
          return {
            ...mockDeliveryOrderEntity,
            status,
          };
        });
      try {
        await adminDeliveryOrdersService.validateInputData(
          validPatchBody,
          validPatchDoId,
        );
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.INVALID_DO_STATUS_TO_UPDATE.code,
        );
      }
    });
    it.each([
      { lastUpdate: '23/12/2040' },
      { appointmentDate: '2024-02-15' },
      { lastUpdate: '23/12/2040', appointmentDate: '2024-02-15' },
      { appointmentDate: '0001-01-01T00:00:00.000Z' },
      { awbNumber: '12345678' },
      { awbNumber: '1234567890' },
    ])('invalid body', async (invalidData) => {
      const body = { ...validPatchBody, ...invalidData };
      try {
        await adminDeliveryOrdersService.validateInputData(
          body,
          validPatchDoId,
        );
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.INVALID_INPUT_FOR_UPDATE_DO.code,
        );
      }
    });
  });
  describe('prepareBeforeUpdate', () => {
    it('should return correctly', () => {
      const result = adminDeliveryOrdersService.prepareBeforeUpdate(
        'userKey',
        validPatchBody,
        validPatchDoId,
      );
      expect(result).resolves.toBeInstanceOf(DeliveryOrderEntity);
    });
  });
  describe('computeUpdatePayload', () => {
    let mockInput;
    beforeEach(() => {
      mockInput = { ...mockInputDeliveryOrderEntity };
    });
    it(`status:${DeliveryOrderStatus.APPOINTMENT_PENDING} Success`, () => {
      const result = adminDeliveryOrdersService.computeUpdatePayload(
        mockDeliveryOrderEntity,
        mockInput,
      );
      expect(result).toHaveProperty('confirmAppointmentAt');
      expect(result).toHaveProperty('confirmAppointmentUserKey');
    });
    it(`status:${DeliveryOrderStatus.APPOINTMENT_PENDING} Fail`, () => {
      try {
        adminDeliveryOrdersService.computeUpdatePayload(
          {
            ...mockDeliveryOrderEntity,
            updatedAt: new Date('2024-03-14T00:00:00.000Z'),
          },
          mockInput,
        );
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.DO_ALREADY_CHANGED.code,
        );
      }
    });
    it(`status:${DeliveryOrderStatus.APPOINTMENT_CONFIRMED} Success`, () => {
      const result = adminDeliveryOrdersService.computeUpdatePayload(
        {
          ...mockDeliveryOrderEntity,
          status: DeliveryOrderStatus.APPOINTMENT_CONFIRMED,
        },
        mockInput,
      );
      expect(result).not.toHaveProperty('confirmAppointmentAt');
      expect(result).not.toHaveProperty('confirmAppointmentUserKey');
    });
  });

  describe('get delivery order count', () => {
    it('should be return appointmentPending: 1', async () => {
      jest
        .spyOn(deliveryOrdersRepository, 'findAndCount')
        .mockResolvedValue([mockDO, 1]);

      const result = await adminDeliveryOrdersService.getDeliveryOrderCount();
      expect(result).toStrictEqual({ appointmentPending: 1 });
    });
  });
});
