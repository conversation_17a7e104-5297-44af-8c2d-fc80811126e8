import { getRepositoryToken } from '@nestjs/typeorm';
import { Test } from '@nestjs/testing';
import {
  ModelMasterFunctionEntity,
  ModelChecklistEntity,
  ModelMasterEntity,
  QuestionType,
  ChecklistType,
  ModelPriceActivitiesEntity,
} from '../../src/entities';
import { BaseExceptionModule } from '../../src/base-exceptions/base-exceptions.module';
import {
  MODEL_MASTER_KEY,
  ModelMasterFunctionService,
} from '../../src/admin/model-master-function/model-master-function.service';
import { ExcelManagerService } from '../../src/excel/excel-manager.service';
import { Repository } from 'typeorm';
import {
  count,
  mockAfterLoadResult,
  mockAggFunctionKeyCond,
  mockAggPenalties,
  mockMapArrayResult,
  mockMasterFunctionQueryResult,
  mockOptionsHeader,
  mockDictAllData,
  mockfileData,
  mockcheckListFunction,
  mockresultData,
} from '../../__tests__/mock-data/model-master-function';
import {
  ModelMasterFunctionQueryResult,
  ModelMasterFunctionByModelKeyResult,
  ModelMasterQuestionByModelKeyResult,
} from 'contracts';
import { BASE_COLLATE, BASE_EXCEPTIONS } from '../../src/config';
import { DEFAULT_PAGE_SIZE } from '../../src/crud';
import { ExcelManagerModule } from '../../src/excel/excel-manager.module';
import { EncryptDecryptModule } from '../../src/encrypt-decrypt-message/encrypt-decrypt.module';
import { BaseExceptionService } from '../../src/exceptions';
import * as ExcelJS from 'exceljs';
import {
  mockCheckListForExcel,
  mockHeaderExcelV2,
  mockRowExcelV2,
} from '../../__tests__/mock-data/excel-manager';

describe('ModelMasterFunctionService', () => {
  let modelMasterFunctionService: ModelMasterFunctionService;
  let excelManagerService: ExcelManagerService;
  let modelMasterRepository: Repository<ModelMasterEntity>;
  let modelMasterFunctionRepository: Repository<ModelMasterFunctionEntity>;
  let modelChecklistRepository: Repository<ModelChecklistEntity>;

  const createQueryBuilder = jest.fn(() => ({
    find: createQueryBuilder,
    leftJoin: createQueryBuilder,
    select: createQueryBuilder,
    addSelect: createQueryBuilder,
    orderBy: createQueryBuilder,
    groupBy: createQueryBuilder,
    addGroupBy: createQueryBuilder,
    where: createQueryBuilder,
    andWhere: createQueryBuilder,
    offset: createQueryBuilder,
    limit: createQueryBuilder,
    getRawMany: jest.fn(() => mockMasterFunctionQueryResult), //tested result
    getRawOne: jest.fn(() => count), //tested result
  }));

  beforeEach(async () => {
    const module = await Test.createTestingModule({
      imports: [
        EncryptDecryptModule.register(),
        BaseExceptionModule.register(),
        ExcelManagerModule.register(mockOptionsHeader),
      ],
      providers: [
        ModelMasterFunctionService,
        {
          provide: getRepositoryToken(ModelMasterFunctionEntity),
          useValue: {
            createQueryBuilder: createQueryBuilder,
            find: jest.fn(() => mockDictAllData),
            save: jest.fn().mockReturnThis(),
            manager: {
              connection: {
                createQueryRunner: jest.fn().mockReturnValue({
                  manager: {
                    delete: jest.fn().mockResolvedValue({}), // mock the delete method
                    transaction: jest.fn().mockReturnThis(),
                  },
                  release: jest.fn(), // mock the release method (required after a transaction)
                  connect: jest.fn().mockReturnThis(),
                  startTransaction: jest.fn().mockReturnThis(),
                  commitTransaction: jest.fn().mockReturnThis(),
                  rollbackTransaction: jest.fn().mockReturnThis(),
                  isTransactionActive: true,
                }),
              },
              transaction: jest.fn().mockReturnThis(),
            },
          },
        },
        {
          provide: getRepositoryToken(ModelMasterEntity),
          useValue: {
            createQueryBuilder: createQueryBuilder,
            find: jest.fn(() => mockDictAllData),
            findOneBy: jest.fn().mockReturnThis(),
            manager: {
              transaction: jest.fn().mockReturnThis(),
            },
          },
        },
        {
          provide: getRepositoryToken(ModelChecklistEntity),
          useValue: {
            find: jest.fn(() => mockDictAllData),
          },
        },
        {
          provide: getRepositoryToken(ModelPriceActivitiesEntity),
          useValue: {
            save: jest.fn().mockReturnThis(),
          },
        },
      ],
    }).compile();

    modelMasterFunctionService = module.get<ModelMasterFunctionService>(
      ModelMasterFunctionService,
    );
    excelManagerService = module.get<ExcelManagerService>(ExcelManagerService);

    modelMasterRepository = module.get<Repository<ModelMasterEntity>>(
      getRepositoryToken(ModelMasterEntity),
    );

    modelMasterFunctionRepository = module.get<
      Repository<ModelMasterFunctionEntity>
    >(getRepositoryToken(ModelMasterFunctionEntity));

    modelChecklistRepository = module.get<Repository<ModelChecklistEntity>>(
      getRepositoryToken(ModelChecklistEntity),
    );
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('helper function', () => {
    it('mapArray should return in {arr1[0]:arr2[0]}', async () => {
      expect(
        modelMasterFunctionService.mapArray(
          mockAggFunctionKeyCond.split(','),
          mockAggPenalties.split(','),
        ),
      ).toEqual(mockMapArrayResult);
    });

    it('afterLoad should return correctly', async () => {
      const result = modelMasterFunctionService.afterLoad(
        mockMasterFunctionQueryResult as ModelMasterFunctionQueryResult[],
      );
      expect(result).toEqual(
        mockMasterFunctionQueryResult.map((functions) => ({
          id: functions.modelKey,
          modelIdentifiers: functions.modelIdentifiers,
          ownerName: functions.ownerName,
          penalty: modelMasterFunctionService.mapArray(
            functions.aggFunctionKeyCond.split(','),
            functions.aggPenalties.split(','),
          ),
        })),
      );
    });

    it('getColumnHeader should return correctly', () => {
      const result = modelMasterFunctionService.getColumnHeader();
      expect(result).toEqual(expect.arrayContaining([expect.any(String)]));
    });

    it('getQueryMasterFunctionGroupByModelKey', () => {
      const result =
        modelMasterFunctionService.getQueryMasterFunctionGroupByModelKey(
          'company',
        );
      expect(result).toHaveProperty('getRawMany');
    });
  });

  describe('customQueryModelMasterFunction', () => {
    it('should return correctly', async () => {
      const query = {
        pagination: 'false',
      };

      const result =
        await modelMasterFunctionService.customQueryModelMasterFunction(
          'company',
          query,
        );
      expect(result).toHaveProperty('items');
      expect(result).toHaveProperty('paginationResult');
      expect(result).toHaveProperty('column');
      expect(
        modelMasterFunctionRepository.createQueryBuilder,
      ).toHaveBeenCalledTimes(17);
    });
    it('no query', async () => {
      jest
        .spyOn(modelMasterFunctionRepository.createQueryBuilder(), 'getRawOne')
        .mockImplementationOnce(async () => {
          return { count: false };
        });
      const result =
        await modelMasterFunctionService.customQueryModelMasterFunction(
          'company',
          {},
        );
      expect(result).toHaveProperty('items');
      expect(result).toHaveProperty('paginationResult');
      expect(result).toHaveProperty('column');
      expect(
        modelMasterFunctionRepository.createQueryBuilder,
      ).toHaveBeenCalledTimes(20);
    });
    it.each`
      orderBy             | expectOrder | expectKey
      ${'modelKey asc'}   | ${'ASC'}    | ${`${MODEL_MASTER_KEY}.model_key`}
      ${'modelKey desc'}  | ${'DESC'}   | ${`${MODEL_MASTER_KEY}.model_key`}
      ${'brand asc'}      | ${'ASC'}    | ${`"${MODEL_MASTER_KEY}".model_identifiers->>'${'brand'}'`}
      ${'brand desc'}     | ${'DESC'}   | ${`"${MODEL_MASTER_KEY}".model_identifiers->>'${'brand'}'`}
      ${'model asc'}      | ${'ASC'}    | ${`"${MODEL_MASTER_KEY}".model_identifiers->>'${'model'}'`}
      ${'model desc'}     | ${'DESC'}   | ${`"${MODEL_MASTER_KEY}".model_identifiers->>'${'model'}'`}
      ${'ownerName asc'}  | ${'ASC'}    | ${`"${MODEL_MASTER_KEY}".owner_name`}
      ${'ownerName desc'} | ${'DESC'}   | ${`"${MODEL_MASTER_KEY}".owner_name`}
    `('call orderby correctly', async ({ orderBy, expectOrder, expectKey }) => {
      const query = {
        orderBy: orderBy,
        pagination: 'false',
      };
      await modelMasterFunctionService.customQueryModelMasterFunction(
        'company',
        query,
      );
      expect(
        modelMasterFunctionRepository.createQueryBuilder().orderBy,
      ).toHaveBeenNthCalledWith(
        13,
        `${expectKey} ${BASE_COLLATE}`,
        expectOrder,
      );
    });
    it.each(['tt desc', 'model aesc', undefined])(
      'call orderby incorrect',
      async (orderBy) => {
        const query = {
          orderBy: orderBy,
          pagination: 'false',
        };
        await modelMasterFunctionService.customQueryModelMasterFunction(
          'company',
          query,
        );
        expect(
          modelMasterFunctionRepository.createQueryBuilder,
        ).not.toHaveBeenCalledTimes(13);
      },
    );
    it.each`
      page         | pageSize     | expectLimit          | expectOffset
      ${undefined} | ${undefined} | ${DEFAULT_PAGE_SIZE} | ${0}
      ${1}         | ${10}        | ${10}                | ${0}
      ${2}         | ${10}        | ${10}                | ${10}
    `(
      'call pagination correct and incorrect',
      async ({ page, pageSize, expectLimit, expectOffset }) => {
        const query = {
          page: page,
          pageSize: pageSize,
        };
        await modelMasterFunctionService.customQueryModelMasterFunction(
          'company',
          query,
        );
        expect(
          modelMasterFunctionRepository.createQueryBuilder().limit,
        ).not.toHaveBeenNthCalledWith(1, expectLimit);
        expect(
          modelMasterFunctionRepository.createQueryBuilder().offset,
        ).not.toHaveBeenNthCalledWith(1, expectOffset);
        expect(
          modelMasterFunctionRepository.createQueryBuilder().limit,
        ).not.toHaveBeenCalledTimes(1);
        expect(
          modelMasterFunctionRepository.createQueryBuilder().offset,
        ).not.toHaveBeenCalledTimes(1);
      },
    );
  });

  describe('exportModelMasterFunction', () => {
    it('success export', async () => {
      const result =
        await modelMasterFunctionService.exportModelMasterFunction('company');
      expect(
        modelMasterFunctionRepository.createQueryBuilder,
      ).toHaveBeenCalledTimes(13);
      expect(result).toBeInstanceOf(Buffer);
    });
  });

  describe('putModelMasterFunction', () => {
    it('success put', async () => {
      const testExcelData = mockAfterLoadResult.map((row) => {
        return {
          modelKey: row.id,
          ...row.penalty,
        };
      });
      const excelBuffer = await excelManagerService.generateExcelFile(
        testExcelData,
        'Sheet name',
      );
      await modelMasterFunctionService.putModelMasterFunction(
        'company',
        {
          buffer: excelBuffer,
          mimetype:
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        } as unknown as Express.Multer.File,
        'user',
      );
      expect(
        modelMasterFunctionRepository.manager.transaction,
      ).toHaveBeenCalledTimes(1);
    });
  });

  describe('getModelMasterFunction', () => {
    it('should sucessfully', async () => {
      const mockModelChecklists = [
        {
          id: 'checklist1',
          companyId: 'company1',
          functionKey: 'func1',
          functionSection: 'section1',
          isRequired: true,
          checklistType: ChecklistType.MODULE,
          checklistNameTh: 'Name Function1',
          checklistNameEn: 'Name Function1',
        },
        {
          id: 'checklist2',
          companyId: 'company1',
          functionKey: 'func2',
          functionSection: 'section1',
          isRequired: true,
          checklistType: ChecklistType.MODULE,
          checklistNameTh: 'Name Function2',
          checklistNameEn: 'Name Function2',
        },
      ] as ModelChecklistEntity[];

      const mockModelMasterFunctions = [
        {
          companyId: 'company1',
          modelKey: 'model1',
          functionKeyCond: 'section1.func1=functional',
          penalties: '10',
        },
        {
          companyId: 'company1',
          modelKey: 'model1',
          functionKeyCond: 'section1.func1=non_functional',
          penalties: '5',
        },
        {
          companyId: 'company1',
          modelKey: 'model1',
          functionKeyCond: 'section1.func1=skip',
          penalties: '0',
        },
      ] as ModelMasterFunctionEntity[];

      const mockModelMaster = {
        modelKey: 'model1',
      } as ModelMasterEntity;

      jest
        .spyOn(modelChecklistRepository, 'find')
        .mockResolvedValue(mockModelChecklists);

      jest
        .spyOn(modelMasterRepository, 'findOneBy')
        .mockResolvedValue(mockModelMaster);

      jest
        .spyOn(modelMasterFunctionRepository, 'find')
        .mockResolvedValue(mockModelMasterFunctions);

      const result =
        await modelMasterFunctionService.getModelMasterFunctionByModelKey(
          'company1',
          'model1',
        );

      const expected = [
        {
          checkListId: 'checklist1',
          companyId: 'company1',
          modelKey: 'model1',
          functionKey: 'func1',
          functionSection: 'section1',
          checklistType: ChecklistType.MODULE,
          checklistNameTh: 'Name Function1',
          checklistNameEn: 'Name Function1',
          checked: true,
          functionalPenalties: '10',
          functionalKeyCond: 'section1.func1=functional',
          nonFunctionalPenalties: '5',
          nonFunctionalKeyCond: 'section1.func1=non_functional',
          skipPenalties: '0',
          skipKeyCond: 'section1.func1=skip',
        },
        {
          checkListId: 'checklist2',
          companyId: 'company1',
          modelKey: 'model1',
          functionKey: 'func2',
          functionSection: 'section1',
          checklistType: ChecklistType.MODULE,
          checklistNameTh: 'Name Function2',
          checklistNameEn: 'Name Function2',
          checked: false,
          functionalPenalties: '0',
          functionalKeyCond: 'section1.func2=functional',
          nonFunctionalPenalties: '0',
          nonFunctionalKeyCond: 'section1.func2=non_functional',
          skipPenalties: '0',
          skipKeyCond: 'section1.func2=skip',
        },
      ] as ModelMasterFunctionByModelKeyResult[];

      expect(result).toEqual(expected);
    });
    it('should return error model checklist not found', async () => {
      const mockModelChecklists = [] as ModelChecklistEntity[];

      jest
        .spyOn(modelChecklistRepository, 'find')
        .mockResolvedValue(mockModelChecklists);

      try {
        await modelMasterFunctionService.getModelMasterFunctionByModelKey(
          'company1',
          'model1',
        );
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.NOT_FOUND_DATA.code,
        );
        expect((error as BaseExceptionService).message).toBe('Not found data');
      }
    });
    it('should return error key not found', async () => {
      const mockModelChecklists = [
        {
          id: 'checklist1',
          companyId: 'company1',
          functionKey: 'func1',
          functionSection: 'section1',
          isRequired: true,
          checklistType: ChecklistType.MODULE,
          checklistNameTh: 'Name Function1',
          checklistNameEn: 'Name Function1',
        },
        {
          id: 'checklist2',
          companyId: 'company1',
          functionKey: 'func2',
          functionSection: 'section1',
          isRequired: true,
          checklistType: ChecklistType.MODULE,
          checklistNameTh: 'Name Function2',
          checklistNameEn: 'Name Function2',
        },
      ] as ModelChecklistEntity[];

      jest
        .spyOn(modelChecklistRepository, 'find')
        .mockResolvedValue(mockModelChecklists);

      jest.spyOn(modelMasterRepository, 'findOneBy').mockResolvedValue(null);

      try {
        await modelMasterFunctionService.getModelMasterFunctionByModelKey(
          'company1',
          'model1',
        );
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.NOT_FOUND_DATA.code,
        );
        expect((error as BaseExceptionService).message).toBe('Not found data');
      }
    });
  });

  describe('getModelMasterFunctionQuestion', () => {
    it('should sucessfully', async () => {
      const mockModelChecklists = [
        {
          id: 'checklist1',
          companyId: 'company1',
          functionKey: 'question1',
          functionSection: 'section1',
          checklistType: ChecklistType.QUESTION,
          checklistNameTh: 'Name Question 1',
          checklistNameEn: 'Name Question 1',
          questionType: QuestionType.SELECTION,
          questionChoices: [
            {
              id: 'question1choice1',
              answerEn: 'question1answer1',
              answerTh: 'คำถาม1คำตอบ1',
            },
            {
              id: 'question1choice2',
              answerEn: 'question1answer2',
              answerTh: 'คำถาม1คำตอบ2',
            },
          ],
        },
        {
          id: 'checklist2',
          companyId: 'company1',
          functionKey: 'question2',
          functionSection: 'section1',
          checklistType: ChecklistType.QUESTION,
          checklistNameTh: 'Name Question 2',
          checklistNameEn: 'Name Question 2',
          questionType: QuestionType.SELECTION,
          questionChoices: [
            {
              id: 'question2choice1',
              answerEn: 'question2answer1',
              answerTh: 'คำถาม2คำตอบ1',
            },
            {
              id: 'question2choice2',
              answerEn: 'question2answer2',
              answerTh: 'คำถาม2คำตอบ2',
            },
          ],
        },
      ] as ModelChecklistEntity[];

      const mockModelMasterFunctions = [
        {
          companyId: 'company1',
          modelKey: 'model1',
          functionKeyCond: 'section1.question1=question1choice1',
          penalties: '10',
        },
        {
          companyId: 'company1',
          modelKey: 'model1',
          functionKeyCond: 'section1.question1=question1choice2',
          penalties: '5',
        },
        {
          companyId: 'company1',
          modelKey: 'model1',
          functionKeyCond: 'section1.question1=skip',
          penalties: '0',
        },
      ] as ModelMasterFunctionEntity[];

      const mockModelMaster = {
        modelKey: 'model1',
      } as ModelMasterEntity;

      jest
        .spyOn(modelChecklistRepository, 'find')
        .mockResolvedValue(mockModelChecklists);

      jest
        .spyOn(modelMasterRepository, 'findOneBy')
        .mockResolvedValue(mockModelMaster);

      jest
        .spyOn(modelMasterFunctionRepository, 'find')
        .mockResolvedValue(mockModelMasterFunctions);

      const result =
        await modelMasterFunctionService.getModelMasterQuestionByModelKey(
          'company1',
          'model1',
        );

      const expected = [
        {
          checkListId: 'checklist1',
          companyId: 'company1',
          modelKey: 'model1',
          functionKey: 'question1',
          functionSection: 'section1',
          checklistType: ChecklistType.QUESTION,
          checklistNameTh: 'Name Question 1',
          checklistNameEn: 'Name Question 1',
          questionType: QuestionType.SELECTION,
          checked: true,
          questionChoices: [
            {
              id: 'question1choice1',
              answerEn: 'question1answer1',
              answerTh: 'คำถาม1คำตอบ1',
              penalties: '10',
              keyCond: 'section1.question1=question1choice1',
            },
            {
              id: 'question1choice2',
              answerEn: 'question1answer2',
              answerTh: 'คำถาม1คำตอบ2',
              penalties: '5',
              keyCond: 'section1.question1=question1choice2',
            },
          ],
          skipPenalties: '0',
          skipKeyCond: 'section1.question1=skip',
        },
        {
          checkListId: 'checklist2',
          companyId: 'company1',
          modelKey: 'model1',
          functionKey: 'question2',
          functionSection: 'section1',
          checklistType: ChecklistType.QUESTION,
          checklistNameTh: 'Name Question 2',
          checklistNameEn: 'Name Question 2',
          questionType: QuestionType.SELECTION,
          checked: false,
          questionChoices: [
            {
              id: 'question2choice1',
              answerEn: 'question2answer1',
              answerTh: 'คำถาม2คำตอบ1',
              penalties: '0',
              keyCond: 'section1.question2=question2choice1',
            },
            {
              id: 'question2choice2',
              answerEn: 'question2answer2',
              answerTh: 'คำถาม2คำตอบ2',
              penalties: '0',
              keyCond: 'section1.question2=question2choice2',
            },
          ],
          skipPenalties: '0',
          skipKeyCond: 'section1.question2=skip',
        },
      ] as ModelMasterQuestionByModelKeyResult[];

      expect(result).toEqual(expected);
    });
    it('should return error model checklist not found', async () => {
      jest.spyOn(modelChecklistRepository, 'find').mockResolvedValue([]);

      try {
        await modelMasterFunctionService.getModelMasterQuestionByModelKey(
          'company1',
          'model1',
        );
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.NOT_FOUND_DATA.code,
        );
        expect((error as BaseExceptionService).message).toBe('Not found data');
      }
    });
    it('should return error model key not found', async () => {
      const mockModelChecklists = [
        {
          id: 'checklist1',
          companyId: 'company1',
          functionKey: 'question1',
          functionSection: 'section1',
          checklistType: ChecklistType.QUESTION,
          checklistNameTh: 'Name Question 1',
          checklistNameEn: 'Name Question 1',
          questionType: QuestionType.SELECTION,
          questionChoices: [
            {
              id: 'question1choice1',
              answerEn: 'question1answer1',
              answerTh: 'คำถาม1คำตอบ1',
            },
            {
              id: 'question1choice2',
              answerEn: 'question1answer2',
              answerTh: 'คำถาม1คำตอบ2',
            },
          ],
        },
        {
          id: 'checklist2',
          companyId: 'company1',
          functionKey: 'question2',
          functionSection: 'section1',
          checklistType: ChecklistType.QUESTION,
          checklistNameTh: 'Name Question 2',
          checklistNameEn: 'Name Question 2',
          questionType: QuestionType.SELECTION,
          questionChoices: [
            {
              id: 'question2choice1',
              answerEn: 'question2answer1',
              answerTh: 'คำถาม2คำตอบ1',
            },
            {
              id: 'question2choice2',
              answerEn: 'question2answer2',
              answerTh: 'คำถาม2คำตอบ2',
            },
          ],
        },
      ] as ModelChecklistEntity[];

      jest
        .spyOn(modelChecklistRepository, 'find')
        .mockResolvedValue(mockModelChecklists);

      jest.spyOn(modelMasterRepository, 'findOneBy').mockResolvedValue(null);

      try {
        await modelMasterFunctionService.getModelMasterQuestionByModelKey(
          'company1',
          'model1',
        );
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.NOT_FOUND_DATA.code,
        );
        expect((error as BaseExceptionService).message).toBe('Not found data');
      }
    });
  });

  describe('export master function', () => {
    it('should be success export.', async () => {
      const result =
        await modelMasterFunctionService.exportModelMasterFunctionV2('company');
      // expect(
      //   modelMasterFunctionRepository.createQueryBuilder,
      // ).toHaveBeenCalledTimes(10);
      expect(result).toBeInstanceOf(Buffer);
    });
  });

  describe('upload master function', () => {
    it('function: buildSavelistRows should be work follow expected', async () => {
      const mockModelMaster = {
        modelKey: 'apple|iphone 11 pro|256gb',
      } as ModelMasterEntity;

      jest
        .spyOn(modelMasterRepository, 'findOneBy')
        .mockResolvedValue(mockModelMaster);

      const result = await modelMasterFunctionService.buildSavelistRows(
        mockcheckListFunction as any,
        mockfileData,
        'WW',
        'AXN0030',
      );

      // Update mockresultData with static date for comparison
      const mockDataWithoutDate = mockresultData.map((item) => ({
        ...item,
        updatedAt: expect.any(Date), // Use `expect.any(Date)` to match any date object
      }));

      // Check if result matches the expected data
      expect(result).toEqual(mockDataWithoutDate);
    });

    it('function buildSavelistRows should be error', async () => {
      jest.spyOn(modelMasterRepository, 'findOneBy').mockResolvedValue(null);

      try {
        await modelMasterFunctionService.buildSavelistRows(
          mockcheckListFunction as any,
          mockfileData,
          'WW',
          'AXN0030',
        );
        fail('Expected method to throw an error');
      } catch (error) {
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.NOT_FOUND_DATA.code,
        );
      }
    });

    it('Should be update master excel', async () => {
      jest
        .spyOn(modelChecklistRepository, 'find')
        .mockResolvedValue(mockCheckListForExcel as any);

      const workbook = new ExcelJS.Workbook();
      workbook.addWorksheet('MasterPriceFunction_26092024_154042');
      const worksheet = workbook.worksheets[0];

      worksheet.columns = mockHeaderExcelV2;

      for (const mockitem of mockRowExcelV2) {
        worksheet.addRow(mockitem);
      }

      // Write the workbook to a buffer
      const buffer = await workbook.xlsx.writeBuffer();

      const masterfile = {
        fieldname: 'file',
        originalname:
          'undefined.vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        encoding: '7bit',
        mimetype:
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        buffer: buffer,
        size: 10734,
      } as Express.Multer.File;

      const isSuccess =
        await modelMasterFunctionService.putModelMasterFunctionV2(
          'WW',
          masterfile,
          'AXN0030',
        );

      expect(isSuccess).toBe(true);
    });

    it('Should update master excel error', async () => {
      jest
        .spyOn(modelChecklistRepository, 'find')
        .mockResolvedValue(mockCheckListForExcel as any);

      const workbook = new ExcelJS.Workbook();
      workbook.addWorksheet('MasterPriceFunction_26092024_154042');
      const worksheet = workbook.worksheets[0];

      worksheet.columns = mockHeaderExcelV2;

      for (const mockitem of mockRowExcelV2) {
        worksheet.addRow(mockitem);
      }

      // Write the workbook to a buffer
      const buffer = await workbook.xlsx.writeBuffer();

      const masterfile = {
        fieldname: 'file',
        originalname:
          'undefined.vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        encoding: '7bit',
        mimetype:
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        buffer: buffer,
        size: 10734,
      } as Express.Multer.File;

      jest
        .spyOn(
          modelMasterFunctionService,
          'transactionRemoveModelmasterFunctionUploadExcel',
        )
        .mockImplementationOnce(() => {
          throw new Error('Error');
        });

      try {
        await modelMasterFunctionService.putModelMasterFunctionV2(
          'WW',
          masterfile,
          'AXN0030',
        );
      } catch (error) {
        console.log('error', (error as Error).message);
        expect((error as Error).message).toBe('Error');
      }
    });
  });
});
