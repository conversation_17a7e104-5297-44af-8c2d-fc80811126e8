import { getRepositoryToken } from '@nestjs/typeorm';
import { Test } from '@nestjs/testing';
import { getQueueToken } from '@nestjs/bull';
import {
  ModelMasterEntity,
  CompanyEntity,
  SystemConfigEntity,
  JobEntity,
  ModelChecklistEntity,
  ModelPriceActivitiesEntity,
} from '../../src/entities';
import { BaseExceptionModule } from '../../src/base-exceptions/base-exceptions.module';
import { EncryptDecryptModule } from '../../src/encrypt-decrypt-message/encrypt-decrypt.module';
import { ExcelManagerModule } from '../../src/excel/excel-manager.module';
import { ExcelManagerService } from '../../src/excel/excel-manager.service';

import {
  ModelMastersService,
  avgCostOption,
} from '../../src/admin/model-masters/model-masters.service';
import { generateAvgCostDataListMock } from '../mock-data/avg-cost-excel';
import { Repository } from 'typeorm';
import * as reCal from '../../src/utils/job/queue';

describe('UploadProductService', () => {
  let modelMastersService: ModelMastersService;
  let excelManagerService: ExcelManagerService;
  let modelMasterRepository: Repository<ModelMasterEntity>;
  let jobRepository: Repository<JobEntity>;
  let systemConfigRepository: Repository<SystemConfigEntity>;

  const createQueryBuilder = jest.fn(() => ({
    insert: createQueryBuilder,
    into: createQueryBuilder,
    value: createQueryBuilder,
    execute: createQueryBuilder,
  }));

  beforeEach(async () => {
    const module = await Test.createTestingModule({
      imports: [
        EncryptDecryptModule.register(),
        BaseExceptionModule.register(),
        ExcelManagerModule.register(avgCostOption),
      ],
      providers: [
        ModelMastersService,
        {
          provide: getRepositoryToken(ModelMasterEntity),
          useValue: {
            createQueryBuilder: createQueryBuilder,
            save: jest.fn().mockReturnThis(),
            manager: {
              transaction: jest.fn().mockReturnThis(),
            },
            find: jest.fn().mockReturnThis(),
          },
        },
        {
          provide: getRepositoryToken(CompanyEntity),
          useValue: {
            createQueryBuilder: createQueryBuilder,
            save: jest.fn().mockReturnThis(),
            manager: {
              transaction: jest.fn().mockReturnThis(),
            },
          },
        },
        {
          provide: getRepositoryToken(SystemConfigEntity),
          useValue: {
            findOne: jest.fn().mockReturnThis(),
          },
        },
        {
          provide: getRepositoryToken(JobEntity),
          useValue: {
            findOne: jest.fn().mockReturnThis(),
            find: jest.fn().mockReturnThis(),
          },
        },
        {
          provide: getQueueToken('re-calculate-product-queue'),
          useValue: {
            add: jest.fn(),
            process: jest.fn(),
            empty: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(ModelChecklistEntity),
          useValue: {
            find: jest.fn().mockReturnValue([{ companyId: 'WW' }]),
          },
        },
        {
          provide: getRepositoryToken(ModelPriceActivitiesEntity),
          useValue: {
            save: jest.fn().mockReturnThis(),
          },
        },
      ],
    }).compile();
    modelMastersService = module.get<ModelMastersService>(ModelMastersService);
    excelManagerService = module.get<ExcelManagerService>(ExcelManagerService);
    modelMasterRepository = module.get<Repository<ModelMasterEntity>>(
      getRepositoryToken(ModelMasterEntity),
    );
    jobRepository = module.get<Repository<JobEntity>>(
      getRepositoryToken(JobEntity),
    );
    systemConfigRepository = module.get<Repository<SystemConfigEntity>>(
      getRepositoryToken(SystemConfigEntity),
    );
  });

  afterEach(() => {
    jest.clearAllMocks();
  });
  describe('upload', () => {
    it('Should success upload', async () => {
      //const mock: ModelMasterEntity[] = [];

      const testExcelData = generateAvgCostDataListMock(4);
      jest
        .spyOn(modelMasterRepository, 'find')
        .mockResolvedValueOnce(testExcelData);
      const excelBuffer = await excelManagerService.generateExcelFile(
        testExcelData,
        'Sheet name',
      );
      jest.spyOn(jobRepository, 'find').mockResolvedValueOnce([]);
      jest
        .spyOn(systemConfigRepository, 'findOne')
        .mockResolvedValueOnce({ data: {} } as SystemConfigEntity);
      const mockRecalQueue = jest.spyOn(reCal, 'reCalculateProduct');
      mockRecalQueue.mockResolvedValue();
      const result = await modelMastersService.uploadAverageCost({
        file: {
          buffer: excelBuffer,
          mimetype:
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        } as unknown as Express.Multer.File,
        user: 'user',
        company: 'company',
      });

      expect(result).toEqual({ count: 4 });
      expect(modelMasterRepository.manager.transaction).toHaveBeenCalledTimes(
        1,
      );
      expect(mockRecalQueue).toHaveBeenCalled();
    });
  });

  it('export success', () => {
    const testExcelData = generateAvgCostDataListMock(4);
    const result = modelMastersService.exportAvgCost(testExcelData);
    result.then((value) => {
      expect(value).toBeInstanceOf(Buffer);
    });
  });
});
