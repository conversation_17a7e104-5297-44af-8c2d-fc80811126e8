import {
  DataSource,
  EntityManager,
  EntityMetadata,
  QueryRunner,
} from 'typeorm';
import {
  AllocationOrderEntity,
  AllocationOrderStatus,
  JobEntity,
} from '../../src/entities';
import { Test } from '@nestjs/testing';
import {
  DeliveryOrderEntitySubscriber,
  AllocationOrderEntitySubscriber,
} from '../../src/subscriber';
import { BaseExceptionModule } from '../../src/base-exceptions/base-exceptions.module';

describe('Allocation Order Subscriber', () => {
  let aoSubscriber: AllocationOrderEntitySubscriber;
  let entityManager: EntityManager;

  const allocationOrderEntity = new AllocationOrderEntity();

  const mockedEvent = {
    entity: allocationOrderEntity,
    databaseEntity: {} as AllocationOrderEntity,
    manager: {} as EntityManager,
    updatedColumns: [],
    connection: {} as DataSource,
    queryRunner: {} as QueryRunner,
    metadata: {} as EntityMetadata,
    updatedRelations: [],
  };

  const mockSave = jest.fn().mockReturnThis();

  beforeAll(async () => {
    const module = await Test.createTestingModule({
      imports: [BaseExceptionModule.register()],
      providers: [
        {
          provide: DataSource,
          useValue: {
            subscribers: {
              push: jest.fn().mockReturnThis(),
            },
          },
        },
        {
          provide: EntityManager,
          useValue: {
            getRepository: jest.fn().mockImplementation(() => {
              return { save: mockSave };
            }),
            findOne: jest.fn(() => {
              return {
                roles: [
                  {
                    branchId: 'test',
                    role: ['Sale', 'Manager'],
                  },
                ],
              };
            }),
          },
        },
        AllocationOrderEntitySubscriber,
      ],
    }).compile();

    aoSubscriber = module.get<AllocationOrderEntitySubscriber>(
      AllocationOrderEntitySubscriber,
    );
    entityManager = module.get<EntityManager>(EntityManager);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('After insert aoSubscriber', () => {
    it('afterInsert', async () => {
      await aoSubscriber.afterInsert({
        ...mockedEvent,
        manager: entityManager as EntityManager,
      });
      expect(entityManager.getRepository).toHaveBeenCalled();
      expect(mockSave).toHaveBeenCalled();
    });
    it('listenTo', async () => {
      const result = aoSubscriber.listenTo();
      expect(result).toBe(AllocationOrderEntity);
    });
  });

  describe('After update aoSubscriber', () => {
    it.each([
      AllocationOrderStatus.APPOINTMENT_PENDING,
      AllocationOrderStatus.APPOINTMENT_CONFIRMED,
      AllocationOrderStatus.DRAFT,
    ])('afterUpdate', async (statusTo) => {
      let allocationOrderEntity = new AllocationOrderEntity();
      allocationOrderEntity.status = statusTo;
      allocationOrderEntity.createdBy = 'test';
      allocationOrderEntity.jobs = [{ jobId: 'test' }] as JobEntity[];
      await aoSubscriber.afterUpdate({
        ...mockedEvent,
        entity: allocationOrderEntity,
        databaseEntity: {
          status: AllocationOrderStatus.DRAFT,
          jobs: [{ jobId: 'test2' }] as JobEntity[],
        } as AllocationOrderEntity,
        manager: entityManager as EntityManager,
      });
      expect(entityManager.getRepository).toHaveBeenCalled();
      expect(mockSave).toHaveBeenCalled();
    });
  });
});
