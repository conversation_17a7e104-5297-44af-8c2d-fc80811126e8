import { Test } from '@nestjs/testing';
import { Repository } from 'typeorm';
import { getRepositoryToken } from '@nestjs/typeorm';
import { JobEntity } from '../../src/entities';
import { JobsConsumer } from '../../src/admin/jobs/jobs.consumer';
import { Job } from 'bull';
import * as calFunc from '../../src/utils/job/calculateProduct';

describe('Jobs Consumer', () => {
  let jobsConsumer: JobsConsumer;
  let jobsRepository: Repository<JobEntity>;

  beforeEach(async () => {
    const module = await Test.createTestingModule({
      imports: [],
      providers: [
        JobsConsumer,
        {
          provide: getRepositoryToken(JobEntity),
          useValue: {
            findOne: jest.fn(),
            save: jest.fn(),
          },
        },
      ],
    }).compile();

    jobsConsumer = module.get<JobsConsumer>(JobsConsumer);
    jobsRepository = module.get<Repository<JobEntity>>(
      getRepositoryToken(JobEntity),
    );
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('re calculate product process', () => {
    let mockCalculatedProduct;
    let mockJobEntity;
    beforeEach(() => {
      mockCalculatedProduct = {
        wholeSaleMargin: 90.91,
        wholeSalePrice: 11000,
        marginWholeSaleBaht: 1000,
        retailMargin: 83.33,
        retailPrice: 12000,
        marginRetailBaht: 2000,
        costPrice: 10000,
      };
      mockJobEntity = {
        jobId: 'TestId',
        modelMaster: {},
        wholeSaleMargin: 94.74,
        wholeSalePrice: 9500,
        marginWholeSaleBaht: 500,
        retailMargin: 90.0,
        retailPrice: 10000,
        marginRetailBaht: 1000,
        costPrice: 9000,
      } as JobEntity;
    });
    it('case admin never updated', async () => {
      jest
        .spyOn(jobsRepository, 'findOne')
        .mockResolvedValueOnce(mockJobEntity);
      jest
        .spyOn(calFunc, 'calculateProductPrices')
        .mockReturnValue(mockCalculatedProduct);
      await jobsConsumer.reCalculateProductQueue({
        data: { jobId: mockJobEntity.jobId },
      } as Job);
      expect(jobsRepository.save).toHaveBeenCalledWith(
        expect.objectContaining({
          wholeSaleMargin: mockCalculatedProduct.wholeSaleMargin,
          wholeSalePrice: mockCalculatedProduct.wholeSalePrice,
          marginWholeSaleBaht: mockCalculatedProduct.marginWholeSaleBaht,
          retailMargin: mockCalculatedProduct.retailMargin,
          retailPrice: mockCalculatedProduct.retailPrice,
          marginRetailBaht: mockCalculatedProduct.marginRetailBaht,
          costPrice: mockCalculatedProduct.costPrice,
          isConfirmPrice: false,
        }),
      );
      expect(jobsRepository.save).not.toHaveBeenCalledWith(
        expect.objectContaining({
          modelMaster: expect.anything,
        }),
      );
    });
    it('case admin already updated', async () => {
      jest.spyOn(jobsRepository, 'findOne').mockResolvedValueOnce({
        ...mockJobEntity,
        adminUpdateCostListValue: [
          {
            retailPrice: 10000,
            wholeSalePrice: 9500,
            by: { key: 'testKey', name: 'Tester' },
            at: new Date(),
          },
        ],
      });
      jest
        .spyOn(calFunc, 'calculateProductPrices')
        .mockReturnValue(mockCalculatedProduct);
      await jobsConsumer.reCalculateProductQueue({
        data: { jobId: mockJobEntity.jobId },
      } as Job);
      expect(jobsRepository.save).toHaveBeenCalledWith(
        expect.objectContaining({
          wholeSalePrice: mockJobEntity.wholeSalePrice,
          retailPrice: mockJobEntity.retailPrice,
          costPrice: mockCalculatedProduct.costPrice,
          isConfirmPrice: false,
        }),
      );
      expect(jobsRepository.save).not.toHaveBeenCalledWith(
        expect.objectContaining({
          modelMaster: expect.anything,
          wholeSaleMargin: mockCalculatedProduct.wholeSaleMargin,
          retailMargin: mockCalculatedProduct.wholeSaleMargin,
          marginWholeSaleBaht: mockCalculatedProduct.marginWholeSaleBaht,
          marginRetailBaht: mockCalculatedProduct.marginRetailBaht,
        }),
      );
      expect(jobsRepository.save).not.toHaveBeenCalledWith(
        expect.objectContaining({
          wholeSaleMargin: mockJobEntity.wholeSaleMargin,
          retailMargin: mockJobEntity.wholeSaleMargin,
          marginWholeSaleBaht: mockJobEntity.marginWholeSaleBaht,
          marginRetailBaht: mockJobEntity.marginRetailBaht,
        }),
      );
    });
  });
});
