import { getRepositoryToken } from '@nestjs/typeorm';
import { getQueueToken } from '@nestjs/bull';
import { Test } from '@nestjs/testing';
import {
  CampaignEntity,
  CampaignRedemptionCodeEntity,
  JobEntity,
  ModelMasterEntity,
  UserEntity,
  CompanyEntity,
  GeneralActivitiesEntity,
  CampaignStatus,
} from '../../src/entities';
import * as ExcelJS from 'exceljs';
import { BaseExceptionModule } from '../../src/base-exceptions/base-exceptions.module';
import { BaseExceptionService } from '../../src/exceptions';
import { BASE_EXCEPTIONS } from '../../src/config';
import { Repository } from 'typeorm';
import {
  AdminCampaignsService,
  campaignSummaryHeaderOption,
} from '../../src/admin/campaigns/campaigns.service';
import { ExcelManagerModule } from '../../src/excel/excel-manager.module';
import { EncryptDecryptModule } from '../../src/encrypt-decrypt-message/encrypt-decrypt.module';
import {
  mockSuccessColumnCampaignSummary,
  mockNewRowDataCampaignSummary,
  mockSuccessColumnCampaignModel,
  mockNewRowDataCampaignModel,
  mockModelMasters,
  mockCampaignData,
  generateCampaignCodeExcelDataListMock,
  mockSuccessColumnCampaignRedemptionCode,
} from '../mock-data/excel-manager';
import { Readable } from 'stream';
import { mockCompany } from '../mock-data/allocation-order';
import { DateTime } from 'luxon';
import { mockCampaignCancel } from '../mock-data/campaign';
import { WithUserContext } from 'src/interfaces';

jest.mock('../../src/utils/general', () => {
  const original = jest.requireActual('../../src/utils/general');
  return {
    ...original,
    getDateFromToday: jest.fn(),
  };
});

describe('AdminCampaignsService', () => {
  let adminCampaignsService: AdminCampaignsService;
  let campaignRepository: Repository<CampaignEntity>;
  let companyRepository: Repository<CompanyEntity>;
  let modelMasterRepository: Repository<ModelMasterEntity>;
  let campaignRedemptionCodeRepository: Repository<CampaignRedemptionCodeEntity>;

  const subquery = jest.fn(() => ({
    select: jest.fn().mockReturnThis(),
    from: jest.fn().mockReturnThis(),
    leftJoin: jest.fn().mockReturnThis(),
    groupBy: jest.fn().mockReturnThis(),
  }));
  const createQueryBuilder = jest.fn(() => ({
    select: jest.fn().mockReturnThis(),
    from: jest.fn().mockReturnThis(),
    where: jest.fn().mockReturnThis(),
    andWhere: jest.fn().mockReturnThis(),
    leftJoin: jest.fn().mockReturnThis(),
    groupBy: jest.fn().mockReturnThis(),
    orderBy: jest.fn().mockReturnThis(),
    addOrderBy: jest.fn().mockReturnThis(),
    offset: jest.fn().mockReturnThis(),
    limit: jest.fn().mockReturnThis(),
    insert: jest.fn().mockReturnThis(),
    into: jest.fn().mockReturnThis(),
    value: jest.fn().mockReturnThis(),
    execute: jest.fn().mockReturnThis(),
    getRawMany: jest.fn(),
    getRawOne: jest.fn(() => {
      return { count: '1' };
    }),
  }));
  beforeEach(async () => {
    const module = await Test.createTestingModule({
      imports: [
        EncryptDecryptModule.register(),
        BaseExceptionModule.register(),
        ExcelManagerModule.register(campaignSummaryHeaderOption),
      ],
      providers: [
        AdminCampaignsService,
        {
          provide: getRepositoryToken(GeneralActivitiesEntity),
          useValue: {
            save: jest.fn().mockReturnThis(),
          },
        },
        {
          provide: getRepositoryToken(CampaignEntity),
          useValue: {
            query: jest.fn().mockReturnThis(),
            findOne: jest.fn().mockReturnThis(),
            find: jest.fn().mockReturnThis(),
            insert: jest.fn().mockReturnThis(),
            save: jest.fn().mockReturnThis(),
            manager: {
              transaction: jest.fn().mockReturnThis(),
              getRepository: jest.fn().mockReturnThis(),
              connection: {
                createQueryRunner: jest.fn().mockReturnThis(),
                connect: jest.fn().mockReturnThis(),
                startTransaction: jest.fn().mockReturnThis(),
                release: jest.fn().mockReturnThis(),
                rollbackTransaction: jest.fn().mockReturnThis(),
                commitTransaction: jest.fn().mockReturnThis(),
                manager: {
                  getRepository: jest.fn().mockReturnThis(),
                  createQueryBuilder: jest.fn().mockReturnThis(),
                  select: jest.fn().mockReturnThis(),
                  update: jest.fn().mockReturnThis(),
                  where: jest.fn().mockReturnThis(),
                  andWhere: jest.fn().mockReturnThis(),
                  insert: jest.fn().mockReturnThis(),
                  orIgnore: jest.fn().mockReturnThis(),
                  values: jest.fn().mockReturnThis(),
                  save: jest.fn().mockReturnThis(),
                  execute: jest.fn().mockResolvedValue({ raw: [] }),
                  delete: jest.fn().mockReturnThis(),
                  count: jest.fn(),
                },
              },
            },
            createQueryBuilder: createQueryBuilder,
          },
        },
        {
          provide: getRepositoryToken(CampaignRedemptionCodeEntity),
          useValue: {
            find: jest.fn().mockReturnThis(),
            findOne: jest.fn().mockReturnThis(),
            insert: jest.fn().mockReturnThis(),
            save: jest.fn().mockReturnThis(),
            createQueryBuilder: createQueryBuilder,
          },
        },
        {
          provide: getRepositoryToken(UserEntity),
          useValue: {
            findOne: jest.fn().mockReturnThis(),
            findOneBy: jest.fn().mockReturnThis(),
          },
        },
        {
          provide: getRepositoryToken(JobEntity),
          useValue: {
            findOne: jest.fn().mockReturnThis(),
            findOneBy: jest.fn().mockReturnThis(),
          },
        },
        {
          provide: getRepositoryToken(ModelMasterEntity),
          useValue: {
            find: jest.fn().mockReturnThis(),
            findOne: jest.fn().mockReturnThis(),
            findOneBy: jest.fn().mockReturnThis(),
          },
        },
        {
          provide: getRepositoryToken(CompanyEntity),
          useValue: {
            findOne: jest.fn().mockReturnThis(),
            findOneBy: jest.fn().mockReturnThis(),
          },
        },
        {
          provide: getQueueToken('campaign-redemption-code-queue'),
          useValue: {
            add: jest.fn(),
            process: jest.fn(),
          },
        },
      ],
    }).compile();

    adminCampaignsService = module.get<AdminCampaignsService>(
      AdminCampaignsService,
    );
    campaignRepository = module.get<Repository<CampaignEntity>>(
      getRepositoryToken(CampaignEntity),
    );
    companyRepository = module.get<Repository<CompanyEntity>>(
      getRepositoryToken(CompanyEntity),
    );
    modelMasterRepository = module.get<Repository<ModelMasterEntity>>(
      getRepositoryToken(ModelMasterEntity),
    );
    campaignRedemptionCodeRepository = module.get<
      Repository<CampaignRedemptionCodeEntity>
    >(getRepositoryToken(CampaignRedemptionCodeEntity));
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('getCampaignDetail', () => {
    const mockCampaign = {
      companyId: 'WW',
      campaignCode: 'cp-code',
      campaignName: 'cp-name',
      description: 'cp-description',
      remark: 'cp-remark',
      maxRedemptionCode: 1,
      startDate: new Date(),
      endDate: new Date(),
      createdBy: 'user-create',
      updatedBy: 'user-update',
    } as CampaignEntity;

    const mockCampaignRedemptionCode: CampaignRedemptionCodeEntity = {
      campaignCode: mockCampaign.campaignCode,
      companyId: mockCampaign.companyId,
      redemptionCode: 'rc-1',
      order: 1,
      grade: ['A', 'B', 'C'],
    } as CampaignRedemptionCodeEntity;

    const mockModelMaster: ModelMasterEntity = {
      modelKey: 'model-key',
    } as ModelMasterEntity;

    const {
      createdBy,
      updatedBy,
      createdAt,
      updatedAt,
      companyId,
      ...campaignDetail
    } = mockCampaign;

    it('should return campaign detail with complete upload true', async () => {
      jest.spyOn(campaignRepository, 'findOne').mockResolvedValueOnce({
        ...mockCampaign,
        campaignRedemptionCode: [mockCampaignRedemptionCode],
        modelMasters: [mockModelMaster],
      });

      const result = await adminCampaignsService.getCampaignDetail({
        campaignCode: mockCampaign.campaignCode,
        companyId: mockCampaign.companyId,
      });

      expect(result).toEqual({
        ...campaignDetail,
        modelMasterIds: [mockModelMaster.modelKey],
        isCompleteUpload: true,
      });
    });

    it('should return campaign detail with complete upload false (no model case)', async () => {
      jest.spyOn(campaignRepository, 'findOne').mockResolvedValueOnce({
        ...mockCampaign,
        campaignRedemptionCode: [mockCampaignRedemptionCode],
        modelMasters: [],
      });

      const result = await adminCampaignsService.getCampaignDetail({
        campaignCode: mockCampaign.campaignCode,
        companyId: mockCampaign.companyId,
      });

      expect(result).toEqual({
        ...campaignDetail,
        modelMasterIds: [],
        isCompleteUpload: false,
      });
    });

    it('should return campaign detail with complete upload false (no campaign case)', async () => {
      jest.spyOn(campaignRepository, 'findOne').mockResolvedValueOnce({
        ...mockCampaign,
        campaignRedemptionCode: [],
        modelMasters: [mockModelMaster],
      });

      const result = await adminCampaignsService.getCampaignDetail({
        campaignCode: mockCampaign.campaignCode,
        companyId: mockCampaign.companyId,
      });

      expect(result).toEqual({
        ...campaignDetail,
        modelMasterIds: [mockModelMaster.modelKey],
        isCompleteUpload: false,
      });
    });

    it('should return campaign detail with complete upload false (no campaign and model case)', async () => {
      jest.spyOn(campaignRepository, 'findOne').mockResolvedValueOnce({
        ...mockCampaign,
        campaignRedemptionCode: [],
        modelMasters: [],
      });

      const result = await adminCampaignsService.getCampaignDetail({
        campaignCode: mockCampaign.campaignCode,
        companyId: mockCampaign.companyId,
      });

      expect(result).toEqual({
        ...campaignDetail,
        modelMasterIds: [],
        isCompleteUpload: false,
      });
    });

    it('should failed not found campaign', async () => {
      jest.spyOn(campaignRepository, 'findOne').mockResolvedValueOnce(null);

      try {
        await adminCampaignsService.getCampaignDetail({
          campaignCode: mockCampaign.campaignCode,
          companyId: mockCampaign.companyId,
        });
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.CAMPAIGN_NOT_FOUND.code,
        );
        expect((error as BaseExceptionService).data).toBe(
          'Campaign not found.',
        );
      }
    });
  });

  describe('getCampaignRedemptionCodeSummary', () => {
    const mockResultQueryItem = {
      order: 1,
      description: 'test-sescription',
      value: '2000',
      total: '300',
      used: '5',
      grade: ['A', 'B', 'C'],
    };
    const mockResultReturnItem = {
      order: 1,
      description: 'test-sescription',
      value: 2000,
      total: 300,
      used: 5,
      grades: ['A', 'B', 'C'],
    };
    it('should success', async () => {
      jest
        .spyOn(campaignRepository, 'query')
        .mockResolvedValueOnce([mockResultQueryItem]);
      const result =
        await adminCampaignsService.getCampaignRedemptionCodeSummary({
          companyId: 'WW',
          campaignCode: 'cp-code',
        });

      expect(result).toEqual([mockResultReturnItem]);
    });
  });

  describe('saveCampaignSummaryFromFile', () => {
    const mockExistedRowDataCampaignSummary = [
      {
        companyId: 'WW',
        campaignCode: 'B001',
        campaignName: 'NameB001',
        description: 'Desc001',
        remark: 'Remark001',
        maxRedemptionCode: 10,
        startDate: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000),
        endDate: new Date(),
        createdBy: 'system',
        updatedBy: 'system',
      },
    ] as CampaignEntity[];

    const campaignData = {
      companyId: 'WW',
      campaignCode: 'B001',
      campaignName: 'NameB001',
      description: 'Desc001',
      remark: 'Remark001',
      maxRedemptionCode: 10,
      startDate: new Date(Date.now() + 100 * 24 * 60 * 60 * 1000),
      endDate: new Date(),
      createdBy: 'system',
      updatedBy: 'system',
    } as CampaignEntity;

    it('should sucessfully save', async () => {
      jest.spyOn(companyRepository, 'findOne').mockResolvedValue(mockCompany);
      jest
        .spyOn(campaignRepository, 'find')
        .mockResolvedValueOnce([campaignData]);

      const workbook = new ExcelJS.Workbook();
      workbook.addWorksheet('sheet1');
      const worksheet = workbook.worksheets[0];
      worksheet.columns = mockSuccessColumnCampaignSummary;

      for (let i = 0; i < mockNewRowDataCampaignSummary.length; i++) {
        worksheet.addRow(mockNewRowDataCampaignSummary[i]);
      }

      const mockBuffer = (await workbook.xlsx.writeBuffer()) as Buffer;

      const mockFile: Express.Multer.File = {
        originalname: 'mockExcel.xlsx',
        mimetype:
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        encoding: '7bit',
        buffer: mockBuffer,
        fieldname: 'test',
        size: 10,
        filename: 'mockExcel.xlsx',
        path: 'testPath',
        destination: 'destination',
        stream: new Readable(),
      };

      const result = await adminCampaignsService.saveCampaignSummaryFromFile(
        'WW',
        mockFile,
        'test_user',
      );
      expect(result).toBeNull();
    });

    it('should failed campaign code length exceed', async () => {
      const mockErrorRowDataCampaignSummary = [
        'ButImustexplaintoyouhowallthismistakenideaofdenouncingpleasureandpraisingpainwasbornandIwillgiveyouacompleteaccountofthesystem,andexpoundtheactualteachingsofthegreatexplorerofthetruth,themaster-builderofhu',
        'NameA001',
        'Desc001',
        'Remark001',
        new Date(),
        new Date(),
        10,
      ];

      jest.spyOn(companyRepository, 'findOne').mockResolvedValue(mockCompany);
      jest
        .spyOn(campaignRepository, 'find')
        .mockResolvedValueOnce([campaignData]);

      const workbook = new ExcelJS.Workbook();
      workbook.addWorksheet('sheet1');
      const worksheet = workbook.worksheets[0];
      worksheet.columns = mockSuccessColumnCampaignSummary;

      worksheet.addRow(mockErrorRowDataCampaignSummary);
      worksheet.addRow(mockErrorRowDataCampaignSummary);

      const mockBuffer = (await workbook.xlsx.writeBuffer()) as Buffer;

      const mockFile: Express.Multer.File = {
        originalname: 'mockExcel.xlsx',
        mimetype:
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        encoding: '7bit',
        buffer: mockBuffer,
        fieldname: 'test',
        size: 10,
        filename: 'mockExcel.xlsx',
        path: 'testPath',
        destination: 'destination',
        stream: new Readable(),
      };

      try {
        await adminCampaignsService.saveCampaignSummaryFromFile(
          'WW',
          mockFile,
          'test_user',
        );
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.BODY_PAYLOAD_INVALID.code,
        );
        expect((error as BaseExceptionService).message).toBe(
          'Body payload invalid',
        );
        expect((error as BaseExceptionService).data).toBe(
          `${mockErrorRowDataCampaignSummary[0]}: campaignCode exceed 200 characters`,
        );
      }
    });

    it('should failed campaign name length exceed', async () => {
      const mockErrorRowDataCampaignSummary = [
        'CodeA001',
        'ButImustexplaintoyouhowallthismistakenideaofdenouncingpleasureandpraisingpainwasbornandIwillgiveyouacompleteaccountofthesystem,andexpoundtheactualteachingsofthegreatexplorerofthetruth,themaster-builderofhu',
        'Desc001',
        'Remark001',
        new Date(),
        new Date(),
        10,
      ];

      jest.spyOn(companyRepository, 'findOne').mockResolvedValue(mockCompany);
      jest
        .spyOn(campaignRepository, 'find')
        .mockResolvedValueOnce([campaignData]);

      const workbook = new ExcelJS.Workbook();
      workbook.addWorksheet('sheet1');
      const worksheet = workbook.worksheets[0];
      worksheet.columns = mockSuccessColumnCampaignSummary;

      worksheet.addRow(mockErrorRowDataCampaignSummary);
      worksheet.addRow(mockErrorRowDataCampaignSummary);

      const mockBuffer = (await workbook.xlsx.writeBuffer()) as Buffer;

      const mockFile: Express.Multer.File = {
        originalname: 'mockExcel.xlsx',
        mimetype:
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        encoding: '7bit',
        buffer: mockBuffer,
        fieldname: 'test',
        size: 10,
        filename: 'mockExcel.xlsx',
        path: 'testPath',
        destination: 'destination',
        stream: new Readable(),
      };

      try {
        await adminCampaignsService.saveCampaignSummaryFromFile(
          'WW',
          mockFile,
          'test_user',
        );
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.BODY_PAYLOAD_INVALID.code,
        );
        expect((error as BaseExceptionService).message).toBe(
          'Body payload invalid',
        );
        expect((error as BaseExceptionService).data).toBe(
          `${mockErrorRowDataCampaignSummary[1]}: campaignName exceed 100 characters`,
        );
      }
    });

    it('should failed description length exceed', async () => {
      const mockErrorRowDataCampaignSummary = [
        'CodeA001',
        'NameA001',
        'ButImustexplaintoyouhowallthismistakenideaofdenouncingpleasureandpraisingpainwasbornandIwillgiveyoyouhowallthismistakenideaofdenouncingpleasureandpraisingpainwasbornandIwillgiveyouacompleteaccountofthesystem,andexpoundtyouhowallthismistakenideaofdenouncingpleasureandpraisingpainwasbornandIwillgiveyouacompleteaccountofthesystem,andexpoundtuacompleteaccountofthesystem,andexpoundtheactualteachingsofthegreatexplorerofthetruth,themaster-builderofhu',
        'Remark001',
        new Date(),
        new Date(),
        10,
      ];

      jest.spyOn(companyRepository, 'findOne').mockResolvedValue(mockCompany);
      jest
        .spyOn(campaignRepository, 'find')
        .mockResolvedValueOnce([campaignData]);

      const workbook = new ExcelJS.Workbook();
      workbook.addWorksheet('sheet1');
      const worksheet = workbook.worksheets[0];
      worksheet.columns = mockSuccessColumnCampaignSummary;

      worksheet.addRow(mockErrorRowDataCampaignSummary);
      worksheet.addRow(mockErrorRowDataCampaignSummary);

      const mockBuffer = (await workbook.xlsx.writeBuffer()) as Buffer;

      const mockFile: Express.Multer.File = {
        originalname: 'mockExcel.xlsx',
        mimetype:
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        encoding: '7bit',
        buffer: mockBuffer,
        fieldname: 'test',
        size: 10,
        filename: 'mockExcel.xlsx',
        path: 'testPath',
        destination: 'destination',
        stream: new Readable(),
      };

      try {
        await adminCampaignsService.saveCampaignSummaryFromFile(
          'WW',
          mockFile,
          'test_user',
        );
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.BODY_PAYLOAD_INVALID.code,
        );
        expect((error as BaseExceptionService).message).toBe(
          'Body payload invalid',
        );
        expect((error as BaseExceptionService).data).toBe(
          `${mockErrorRowDataCampaignSummary[2]}: description exceed 300 characters`,
        );
      }
    });

    it('should failed remark length exceed', async () => {
      const mockErrorRowDataCampaignSummary = [
        'CodeA001',
        'NameA001',
        'Desc001',
        'ButImustexplaintoyouhowallthismistakenideaofdenouncingpleasureandpraisingpainwasbornandIwillgiveyoyouhowallthismistakenideaofdenouncingpleasureandpraisingpainwasbornandIwillgiveyouacompleteaccountofthesystem,andexpoundtyouhowallthismistakenideaofdenouncingpleasureandpraisingpainwasbornandIwillgiveyouacompleteaccountofthesystem,andexpoundtuacompleteaccountofthesystem,andexpoundtheactualteachingsofthegreatexplorerofthetruth,themaster-builderofhu',
        new Date(),
        new Date(),
        10,
      ];

      jest.spyOn(companyRepository, 'findOne').mockResolvedValue(mockCompany);
      jest
        .spyOn(campaignRepository, 'find')
        .mockResolvedValueOnce([campaignData]);

      const workbook = new ExcelJS.Workbook();
      workbook.addWorksheet('sheet1');
      const worksheet = workbook.worksheets[0];
      worksheet.columns = mockSuccessColumnCampaignSummary;

      worksheet.addRow(mockErrorRowDataCampaignSummary);
      worksheet.addRow(mockErrorRowDataCampaignSummary);

      const mockBuffer = (await workbook.xlsx.writeBuffer()) as Buffer;

      const mockFile: Express.Multer.File = {
        originalname: 'mockExcel.xlsx',
        mimetype:
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        encoding: '7bit',
        buffer: mockBuffer,
        fieldname: 'test',
        size: 10,
        filename: 'mockExcel.xlsx',
        path: 'testPath',
        destination: 'destination',
        stream: new Readable(),
      };

      try {
        await adminCampaignsService.saveCampaignSummaryFromFile(
          'WW',
          mockFile,
          'test_user',
        );
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.BODY_PAYLOAD_INVALID.code,
        );
        expect((error as BaseExceptionService).message).toBe(
          'Body payload invalid',
        );
        expect((error as BaseExceptionService).data).toBe(
          `${mockErrorRowDataCampaignSummary[3]}: remark exceed 100 characters`,
        );
      }
    });

    it('should failed started date', async () => {
      jest.spyOn(companyRepository, 'findOne').mockResolvedValue(mockCompany);
      jest
        .spyOn(campaignRepository, 'find')
        .mockResolvedValueOnce(mockExistedRowDataCampaignSummary);

      const workbook = new ExcelJS.Workbook();
      workbook.addWorksheet('sheet1');
      const worksheet = workbook.worksheets[0];
      worksheet.columns = mockSuccessColumnCampaignSummary;

      for (let i = 0; i < mockNewRowDataCampaignSummary.length; i++) {
        worksheet.addRow(mockNewRowDataCampaignSummary[i]);
      }

      const mockBuffer = (await workbook.xlsx.writeBuffer()) as Buffer;

      const mockFile: Express.Multer.File = {
        originalname: 'mockExcel.xlsx',
        mimetype:
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        encoding: '7bit',
        buffer: mockBuffer,
        fieldname: 'test',
        size: 10,
        filename: 'mockExcel.xlsx',
        path: 'testPath',
        destination: 'destination',
        stream: new Readable(),
      };

      try {
        await adminCampaignsService.saveCampaignSummaryFromFile(
          'WW',
          mockFile,
          'test_user',
        );
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.BODY_PAYLOAD_INVALID.code,
        );
        expect((error as BaseExceptionService).message).toBe(
          'Body payload invalid',
        );
        expect((error as BaseExceptionService).data).toBe(
          'Body payload invalid, campaign already started.',
        );
      }
    });

    it('should failed date invalid 1', async () => {
      try {
        await adminCampaignsService.getDateTimeFromBkkTimezone(
          'test_st' as any,
        );
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.BODY_PAYLOAD_INVALID.code,
        );
        expect((error as BaseExceptionService).message).toBe(
          'Body payload invalid',
        );
        expect((error as BaseExceptionService).data).toBe(
          'Body payload invalid, invalid date format.',
        );
      }
    });

    it('should failed date invalid 2', async () => {
      jest.spyOn(DateTime.prototype, 'toISO').mockReturnValueOnce(null);

      try {
        await adminCampaignsService.getDateTimeFromBkkTimezone(
          '2024-10-20T00:00:00.000+07:00',
        );
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.BODY_PAYLOAD_INVALID.code,
        );
        expect((error as BaseExceptionService).message).toBe(
          'Body payload invalid',
        );
        expect((error as BaseExceptionService).data).toBe(
          'Body payload invalid, invalid date format.',
        );
      }
    });
  });

  describe('saveCampaignModelFromFile', () => {
    it('should sucessfully save', async () => {
      jest
        .spyOn(campaignRepository, 'find')
        .mockResolvedValueOnce(mockCampaignData);
      jest
        .spyOn(modelMasterRepository, 'find')
        .mockResolvedValue(mockModelMasters);
      const workbook = new ExcelJS.Workbook();
      const sheet = workbook.addWorksheet('sheet1');
      const worksheet = workbook.worksheets[0];
      worksheet.columns = mockSuccessColumnCampaignModel;

      for (let i = 0; i < mockNewRowDataCampaignModel.length; i++) {
        worksheet.addRow(mockNewRowDataCampaignModel[i]);
      }

      const mockBuffer = (await workbook.xlsx.writeBuffer()) as Buffer;

      const mockFile: Express.Multer.File = {
        originalname: 'mockExcel.xlsx',
        mimetype:
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        encoding: '7bit',
        buffer: mockBuffer,
        fieldname: 'test',
        size: 10,
        filename: 'mockExcel.xlsx',
        path: 'testPath',
        destination: 'destination',
        stream: new Readable(),
      };

      const result = await adminCampaignsService.saveCampaignModelFromFile(
        'WW',
        mockFile,
        'test_user',
      );
      expect(result).toBeNull();
    });

    it('should erro campaign not existed', async () => {
      jest.spyOn(campaignRepository, 'find').mockResolvedValueOnce([]);
      jest
        .spyOn(modelMasterRepository, 'find')
        .mockResolvedValue(mockModelMasters);
      const workbook = new ExcelJS.Workbook();

      const sheet = workbook.addWorksheet('sheet1');
      const worksheet = workbook.worksheets[0];
      worksheet.columns = mockSuccessColumnCampaignModel;

      for (let i = 0; i < mockNewRowDataCampaignModel.length; i++) {
        worksheet.addRow(mockNewRowDataCampaignModel[i]);
      }

      const mockBuffer = (await workbook.xlsx.writeBuffer()) as Buffer;

      const mockFile: Express.Multer.File = {
        originalname: 'mockExcel.xlsx',
        mimetype:
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        encoding: '7bit',
        buffer: mockBuffer,
        fieldname: 'test',
        size: 10,
        filename: 'mockExcel.xlsx',
        path: 'testPath',
        destination: 'destination',
        stream: new Readable(),
      };

      try {
        await adminCampaignsService.saveCampaignModelFromFile(
          'WW',
          mockFile,
          'test_user',
        );
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.CAMPAIGN_NOT_EXISTED.code,
        );
      }
    });

    it('should erro modelKey not existed', async () => {
      jest
        .spyOn(campaignRepository, 'find')
        .mockResolvedValueOnce(mockCampaignData);
      jest.spyOn(modelMasterRepository, 'find').mockResolvedValue([]);
      const workbook = new ExcelJS.Workbook();

      const sheet = workbook.addWorksheet('sheet1');
      const worksheet = workbook.worksheets[0];
      worksheet.columns = mockSuccessColumnCampaignModel;

      for (let i = 0; i < mockNewRowDataCampaignModel.length; i++) {
        worksheet.addRow(mockNewRowDataCampaignModel[i]);
      }

      const mockBuffer = (await workbook.xlsx.writeBuffer()) as Buffer;

      const mockFile: Express.Multer.File = {
        originalname: 'mockExcel.xlsx',
        mimetype:
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        encoding: '7bit',
        buffer: mockBuffer,
        fieldname: 'test',
        size: 10,
        filename: 'mockExcel.xlsx',
        path: 'testPath',
        destination: 'destination',
        stream: new Readable(),
      };

      try {
        await adminCampaignsService.saveCampaignModelFromFile(
          'WW',
          mockFile,
          'test_user',
        );
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.MODEL_KEY_NOT_EXISTED.code,
        );
      }
    });
  });

  describe('saveCampaignRedemptionCodeFromFile', () => {
    it('Should success upload', async () => {
      const testExcelData = generateCampaignCodeExcelDataListMock(4);

      const workbook = new ExcelJS.Workbook();
      workbook.addWorksheet('sheet1');
      const worksheet = workbook.worksheets[0];
      worksheet.columns = mockSuccessColumnCampaignRedemptionCode;

      for (let i = 0; i < testExcelData.length; i++) {
        worksheet.addRow(testExcelData[i]);
      }

      const mockBuffer = (await workbook.xlsx.writeBuffer()) as Buffer;

      const mockFile: Express.Multer.File = {
        originalname: 'mockExcel.xlsx',
        mimetype:
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        encoding: '7bit',
        buffer: mockBuffer,
        fieldname: 'test',
        size: 10,
        filename: 'mockExcel.xlsx',
        path: 'testPath',
        destination: 'destination',
        stream: new Readable(),
      };

      const result =
        await adminCampaignsService.saveCampaignRedemptionCodeFromFile(
          'WW',
          mockFile,
          'test_user',
        );
      expect(result).toEqual({ count: 4 });
    });
  });

  describe('convertCampaignStatusToQuery', () => {
    it('Should return query status ALL', async () => {
      const result = await adminCampaignsService.convertCampaignStatusToQuery(
        CampaignStatus.ALL,
      );
      expect(result).toEqual({
        query: '1=1',
        value: {},
      });
    });
    it('Should return query status IN_PROCESS', async () => {
      const result = await adminCampaignsService.convertCampaignStatusToQuery(
        CampaignStatus.IN_PROCESS,
      );
      expect(result).toEqual({
        query:
          'campaign.isActive = true and campaign.startDate <= :currentDate AND campaign.endDate > :beforeDate',
        value: {
          currentDate: expect.any(String),
          beforeDate: expect.any(String),
        },
      });
    });
    it('Should return query status NOT_START', async () => {
      const result = await adminCampaignsService.convertCampaignStatusToQuery(
        CampaignStatus.NOT_START,
      );
      expect(result).toEqual({
        query:
          'campaign.isActive = true and campaign.startDate > :currentDate AND campaign.endDate > :beforeDate',
        value: {
          currentDate: expect.any(String),
          beforeDate: expect.any(String),
        },
      });
    });
    it('Should return query status EXPIRED', async () => {
      const result = await adminCampaignsService.convertCampaignStatusToQuery(
        CampaignStatus.EXPIRED,
      );
      expect(result).toEqual({
        query: 'campaign.isActive = true and campaign.endDate <= :beforeDate',
        value: { beforeDate: expect.any(String) },
      });
    });
    it('Should return query status CANCELLED', async () => {
      const result = await adminCampaignsService.convertCampaignStatusToQuery(
        CampaignStatus.CANCELLED,
      );
      expect(result).toEqual({
        query: 'campaign.isActive = false',
        value: {},
      });
    });
  });

  describe('getCampaignList', () => {
    it('Should return all campaign', async () => {
      const query = {
        orderBy: 'campaignCode asc',
        page: '1',
        pageSize: '10',
        status: CampaignStatus.ALL,
      };
      const result = await adminCampaignsService.getCampaignList({
        companyId: 'WW',
        query,
      });
      expect(result).not.toBeNull();
    });
    it('Should return all campaign (order by other)', async () => {
      const query = {
        orderBy: 'startDate asc',
        page: '1',
        pageSize: '10',
        status: CampaignStatus.ALL,
      };
      const result = await adminCampaignsService.getCampaignList({
        companyId: 'WW',
        query,
      });
      expect(result).not.toBeNull();
    });
  });

  describe('cancelCampaign', () => {
    it('Should return success', async () => {
      jest
        .spyOn(campaignRepository, 'findOne')
        .mockResolvedValue(mockCampaignCancel);
      const result = await adminCampaignsService.cancelCampaign({
        campaignCode: 'campaign1',
        companyId: 'CompanyX',
        userKey: 'test_user',
      });
      expect(result).toBeNull();
    });

    it('Should return failed campaign not found', async () => {
      jest.spyOn(campaignRepository, 'findOne').mockResolvedValue(null);
      try {
        await adminCampaignsService.cancelCampaign({
          campaignCode: 'campaign1',
          companyId: 'CompanyX',
          userKey: 'test_user',
        });
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.CAMPAIGN_NOT_FOUND.code,
        );
      }
    });

    it('Should return failed campaign already canceled', async () => {
      jest
        .spyOn(campaignRepository, 'findOne')
        .mockResolvedValue(mockCampaignCancel);
      try {
        await adminCampaignsService.cancelCampaign({
          campaignCode: 'B001',
          companyId: 'WW',
          userKey: 'test_user',
        });
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.CAMPAIGN_NOT_ACTIVE.code,
        );
      }
    });
  });

  describe('get Campaign Template', () => {
    it('Should return excel as buffer (campaign)', async () => {
      const data =
        await adminCampaignsService.exportCampaignTemplate('campaign');
      expect(data).toBeInstanceOf(Buffer);
    });

    it('Should return excel as buffer (model)', async () => {
      const data = await adminCampaignsService.exportCampaignTemplate('model');
      expect(data).toBeInstanceOf(Buffer);
    });

    it('Should return excel as buffer (redemption-code)', async () => {
      const data =
        await adminCampaignsService.exportCampaignTemplate('redemption-code');
      expect(data).toBeInstanceOf(Buffer);
    });

    it('Should return error', async () => {
      try {
        await adminCampaignsService.exportCampaignTemplate('wrong-type');
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.BODY_PAYLOAD_INVALID.code,
        );
      }
    });
  });

  describe('get Remaining Vouchers', () => {
    it('Should return remaining vouchers', async () => {
      const result = await adminCampaignsService.getRemainingVouchers({
        companyId: 'WW',
      });
      expect(result).not.toBeNull();
    });
  });

  describe('getActiveCampaign', () => {
    const mockCampaign = [
      {
        companyId: 'WW',
        campaignCode: 'cp-code',
        campaignName: 'cp-name',
        description: 'cp-description',
        remark: 'cp-remark',
        maxRedemptionCode: 1,
        startDate: new Date(),
        endDate: new Date(),
        createdBy: 'user-create',
        updatedBy: 'user-update',
      },
    ] as CampaignEntity[];

    const mockUser = {
      company: 'WW',
      userKey: 'test-user-key',
      name: 'test-name',
    } as WithUserContext;

    it('should return active campaign', async () => {
      jest
        .spyOn(campaignRepository, 'find')
        .mockResolvedValueOnce(mockCampaign);

      const result = await adminCampaignsService.getActiveCampaign(mockUser);

      expect(result).toEqual(mockCampaign);
    });

    it('should return empty campaign', async () => {
      jest.spyOn(campaignRepository, 'find').mockResolvedValueOnce([]);

      const result = await adminCampaignsService.getActiveCampaign(mockUser);

      expect(result.length).toEqual(0);
    });
  });

  describe('transfer campaign', () => {
    const nowDate = new Date().toISOString();
    const yesterday = new Date(nowDate);
    const nextWeek = new Date(nowDate);
    yesterday.setDate(yesterday.getDate() - 2);
    nextWeek.setDate(yesterday.getDate() + 7);
    const mockCampaign = [
      {
        companyId: 'WW',
        campaignCode: 'fromCampaign',
        campaignName: 'fromCampaign',
        description: 'fromCampaign-description',
        remark: 'fromCampaign-remark',
        maxRedemptionCode: 2,
        startDate: new Date(),
        endDate: yesterday,
        createdBy: 'user-create',
        updatedBy: 'user-update',
        isActive: false,
      },
      {
        companyId: 'WW',
        campaignCode: 'cp-code',
        campaignName: 'cp-name',
        description: 'cp-description',
        remark: 'cp-remark',
        maxRedemptionCode: 1,
        startDate: new Date(),
        endDate: nextWeek,
        createdBy: 'user-create',
        updatedBy: 'user-update',
        isActive: true,
      },
      {
        companyId: 'WW',
        campaignCode: 'cp-code',
        campaignName: 'cp-name',
        description: 'cp-description',
        remark: 'cp-remark',
        maxRedemptionCode: 1,
        startDate: new Date(),
        endDate: yesterday,
        createdBy: 'user-create',
        updatedBy: 'user-update',
        isActive: true,
      },
      {
        companyId: 'WW',
        campaignCode: 'toCampaign',
        campaignName: 'toCampaign',
        description: 'toCampaign-description',
        remark: 'toCampaign-remark',
        maxRedemptionCode: 3,
        startDate: new Date(),
        endDate: nextWeek,
        createdBy: 'user-create',
        updatedBy: 'user-update',
        isActive: true,
      },
    ] as CampaignEntity[];

    const mockUser = {
      company: 'WW',
      userKey: 'test-user-key',
      name: 'test-name',
    } as WithUserContext;

    const mockRequest = {
      fromCampaignCode: 'cp-code',
      toCampaignCode: 'cp-code',
      supporter: 'test-supporter',
      value: 1000,
    };

    const mockRedemptionCode = [
      {
        companyId: 'WW',
        campaignCode: 'fromCampaign',
        order: 1,
        value: 1000,
        supporter: 'test-supporter',
      },
    ] as CampaignRedemptionCodeEntity[];
    const mockRedemptionCodeOrder = [
      {
        order: 1,
      },
    ] as CampaignRedemptionCodeEntity[];
    it('should success transfer', async () => {
      jest
        .spyOn(campaignRepository, 'findOne')
        .mockResolvedValueOnce(mockCampaign[0]);
      jest
        .spyOn(campaignRepository, 'findOne')
        .mockResolvedValueOnce(mockCampaign[3]);
      jest
        .spyOn(campaignRedemptionCodeRepository, 'find')
        .mockResolvedValueOnce(mockRedemptionCode);
      jest
        .spyOn(campaignRedemptionCodeRepository, 'find')
        .mockResolvedValueOnce(mockRedemptionCodeOrder);

      const result = await adminCampaignsService.transferCampaign(
        mockRequest,
        mockUser,
      );

      expect(result).toBeNull();
    });

    it('should error fromCampaign not found', async () => {
      jest.spyOn(campaignRepository, 'findOne').mockResolvedValueOnce(null);
      try {
        await adminCampaignsService.transferCampaign(mockRequest, mockUser);
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.NOT_FOUND_DATA.code,
        );
        expect((error as BaseExceptionService).data).toBe(
          'fromCampaignCode not found',
        );
      }
    });

    it('should error fromCampaign is active and not end yet', async () => {
      jest
        .spyOn(campaignRepository, 'findOne')
        .mockResolvedValueOnce(mockCampaign[1]);

      try {
        await adminCampaignsService.transferCampaign(mockRequest, mockUser);
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.BODY_PAYLOAD_INVALID.code,
        );
        expect((error as BaseExceptionService).data).toBe(
          'fromCampaignCode not match condition',
        );
      }
    });

    it('should error toCampaign not found', async () => {
      jest
        .spyOn(campaignRepository, 'findOne')
        .mockResolvedValueOnce(mockCampaign[0]);
      jest.spyOn(campaignRepository, 'findOne').mockResolvedValueOnce(null);
      try {
        await adminCampaignsService.transferCampaign(mockRequest, mockUser);
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.NOT_FOUND_DATA.code,
        );
        expect((error as BaseExceptionService).data).toBe(
          'toCampaignCode not found',
        );
      }
    });

    it('should error toCampaign is not active', async () => {
      jest
        .spyOn(campaignRepository, 'findOne')
        .mockResolvedValueOnce(mockCampaign[0]);
      jest
        .spyOn(campaignRepository, 'findOne')
        .mockResolvedValueOnce(mockCampaign[0]);
      try {
        await adminCampaignsService.transferCampaign(mockRequest, mockUser);
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.BODY_PAYLOAD_INVALID.code,
        );
        expect((error as BaseExceptionService).data).toBe(
          'toCampaignCode not match condition',
        );
      }
    });

    it('should error toCampaign already end', async () => {
      jest
        .spyOn(campaignRepository, 'findOne')
        .mockResolvedValueOnce(mockCampaign[0]);
      jest
        .spyOn(campaignRepository, 'findOne')
        .mockResolvedValueOnce(mockCampaign[2]);
      try {
        await adminCampaignsService.transferCampaign(mockRequest, mockUser);
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.BODY_PAYLOAD_INVALID.code,
        );
        expect((error as BaseExceptionService).data).toBe(
          'toCampaignCode not match condition',
        );
      }
    });

    it('should error redemptionCode is unavailable', async () => {
      jest
        .spyOn(campaignRepository, 'findOne')
        .mockResolvedValueOnce(mockCampaign[0]);
      jest
        .spyOn(campaignRepository, 'findOne')
        .mockResolvedValueOnce(mockCampaign[3]);
      jest
        .spyOn(campaignRedemptionCodeRepository, 'find')
        .mockResolvedValueOnce([]);
      try {
        await adminCampaignsService.transferCampaign(mockRequest, mockUser);
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.NOT_FOUND_DATA.code,
        );
        expect((error as BaseExceptionService).data).toBe(
          'voucher in fromCampaignCode is unavailable',
        );
      }
    });

    it('should error voucher is occupied', async () => {
      const mockRedemptionCodeOrderMax = [
        {
          order: 1,
        },
        {
          order: 2,
        },
        {
          order: 3,
        },
      ] as CampaignRedemptionCodeEntity[];
      jest
        .spyOn(campaignRepository, 'findOne')
        .mockResolvedValueOnce(mockCampaign[0]);
      jest
        .spyOn(campaignRepository, 'findOne')
        .mockResolvedValueOnce(mockCampaign[3]);
      jest
        .spyOn(campaignRedemptionCodeRepository, 'find')
        .mockResolvedValueOnce(mockRedemptionCode);
      jest
        .spyOn(campaignRedemptionCodeRepository, 'find')
        .mockResolvedValueOnce(mockRedemptionCodeOrderMax);
      try {
        await adminCampaignsService.transferCampaign(mockRequest, mockUser);
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.VOUCHER_ORDER_IS_OCCUPIED.code,
        );
        expect((error as BaseExceptionService).data).toBe(
          'Voucher order is occupied',
        );
      }
    });
  });
});
