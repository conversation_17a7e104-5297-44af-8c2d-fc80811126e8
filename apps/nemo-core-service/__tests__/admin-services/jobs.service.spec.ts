import { getRepositoryToken } from '@nestjs/typeorm';
import { Test } from '@nestjs/testing';
import { getQueueToken } from '@nestjs/bull';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import {
  JobActivitiesEntity,
  JobEntity,
  JobStatus,
  ModelMasterEntity,
  PenaltiesView,
  JobTemplateEntity,
  CompanyEntity,
  BranchEntity,
  DeliveryOrderEntity,
  JobShippingStatus,
  QCStatus,
  ImportedVoucherEntity,
  UserEntity,
  InspectHx,
  RepairHx,
  SystemConfigEntity,
  ExportActivitiesEntity,
  CompanyRoleEntity,
  ConfigActivitiesEntity,
  ContractEntity,
  VoucherEntity,
  ModelMasterColorEntity,
  AllocationOrderEntity,
  AOShippingStatus,
  AllocationOrderStatus,
  RoleEntity,
  GeneralActivitiesEntity,
  UserRoleBranchEntity,
} from '../../src/entities';
import { WithUserContext } from '../../src/interfaces';
import { BaseExceptionModule } from '../../src/base-exceptions/base-exceptions.module';
import { Repository, SelectQueryBuilder } from 'typeorm';
import { Request } from 'express';
import { JobsController } from '../../src/admin/jobs/jobs.controller';
import { AdminJobsService } from '../../src/admin/jobs/jobs.service';
import { AdminUsersService } from '../../src/admin/users/users.service';
import { JobsService } from '../../src/shop/jobs/jobs.service';
import { BranchesService } from '../../src/admin/branches/branches.service';
import { SystemConfigService } from '../../src/system-config/system-config.service';
import { CacheManagerService } from '../../src/cache-manager/cache-manager.service';
import { AES128MessageService } from '../../src/encrypt-decrypt-message/encrypt-decrypt.service';
import { ContractsService } from '../../src/shop/contracts/contracts.service';
import { S3Service } from '../../src/storage/s3.service';
import { SmtpService } from '../../src/smtp/smtp.service';
import { AdminAllocationOrdersService } from '../../src/admin/allocation-orders/allocation-orders.service';
import { mockModelMasters } from '../mock-data/model-master';
import { BaseExceptionService } from '../../src/exceptions';
import { BASE_EXCEPTIONS, getS3JobUrlPath } from '../../src/config';
import { CrudController } from '../../src/crud';
import {
  IConfirmRepairJobBody,
  ReceiveJobsBody,
  AssignRepairBody,
  InspectJobBody,
} from 'contracts';
import {
  mockGetJobsRequest,
  mockPurchasedJob,
  mockReceivedJob,
  mockInTransitDO,
  mockInTransitDOPartial,
  mockQCCompletedJob,
  mockRepairAssignedJob,
  mockRepairedUser,
  mockJob,
  getGenerateMockMonthly,
  mockInspectionJob,
  mockProductJobWithConfirmPrice,
  mockInspectionJobNotConfirmed,
} from '../mock-data/job';
import { mockUserEntity } from '../mock-data/user';
import { mockAO } from '../mock-data/allocation-order';
import {
  getDateUTCFromYMDArray,
  getDateFromToday,
} from '../../src/utils/general';
import { excelManagerOption } from '../../src/admin/model-masters/model-masters.service';
import { ExcelManagerModule } from '../../src/excel/excel-manager.module';
import * as reCal from '../../src/utils/job/queue';
import { prepareJobsSnapshot } from '../../src/utils/job/jobsSnapshot';
import { EncryptDecryptModule } from '../../src/encrypt-decrypt-message/encrypt-decrypt.module';

jest.mock('../../src/utils/general', () => {
  const original = jest.requireActual('../../src/utils/general');
  return {
    ...original,
    getDateFromToday: jest.fn(),
  };
});

describe('AdminJobsService', () => {
  let adminJobsService: AdminJobsService;
  let adminAllocationOrdersService: AdminAllocationOrdersService;
  let adminJobsController: JobsController;
  let systemConfigService: SystemConfigService<string>;
  let jobRepository: Repository<JobEntity>;
  let userRepository: Repository<UserEntity>;
  let branchRepository: Repository<BranchEntity>;
  let importedVoucherRepository: Repository<ImportedVoucherEntity>;
  let deliveryOrderRepository: Repository<DeliveryOrderEntity>;
  let systemConfigRepository: Repository<SystemConfigEntity>;
  let companyRepository: Repository<CompanyEntity>;
  let roleRepository: Repository<RoleEntity>;
  let generalActivitiesRepository: Repository<GeneralActivitiesEntity>;
  let userRoleBranchRepository: Repository<UserRoleBranchEntity>;
  let allocationOrderRepository: Repository<AllocationOrderEntity>;

  beforeAll(async () => {
    const module = await Test.createTestingModule({
      imports: [
        EncryptDecryptModule.register(),
        BaseExceptionModule.register(),
        ExcelManagerModule.register(excelManagerOption),
      ],
      controllers: [JobsController],
      providers: [
        AdminUsersService,
        AdminJobsService,
        BranchesService,
        SystemConfigService,
        CacheManagerService,
        AdminAllocationOrdersService,
        {
          provide: ContractsService,
          useValue: {
            getTransactionInspection: jest.fn(() => 'contract'),
          },
        },
        {
          provide: AES128MessageService,
          useFactory: () => {
            // Get aes key
            const aesKey = process.env.AES128_KEY;

            // Get aes salt
            const aesSalt = process.env.AES128_SALT;

            // Prevent key or salt invalid
            if (!aesKey || !aesSalt) {
              throw new Error(
                'AES_KEY and AES_SALT must be defined in .env file',
              );
            }

            // Initial aes service
            return new AES128MessageService(aesKey, aesSalt);
          },
        },
        {
          provide: JobsService,
          useValue: {
            findOne: jest.fn().mockReturnThis(),
            findOneBy: jest.fn().mockReturnThis(),
            computeUpdatePayload: jest.fn().mockReturnThis(),
            defaultPrepareJob: jest.fn().mockReturnThis(),
            insertJobActivities: jest.fn().mockReturnThis(),
          },
        },
        {
          provide: S3Service,
          useValue: {
            getPreviewUrl: jest.fn(),
            uploadFile: jest.fn(),
            getFile: jest.fn(),
            uploadFileByPresignedUrl: jest.fn(),
            getUploadFilePreSignedUrl: jest.fn(
              () => `company/CompanyX/jobs/jobid-0001/media/test_video_111`,
            ),
          },
        },
        {
          provide: SmtpService,
          useValue: {
            sendMail: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(UserEntity),
          useValue: {
            find: jest.fn().mockReturnThis(),
          },
        },
        {
          provide: getRepositoryToken(BranchEntity),
          useValue: {
            find: jest.fn().mockReturnThis(),
          },
        },
        {
          provide: getRepositoryToken(CompanyEntity),
          useValue: {
            findOne: jest.fn().mockReturnThis(),
          },
        },
        {
          provide: getRepositoryToken(CompanyRoleEntity),
          useValue: {
            find: jest.fn().mockReturnThis(),
          },
        },
        {
          provide: getRepositoryToken(JobEntity),
          useValue: {
            find: jest.fn().mockReturnThis(),
            query: jest.fn().mockReturnThis(),
            findOne: jest.fn().mockReturnThis(),
            findOneBy: jest.fn().mockReturnThis(),
            count: jest.fn().mockReturnThis(),
            createQueryBuilder: jest.fn().mockReturnThis(),
            where: jest.fn().mockReturnThis(),
            getMany: jest.fn().mockReturnThis(),
            save: jest.fn().mockReturnThis(),
          },
        },
        {
          provide: getRepositoryToken(ModelMasterEntity),
          useValue: {
            findOne: jest.fn().mockReturnThis(),
          },
        },
        {
          provide: getRepositoryToken(PenaltiesView),
          useValue: {
            findOne: jest.fn().mockReturnThis(),
          },
        },
        {
          provide: getRepositoryToken(JobTemplateEntity),
          useValue: {
            findOne: jest.fn().mockReturnThis(),
          },
        },
        {
          provide: getRepositoryToken(JobActivitiesEntity),
          useValue: {
            findOne: jest.fn().mockReturnThis(),
            save: jest.fn().mockReturnThis(),
          },
        },
        {
          provide: getRepositoryToken(DeliveryOrderEntity),
          useValue: {
            findOne: jest.fn().mockReturnThis(),
            save: jest.fn().mockReturnThis(),
            createQueryBuilder: jest.fn().mockReturnThis(),
            where: jest.fn().mockReturnThis(),
            getMany: jest.fn().mockReturnThis(),
          },
        },
        {
          provide: getRepositoryToken(SystemConfigEntity),
          useValue: {
            findOne: jest.fn().mockReturnThis(),
          },
        },
        {
          provide: getRepositoryToken(ImportedVoucherEntity),
          useValue: {
            findOneBy: jest.fn().mockReturnThis(),
          },
        },
        {
          provide: getRepositoryToken(ExportActivitiesEntity),
          useValue: {
            save: jest.fn().mockReturnThis(),
          },
        },
        {
          provide: getRepositoryToken(ContractEntity),
          useValue: {
            findOne: jest.fn().mockReturnThis(),
          },
        },
        {
          provide: getRepositoryToken(VoucherEntity),
          useValue: {
            findOne: jest.fn().mockReturnThis(),
          },
        },
        {
          provide: getQueueToken('re-calculate-product-queue'),
          useValue: {
            add: jest.fn(),
            process: jest.fn(),
          },
        },
        {
          provide: getQueueToken('job-request-queue'),
          useValue: {
            add: jest.fn(),
            process: jest.fn(),
          },
        },
        {
          provide: CACHE_MANAGER,
          useValue: {
            get: jest.fn(),
            set: jest.fn(),
            reset: jest.fn(),
            del: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(ConfigActivitiesEntity),
          useValue: {
            save: jest.fn().mockReturnThis(),
          },
        },
        {
          provide: getRepositoryToken(ModelMasterColorEntity),
          useValue: {
            find: jest.fn(() => []),
          },
        },
        {
          provide: getRepositoryToken(AllocationOrderEntity),
          useValue: {
            save: jest.fn().mockReturnThis(),
            findOne: jest.fn().mockReturnThis(),
            insert: jest.fn().mockReturnThis(),
          },
        },
        {
          provide: getRepositoryToken(RoleEntity),
          useValue: {
            save: jest.fn().mockReturnThis(),
            findOne: jest.fn().mockReturnThis(),
            insert: jest.fn().mockReturnThis(),
          },
        },
        {
          provide: getRepositoryToken(GeneralActivitiesEntity),
          useValue: {
            save: jest.fn().mockReturnThis(),
            findOne: jest.fn().mockReturnThis(),
            insert: jest.fn().mockReturnThis(),
          },
        },
        {
          provide: getRepositoryToken(UserRoleBranchEntity),
          useValue: {
            save: jest.fn().mockReturnThis(),
            findOne: jest.fn().mockReturnThis(),
            insert: jest.fn().mockReturnThis(),
          },
        },
      ],
    }).compile();

    adminJobsService = module.get<AdminJobsService>(AdminJobsService);
    adminAllocationOrdersService = module.get<AdminAllocationOrdersService>(
      AdminAllocationOrdersService,
    );
    adminJobsController = await module.resolve<JobsController>(JobsController);
    systemConfigService =
      module.get<SystemConfigService<string>>(SystemConfigService);
    jobRepository = module.get<Repository<JobEntity>>(
      getRepositoryToken(JobEntity),
    );
    importedVoucherRepository = module.get<Repository<ImportedVoucherEntity>>(
      getRepositoryToken(ImportedVoucherEntity),
    );
    deliveryOrderRepository = module.get<Repository<DeliveryOrderEntity>>(
      getRepositoryToken(DeliveryOrderEntity),
    );
    userRepository = module.get<Repository<UserEntity>>(
      getRepositoryToken(UserEntity),
    );
    branchRepository = module.get<Repository<BranchEntity>>(
      getRepositoryToken(BranchEntity),
    );
    systemConfigRepository = module.get<Repository<SystemConfigEntity>>(
      getRepositoryToken(SystemConfigEntity),
    );
    companyRepository = module.get<Repository<CompanyEntity>>(
      getRepositoryToken(CompanyEntity),
    );
    allocationOrderRepository = module.get<Repository<AllocationOrderEntity>>(
      getRepositoryToken(AllocationOrderEntity),
    );
    roleRepository = module.get<Repository<RoleEntity>>(
      getRepositoryToken(RoleEntity),
    );
    generalActivitiesRepository = module.get<
      Repository<GeneralActivitiesEntity>
    >(getRepositoryToken(GeneralActivitiesEntity));
    userRoleBranchRepository = module.get<Repository<UserRoleBranchEntity>>(
      getRepositoryToken(UserRoleBranchEntity),
    );
  });

  beforeEach(() => {
    jest.clearAllMocks();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('Get Jobs Count', () => {
    const mockRequest = {
      withUserContext: {
        name: 'test-name',
        userKey: 'test-user-key',
        companyId: 'ww',
      },
    } as unknown as Request & { withUserContext?: WithUserContext };
    it('should return count for every count', async () => {
      jest.spyOn(jobRepository, 'count').mockResolvedValue(3);
      const result = await adminJobsService.jobCount(mockRequest);

      expect(result).toEqual([3, 3, 3]);
    });
  });

  describe('Get Jobs', () => {
    describe('helper function', () => {
      describe('checkStatus', () => {
        it('error case', () => {
          const result = adminJobsService.checkStatus('field', ['']);
          expect(result).toEqual('');
        });
        it('one case', () => {
          const result = adminJobsService.checkStatus('field', ['status1']);
          expect(result).toEqual(`field IN ('status1')`);
        });
        it('two case', () => {
          const result = adminJobsService.checkStatus('field', [
            'status1',
            'status2',
          ]);
          expect(result).toEqual(`field IN ('status1','status2')`);
        });
      });
    });
    describe('buildSearchQuery', () => {
      const mockQueryBuilder = {
        andWhere: jest.fn(),
      } as unknown as SelectQueryBuilder<JobEntity>;
      it.each([
        'myJobs',
        'partialReceive',
        'statusOne',
        'statusMoreThanOne',
        'shippingStatusOne',
        'shippingStatusMoreThanOne',
        'others',
        'all',
        'ReceiveByJobSearchJobIdOrDeviceKey',
        'myQCJobs',
        'qcStatusOne',
        'qcStatusMoreThanOne',
        'myRepairJob',
        'isRepaired',
        'isNotRepaired',
        'minPrice',
        'maxPrice',
        'minMaxUpdatedDate',
        'isConfirmPriceFalse',
        'isConfirmPriceTrue',
        'isIncompleteAO',
      ])('query param each case', async (testCase) => {
        const mockParam = mockGetJobsRequest[testCase];
        const mockRequest = mockParam.request as unknown as Request & {
          withUserContext?: WithUserContext;
        };
        const result = adminJobsService.buildSearchQuery(
          mockRequest,
          mockQueryBuilder,
        );

        expect(result.andWhere).toHaveBeenCalledWith(mockParam.result);
      });

      it('should error number format (string)', () => {
        const mockParam = mockGetJobsRequest['wrongFormatPriceString'];
        const mockRequest = mockParam.request as unknown as Request & {
          withUserContext?: WithUserContext;
        };

        try {
          adminJobsService.buildSearchQuery(mockRequest, mockQueryBuilder);
          fail('Expected method to throw an error');
        } catch (error) {
          expect(error).toBeInstanceOf(BaseExceptionService);
          expect((error as BaseExceptionService).code).toBe(
            BASE_EXCEPTIONS.BODY_PAYLOAD_INVALID.code,
          );
          expect((error as BaseExceptionService).message).toBe(
            'Body payload invalid',
          );
          expect((error as BaseExceptionService).data).toBe(
            'Incorrect input: should be a number.',
          );
        }
      });

      it('should error number format (decimal)', () => {
        const mockParam = mockGetJobsRequest['wrongFormatPriceDecimal'];
        const mockRequest = mockParam.request as unknown as Request & {
          withUserContext?: WithUserContext;
        };

        try {
          adminJobsService.buildSearchQuery(mockRequest, mockQueryBuilder);
          fail('Expected method to throw an error');
        } catch (error) {
          expect(error).toBeInstanceOf(BaseExceptionService);
          expect((error as BaseExceptionService).code).toBe(
            BASE_EXCEPTIONS.BODY_PAYLOAD_INVALID.code,
          );
          expect((error as BaseExceptionService).message).toBe(
            'Body payload invalid',
          );
          expect((error as BaseExceptionService).data).toBe(
            'Incorrect input: should be a number.',
          );
        }
      });

      it('should error number format (zero lead)', () => {
        const mockParam = mockGetJobsRequest['wrongFormatPriceZeroLead'];
        const mockRequest = mockParam.request as unknown as Request & {
          withUserContext?: WithUserContext;
        };

        try {
          adminJobsService.buildSearchQuery(mockRequest, mockQueryBuilder);
          fail('Expected method to throw an error');
        } catch (error) {
          expect(error).toBeInstanceOf(BaseExceptionService);
          expect((error as BaseExceptionService).code).toBe(
            BASE_EXCEPTIONS.BODY_PAYLOAD_INVALID.code,
          );
          expect((error as BaseExceptionService).message).toBe(
            'Body payload invalid',
          );
          expect((error as BaseExceptionService).data).toBe(
            'Incorrect input: should be a number.',
          );
        }
      });

      it('should error date format', () => {
        const mockParam = mockGetJobsRequest['wrongFormatUpdatedDate'];
        const mockRequest = mockParam.request as unknown as Request & {
          withUserContext?: WithUserContext;
        };

        try {
          adminJobsService.buildSearchQuery(mockRequest, mockQueryBuilder);
          fail('Expected method to throw an error');
        } catch (error) {
          expect(error).toBeInstanceOf(BaseExceptionService);
          expect((error as BaseExceptionService).code).toBe(
            BASE_EXCEPTIONS.BODY_PAYLOAD_INVALID.code,
          );
          expect((error as BaseExceptionService).message).toBe(
            'Body payload invalid',
          );
          expect((error as BaseExceptionService).data).toBe(
            'Date incorrect format. Date format should be YYYY-MM-DDTHH:MM:TT.TTTZ',
          );
        }
      });

      it('should error time span 1 year', () => {
        const startDate = '2024-01-01T00:00:00.000';
        const endDate = '2026-01-01T00:00:00.000';

        try {
          adminJobsService.validateTimeInput(startDate, endDate);
          fail('Expected method to throw an error');
        } catch (error) {
          expect(error).toBeInstanceOf(BaseExceptionService);
          expect((error as BaseExceptionService).code).toBe(
            BASE_EXCEPTIONS.BODY_PAYLOAD_INVALID.code,
          );
          expect((error as BaseExceptionService).message).toBe(
            'Body payload invalid',
          );
          expect((error as BaseExceptionService).data).toBe(
            'Incorrect input: timespan between min and max date should not more than 1 year.',
          );
        }
      });

      it('should error time span 31 day', () => {
        const startDate = '2024-01-01T00:00:00.000';
        const endDate = '2026-01-01T00:00:00.000';

        try {
          adminJobsService.validateTimeInput(startDate, endDate, 'export');
          fail('Expected method to throw an error');
        } catch (error) {
          expect(error).toBeInstanceOf(BaseExceptionService);
          expect((error as BaseExceptionService).code).toBe(
            BASE_EXCEPTIONS.BODY_PAYLOAD_INVALID.code,
          );
          expect((error as BaseExceptionService).message).toBe(
            'Body payload invalid',
          );
          expect((error as BaseExceptionService).data).toBe(
            'Incorrect input: timespan between min and max date should not more than 31 days.',
          );
        }
      });

      it('should error max date less than min date', () => {
        const mockParam = mockGetJobsRequest['minOverMaxUpdatedDate'];
        const mockRequest = mockParam.request as unknown as Request & {
          withUserContext?: WithUserContext;
        };

        try {
          adminJobsService.buildSearchQuery(mockRequest, mockQueryBuilder);
          fail('Expected method to throw an error');
        } catch (error) {
          expect(error).toBeInstanceOf(BaseExceptionService);
          expect((error as BaseExceptionService).code).toBe(
            BASE_EXCEPTIONS.BODY_PAYLOAD_INVALID.code,
          );
          expect((error as BaseExceptionService).message).toBe(
            'Body payload invalid',
          );
          expect((error as BaseExceptionService).data).toBe(
            'Incorrect input: minDate should less than maxDate.',
          );
        }
      });

      it('should not add conditions if parameters are not provided', () => {
        const mockRequest = {
          query: {},
        } as unknown as Request;

        const result = adminJobsService.buildSearchQuery(
          mockRequest,
          mockQueryBuilder,
        );

        expect(result.andWhere).not.toHaveBeenCalled();
      });
    });
  });

  describe('Compute Update Payload', () => {
    const mockJob = {
      jobId: 'test',
      companyId: 'WW',
      deviceKey: '1111111111',
      branchId: 'test',
      modelKey: 'test|test|test',
      createdBy: 'test',
      createdAt: new Date(),
      checkList: [],
      modelIdentifiers: 'test',
      modelTemplate: mockModelMasters,
      modelMaster: mockModelMasters,
      checkListValues: [],
      company: {} as CompanyEntity,
      branch: {} as BranchEntity,
      shopUserKey: 'test',
      transformModelKey: () => {},
      isUpdatedToEstimated: () => true,
      isConfirmPrice: false,
      phoneNumber: '0123456789',
      jobVendor: 'MASS',
    };
    it('should compute update payload', async () => {
      adminJobsService.computeUpdatePayload(
        {
          ...mockJob,
          status: JobStatus.ESTIMATE_PRICE_PROCESSING,
        },
        {
          status: JobStatus.PRICE_ESTIMATED,
        },
      );
    });
    it('should throw error if job is canceled', async () => {
      try {
        adminJobsService.computeUpdatePayload(
          {
            ...mockJob,
            status: JobStatus.REJECT_BY_SHOP,
          },
          {},
        );
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.UNAVAILABLE_JOB.code,
        );
      }
    });
  });

  describe('Prepare Assign Job', () => {
    it('should prepare an assign job', async () => {
      const user = { userKey: 'user123' };

      // Call the method being tested
      const result = await adminJobsService.prepareAssignJob(user as any);

      // Assertions
      expect(result).toBeInstanceOf(JobEntity);
      expect(result.updatedBy).toBe(user.userKey);
      expect(result.status).toBe(JobStatus.ESTIMATE_PRICE_PROCESSING);
    });
  });

  describe('Prepare Suggest Price', () => {
    const body = {
      suggestedPrice: 15000.0,
      grade: 'B',
      adminCheckListValues: { value1: 'A', value2: 'B' },
    };
    const user = { userKey: 'user123' };

    const mockJob = {
      modelTemplate: {
        modelMasterGrades: [
          {
            grade: 'A',
            purchasePrice: '37900.00',
            lastPurchasedOn: 'ISO8601',
            lastPurchasedPrice: 'Grade Info',
          },
        ],
      },
    } as JobEntity;

    const mockImportVoucher = {
      voucherValue: 37900.0,
      contractId: undefined,
    } as ImportedVoucherEntity;

    it('should prepare an suggest price', async () => {
      jest.spyOn(jobRepository, 'findOneBy').mockResolvedValueOnce(mockJob);
      jest
        .spyOn(importedVoucherRepository, 'findOneBy')
        .mockResolvedValueOnce(mockImportVoucher);
      // Call the method being tested
      const result = await adminJobsService.prepareSuggestPrice(
        'job-id',
        body as any,
        user as any,
      );

      // Assertions
      expect(result).toBeInstanceOf(JobEntity);
      expect(result.updatedBy).toEqual(user.userKey);
      expect(result.status).toBe(JobStatus.PRICE_ESTIMATED);
      expect(result.currentGrade).toEqual(body.grade);
      expect(result.suggestedPrice).toEqual(body.suggestedPrice);
      expect(result.adminCheckListValues).toEqual(body.adminCheckListValues);
    });
    it('should throw invalid input for id', async () => {
      jest.spyOn(jobRepository, 'findOneBy').mockResolvedValueOnce(null);
      // Call the method being tested
      try {
        await adminJobsService.prepareSuggestPrice(
          'job-id',
          body as any,
          user as any,
        );
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.INVALID_INPUT_FOR_UPDATE_JOB.code,
        );
      }
    });
    it('should throw invalid input for suggested price', async () => {
      jest.spyOn(jobRepository, 'findOneBy').mockResolvedValueOnce(mockJob);
      // Call the method being tested
      try {
        await adminJobsService.prepareSuggestPrice(
          'job-id',
          { ...body, suggestedPrice: 1000000 } as any,
          user as any,
        );
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.INVALID_INPUT_FOR_UPDATE_JOB.code,
        );
      }
    });
    it('should throw voucher not found', async () => {
      jest.spyOn(jobRepository, 'findOneBy').mockResolvedValueOnce(mockJob);
      jest
        .spyOn(importedVoucherRepository, 'findOneBy')
        .mockResolvedValueOnce(null);
      // Call the method being tested
      try {
        const aaa = await adminJobsService.prepareSuggestPrice(
          'job-id',
          { ...body, suggestedPrice: 370.0 } as any,
          user as any,
        );
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.VOUCHER_NOT_FOUND.code,
        );
      }
    });
    it('should throw invalid input for grade', async () => {
      jest.spyOn(jobRepository, 'findOneBy').mockResolvedValueOnce(mockJob);
      // Call the method being tested
      const { grade, ...adjustedBody } = body;
      try {
        await adminJobsService.prepareSuggestPrice(
          'job-id',
          adjustedBody as any,
          user as any,
        );
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.INVALID_INPUT_FOR_UPDATE_JOB.code,
        );
      }
    });
  });
  describe('comment job', () => {
    const mockRequest = {
      withUserContext: {
        name: 'test-name',
        userKey: 'test-user-key',
        companyId: 'ww',
      },
    } as unknown as Request & { withUserContext: WithUserContext };

    it('comment success', async () => {
      jest
        .spyOn(CrudController.prototype, 'update')
        .mockImplementationOnce(async () => ({ jobId: 'test' }));

      const result = await adminJobsController.commentJob(
        mockRequest,
        'TEST-ID',
        { message: 'test' },
        mockRequest.withUserContext,
      );

      expect(result.jobId).toEqual('test');
    });

    it('called flow check', async () => {
      const mockUpdate = jest.fn(async () => ({ jobId: 'test' }) as JobEntity);

      const mockDefaultPrepareJob = jest.fn(
        async () =>
          ({
            updatedBy: mockRequest.withUserContext.userKey,
            isAdditionalCheckList: true,
          }) as JobEntity,
      );

      const mockInsertJobActivities = jest.fn(async () => {
        console.log('mock insert job activities');
      });

      jest
        .spyOn(CrudController.prototype, 'update')
        .mockImplementationOnce(mockUpdate);

      jest
        .spyOn(adminJobsService, 'defaultPrepareJob')
        .mockImplementationOnce(mockDefaultPrepareJob);

      jest
        .spyOn(adminJobsService, 'insertJobActivities')
        .mockImplementationOnce(mockInsertJobActivities);
      await adminJobsController.commentJob(
        mockRequest,
        'TEST-ID',
        { message: 'test' },
        mockRequest.withUserContext,
      );

      expect(mockUpdate).toHaveBeenCalledTimes(1);
      expect(mockDefaultPrepareJob).toHaveBeenCalledTimes(1);
      expect(mockInsertJobActivities).toHaveBeenCalledTimes(1);
    });
  });

  describe('getShippingStatus', () => {
    it('should return JobShippingStatus', async () => {
      const result = adminJobsService.shippingStatus();
      const listShippingStatus: string[] = [];
      for (const key in JobShippingStatus) {
        listShippingStatus.push(JobShippingStatus[key]);
      }
      for (const status of result) {
        expect(listShippingStatus.includes(status.value)).toBeTruthy();
      }
    });
  });

  describe('Patch Receive Jobs', () => {
    const mockUser = {
      company: 'WW',
      userKey: 'test-user-key',
      name: 'test-name',
    } as WithUserContext;

    it('should success update', async () => {
      const mockRequest = [] as ReceiveJobsBody[];
      mockRequest.push({
        id: 'test',
        shippingStatus: '12_RECEIVED_OTHER',
        remark: 'No sim tray',
      });

      jest.spyOn(jobRepository, 'createQueryBuilder').mockReturnValueOnce({
        where: jest.fn().mockReturnThis(),
        getMany: jest.fn().mockResolvedValue([mockPurchasedJob] as JobEntity[]),
      } as any);

      jest.spyOn(jobRepository, 'createQueryBuilder').mockReturnValueOnce({
        where: jest.fn().mockReturnThis(),
        getMany: jest.fn().mockResolvedValue([] as JobEntity[]),
      } as any);

      const mockQueryRunner = {
        connect: jest.fn().mockResolvedValue(undefined),
        startTransaction: jest.fn().mockResolvedValue(undefined),
        commitTransaction: jest.fn().mockResolvedValue(undefined),
        rollbackTransaction: jest.fn().mockResolvedValue(undefined),
        release: jest.fn().mockResolvedValue(undefined),
      } as any;

      const mockConnection = {
        createQueryRunner: jest.fn().mockReturnValue(mockQueryRunner),
      } as any;

      const mockManager = {
        connection: mockConnection,
        getMany: jest.fn().mockResolvedValue([mockPurchasedJob] as JobEntity[]),
        save: jest.fn().mockResolvedValue(null),
      } as any;

      Object.defineProperty(jobRepository, 'manager', {
        value: mockManager,
      });
      Object.defineProperty(mockQueryRunner, 'manager', {
        value: mockManager,
      });

      jest
        .spyOn(deliveryOrderRepository, 'createQueryBuilder')
        .mockReturnValueOnce({
          where: jest.fn().mockReturnThis(),
          getMany: jest.fn().mockResolvedValue(mockInTransitDO),
        } as any);

      const result = await adminJobsService.updateReceiveJobs(
        mockRequest,
        mockUser,
      );
      expect(result).toBeNull();
    });

    it('should success update partial ', async () => {
      jest.spyOn(jobRepository, 'createQueryBuilder').mockReturnValueOnce({
        where: jest.fn().mockReturnThis(),
        getMany: jest.fn().mockResolvedValue([mockPurchasedJob] as JobEntity[]),
      } as any);

      jest.spyOn(jobRepository, 'createQueryBuilder').mockReturnValueOnce({
        where: jest.fn().mockReturnThis(),
        getMany: jest.fn().mockResolvedValue([mockReceivedJob] as JobEntity[]),
      } as any);

      const mockQueryRunner = {
        connect: jest.fn().mockResolvedValue(undefined),
        startTransaction: jest.fn().mockResolvedValue(undefined),
        commitTransaction: jest.fn().mockResolvedValue(undefined),
        rollbackTransaction: jest.fn().mockResolvedValue(undefined),
        release: jest.fn().mockResolvedValue(undefined),
      } as any;

      const mockConnection = {
        createQueryRunner: jest.fn().mockReturnValue(mockQueryRunner),
      } as any;

      const mockManager = {
        connection: mockConnection,
        getMany: jest.fn().mockResolvedValue([mockPurchasedJob] as JobEntity[]),
        save: jest.fn().mockResolvedValue(null),
      } as any;

      jest
        .spyOn(deliveryOrderRepository, 'createQueryBuilder')
        .mockReturnValueOnce({
          where: jest.fn().mockReturnThis(),
          getMany: jest.fn().mockResolvedValue(mockInTransitDOPartial),
        } as any);

      const mockRequest = [] as ReceiveJobsBody[];
      mockRequest.push({
        id: 'test',
        shippingStatus: '12_RECEIVED_OTHER',
        remark: 'No sim tray',
      });

      const result = await adminJobsService.updateReceiveJobs(
        mockRequest,
        mockUser,
      );
      expect(result).toBeNull();
    });

    it('should handle errors and roll back transaction', async () => {
      const mockRequest = [] as ReceiveJobsBody[];
      mockRequest.push({
        id: 'test',
        shippingStatus: '12_RECEIVED_OTHER',
        remark: 'No sim tray',
      });

      jest.spyOn(jobRepository, 'createQueryBuilder').mockReturnValueOnce({
        where: jest.fn().mockReturnThis(),
        getMany: jest.fn().mockResolvedValue([mockPurchasedJob] as JobEntity[]),
      } as any);

      jest.spyOn(jobRepository, 'createQueryBuilder').mockReturnValueOnce({
        where: jest.fn().mockReturnThis(),
        getMany: jest.fn().mockResolvedValue([mockReceivedJob] as JobEntity[]),
      } as any);

      const mockQueryRunner = {
        connect: jest.fn().mockResolvedValue(undefined),
        startTransaction: jest.fn().mockResolvedValue(undefined),
        commitTransaction: jest.fn().mockResolvedValue(undefined),
        rollbackTransaction: jest.fn().mockResolvedValue(undefined),
        release: jest.fn().mockResolvedValue(undefined),
      } as any;

      const mockConnection = {
        createQueryRunner: jest.fn().mockReturnValue(mockQueryRunner),
      } as any;

      const mockManager = {
        connection: mockConnection,
        getMany: jest.fn().mockResolvedValue([mockPurchasedJob] as JobEntity[]),
        save: jest.fn().mockResolvedValue(null),
      } as any;

      jest
        .spyOn(deliveryOrderRepository, 'createQueryBuilder')
        .mockReturnValueOnce({
          where: jest.fn().mockReturnThis(),
          getMany: jest.fn().mockResolvedValue(mockInTransitDOPartial),
        } as any);

      jest.spyOn(deliveryOrderRepository, 'save').mockImplementation(() => {
        throw new Error('Mock error');
      });

      try {
        await adminJobsService.updateReceiveJobs(mockRequest, mockUser);
        // If it doesn't throw an error, fail the test
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(Error);
        expect((error as Error).message).toBe('Mock error');
      }
    });

    it('should over max jobs limit (20)', async () => {
      const mockRequest = [] as ReceiveJobsBody[];
      for (let i = 10; i <= 30; i++) {
        mockRequest.push({
          id: `242KD2VSTRILP9R${i}`,
          shippingStatus: '12_RECEIVED_OTHER',
          remark: 'No sim tray',
        });
      }

      try {
        await adminJobsService.updateReceiveJobs(mockRequest, mockUser);
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.INVALID_INPUT_FOR_UPDATE_JOB.code,
        );
        expect((error as BaseExceptionService).message).toBe(
          'Invalid input for update job',
        );
        expect((error as BaseExceptionService).data).toBe(
          'Over max jobs limit (20)',
        );
      }
    });

    it('missing required field (jobid)', async () => {
      const mockRequest: any[] = [];
      mockRequest.push({
        shippingStatus: '12_RECEIVED_OTHER',
        remark: 'No sim tray',
      });

      try {
        await adminJobsService.updateReceiveJobs(mockRequest, mockUser);
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.INVALID_INPUT_FOR_UPDATE_JOB.code,
        );
        expect((error as BaseExceptionService).message).toBe(
          'Invalid input for update job',
        );
        expect((error as BaseExceptionService).data).toBe(
          'missing required field(s)',
        );
      }
    });

    it('missing required field (shippingStatus)', async () => {
      const mockRequest: any[] = [];
      mockRequest.push({
        id: `242KD2VSTRILP9R00`,
        remark: 'No sim tray',
      });

      try {
        await adminJobsService.updateReceiveJobs(mockRequest, mockUser);
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.INVALID_INPUT_FOR_UPDATE_JOB.code,
        );
        expect((error as BaseExceptionService).message).toBe(
          'Invalid input for update job',
        );
        expect((error as BaseExceptionService).data).toBe(
          'missing required field(s)',
        );
      }
    });

    it('missing required field (remark)', async () => {
      const mockRequest: any[] = [];
      mockRequest.push({
        id: `242KD2VSTRILP9R00`,
        shippingStatus: '12_RECEIVED_OTHER',
      });

      try {
        await adminJobsService.updateReceiveJobs(mockRequest, mockUser);
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.INVALID_INPUT_FOR_UPDATE_JOB.code,
        );
        expect((error as BaseExceptionService).message).toBe(
          'Invalid input for update job',
        );
        expect((error as BaseExceptionService).data).toBe(
          'missing required field(s)',
        );
      }
    });

    it('should invalid shipping status', async () => {
      const mockRequest: any[] = [];
      mockRequest.push({
        id: `242KD2VSTRILP9R00`,
        shippingStatus: '15_RECEIVED_OTHER',
        remark: 'test',
      });

      try {
        await adminJobsService.updateReceiveJobs(mockRequest, mockUser);
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.INVALID_INPUT_FOR_UPDATE_JOB.code,
        );
        expect((error as BaseExceptionService).message).toBe(
          'Invalid input for update job',
        );
        expect((error as BaseExceptionService).data).toBe(
          'invalid shipping status - the status must be: 10_RECEIVED, 11_RECEIVED_WITH_CONDITION, 12_RECEIVED_OTHER',
        );
      }
    });

    it('remark should not null', async () => {
      const mockRequest: any[] = [];
      mockRequest.push({
        id: `242KD2VSTRILP9R00`,
        shippingStatus: '12_RECEIVED_OTHER',
        remark: null,
      });

      try {
        await adminJobsService.updateReceiveJobs(mockRequest, mockUser);
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.INVALID_INPUT_FOR_UPDATE_JOB.code,
        );
        expect((error as BaseExceptionService).message).toBe(
          'Invalid input for update job',
        );
        expect((error as BaseExceptionService).data).toBe(
          'remark should not null - shipping status: 11_RECEIVED_WITH_CONDITION, 12_RECEIVED_OTHER',
        );
      }
    });

    it('remark should null', async () => {
      const mockRequest: any[] = [];
      mockRequest.push({
        id: `242KD2VSTRILP9R00`,
        shippingStatus: '10_RECEIVED',
        remark: 'REMARK',
      });

      try {
        await adminJobsService.updateReceiveJobs(mockRequest, mockUser);
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.INVALID_INPUT_FOR_UPDATE_JOB.code,
        );
        expect((error as BaseExceptionService).message).toBe(
          'Invalid input for update job',
        );
        expect((error as BaseExceptionService).data).toBe(
          'remark should null - shipping status: 10_RECEIVED',
        );
      }
    });

    it('remark characters should not over 200', async () => {
      const mockRequest: any[] = [];
      mockRequest.push({
        id: `242KD2VSTRILP9R00`,
        shippingStatus: '12_RECEIVED_OTHER',
        remark:
          'ABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890ABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890ABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890ABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890ABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890ABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890ABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890ABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890ABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890',
      });

      try {
        await adminJobsService.updateReceiveJobs(mockRequest, mockUser);
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.INVALID_INPUT_FOR_UPDATE_JOB.code,
        );
        expect((error as BaseExceptionService).message).toBe(
          'Invalid input for update job',
        );
        expect((error as BaseExceptionService).data).toBe(
          'Remark, over max character limit (200)',
        );
      }
    });

    it('should duplicate jobid', async () => {
      const mockRequest = [] as ReceiveJobsBody[];

      mockRequest.push(
        {
          id: `242KD2VSTRILP9R00`,
          shippingStatus: '12_RECEIVED_OTHER',
          remark: 'No sim tray',
        },
        {
          id: `242KD2VSTRILP9R00`,
          shippingStatus: '12_RECEIVED_OTHER',
          remark: 'No sim tray',
        },
      );

      try {
        await adminJobsService.updateReceiveJobs(mockRequest, mockUser);
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.INVALID_INPUT_FOR_UPDATE_JOB.code,
        );
        expect((error as BaseExceptionService).message).toBe(
          'Invalid input for update job',
        );
        expect((error as BaseExceptionService).data).toBe(
          'Duplicate id not allowed',
        );
      }
    });

    it('should invalid job status', async () => {
      jest.spyOn(jobRepository, 'createQueryBuilder').mockReturnValueOnce({
        where: jest.fn().mockReturnThis(),
        getMany: jest
          .fn()
          .mockResolvedValue([
            mockPurchasedJob,
            mockPurchasedJob,
          ] as JobEntity[]),
      } as any);

      const mockRequest = [] as ReceiveJobsBody[];

      mockRequest.push({
        id: `invalid-job-id`,
        shippingStatus: '12_RECEIVED_OTHER',
        remark: 'No sim tray',
      });

      try {
        await adminJobsService.updateReceiveJobs(mockRequest, mockUser);
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.INVALID_INPUT_FOR_UPDATE_JOB.code,
        );
        expect((error as BaseExceptionService).message).toBe(
          'Invalid input for update job',
        );
        expect((error as BaseExceptionService).data).toBe(
          'Invalid job status - the status must be: 40_PURCHASED',
        );
      }
    });
  });

  describe('Patch QC Status', () => {
    const mockUser = {
      company: 'WW',
      userKey: 'test-user-key',
      name: 'test-name',
    } as WithUserContext;

    it('should success update', async () => {
      jest.spyOn(jobRepository, 'createQueryBuilder').mockReturnValueOnce({
        where: jest.fn().mockReturnThis(),
        getOne: jest.fn().mockResolvedValue(mockReceivedJob as JobEntity),
      } as any);

      const mockRequest = {
        status: 'fix',
      };

      const result = await adminJobsService.updateQCStatus(
        mockReceivedJob.jobId,
        mockRequest,
        mockUser,
      );
      expect(result).toBeNull();
    });

    it('should success update (qc in repairListValue)', async () => {
      jest.spyOn(jobRepository, 'createQueryBuilder').mockReturnValueOnce({
        where: jest.fn().mockReturnThis(),
        getOne: jest.fn().mockResolvedValue(mockReceivedJob as JobEntity),
      } as any);

      const mockRequest = {
        status: 'scrap',
      };

      const result = await adminJobsService.updateQCStatus(
        mockReceivedJob.jobId,
        mockRequest,
        mockUser,
      );
      expect(result).toBeNull();
    });

    it('should missing required field (status)', async () => {
      const mockRequest: any = {
        statusMock: 'fix',
      };

      try {
        await adminJobsService.updateQCStatus(
          mockReceivedJob.jobId,
          mockRequest,
          mockUser,
        );
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.INVALID_INPUT_FOR_UPDATE_JOB.code,
        );
        expect((error as BaseExceptionService).message).toBe(
          'Invalid input for update job',
        );
        expect((error as BaseExceptionService).data).toBe(
          'missing required field: status',
        );
      }
    });

    it('should invalid status', async () => {
      const mockRequest: any = {
        status: 'FIX',
      };

      try {
        await adminJobsService.updateQCStatus(
          mockReceivedJob.jobId,
          mockRequest,
          mockUser,
        );
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.INVALID_INPUT_FOR_UPDATE_JOB.code,
        );
        expect((error as BaseExceptionService).message).toBe(
          'Invalid input for update job',
        );
        expect((error as BaseExceptionService).data).toBe(
          'invalid qc status - the status must be: fix, refurbish, scrap',
        );
      }
    });

    it('should invalid job status', async () => {
      jest.spyOn(jobRepository, 'createQueryBuilder').mockReturnValueOnce({
        where: jest.fn().mockReturnThis(),
        getOne: jest.fn().mockReturnValue(null),
      } as any);

      const mockRequest = {
        status: 'fix',
      };

      try {
        await adminJobsService.updateQCStatus(
          mockQCCompletedJob.jobId,
          mockRequest,
          mockUser,
        );
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.INVALID_INPUT_FOR_UPDATE_JOB.code,
        );
        expect((error as BaseExceptionService).message).toBe(
          'Invalid input for update job',
        );
        expect((error as BaseExceptionService).data).toBe(
          'Invalid job status - the status must be: 50_RECEIVED',
        );
      }
    });
  });

  describe('Patch Assign Repair Job', () => {
    const mockUser = {
      company: 'WW',
      userKey: 'test-user-key',
      name: 'test-name',
    } as WithUserContext;

    const bodyEmpty = {} as AssignRepairBody;
    const bodyFix = { qcStatus: QCStatus.FIX } as AssignRepairBody;
    it.each([QCStatus.FIX, QCStatus.REFURBISH])(
      'should success update',
      async (qcStatus) => {
        jest.spyOn(jobRepository, 'findOne').mockResolvedValueOnce({
          status: JobStatus.QC_COMPLETED,
          qcStatus: qcStatus,
        } as JobEntity);

        const result = await adminJobsService.assignRepairJob(
          mockReceivedJob.jobId,
          mockUser,
          bodyEmpty,
        );
        expect(result).toBeNull();
      },
    );

    it('should success update (inspection fail flow)', async () => {
      jest.spyOn(jobRepository, 'findOne').mockResolvedValueOnce({
        status: JobStatus.INSPECTION_FAILED,
        qcStatus: 'fix',
      } as JobEntity);
      const qcStatusBody = {
        qcStatus: 'fix',
      };
      const result = await adminJobsService.assignRepairJob(
        mockReceivedJob.jobId,
        mockUser,
        qcStatusBody,
      );
      expect(result).toBeNull();
    });

    it('should invalid status', async () => {
      jest.spyOn(jobRepository, 'findOne').mockResolvedValueOnce({
        status: JobStatus.REPAIR_ASSIGNED,
      } as JobEntity);
      try {
        await adminJobsService.assignRepairJob(
          mockReceivedJob.jobId,
          mockUser,
          bodyEmpty,
        );
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.INVALID_INPUT_FOR_UPDATE_JOB.code,
        );
        expect((error as BaseExceptionService).message).toBe(
          BASE_EXCEPTIONS.INVALID_INPUT_FOR_UPDATE_JOB.message,
        );
        expect((error as BaseExceptionService).data).toBe(
          `Invalid job status. Job status must be ${JobStatus.QC_COMPLETED} or ${JobStatus.INSPECTION_FAILED}`,
        );
      }
    });

    it('should invalid QCStatus', async () => {
      jest.spyOn(jobRepository, 'findOne').mockResolvedValueOnce({
        status: JobStatus.QC_COMPLETED,
        qcStatus: QCStatus.SCRAP,
      } as JobEntity);

      try {
        await adminJobsService.assignRepairJob(
          mockReceivedJob.jobId,
          mockUser,
          bodyEmpty,
        );
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.INVALID_INPUT_FOR_UPDATE_JOB.code,
        );
        expect((error as BaseExceptionService).message).toBe(
          BASE_EXCEPTIONS.INVALID_INPUT_FOR_UPDATE_JOB.message,
        );
        expect((error as BaseExceptionService).data).toBe(
          `Invalid qc status. QC status must be ${QCStatus.FIX} or ${QCStatus.REFURBISH}`,
        );
      }
    });

    it('should invalid when status is QCComplete and qcStatus in body', async () => {
      jest.spyOn(jobRepository, 'findOne').mockResolvedValueOnce({
        status: JobStatus.QC_COMPLETED,
        qcStatus: 'fix',
      } as JobEntity);
      const qcStatusBody = {
        qcStatus: 'fix',
      };
      try {
        await adminJobsService.assignRepairJob(
          mockReceivedJob.jobId,
          mockUser,
          qcStatusBody,
        );
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.INVALID_INPUT_FOR_UPDATE_JOB.code,
        );
        expect((error as BaseExceptionService).message).toBe(
          BASE_EXCEPTIONS.INVALID_INPUT_FOR_UPDATE_JOB.message,
        );
        expect((error as BaseExceptionService).data).toBe(
          `QC Completed should not provide qcStatus in body`,
        );
      }
    });

    it('should invalid when status is Inspection Failed and no qcStatus in body', async () => {
      jest.spyOn(jobRepository, 'findOne').mockResolvedValueOnce({
        status: JobStatus.INSPECTION_FAILED,
        qcStatus: 'fix',
      } as JobEntity);
      const qcStatusBody = {};
      try {
        await adminJobsService.assignRepairJob(
          mockReceivedJob.jobId,
          mockUser,
          qcStatusBody,
        );
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.INVALID_INPUT_FOR_UPDATE_JOB.code,
        );
        expect((error as BaseExceptionService).message).toBe(
          BASE_EXCEPTIONS.INVALID_INPUT_FOR_UPDATE_JOB.message,
        );
        expect((error as BaseExceptionService).data).toBe(
          `Inspection Failed should provide qcStatus in body`,
        );
      }
    });

    it('should invalid when qcStatus from body', async () => {
      jest.spyOn(jobRepository, 'findOne').mockResolvedValueOnce({
        status: JobStatus.INSPECTION_FAILED,
        qcStatus: 'fix',
      } as JobEntity);
      const qcStatusBody = {
        qcStatus: 'repair',
      };
      try {
        await adminJobsService.assignRepairJob(
          mockReceivedJob.jobId,
          mockUser,
          qcStatusBody,
        );
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.INVALID_INPUT_FOR_UPDATE_JOB.code,
        );
        expect((error as BaseExceptionService).message).toBe(
          BASE_EXCEPTIONS.INVALID_INPUT_FOR_UPDATE_JOB.message,
        );
        expect((error as BaseExceptionService).data).toBe(
          `Invalid qcStatus Body. QC status must be ${QCStatus.FIX} or ${QCStatus.REFURBISH}`,
        );
      }
    });

    it('should invalid jobEntity', async () => {
      jest.spyOn(jobRepository, 'findOne').mockResolvedValueOnce(null);
      try {
        await adminJobsService.assignRepairJob(
          mockReceivedJob.jobId,
          mockUser,
          bodyEmpty,
        );
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.NOT_FOUND_DATA.code,
        );
        expect((error as BaseExceptionService).message).toBe(
          BASE_EXCEPTIONS.NOT_FOUND_DATA.message,
        );
        expect((error as BaseExceptionService).data).toBe(
          'Transaction ID not found',
        );
      }
    });
  });

  describe('confirmRepairJob', () => {
    // --- body mock
    const bodyDetail = {
      pass: {
        normal: 'test detail always pass',
        border: { min: '1', max: Array(201).join('a') },
      },
      notPass: { less: '', over: Array(202).join('a') },
    };

    const gradeList = ['A', 'B', 'C', 'D'];

    const bodyGrade = {
      pass: {
        normal: 'A',
      },
      notPass: { notDefined: '', notABCD: 'X' },
    };

    const bodyCost = {
      pass: {
        normal: 12,
        border: { min: 0 },
        digit: { none: 1, one: 1.0, two: 1.0 },
      },
      notPass: { less: -1, notNumber: NaN, digit: { three: 1.012 } },
    };

    const mockBodyByTypeDefault: {
      ['pass']: {
        [k: string]: IConfirmRepairJobBody;
      };
    } = {
      pass: {
        confirm: {
          detail: bodyDetail.pass.normal,
          cost: bodyCost.pass.normal,
          grade: bodyGrade.pass.normal,
          type: 'confirm',
        },
        refurbish: {
          detail: bodyDetail.pass.normal,
          cost: bodyCost.pass.normal,
          grade: bodyGrade.pass.normal,
          type: 'refurbish',
        },
        scrap: {
          detail: bodyDetail.pass.normal,
          type: 'scrap',
        },
      },
    };
    // --- service param mock
    const mockDefaultConfirmRepairJobServiceParam = {
      id: mockRepairAssignedJob.jobId,
      user: mockRepairedUser,
      body: mockBodyByTypeDefault.pass.confirm,
    };

    // --- test happy JOB_STATUS_MATCH_ACTION QC_STATUS_MATCH_ACTION + FIELD_DATA_MATCH + USER_MATCH case
    it.each([
      [QCStatus.REFURBISH, 'confirm', true, true],
      [QCStatus.FIX, 'confirm', true, true],
      [QCStatus.FIX, 'refurbish', true, true],
      [QCStatus.FIX, 'scrap', false, false],
    ])(
      `should pass when qcStatus type has/not have cost and grade match: %s to %s`,
      async (qcStatus, type, hasCost, hasGrade) => {
        const body: IConfirmRepairJobBody = {
          type: type as 'confirm' | 'refurbish' | 'scrap',
          detail: bodyDetail.pass.normal,
        };
        if (hasCost) {
          body.cost = bodyCost.pass.normal;
        }
        if (hasGrade) {
          body.grade = bodyGrade.pass.normal;
        }
        jest.spyOn(jobRepository, 'findOne').mockResolvedValueOnce({
          ...mockRepairAssignedJob,
          qcStatus,
        } as JobEntity);
        const result = await adminJobsService.confirmRepairJob({
          ...mockDefaultConfirmRepairJobServiceParam,
          body: mockBodyByTypeDefault.pass[type],
        });
        expect(result).toBeNull();
      },
    );

    it.each([
      [QCStatus.REFURBISH, 'confirm', true, true],
      [QCStatus.FIX, 'confirm', true, true],
      [QCStatus.FIX, 'refurbish', true, true],
    ])(
      `should pass when loop inspection with qcStatus type has/not have cost and grade match: %s to %s`,
      async (qcStatus, type, hasCost, hasGrade) => {
        const body: IConfirmRepairJobBody = {
          type: type as 'confirm' | 'refurbish' | 'scrap',
          detail: bodyDetail.pass.normal,
        };
        if (hasCost) {
          body.cost = bodyCost.pass.normal;
        }
        if (hasGrade) {
          body.grade = bodyGrade.pass.normal;
        }
        jest.spyOn(jobRepository, 'findOne').mockResolvedValueOnce({
          ...mockRepairAssignedJob,
          qcStatus,
          inspectListValue: [{}],
        } as JobEntity);
        const result = await adminJobsService.confirmRepairJob({
          ...mockDefaultConfirmRepairJobServiceParam,
          body: mockBodyByTypeDefault.pass[type],
        });
        expect(result).toBeNull();
      },
    );

    // --- test happy DATA_FORMAT_MATCH case
    it.each([
      bodyDetail.pass.normal,
      bodyDetail.pass.border.max,
      bodyDetail.pass.border.min,
    ])('should detail pass: %s', async (detail) => {
      jest
        .spyOn(jobRepository, 'findOne')
        .mockResolvedValueOnce({ ...mockRepairAssignedJob } as JobEntity);

      const result = await adminJobsService.confirmRepairJob({
        ...mockDefaultConfirmRepairJobServiceParam,
        body: { ...mockBodyByTypeDefault.pass.confirm, detail },
      });
      expect(result).toBeNull();
    });

    it.each([mockRepairAssignedJob.estimatedGrade, 'D'])(
      'should grade pass: %s',
      async (grade) => {
        jest
          .spyOn(jobRepository, 'findOne')
          .mockResolvedValueOnce({ ...mockRepairAssignedJob } as JobEntity);

        const result = await adminJobsService.confirmRepairJob({
          ...mockDefaultConfirmRepairJobServiceParam,
          body: { ...mockBodyByTypeDefault.pass.confirm, grade },
        });
        expect(result).toBeNull();
      },
    );

    it.each([
      bodyCost.pass.normal,
      bodyCost.pass.border.min,
      bodyCost.pass.digit.none,
      bodyCost.pass.digit.one,
      bodyCost.pass.digit.two,
    ])('should valid cost: %s', async (cost) => {
      jest
        .spyOn(jobRepository, 'findOne')
        .mockResolvedValueOnce({ ...mockRepairAssignedJob } as JobEntity);

      const result = await adminJobsService.confirmRepairJob({
        ...mockDefaultConfirmRepairJobServiceParam,
        body: { ...mockBodyByTypeDefault.pass.confirm, cost },
      });
      expect(result).toBeNull();
    });

    // --- test JOB_NOT_FOUND case
    it('should invalid jobEntity', async () => {
      jest.spyOn(jobRepository, 'findOne').mockResolvedValueOnce(null);
      try {
        await adminJobsService.confirmRepairJob(
          mockDefaultConfirmRepairJobServiceParam,
        );
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.NOT_FOUND_DATA.code,
        );
        expect((error as BaseExceptionService).data.type).toBe('JOB_NOT_FOUND');
      }
    });

    // --- test JOB_STATUS_NOT_MATCH_ACTION case
    it.each(['status', 'qcStatus', 'repairedBy'])(
      `should invalid job status when Job has no data in required field: %s`,
      async (noDataField) => {
        const mockResolvedValue = {
          ...mockRepairAssignedJob,
          [noDataField]: null,
        };
        jest
          .spyOn(jobRepository, 'findOne')
          .mockResolvedValueOnce(mockResolvedValue as JobEntity);
        try {
          await adminJobsService.confirmRepairJob(
            mockDefaultConfirmRepairJobServiceParam,
          );
          fail('Expected method to throw an error');
        } catch (error) {
          expect(error).toBeInstanceOf(BaseExceptionService);
          expect((error as BaseExceptionService).code).toBe(
            BASE_EXCEPTIONS.INVALID_INPUT_FOR_UPDATE_JOB.code,
          );
          expect((error as BaseExceptionService).data.type).toBe(
            'JOB_STATUS_NOT_MATCH_ACTION',
          );
        }
      },
    );

    it.each(
      Object.values(JobStatus).filter(
        (item: JobStatus) => item !== JobStatus.REPAIR_ASSIGNED,
      ),
    )(
      `should invalid job status when Job status not ${JobStatus.REPAIR_ASSIGNED}: %s`,
      async (jobStatus) => {
        jest.spyOn(jobRepository, 'findOne').mockResolvedValueOnce({
          ...mockRepairAssignedJob,
          status: jobStatus,
        } as JobEntity);
        try {
          await adminJobsService.confirmRepairJob(
            mockDefaultConfirmRepairJobServiceParam,
          );
          fail('Expected method to throw an error');
        } catch (error) {
          expect(error).toBeInstanceOf(BaseExceptionService);
          expect((error as BaseExceptionService).code).toBe(
            BASE_EXCEPTIONS.INVALID_INPUT_FOR_UPDATE_JOB.code,
          );
          expect((error as BaseExceptionService).data.type).toBe(
            'JOB_STATUS_NOT_MATCH_ACTION',
          );
        }
      },
    );

    // --- test QC_STATUS_NOT_MATCH_ACTION case
    it(`should invalid qc status`, async () => {
      jest.spyOn(jobRepository, 'findOne').mockResolvedValueOnce({
        ...mockRepairAssignedJob,
        qcStatus: QCStatus.SCRAP,
      } as JobEntity);
      try {
        await adminJobsService.confirmRepairJob(
          mockDefaultConfirmRepairJobServiceParam,
        );
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.INVALID_INPUT_FOR_UPDATE_JOB.code,
        );
        expect((error as BaseExceptionService).data.type).toBe(
          'QC_STATUS_NOT_MATCH_ACTION',
        );
      }
    });

    it.each(['refurbish', 'scrap'])(
      `should invalid type for job qc status refurbish: %s`,
      async (type) => {
        jest.spyOn(jobRepository, 'findOne').mockResolvedValueOnce({
          ...mockRepairAssignedJob,
          qcStatus: QCStatus.REFURBISH,
        } as JobEntity);
        try {
          await adminJobsService.confirmRepairJob({
            ...mockDefaultConfirmRepairJobServiceParam,
            body: mockBodyByTypeDefault.pass[type],
          });
          fail('Expected method to throw an error');
        } catch (error) {
          expect(error).toBeInstanceOf(BaseExceptionService);
          expect((error as BaseExceptionService).code).toBe(
            BASE_EXCEPTIONS.INVALID_INPUT_FOR_UPDATE_JOB.code,
          );
          expect((error as BaseExceptionService).data.type).toBe(
            'QC_STATUS_NOT_MATCH_ACTION',
          );
        }
      },
    );

    it(`should invalid type scrap for job qc status fix when loop inspection`, async () => {
      jest.spyOn(jobRepository, 'findOne').mockResolvedValueOnce({
        ...mockRepairAssignedJob,
        qcStatus: QCStatus.FIX,
        inspectListValue: [{}],
      } as JobEntity);
      try {
        await adminJobsService.confirmRepairJob({
          ...mockDefaultConfirmRepairJobServiceParam,
          body: mockBodyByTypeDefault.pass.scrap,
        });
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.INVALID_INPUT_FOR_UPDATE_JOB.code,
        );
        expect((error as BaseExceptionService).data.type).toBe(
          'QC_STATUS_NOT_MATCH_ACTION',
        );
      }
    });

    // --- test FIELD_DATA_NOT_MATCH case
    it.each([
      ['confirm', true, false],
      ['confirm', false, true],
      ['confirm', false, false],
      ['refurbish', true, false],
      ['refurbish', false, true],
      ['refurbish', false, false],
      ['scrap', true, false],
      ['scrap', false, true],
      ['scrap', true, true],
    ])(
      `should invalid data cost and grade vs type: %s cost %s grade %s`,
      async (type, hasCost, hasGrade) => {
        const body: IConfirmRepairJobBody = {
          type: type as 'confirm' | 'refurbish' | 'scrap',
          detail: bodyDetail.pass.normal,
        };
        if (hasCost) {
          body.cost = bodyCost.pass.normal;
        }
        if (hasGrade) {
          body.grade = bodyGrade.pass.normal;
        }
        jest
          .spyOn(jobRepository, 'findOne')
          .mockResolvedValueOnce({ ...mockRepairAssignedJob } as JobEntity);
        try {
          await adminJobsService.confirmRepairJob({
            ...mockDefaultConfirmRepairJobServiceParam,
            body,
          });
          fail('Expected method to throw an error');
        } catch (error) {
          expect(error).toBeInstanceOf(BaseExceptionService);
          expect((error as BaseExceptionService).code).toBe(
            BASE_EXCEPTIONS.INVALID_INPUT_FOR_UPDATE_JOB.code,
          );
          expect((error as BaseExceptionService).data.type).toBe(
            'FIELD_DATA_NOT_MATCH',
          );
        }
      },
    );

    // --- test DATA_FORMAT_FAIL case
    it.each([bodyDetail.notPass.less, bodyDetail.notPass.over])(
      'should invalid detail: %s',
      async (detail) => {
        jest
          .spyOn(jobRepository, 'findOne')
          .mockResolvedValueOnce({ ...mockRepairAssignedJob } as JobEntity);
        try {
          await adminJobsService.confirmRepairJob({
            ...mockDefaultConfirmRepairJobServiceParam,
            body: { ...mockBodyByTypeDefault.pass.confirm, detail },
          });
          fail('Expected method to throw an error');
        } catch (error) {
          expect(error).toBeInstanceOf(BaseExceptionService);
          expect((error as BaseExceptionService).code).toBe(
            BASE_EXCEPTIONS.INVALID_INPUT_FOR_UPDATE_JOB.code,
          );
          expect((error as BaseExceptionService).data.type).toBe(
            'DATA_FORMAT_FAIL',
          );
        }
      },
    );

    it.each([
      bodyGrade.notPass.notABCD,
      ...gradeList.filter(
        (grade) =>
          !(grade === mockRepairAssignedJob.estimatedGrade || grade === 'D'),
      ),
    ])(
      //
      'should invalid grade: %s',
      async (grade) => {
        jest
          .spyOn(jobRepository, 'findOne')
          .mockResolvedValueOnce({ ...mockRepairAssignedJob } as JobEntity);
        try {
          await adminJobsService.confirmRepairJob({
            ...mockDefaultConfirmRepairJobServiceParam,
            body: { ...mockBodyByTypeDefault.pass.confirm, grade },
          });
          fail('Expected method to throw an error');
        } catch (error) {
          expect(error).toBeInstanceOf(BaseExceptionService);
          expect((error as BaseExceptionService).code).toBe(
            BASE_EXCEPTIONS.INVALID_INPUT_FOR_UPDATE_JOB.code,
          );
          expect((error as BaseExceptionService).data.type).toBe(
            'DATA_FORMAT_FAIL',
          );
        }
      },
    );

    it.each([
      bodyCost.notPass.less,
      bodyCost.notPass.notNumber,
      bodyCost.notPass.digit.three,
    ])('should invalid cost: %s', async (cost) => {
      jest
        .spyOn(jobRepository, 'findOne')
        .mockResolvedValueOnce({ ...mockRepairAssignedJob } as JobEntity);
      try {
        await adminJobsService.confirmRepairJob({
          ...mockDefaultConfirmRepairJobServiceParam,
          body: { ...mockBodyByTypeDefault.pass.confirm, cost },
        });
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.INVALID_INPUT_FOR_UPDATE_JOB.code,
        );
        expect((error as BaseExceptionService).data.type).toBe(
          'DATA_FORMAT_FAIL',
        );
      }
    });

    // --- test USER_NOT_MATCH case
    it('should invalid user', async () => {
      jest
        .spyOn(jobRepository, 'findOne')
        .mockResolvedValueOnce({ ...mockRepairAssignedJob } as JobEntity);
      try {
        await adminJobsService.confirmRepairJob({
          ...mockDefaultConfirmRepairJobServiceParam,
          user: {
            company: 'WW',
            userKey: 'test-not-repaired-user-key',
            name: 'test-not-repaired-user-name',
          } as WithUserContext,
        });
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.INVALID_JOB_PERMISSION.code,
        );
        expect((error as BaseExceptionService).data.type).toBe(
          'USER_NOT_MATCH',
        );
      }
    });
  });

  describe('getMinInspectDate', () => {
    beforeAll(() => {
      jest.useFakeTimers();
      jest.setSystemTime(new Date(2024, 3, 17, 0, 0, 0));
    });
    afterAll(() => {
      jest.useRealTimers();
    });

    const todayWed: [number, number, number] = [2024, 4, 17];
    const yesterdayWed: [number, number, number] = [2024, 4, 16];

    it.each([
      ['yesterday', yesterdayWed, true],
      ['today', todayWed, false],
      ['notFound', undefined, false],
    ])(
      `case today is wed [ ${todayWed} ] : %s date time %s should pass = %s`,
      async (caseDetail, dateTime, isPass) => {
        let jobFound: JobEntity[] = [];
        let expectResult: Date | null = null;

        if (caseDetail !== 'notFound' && dateTime !== undefined) {
          jobFound = [
            {
              ...mockRepairAssignedJob,
              repairedAt: getDateUTCFromYMDArray(dateTime),
            } as JobEntity,
          ];
        }
        if (caseDetail === 'yesterday') {
          expectResult = getDateUTCFromYMDArray(yesterdayWed);
        }

        jest.spyOn(jobRepository, 'find').mockResolvedValueOnce(jobFound);

        const result = await adminJobsService.getMinInspectDate();

        expect(result).toEqual(expectResult);
      },
    );
  });

  describe('Patch Assign Inspect Job', () => {
    const mockUser = {
      company: 'WW',
      userKey: 'test-user-key',
      name: 'test-name',
    } as WithUserContext;

    it('should success update (70_REPAIR_COMPLETED)', async () => {
      const mockRepairJob = {
        ...mockJob,
        status: JobStatus.REPAIR_COMPLETED,
      };
      jest
        .spyOn(jobRepository, 'findOne')
        .mockResolvedValueOnce(mockRepairJob as JobEntity);

      const result = await adminJobsService.assignInspectJob(
        mockReceivedJob.jobId,
        mockUser,
      );
      expect(result).toBeNull();
    });

    it('should success update (65_REPAIR_ASSIGNED)', async () => {
      const mockRepairJob = {
        ...mockJob,
        status: JobStatus.REPAIR_ASSIGNED,
        qcStatus: QCStatus.SCRAP,
      };
      jest
        .spyOn(jobRepository, 'findOne')
        .mockResolvedValueOnce(mockRepairJob as JobEntity);

      const result = await adminJobsService.assignInspectJob(
        mockReceivedJob.jobId,
        mockUser,
      );
      expect(result).toBeNull();
    });

    it('should success update (60_QC_COMPLETED)', async () => {
      const mockRepairJob = {
        ...mockJob,
        status: JobStatus.REPAIR_ASSIGNED,
        qcStatus: QCStatus.SCRAP,
      };
      jest
        .spyOn(jobRepository, 'findOne')
        .mockResolvedValueOnce(mockRepairJob as JobEntity);

      const result = await adminJobsService.assignInspectJob(
        mockReceivedJob.jobId,
        mockUser,
      );
      expect(result).toBeNull();
    });

    it('should invalid status (job status)', async () => {
      jest.spyOn(jobRepository, 'findOne').mockResolvedValueOnce({
        status: JobStatus.QC_COMPLETED,
      } as JobEntity);
      try {
        await adminJobsService.assignInspectJob(
          mockReceivedJob.jobId,
          mockUser,
        );
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.INVALID_INPUT_FOR_UPDATE_JOB.code,
        );
        expect((error as BaseExceptionService).message).toBe(
          BASE_EXCEPTIONS.INVALID_INPUT_FOR_UPDATE_JOB.message,
        );
        expect((error as BaseExceptionService).data).toBe(
          `Invalid job status.`,
        );
      }
    });

    it('should invalid status (QC_COMPLETED without SCRAP)', async () => {
      jest.spyOn(jobRepository, 'findOne').mockResolvedValueOnce({
        status: JobStatus.QC_COMPLETED,
        qcStatus: QCStatus.FIX,
      } as JobEntity);
      try {
        await adminJobsService.assignInspectJob(
          mockReceivedJob.jobId,
          mockUser,
        );
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.INVALID_INPUT_FOR_UPDATE_JOB.code,
        );
        expect((error as BaseExceptionService).message).toBe(
          BASE_EXCEPTIONS.INVALID_INPUT_FOR_UPDATE_JOB.message,
        );
        expect((error as BaseExceptionService).data).toBe(
          `Invalid job status.`,
        );
      }
    });

    it('should invalid status (65_REPAIR_ASSIGNED without SCRAP)', async () => {
      jest.spyOn(jobRepository, 'findOne').mockResolvedValueOnce({
        status: JobStatus.REPAIR_ASSIGNED,
        qcStatus: QCStatus.FIX,
      } as JobEntity);
      try {
        await adminJobsService.assignInspectJob(
          mockReceivedJob.jobId,
          mockUser,
        );
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.INVALID_INPUT_FOR_UPDATE_JOB.code,
        );
        expect((error as BaseExceptionService).message).toBe(
          BASE_EXCEPTIONS.INVALID_INPUT_FOR_UPDATE_JOB.message,
        );
        expect((error as BaseExceptionService).data).toBe(
          `Invalid job status.`,
        );
      }
    });

    it('should invalid jobEntity', async () => {
      jest.spyOn(jobRepository, 'findOne').mockResolvedValueOnce(null);
      try {
        await adminJobsService.assignInspectJob(
          mockReceivedJob.jobId,
          mockUser,
        );
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.NOT_FOUND_DATA.code,
        );
        expect((error as BaseExceptionService).message).toBe(
          BASE_EXCEPTIONS.NOT_FOUND_DATA.message,
        );
        expect((error as BaseExceptionService).data).toBe(
          'Transaction ID not found',
        );
      }
    });
  });

  describe('Patch Inspect Job', () => {
    const mockUser = {
      company: 'WW',
      userKey: 'test',
      name: 'test',
    } as WithUserContext;

    it('should success update (pass case)', async () => {
      jest.spyOn(jobRepository, 'findOne').mockResolvedValueOnce({
        status: JobStatus.INSPECTION_ASSIGNED,
        inspectedBy: mockUserEntity.userKey,
        repairedAt: new Date(),
        inspectListValue: [
          {
            by: { key: mockUserEntity.userKey, name: mockUserEntity.name },
            at: new Date(),
            isPassed: false,
            isQCScrap: false,
            detail: 'fail',
          },
        ] as InspectHx[],
      } as JobEntity);

      const inspectBody = {
        inspectionResult: 'pass',
      } as InspectJobBody;

      const mockCountJob = [
        {
          grade_a_all: 10,
          grade_a_completed: 0,
        },
      ];
      jest.spyOn(jobRepository, 'query').mockResolvedValueOnce(mockCountJob);

      const result = await adminJobsService.updateInspectedJob(
        mockReceivedJob.jobId,
        mockUser,
        inspectBody,
      );
      expect(result).toBeNull();
    });

    it('should success update (pass case with auto completed)', async () => {
      jest.spyOn(jobRepository, 'findOne').mockResolvedValueOnce({
        status: JobStatus.INSPECTION_ASSIGNED,
        inspectedBy: mockUserEntity.userKey,
        repairedAt: new Date(),
        inspectListValue: [
          {
            by: { key: mockUserEntity.userKey, name: mockUserEntity.name },
            at: new Date(),
            isPassed: false,
            isQCScrap: false,
            detail: 'fail',
          },
        ] as InspectHx[],
      } as JobEntity);

      const inspectBody = {
        inspectionResult: 'pass',
      } as InspectJobBody;

      const mockCountJob = [
        {
          grade_a_all: 10,
          grade_a_completed: 1,
        },
      ];
      jest.spyOn(jobRepository, 'query').mockResolvedValueOnce(mockCountJob);

      jest.spyOn(jobRepository, 'createQueryBuilder').mockReturnValueOnce({
        where: jest.fn().mockReturnThis(),
        innerJoinAndSelect: jest.fn().mockReturnThis(),
        getMany: jest
          .fn()
          .mockResolvedValue([
            mockPurchasedJob,
            mockPurchasedJob,
          ] as JobEntity[]),
      } as any);

      const result = await adminJobsService.updateInspectedJob(
        mockReceivedJob.jobId,
        mockUser,
        inspectBody,
      );
      expect(result).toBeNull();
    });

    it('should success update (fail case)', async () => {
      jest.spyOn(jobRepository, 'findOne').mockResolvedValueOnce({
        status: JobStatus.INSPECTION_ASSIGNED,
        inspectedBy: mockUserEntity.userKey,
        repairedAt: new Date(),
      } as JobEntity);

      const inspectBody = {
        inspectionResult: 'fail',
        inspectionDetail: 'back cover not fully close',
      } as InspectJobBody;

      const mockCountJob = [
        {
          grade_a_all: 10,
          grade_a_completed: 0,
        },
      ];
      jest.spyOn(jobRepository, 'query').mockResolvedValueOnce(mockCountJob);

      const result = await adminJobsService.updateInspectedJob(
        mockReceivedJob.jobId,
        mockUser,
        inspectBody,
      );
      expect(result).toBeNull();
    });

    it('should not found job', async () => {
      jest.spyOn(jobRepository, 'findOne').mockResolvedValueOnce(null);

      const inspectBody = {
        inspectionResult: 'pass',
        inspectionDetail: 'Good Quality',
      } as InspectJobBody;

      try {
        await adminJobsService.updateInspectedJob(
          mockReceivedJob.jobId,
          mockUser,
          inspectBody,
        );

        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.NOT_FOUND_DATA.code,
        );
        expect((error as BaseExceptionService).message).toBe(
          BASE_EXCEPTIONS.NOT_FOUND_DATA.message,
        );
        expect((error as BaseExceptionService).data).toBe(
          'Transaction ID not found',
        );
      }
    });

    it('should invalid job status', async () => {
      jest
        .spyOn(jobRepository, 'findOne')
        .mockResolvedValueOnce(mockReceivedJob as JobEntity);

      const inspectBody = {
        inspectionResult: 'pass',
        inspectionDetail: 'Good Quality',
      } as InspectJobBody;

      try {
        await adminJobsService.updateInspectedJob(
          mockReceivedJob.jobId,
          mockUser,
          inspectBody,
        );

        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.INVALID_INPUT_FOR_UPDATE_JOB.code,
        );
        expect((error as BaseExceptionService).message).toBe(
          BASE_EXCEPTIONS.INVALID_INPUT_FOR_UPDATE_JOB.message,
        );
        expect((error as BaseExceptionService).data).toBe(
          `Invalid job status. Job status must be ${JobStatus.INSPECTION_ASSIGNED}.`,
        );
      }
    });

    it('should unauthorized to update job', async () => {
      jest.spyOn(jobRepository, 'findOne').mockResolvedValueOnce({
        status: JobStatus.INSPECTION_ASSIGNED,
      } as JobEntity);

      const inspectBody = {
        inspectionResult: 'pass',
        inspectionDetail: 'Good Quality',
      } as InspectJobBody;

      try {
        await adminJobsService.updateInspectedJob(
          mockReceivedJob.jobId,
          mockUser,
          inspectBody,
        );

        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.UNAUTHORIZED.code,
        );
        expect((error as BaseExceptionService).message).toBe(
          BASE_EXCEPTIONS.UNAUTHORIZED.message,
        );
        expect((error as BaseExceptionService).data).toBe(
          `user (${mockUser.name}) does not have permission to update this job.`,
        );
      }
    });

    it('should invalid data (no repairedAt)', async () => {
      jest.spyOn(jobRepository, 'findOne').mockResolvedValueOnce({
        status: JobStatus.INSPECTION_ASSIGNED,
        inspectedBy: mockUserEntity.userKey,
      } as JobEntity);

      const inspectBody = {
        inspectionResult: 'pass',
        inspectionDetail: 'Good Quality',
      } as InspectJobBody;

      try {
        await adminJobsService.updateInspectedJob(
          mockReceivedJob.jobId,
          mockUser,
          inspectBody,
        );

        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.INVALID_INPUT_FOR_UPDATE_JOB.code,
        );
        expect((error as BaseExceptionService).message).toBe(
          BASE_EXCEPTIONS.INVALID_INPUT_FOR_UPDATE_JOB.message,
        );
        expect((error as BaseExceptionService).data).toBe(`invalid data`);
      }
    });

    it('should invalid if result is pass, but come with detail', async () => {
      jest.spyOn(jobRepository, 'findOne').mockResolvedValueOnce({
        status: JobStatus.INSPECTION_ASSIGNED,
        inspectedBy: mockUserEntity.userKey,
        repairedAt: new Date(),
      } as JobEntity);

      const inspectBody = {
        inspectionResult: 'pass',
        inspectionDetail: 'Good Quality',
      } as InspectJobBody;

      try {
        await adminJobsService.updateInspectedJob(
          mockReceivedJob.jobId,
          mockUser,
          inspectBody,
        );

        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.INVALID_INPUT_FOR_UPDATE_JOB.code,
        );
        expect((error as BaseExceptionService).message).toBe(
          BASE_EXCEPTIONS.INVALID_INPUT_FOR_UPDATE_JOB.message,
        );
        expect((error as BaseExceptionService).data).toBe(
          `Invalid body. If pass should not have detail`,
        );
      }
    });

    it('should invalid if result is fail, but no detail provided', async () => {
      jest.spyOn(jobRepository, 'findOne').mockResolvedValueOnce({
        status: JobStatus.INSPECTION_ASSIGNED,
        inspectedBy: mockUserEntity.userKey,
        repairedAt: new Date(),
      } as JobEntity);

      const inspectBody = {
        inspectionResult: 'fail',
      } as InspectJobBody;

      try {
        await adminJobsService.updateInspectedJob(
          mockReceivedJob.jobId,
          mockUser,
          inspectBody,
        );

        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.INVALID_INPUT_FOR_UPDATE_JOB.code,
        );
        expect((error as BaseExceptionService).message).toBe(
          BASE_EXCEPTIONS.INVALID_INPUT_FOR_UPDATE_JOB.message,
        );
        expect((error as BaseExceptionService).data).toBe(
          `Detail is required when result is fail.`,
        );
      }
    });

    it('should invalid if result is fail and inspect list value has value more than 2', async () => {
      jest.spyOn(jobRepository, 'findOne').mockResolvedValueOnce({
        status: JobStatus.INSPECTION_ASSIGNED,
        inspectedBy: mockUserEntity.userKey,
        repairedAt: new Date(),
        inspectListValue: [
          {
            by: { key: mockUserEntity.userKey, name: mockUserEntity.name },
            at: new Date(),
            isPassed: false,
            isQCScrap: false,
            detail: 'fail',
          },
          {
            by: { key: mockUserEntity.userKey, name: mockUserEntity.name },
            at: new Date(),
            isPassed: false,
            isQCScrap: false,
            detail: 'fail',
          },
        ] as InspectHx[],
      } as JobEntity);

      const inspectBody = {
        inspectionResult: 'fail',
        inspectionDetail: 'not pass',
      } as InspectJobBody;

      try {
        await adminJobsService.updateInspectedJob(
          mockReceivedJob.jobId,
          mockUser,
          inspectBody,
        );

        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.INVALID_INPUT_FOR_UPDATE_JOB.code,
        );
        expect((error as BaseExceptionService).message).toBe(
          BASE_EXCEPTIONS.INVALID_INPUT_FOR_UPDATE_JOB.message,
        );
        expect((error as BaseExceptionService).data).toBe(
          `Cannot mark as fail more than 2 times`,
        );
      }
    });

    describe('operation cost config', () => {
      let validOperationConfig;
      let validJob;
      let inspectBody;
      let mockCountJob;

      beforeEach(() => {
        validOperationConfig = {
          data: {
            operationCost: {
              logistic: '7.00',
              warehouseRental: '1.00',
              productPackaging: '15.00',
            },
            marketing: '2.00',
          },
        } as SystemConfigEntity;

        validJob = {
          status: JobStatus.INSPECTION_ASSIGNED,
          inspectedBy: mockUserEntity.userKey,
          repairedAt: new Date(),
          inspectListValue: [
            {
              by: { key: mockUserEntity.userKey, name: mockUserEntity.name },
              at: new Date(),
              isPassed: false,
              isQCScrap: false,
              detail: 'fail',
            },
          ] as InspectHx[],
          repairListValue: [{ cost: 100 }, { cost: 200 }, {}],
          purchasedPrice: 15000,
          modelMaster: {
            insuranceCost: 150,
            averageRetailCost: { AA: '18000.00', AD: '9000.00', DD: '8000.00' },
            averageWholeSaleCost: {
              AA: '16000.00',
              AD: '7000.00',
              DD: '6000.00',
            },
          },
          qcStatus: QCStatus.FIX,
          estimatedGrade: 'A',
          currentGrade: 'A',
        } as JobEntity;

        inspectBody = {
          inspectionResult: 'pass',
        } as InspectJobBody;

        mockCountJob = [
          {
            grade_a_all: 10,
            grade_a_completed: 1,
          },
        ];
      });

      it('valid operation cost config', async () => {
        jest.spyOn(jobRepository, 'findOne').mockResolvedValueOnce(validJob);
        jest.spyOn(jobRepository, 'query').mockResolvedValueOnce(mockCountJob);
        jest
          .spyOn(systemConfigRepository, 'findOne')
          .mockResolvedValueOnce(validOperationConfig);

        jest.spyOn(jobRepository, 'createQueryBuilder').mockReturnValueOnce({
          where: jest.fn().mockReturnThis(),
          innerJoinAndSelect: jest.fn().mockReturnThis(),
          getMany: jest.fn().mockResolvedValue([validJob] as JobEntity[]),
        } as any);

        const result = await adminJobsService.updateInspectedJob(
          mockReceivedJob.jobId,
          mockUser,
          inspectBody,
        );
        expect(result).toBeNull();
        expect(systemConfigRepository.findOne).toHaveBeenCalledWith({
          where: { configKey: 'operation_cost' },
        });
        expect(jobRepository.save).toHaveBeenCalledWith(
          expect.objectContaining({
            wholeSaleMargin: expect.any(Number),
            wholeSalePrice: Number(
              validJob.modelMaster.averageWholeSaleCost[
                `${validJob.estimatedGrade}${validJob.currentGrade}`
              ],
            ),
            retailMargin: expect.any(Number),
            retailPrice: Number(
              validJob.modelMaster.averageRetailCost[
                `${validJob.estimatedGrade}${validJob.currentGrade}`
              ],
            ),
            costPrice: expect.any(Number),
          }),
        );
        expect(jobRepository.save).toHaveBeenLastCalledWith(
          expect.arrayContaining([
            expect.objectContaining({
              wholeSaleMargin: expect.any(Number),
              wholeSalePrice: Number(
                validJob.modelMaster.averageWholeSaleCost[
                  `${validJob.estimatedGrade}${validJob.currentGrade}`
                ],
              ),
              retailMargin: expect.any(Number),
              retailPrice: Number(
                validJob.modelMaster.averageRetailCost[
                  `${validJob.estimatedGrade}${validJob.currentGrade}`
                ],
              ),
              costPrice: expect.any(Number),
            }),
          ]),
        );
      });
      it.each([undefined, null])(
        'invalid operation cost config',
        async (operationConfig) => {
          jest.spyOn(jobRepository, 'findOne').mockResolvedValueOnce(validJob);
          jest
            .spyOn(jobRepository, 'query')
            .mockResolvedValueOnce(mockCountJob);
          jest.spyOn(systemConfigRepository, 'findOne').mockResolvedValueOnce({
            data: operationConfig,
          } as SystemConfigEntity);
          jest.spyOn(jobRepository, 'createQueryBuilder').mockReturnValueOnce({
            where: jest.fn().mockReturnThis(),
            innerJoinAndSelect: jest.fn().mockReturnThis(),
            getMany: jest.fn().mockResolvedValue([validJob] as JobEntity[]),
          } as any);

          const result = await adminJobsService.updateInspectedJob(
            mockReceivedJob.jobId,
            mockUser,
            inspectBody,
          );
          expect(result).toBeNull();
          expect(systemConfigRepository.findOne).toHaveBeenCalledWith({
            where: { configKey: 'operation_cost' },
          });
          expect(jobRepository.save).toHaveBeenCalledWith(
            expect.not.objectContaining({
              wholeSaleMargin: expect.anything(),
              wholeSalePrice: expect.anything(),
              retailMargin: expect.anything(),
              retailPrice: expect.anything(),
              costPrice: expect.anything(),
            }),
          );
          expect(jobRepository.save).toHaveBeenLastCalledWith(
            expect.arrayContaining([
              expect.not.objectContaining({
                wholeSaleMargin: expect.anything(),
                wholeSalePrice: expect.anything(),
                retailMargin: expect.anything(),
                retailPrice: expect.anything(),
                costPrice: expect.anything(),
              }),
            ]),
          );
        },
      );
    });
  });

  describe('get Menu Count', () => {
    const mockUser = {
      company: 'WW',
      userKey: 'test-user-key',
      name: 'test-name',
      roles: [
        {
          role: [
            'SuperAdmin',
            'Manager',
            'Sale',
            'Admin',
            'AdminPriceEstimator',
            'AdminReceive',
            'AdminInspection',
            'AdminQC',
            'AdminRepair',
            'AdminSupplyChain',
          ],
          branchId: '10042866',
        },
      ],
      permissions: [
        {
          branchId: 'ADMIN_BRANCH',
          permission: [
            'PS-0014_VIEW',
            'PS-0013_VIEW',
            'PS-0010_VIEW',
            'PS-0011_VIEW',
          ],
        },
      ],
    } as WithUserContext;

    const mockAdminInspectionUser = {
      company: 'WW',
      userKey: 'test-user-key',
      name: 'test-name',
      roles: [
        {
          role: ['AdminInspection'],
          branchId: '10042866',
        },
      ],
      permissions: [
        {
          branchId: 'ADMIN_BRANCH',
          permission: [
            'PS-0014_VIEW',
            'PS-0013_VIEW',
            'PS-0010_VIEW',
            'PS-0011_VIEW',
          ],
        },
      ],
    } as WithUserContext;

    const today: [number, number, number] = [2024, 4, 16];
    jest.mocked(getDateFromToday).mockImplementation((add) => {
      if (!add) {
        return [...today, '2024-04-16'];
      } else {
        return [...today, '2024-04-15'];
      }
    });

    it.each([
      [21, 0, 2, 2, 10],
      [21, 1, 2, 2, 10],
      [21, 2, 2, 2, 10],
      [21, 3, 2, 2, 10],
    ])(
      `inpsection noti calculation`,
      async (
        gradeAAll,
        gradeACompleted,
        gradeOthers,
        gradeScrap,
        myInspection,
      ) => {
        jest.mocked(getDateFromToday).mockImplementation((add) => {
          if (!add) {
            return [...today, '2024-04-16'];
          } else {
            return [...today, '2024-04-15'];
          }
        });

        const resultInspection = [
          {
            grade_a_all: gradeAAll,
            grade_a_completed: gradeACompleted,
            grade_others: gradeOthers,
            grade_scrap: gradeScrap,
          },
        ];

        const resultData = [
          {
            my_inspection: myInspection,
          },
        ];

        const gradeACal = Math.ceil(gradeAAll * 0.05) - gradeACompleted;
        const gradeA = gradeACal >= 0 ? gradeACal : 0;
        const totalInspectionJob = gradeA + gradeOthers + gradeScrap;

        jest
          .spyOn(jobRepository, 'query')
          .mockResolvedValueOnce(resultInspection);

        jest.spyOn(jobRepository, 'query').mockResolvedValueOnce(resultData);
        const result = await adminJobsService.getMenuCount(
          mockAdminInspectionUser,
        );

        expect(result['myInspection']).toEqual(myInspection);
        expect(result['totalInspectionJob']).toEqual(totalInspectionJob);
      },
    );

    it('should get data all', async () => {
      const gradeAAll = 21;
      const gradeACompleted = 1;
      const gradeA = Math.ceil(gradeAAll * 0.05) - gradeACompleted;
      const gradeOthers = 2;
      const gradeScrap = 2;
      const totalInspectionJob = gradeA + gradeOthers + gradeScrap;
      const myInspection = 10;
      const totalRepairJob = 3;
      const myRepair = 6;
      const resultInspection = [
        {
          grade_a_all: gradeAAll,
          grade_a_completed: gradeACompleted,
          grade_others: gradeOthers,
          grade_scrap: gradeScrap,
        },
      ];

      const resultData = [
        {
          my_inspection: myInspection,
          repair: totalRepairJob,
          my_repair: myRepair,
        },
      ];

      jest
        .spyOn(jobRepository, 'query')
        .mockResolvedValueOnce(resultInspection);

      jest.spyOn(jobRepository, 'query').mockResolvedValueOnce(resultData);

      const result = await adminJobsService.getMenuCount(mockUser);

      expect(result['myInspection']).toEqual(myInspection);
      expect(result['totalRepairJob']).toEqual(totalRepairJob);
      expect(result['myRepair']).toEqual(myRepair);
      expect(result['totalInspectionJob']).toEqual(totalInspectionJob);
    });
  });

  describe('get Count By Type', () => {
    const mockUser = {
      company: 'WW',
      userKey: 'test-user-key',
      name: 'test-name',
      roles: [
        {
          role: [
            'SuperAdmin',
            'Manager',
            'Sale',
            'Admin',
            'AdminPriceEstimator',
            'AdminReceive',
            'AdminInspection',
            'AdminQC',
            'AdminRepair',
            'AdminSupplyChain',
          ],
          branchId: '10042866',
        },
      ],
    } as WithUserContext;

    it.each([
      [21, 0, 2, 2],
      [21, 1, 2, 2],
      [21, 2, 2, 2],
      [21, 3, 2, 2],
    ])(
      `get inspection count (all pending item)`,
      async (gradeAAll, gradeACompleted, gradeOthers, gradeScrap) => {
        const mockRequest = {
          query: { inspectionDate: '2024-04-15' },
        } as unknown as Request;

        const gradeACal = Math.ceil(gradeAAll * 0.05) - gradeACompleted;
        const gradeA = gradeACal >= 0 ? gradeACal : 0;
        const totalInspectionJob = gradeA + gradeOthers + gradeScrap;

        jest.spyOn(jobRepository, 'query').mockResolvedValueOnce([
          {
            grade_a_all: gradeAAll,
            grade_a_completed: gradeACompleted,
            grade_others: gradeOthers,
            grade_scrap: gradeScrap,
          },
        ]);

        const result = await adminJobsService.getCountByType(
          mockRequest,
          mockUser,
          'inspection',
        );

        if (result) {
          expect(result['total']).toEqual(totalInspectionJob);
          expect(result['gradeATotal']).toEqual(gradeAAll);
          expect(result['gradeACompleted']).toEqual(gradeACompleted);
          expect(result['gradeA']).toEqual(gradeA);
          expect(result['gradeOthers']).toEqual(gradeOthers);
          expect(result['gradeScrap']).toEqual(gradeScrap);
        }
      },
    );

    it('should get myInspection count', async () => {
      const mockRequest = {
        query: { isMyInspectionJob: 'true' },
      } as unknown as Request;

      jest.spyOn(jobRepository, 'query').mockResolvedValueOnce([
        {
          grade_a: 5,
          grade_others: 2,
          grade_scrap: 3,
        },
      ]);

      const result = await adminJobsService.getCountByType(
        mockRequest,
        mockUser,
        'inspection',
      );

      if (result) {
        expect(result['total']).toEqual(10);
        expect(result['gradeATotal']).toEqual(0);
        expect(result['gradeACompleted']).toEqual(0);
        expect(result['gradeA']).toEqual(5);
        expect(result['gradeOthers']).toEqual(2);
        expect(result['gradeScrap']).toEqual(3);
      }
    });

    it('should get repair count (all pending item)', async () => {
      const mockRequest = {
        query: {},
      } as unknown as Request;

      jest.spyOn(jobRepository, 'query').mockResolvedValueOnce([
        {
          repair_total: 6,
          fix: 2,
          refurbish: 3,
          inspection_failed: 1,
        },
      ]);

      const result = await adminJobsService.getCountByType(
        mockRequest,
        mockUser,
        'repair',
      );

      if (result) {
        expect(result['total']).toEqual(6);
        expect(result['fix']).toEqual(2);
        expect(result['refurbish']).toEqual(3);
        expect(result['inspectionFailed']).toEqual(1);
      }
    });

    it('should get myRepair count', async () => {
      const mockRequest = {
        query: { isMyRepairJob: 'true' },
      } as unknown as Request;

      jest.spyOn(jobRepository, 'query').mockResolvedValueOnce([
        {
          my_repair_total: 28,
          my_repair_fix: 10,
          my_repair_refurbish_from_qc: 15,
          my_repair_refurbish_from_fix: 3,
        },
      ]);

      const result = await adminJobsService.getCountByType(
        mockRequest,
        mockUser,
        'repair',
      );

      if (result) {
        expect(result['myRepairTotal']).toEqual(28);
        expect(result['myRepairFix']).toEqual(10);
        expect(result['myRepairRefurbishFromQC']).toEqual(15);
        expect(result['myRepairRefurbishFromFix']).toEqual(3);
      }
    });

    it('should get nothing', async () => {
      const mockRequest = {
        query: {},
      } as unknown as Request;

      const result = await adminJobsService.getCountByType(
        mockRequest,
        mockUser,
        'mockType',
      );

      expect(result).toBeNull();
    });
  });

  describe('getMonthlyInspect', () => {
    const today: [number, number, number] = [2024, 4, 16];

    it.each([
      ['thisMonth', 2024, 4, '2024-04-01', '2024-04-15'],
      ['prevMonth', 2024, 3, '2024-03-01', '2024-03-31'],
    ])(
      `happy case today ${today} %s: body %s %s start %s end %s`,
      async (caseDetail, year, month, startDate, endDate) => {
        jest.mocked(getDateFromToday).mockImplementationOnce((add) => {
          if (!add) {
            return [...today, '2024-04-16'];
          } else {
            return [...today, '2024-04-15'];
          }
        });
        const { mockResult, mockDataByDate } = getGenerateMockMonthly(
          startDate,
          endDate,
        );

        jest
          .spyOn(jobRepository, 'query')
          .mockResolvedValueOnce(mockDataByDate);

        const result = await adminJobsService.getMonthlyInspect({
          month,
          year,
        });

        expect(result.startDate).toEqual(startDate);
        expect(result.endDate).toEqual(endDate);
        expect(result.monthlyInspectData).toEqual(mockResult);
      },
    );

    it.each([
      ['MONTH_ERROR', 'NaN month', 2024, 'string'],
      ['MONTH_ERROR', 'month less than 1', 2024, 0],
      ['MONTH_ERROR', 'month more than 12', 2024, 13],
      ['YEAR_ERROR', 'Nan year', 'string', 1],
      ['YEAR_ERROR', 'year less than 1000', 999, 1],
      ['YEAR_ERROR', 'year more than 9999', 10000, 1],
      ['TIME_ERROR', 'current year less than year', 2025, 1],
      ['TIME_ERROR', 'current month less than month', 2024, 5],
    ])(
      `fail case today ${today} - %s - %s: body %s %s`,
      async (type, caseDetail, year, month) => {
        jest.mocked(getDateFromToday).mockImplementation((add) => {
          if (!add) {
            return [...today, '2024-04-16'];
          } else {
            return [...today, '2024-04-15'];
          }
        });
        try {
          await adminJobsService.getMonthlyInspect({
            month: month as number,
            year: year as number,
          });
          fail('Expected method to throw an error');
        } catch (error) {
          expect(error).toBeInstanceOf(BaseExceptionService);
          expect((error as BaseExceptionService).code).toBe(
            BASE_EXCEPTIONS.BODY_PAYLOAD_INVALID.code,
          );
          expect((error as BaseExceptionService).data.type).toBe(type);
        }
      },
    );

    it.each([['TIME_ERROR', 'current month', 2024, 4]])(
      `fail case today 2024-04-01 - %s - %s: body %s %s`,
      async (type, caseDetail, year, month) => {
        jest.mocked(getDateFromToday).mockImplementation((add) => {
          if (!add) {
            return [2024, 4, 1, '2024-04-01'];
          } else {
            return [2024, 3, 31, '2024-03-31'];
          }
        });
        try {
          await adminJobsService.getMonthlyInspect({
            month: month as number,
            year: year as number,
          });
          fail('Expected method to throw an error');
        } catch (error) {
          expect(error).toBeInstanceOf(BaseExceptionService);
          expect((error as BaseExceptionService).code).toBe(
            BASE_EXCEPTIONS.BODY_PAYLOAD_INVALID.code,
          );
          expect((error as BaseExceptionService).data.type).toBe(type);
        }
      },
    );

    it.each([
      ['prevMonth', 2024, 3, '2024-03-01', '2024-03-31', false],
      ['thisMonth', 2024, 4, '2024-04-01', '2024-03-31', true],
    ])(
      `case today is 2024-04-01 %s: body %s %s start %s end %s must fail = %s`,
      async (caseDetail, year, month, startDate, endDate, failCase) => {
        jest.mocked(getDateFromToday).mockImplementation((add) => {
          if (!add) {
            return [2024, 4, 1, '2024-04-01'];
          } else {
            return [2024, 3, 31, '2024-03-31'];
          }
        });
        const { mockResult, mockDataByDate } = getGenerateMockMonthly(
          startDate,
          endDate,
        );

        jest
          .spyOn(jobRepository, 'query')
          .mockResolvedValueOnce(mockDataByDate);

        try {
          const result = await adminJobsService.getMonthlyInspect({
            month,
            year,
          });
          if (!failCase) {
            expect(result.startDate).toEqual(startDate);
            expect(result.endDate).toEqual(endDate);
            expect(result.monthlyInspectData).toEqual(mockResult);
          } else {
            fail('Expected method to throw an error');
          }
        } catch (error) {
          if (failCase) {
            expect(error).toBeInstanceOf(BaseExceptionService);
            expect((error as BaseExceptionService).code).toBe(
              BASE_EXCEPTIONS.BODY_PAYLOAD_INVALID.code,
            );
          }
        }
      },
    );
  });

  describe('export jobs', () => {
    const mockUser = {
      company: 'WW',
      userKey: 'test-user-key',
      name: 'test-name',
    } as WithUserContext;
    const body = {
      filter: {
        transactionId: 'a',
        minUpdatedDate: '2024-01-01T00:00:00.000Z',
        maxUpdatedDate: '2024-06-20T00:00:00.000Z',
        branch: 'branchID',
      },
      headerSlug: ['jobId', 'deviceKey', 'status', 'test', 'inspectedAt'],
    };
    const today: [number, number, number] = [2024, 4, 16];
    jest.mocked(getDateFromToday).mockImplementation((add) => {
      if (!add) {
        return [...today, '2024-04-16'];
      } else {
        return [...today, '2024-04-15'];
      }
    });

    it('should get excel base64 (all job report)', async () => {
      const customQueryJob1 = {
        ...mockJob,
        r_model_identifiers: {
          rom: '10GB',
          model: '20x',
        },
      };
      const customQueryJob2 = {
        ...mockJob,
        r_model_identifiers: {
          rom: '10GB',
          model: '20x',
        },
        r_status: JobStatus.INSPECTION_AUTO_COMPLETED,
        r_branch_id: 100000,
        r_inspected_at: new Date(),
      };

      const user = { userKey: 'user123', name: 'test' } as UserEntity;
      const branch = { branchId: '1111', title: 'test title' } as BranchEntity;
      jest.spyOn(userRepository, 'find').mockResolvedValueOnce([user]);
      jest.spyOn(branchRepository, 'find').mockResolvedValueOnce([branch]);

      await adminJobsService.exportJobs(body, mockUser, [
        customQueryJob1,
        customQueryJob2,
        customQueryJob1,
      ]);
    });

    it.each(['PRODUCT_READY', 'PRODUCT_SCRAP', 'PRODUCT_ALL'])(
      'export product each case (%s)',
      async (testCase) => {
        const exportType = 'PRODUCT';
        const productBody = {
          filter: {
            transactionId: 'a',
            minUpdatedDate: '2024-01-01T00:00:00.000Z',
            maxUpdatedDate: '2024-06-20T00:00:00.000Z',
            branch: 'branchID',
            qcStatusFromTab: `${testCase}`,
          },
          headerSlug: ['jobId', 'deviceKey', 'status', 'test'],
        };
        const user = { userKey: 'user123', name: 'test' } as UserEntity;
        const branch = {
          branchId: '1111',
          title: 'test title',
        } as BranchEntity;
        jest.spyOn(userRepository, 'find').mockResolvedValueOnce([user]);
        jest.spyOn(branchRepository, 'find').mockResolvedValueOnce([branch]);

        await adminJobsService.exportJobs(
          productBody,
          mockUser,
          [mockJob, mockJob, mockJob],
          exportType,
        );
      },
    );

    it.each([
      {
        filter: {
          minUpdatedDate: '2024-01-01T00:00:00.000Z',
          maxUpdatedDate: '2024-06-20T00:00:00.000Z',
        },
        headerSlug: ['jobId', 'deviceKey', 'status', 'test'],
      },
      {
        filter: {
          minUpdatedDate: '2024-01-01T00:00:00.000Z',
        },
        headerSlug: ['jobId', 'deviceKey', 'status', 'test'],
      },
      {
        filter: {
          maxUpdatedDate: '2024-06-20T00:00:00.000Z',
        },
        headerSlug: ['jobId', 'deviceKey', 'status', 'test'],
      },
      {
        filter: {
          minPrice: '1000',
        },
        headerSlug: ['jobId', 'deviceKey', 'status', 'test'],
      },
      {
        filter: {
          maxPrice: '10000',
        },
        headerSlug: ['jobId', 'deviceKey', 'status', 'test'],
      },
      {
        filter: {
          minPrice: '1000',
          maxPrice: '10000',
        },
        headerSlug: ['jobId', 'deviceKey', 'status', 'test'],
      },
    ])('export report filter date & price', async (testCase) => {
      const user = { userKey: 'user123', name: 'test' } as UserEntity;
      const branch = {
        branchId: '1111',
        title: 'test title',
      } as BranchEntity;
      jest.spyOn(userRepository, 'find').mockResolvedValueOnce([user]);
      jest.spyOn(branchRepository, 'find').mockResolvedValueOnce([branch]);

      await adminJobsService.exportJobs(testCase, mockUser, [
        mockJob,
        mockJob,
        mockJob,
      ]);
    });

    it('get ICT Time', async () => {
      const utcTime = '2024-05-01T00:00:00.000Z';
      const ictTime = '2024-05-01T07:00:00.000Z';
      const UTCDate = new Date(utcTime);
      const result = adminJobsService.getICTDateTime(UTCDate);
      expect(result).toStrictEqual(new Date(ictTime));
    });
  });

  describe('Patch Product', () => {
    const mockUser = {
      company: 'WW',
      userKey: 'test',
      name: 'test',
    } as WithUserContext;

    const body = {
      retailPrice: 20000,
      whileSalePrice: 17000,
    };

    it('should success update (no previous history record)', async () => {
      const productJob = {
        ...mockJob,
        costPrice: 10000,
        status: JobStatus.INSPECTION_COMPLETED,
        isConfirmPrice: false,
      };
      jest.spyOn(jobRepository, 'findOneBy').mockResolvedValueOnce(productJob);

      const result = await adminJobsService.updateJobProduct(
        'job-id',
        body as any,
        mockUser,
      );

      expect(result).toBeNull();
    });

    it('should success update (have at least 1 history record)', async () => {
      const productJob = {
        ...mockJob,
        adminUpdateCostListValue: [
          {
            at: new Date(),
            by: { key: 'userKey', name: 'name' },
            retailPrice: 20000,
            wholeSalePrice: 17000,
          },
        ],
        costPrice: 10000,
        status: JobStatus.INSPECTION_AUTO_COMPLETED,
        isConfirmPrice: false,
      };
      jest.spyOn(jobRepository, 'findOneBy').mockResolvedValueOnce(productJob);

      const result = await adminJobsService.updateJobProduct(
        'job-id',
        body as any,
        mockUser,
      );

      expect(result).toBeNull();
    });

    it('should not found job', async () => {
      jest.spyOn(jobRepository, 'findOneBy').mockResolvedValueOnce(null);
      try {
        await adminJobsService.updateJobProduct(
          'job-id',
          body as any,
          mockUser,
        );

        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.NOT_FOUND_DATA.code,
        );
        expect((error as BaseExceptionService).message).toBe(
          BASE_EXCEPTIONS.NOT_FOUND_DATA.message,
        );
        expect((error as BaseExceptionService).data).toBe(
          'Transaction ID not found',
        );
      }
    });

    it('should invalid job status', async () => {
      const productJob = {
        ...mockJob,
        status: JobStatus.INSPECTION_ASSIGNED,
      };
      jest.spyOn(jobRepository, 'findOneBy').mockResolvedValueOnce(productJob);
      try {
        await adminJobsService.updateJobProduct(
          'job-id',
          body as any,
          mockUser,
        );

        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.INVALID_INPUT_FOR_UPDATE_JOB.code,
        );
        expect((error as BaseExceptionService).message).toBe(
          BASE_EXCEPTIONS.INVALID_INPUT_FOR_UPDATE_JOB.message,
        );
        expect((error as BaseExceptionService).data).toBe(
          `Invalid job status - the status must be: ${JobStatus.INSPECTION_COMPLETED}, ${JobStatus.INSPECTION_AUTO_COMPLETED}`,
        );
      }
    });

    it('should error job already confirm price', async () => {
      const productJob = {
        ...mockJob,
        status: JobStatus.INSPECTION_AUTO_COMPLETED,
        isConfirmPrice: true,
      };
      jest.spyOn(jobRepository, 'findOneBy').mockResolvedValueOnce(productJob);
      try {
        await adminJobsService.updateJobProduct(
          'job-id',
          body as any,
          mockUser,
        );

        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.INVALID_JOB_FOR_ACTION.code,
        );
      }
    });

    it('should update error, no cost price for this job', async () => {
      const productJob = {
        ...mockJob,
        status: JobStatus.INSPECTION_COMPLETED,
        isConfirmPrice: false,
      };
      jest.spyOn(jobRepository, 'findOneBy').mockResolvedValueOnce(productJob);
      try {
        await adminJobsService.updateJobProduct(
          'job-id',
          body as any,
          mockUser,
        );

        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.INVALID_INPUT_FOR_UPDATE_JOB.code,
        );
        expect((error as BaseExceptionService).message).toBe(
          BASE_EXCEPTIONS.INVALID_INPUT_FOR_UPDATE_JOB.message,
        );
        expect((error as BaseExceptionService).data).toBe(
          'There is no costPrice for this job.',
        );
      }
    });
  });

  describe('setOperationCost', () => {
    let operationConfig;
    let mockRecalQueue;
    beforeEach(() => {
      operationConfig = {
        companyId: 'TestCompany',
        configKey: 'operation_cost',
        data: {},
      };
      mockRecalQueue = jest.spyOn(reCal, 'reCalculateProduct');
      mockRecalQueue.mockResolvedValue();
    });
    it('set operation cost success', async () => {
      jest
        .spyOn(systemConfigService, 'setSystemConfig')
        .mockResolvedValueOnce(operationConfig);
      const result = await adminJobsService.setOperationCost(
        {},
        operationConfig.companyId,
      );
      expect(result).toBe(operationConfig);
      expect(mockRecalQueue).toHaveBeenCalled();
    });
    it('dont have operation cost', async () => {
      jest
        .spyOn(systemConfigService, 'setSystemConfig')
        .mockResolvedValueOnce(undefined);
      jest.spyOn(reCal, 'reCalculateProduct').mockResolvedValueOnce();
      const result = await adminJobsService.setOperationCost(
        {},
        operationConfig.companyId,
      );
      expect(result).toBe(undefined);
      expect(mockRecalQueue).not.toHaveBeenCalled();
    });
  });

  describe('get Transaction', () => {
    const today: [number, number, number, string] = [2024, 4, 16, '2024-04-16'];
    jest.mocked(getDateFromToday).mockImplementation((add) => today);
    const mockDate = new Date('2024-04-17');
    const mockTransactionJob = {
      ...mockJob,
      status: JobStatus.INSPECTION_COMPLETED,
      repairListValue: [{} as RepairHx],
      inspectListValue: [{} as InspectHx],
    } as JobEntity;
    const mockCompany: CompanyEntity = {
      companyId: '123',
      title: 'company title',
      logoUrl: 'company logo url',
      createdBy: 'company created by',
      userKeyClaimFnName: 'company user key claim fn name',
      empUploadMapperFnName: 'company emp upload mapper fn name',
      createdAt: new Date(),
      updatedAt: new Date(),
      logoPath: 'company logo path',
    };

    it.each([
      [true, true],
      [false, false],
    ])(`should found job ( %s ) pass ( %s )`, async (foundJob, isPass) => {
      jest.spyOn(companyRepository, 'findOne').mockResolvedValue(mockCompany);
      jest
        .spyOn(jobRepository, 'findOne')
        .mockResolvedValue(foundJob ? mockTransactionJob : null);

      if (isPass) {
        const result = await adminJobsService.getTransactionInspection('id');
        expect(result).toBe('contract');
      } else {
        try {
          await adminJobsService.getTransactionInspection('id');
          fail('Expected method to throw an error');
        } catch (error) {
          expect(error).toBeInstanceOf(BaseExceptionService);
          expect((error as BaseExceptionService).code).toBe(
            BASE_EXCEPTIONS.NOT_FOUND_DATA.code,
          );
          expect((error as BaseExceptionService).data).toBe(
            'Transaction ID not found',
          );
        }
      }
    });

    it.each([
      [true, true],
      [false, false],
    ])(`should found logo ( %s ) pass ( %s )`, async (hasLogoPath, isPass) => {
      jest.spyOn(companyRepository, 'findOne').mockResolvedValue({
        ...mockCompany,
        logoPath: hasLogoPath ? 'logo-path' : '',
      });
      jest
        .spyOn(jobRepository, 'findOne')
        .mockResolvedValue(mockTransactionJob);

      if (isPass) {
        const result = await adminJobsService.getTransactionInspection('id');
        expect(result).toBe('contract');
      } else {
        try {
          await adminJobsService.getTransactionInspection('id');
          fail('Expected method to throw an error');
        } catch (error) {
          expect(error).toBeInstanceOf(BaseExceptionService);
          expect((error as BaseExceptionService).code).toBe(
            BASE_EXCEPTIONS.NOT_FOUND_DATA.code,
          );
          expect((error as BaseExceptionService).data).toBe(
            'Company image not found - invalid url',
          );
        }
      }
    });

    const notAcceptStatusCase = Object.values(JobStatus)
      .filter(
        (item: JobStatus) =>
          ![
            JobStatus.INSPECTION_COMPLETED,
            JobStatus.INSPECTION_AUTO_COMPLETED,
          ].includes(item),
      )
      .map((status) => {
        const returnData: [
          string,
          undefined,
          undefined,
          boolean,
          string,
          string,
        ] = [
          status,
          undefined,
          undefined,
          false,
          'INVALID_JOB_FOR_ACTION',
          'Job status not match.',
        ];
        return returnData;
      });

    it.each([
      [
        JobStatus.INSPECTION_COMPLETED,
        undefined,
        undefined,
        true,
        undefined,
        undefined,
      ],
      [
        JobStatus.INSPECTION_AUTO_COMPLETED,
        undefined,
        undefined,
        true,
        undefined,
        undefined,
      ],
      ...notAcceptStatusCase,
      [
        JobStatus.INSPECTION_COMPLETED,
        'repairListValue',
        null,
        false,
        'INVALID_JOB_FOR_ACTION',
        undefined,
      ],
      [
        JobStatus.INSPECTION_COMPLETED,
        'inspectListValue',
        null,
        false,
        'INVALID_JOB_FOR_ACTION',
        undefined,
      ],
      [
        JobStatus.INSPECTION_COMPLETED,
        'repairListValue',
        [],
        false,
        'INVALID_JOB_FOR_ACTION',
        undefined,
      ],
      [
        JobStatus.INSPECTION_COMPLETED,
        'inspectListValue',
        [],
        false,
        'INVALID_JOB_FOR_ACTION',
        undefined,
      ],
    ])(
      `should status ( %s ) modify field name ( %s ) to value ( %s ) pass ( %s )`,
      async (status, modifyField, toValue, isPass, errorType, errorMsg) => {
        let dataJob = { ...mockTransactionJob, status } as JobEntity;
        if (modifyField) {
          dataJob[modifyField as string] = toValue;
        }

        jest.spyOn(companyRepository, 'findOne').mockResolvedValue(mockCompany);
        jest.spyOn(jobRepository, 'findOne').mockResolvedValue(dataJob);

        if (isPass) {
          const result = await adminJobsService.getTransactionInspection('id');
          expect(result).toBe('contract');
        } else {
          try {
            await adminJobsService.getTransactionInspection('id');
            fail('Expected method to throw an error');
          } catch (error) {
            expect(error).toBeInstanceOf(BaseExceptionService);
            expect((error as BaseExceptionService).code).toBe(
              BASE_EXCEPTIONS[errorType as string].code,
            );
            if (errorMsg) {
              expect((error as BaseExceptionService).data).toBe(errorMsg);
            }
          }
        }
      },
    );
  });

  describe('Confirm Jobs', () => {
    const mockUser = {
      company: 'WW',
      userKey: 'test',
      name: 'test',
    } as WithUserContext;

    const mockRequest = {
      jobs: [
        {
          jobId: '00001',
          updatedAt: '2024-02-14T00:00:00.000Z',
        },
        {
          jobId: '00002',
          updatedAt: '2024-02-14T00:00:00.000Z',
        },
      ],
    };
    const mockRequestErrorNotFound = {
      jobs: [
        {
          jobId: '00003',
          updatedAt: '2024-02-14T00:00:00.000Z',
        },
      ],
    };
    const mockRequestErrorUpdated = {
      jobs: [
        {
          jobId: '00001',
          updatedAt: '2024-02-14T00:01:00.000Z',
        },
      ],
    };
    const mockRequestErrorConfirmedPrice = {
      jobs: [
        {
          jobId: '00002',
          updatedAt: '2024-02-14T00:00:00.000Z',
        },
      ],
    };
    it('should success confirm', async () => {
      jest
        .spyOn(jobRepository, 'find')
        .mockResolvedValueOnce(mockInspectionJobNotConfirmed);

      const result = await adminJobsService.confirmPrice(mockRequest, mockUser);

      expect(result).toBeNull();
    });
    it('should throw error with not found jobs', async () => {
      jest
        .spyOn(jobRepository, 'find')
        .mockResolvedValueOnce(mockInspectionJob);

      try {
        await adminJobsService.confirmPrice(mockRequestErrorNotFound, mockUser);
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS['INVALID_JOB_FOR_ACTION'].code,
        );
        expect(
          (error as BaseExceptionService).data.notFoundJobs.length,
        ).toBeGreaterThan(0);
      }
    });
    it('should throw error with updated jobs', async () => {
      jest
        .spyOn(jobRepository, 'find')
        .mockResolvedValueOnce(mockProductJobWithConfirmPrice);

      try {
        await adminJobsService.confirmPrice(mockRequestErrorUpdated, mockUser);
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS['INVALID_JOB_FOR_ACTION'].code,
        );
        expect(
          (error as BaseExceptionService).data.updatedJobs.length,
        ).toBeGreaterThan(0);
      }
    });
    it('should throw error with confirmed jobs', async () => {
      jest
        .spyOn(jobRepository, 'find')
        .mockResolvedValueOnce(mockProductJobWithConfirmPrice);

      try {
        await adminJobsService.confirmPrice(
          mockRequestErrorConfirmedPrice,
          mockUser,
        );
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS['INVALID_JOB_FOR_ACTION'].code,
        );
        expect(
          (error as BaseExceptionService).data.confirmedJobs.length,
        ).toBeGreaterThan(0);
      }
    });
  });

  describe('Patch Incomplete Shipping', () => {
    const mockUser = {
      company: 'WW',
      userKey: 'test-user-key',
      name: 'test-name',
    } as WithUserContext;

    const mockJobIncompleteShip = {
      ...mockJob,
      jobId: 'test-job-id',
      aoShippingStatus: AOShippingStatus.SHIPPED,
    } as JobEntity;

    const rejectBody = {
      remark: 'test remark',
    };

    it('should success update (status partial_received)', async () => {
      const mockAOShip = {
        ...mockAO[0],
        status: AllocationOrderStatus.PARTIAL_RECEIVED,
      } as AllocationOrderEntity;

      const mockJobSuccess = {
        ...mockJobIncompleteShip,
        allocationOrder: mockAOShip,
        allocationOrderId: mockAOShip.allocationOrderId,
      } as JobEntity;

      jest.spyOn(jobRepository, 'findOne').mockResolvedValue(mockJobSuccess);
      jest
        .spyOn(allocationOrderRepository, 'findOne')
        .mockResolvedValue(mockAOShip);

      const result = await adminJobsService.updateIncompleteShipping(
        mockJobSuccess.jobId,
        rejectBody,
        mockUser,
      );

      expect(result).toBeNull();
    });

    it('should success update (status reject_by_shop)', async () => {
      const mockAOShip = {
        ...mockAO[0],
        status: AllocationOrderStatus.REJECT_BY_SHOP,
      } as AllocationOrderEntity;

      const mockJobSuccess = {
        ...mockJobIncompleteShip,
        allocationOrder: mockAOShip,
        allocationOrderId: mockAOShip.allocationOrderId,
      } as JobEntity;

      jest.spyOn(jobRepository, 'findOne').mockResolvedValue(mockJobSuccess);
      jest
        .spyOn(allocationOrderRepository, 'findOne')
        .mockResolvedValue(mockAOShip);

      const result = await adminJobsService.updateIncompleteShipping(
        mockJobSuccess.jobId,
        rejectBody,
        mockUser,
      );

      expect(result).toBeNull();
    });

    it('should not found job', async () => {
      jest.spyOn(jobRepository, 'findOne').mockResolvedValue(null);
      try {
        await adminJobsService.updateIncompleteShipping(
          mockJobIncompleteShip.jobId,
          rejectBody,
          mockUser,
        );
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.NOT_FOUND_DATA.code,
        );
      }
    });

    it('should not found ao', async () => {
      const mockJobWithoutAO = {
        ...mockJobIncompleteShip,
        allocationOrder: new AllocationOrderEntity(),
      } as JobEntity;

      jest.spyOn(jobRepository, 'findOne').mockResolvedValue(mockJob);
      try {
        await adminJobsService.updateIncompleteShipping(
          mockJobWithoutAO.jobId,
          rejectBody,
          mockUser,
        );
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.NOT_FOUND_DATA.code,
        );
        expect((error as BaseExceptionService).data).toBe(
          'Job is not in this allocation order',
        );
      }
    });

    it('should invalid ao status', async () => {
      const mockJobWithInvalidAOStatus = {
        ...mockJobIncompleteShip,
        allocationOrder: mockAO[0],
        allocationOrderId: mockAO[0].allocationOrderId,
      } as JobEntity;

      jest
        .spyOn(jobRepository, 'findOne')
        .mockResolvedValue(mockJobWithInvalidAOStatus);
      jest
        .spyOn(allocationOrderRepository, 'findOne')
        .mockResolvedValue(mockAO[0]);
      try {
        await adminJobsService.updateIncompleteShipping(
          mockJobWithInvalidAOStatus.jobId,
          rejectBody,
          mockUser,
        );
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.INVALID_AO_STATUS_TO_CONFIRM.code,
        );
      }
    });

    it('should invalid job ao shipping status', async () => {
      const mockAOShip = {
        ...mockAO[0],
        status: AllocationOrderStatus.PARTIAL_RECEIVED,
      } as AllocationOrderEntity;

      const mockJobInvalidShipStatus = {
        ...mockJobIncompleteShip,
        allocationOrder: mockAOShip,
        allocationOrderId: mockAOShip.allocationOrderId,
        aoShippingStatus: AOShippingStatus.RECEIVED,
      } as JobEntity;

      jest
        .spyOn(jobRepository, 'findOne')
        .mockResolvedValue(mockJobInvalidShipStatus);
      jest
        .spyOn(allocationOrderRepository, 'findOne')
        .mockResolvedValue(mockAOShip);
      try {
        await adminJobsService.updateIncompleteShipping(
          mockJobInvalidShipStatus.jobId,
          rejectBody,
          mockUser,
        );
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.INVALID_INPUT_FOR_UPDATE_JOB.code,
        );
      }
    });
  });

  describe('Patch Incomplete Receiving', () => {
    const mockUser = {
      company: 'WW',
      userKey: 'test-user-key',
      name: 'test-name',
    } as WithUserContext;

    const mockJobIncompleteShip = {
      ...mockJob,
      aoShippingStatus: AOShippingStatus.SHIPPED,
    } as JobEntity;

    const rejectBody = {
      remark: 'test remark',
    };

    it('should success update (status partial_received)', async () => {
      const mockAOShip = {
        ...mockAO[0],
        status: AllocationOrderStatus.PARTIAL_RECEIVED,
      } as AllocationOrderEntity;

      const mockJobSuccess = {
        ...mockJobIncompleteShip,
        allocationOrder: mockAOShip,
        allocationOrderId: mockAOShip.allocationOrderId,
      } as JobEntity;

      const mockAODraft = {
        ...mockAO[0],
        allocationOrderId: 'AO-0002',
        status: AllocationOrderStatus.DRAFT,
      } as AllocationOrderEntity;

      jest.spyOn(jobRepository, 'findOne').mockResolvedValue(mockJobSuccess);
      jest
        .spyOn(allocationOrderRepository, 'findOne')
        .mockResolvedValue(mockAOShip);

      jest
        .spyOn(allocationOrderRepository, 'findOne')
        .mockResolvedValue(mockAODraft);
      const result = await adminJobsService.updateIncompleteReceiving(
        mockJobSuccess.jobId,
        rejectBody,
        mockUser,
      );

      expect(result.allocationOrderId).toEqual(mockAODraft.allocationOrderId);
      expect(result.status).toEqual(AllocationOrderStatus.IN_TRANSIT);
      expect(result.quantity).toEqual(1);
      expect(result.createdBy).toBeNull();
    });

    it('should success update (status reject_by_shop)', async () => {
      const mockAOShip = {
        ...mockAO[0],
        status: AllocationOrderStatus.REJECT_BY_SHOP,
      } as AllocationOrderEntity;

      const mockJobSuccess = {
        ...mockJobIncompleteShip,
        allocationOrder: mockAOShip,
        allocationOrderId: mockAOShip.allocationOrderId,
      } as JobEntity;

      const mockAODraft = {
        ...mockAO[0],
        allocationOrderId: 'AO-0002',
        status: AllocationOrderStatus.DRAFT,
      } as AllocationOrderEntity;

      jest.spyOn(jobRepository, 'findOne').mockResolvedValue(mockJobSuccess);
      jest
        .spyOn(allocationOrderRepository, 'findOne')
        .mockResolvedValue(mockAOShip);

      jest
        .spyOn(allocationOrderRepository, 'findOne')
        .mockResolvedValue(mockAODraft);
      const result = await adminJobsService.updateIncompleteReceiving(
        mockJobSuccess.jobId,
        rejectBody,
        mockUser,
      );

      expect(result.allocationOrderId).toEqual(mockAODraft.allocationOrderId);
      expect(result.status).toEqual(AllocationOrderStatus.IN_TRANSIT);
      expect(result.quantity).toEqual(1);
      expect(result.createdBy).toBeNull();
    });

    it('should success update (append snapshot)', async () => {
      const jobsSnapshot = prepareJobsSnapshot({ job: mockJob });
      const mockAOShip = {
        ...mockAO[0],
        status: AllocationOrderStatus.PARTIAL_RECEIVED,
        jobsSnapshot: [jobsSnapshot],
      } as AllocationOrderEntity;

      const mockJobSuccess = {
        ...mockJobIncompleteShip,
        jobId: 'new-id-test',
        allocationOrder: mockAOShip,
        allocationOrderId: mockAOShip.allocationOrderId,
      } as JobEntity;

      const mockAODraft = {
        ...mockAO[0],
        allocationOrderId: 'AO-0002',
        status: AllocationOrderStatus.DRAFT,
      } as AllocationOrderEntity;

      jest.spyOn(jobRepository, 'findOne').mockResolvedValue(mockJobSuccess);
      jest
        .spyOn(allocationOrderRepository, 'findOne')
        .mockResolvedValue(mockAOShip);

      jest
        .spyOn(allocationOrderRepository, 'findOne')
        .mockResolvedValue(mockAODraft);
      const result = await adminJobsService.updateIncompleteReceiving(
        mockJobSuccess.jobId,
        rejectBody,
        mockUser,
      );

      expect(result.allocationOrderId).toEqual(mockAODraft.allocationOrderId);
      expect(result.status).toEqual(AllocationOrderStatus.IN_TRANSIT);
      expect(result.quantity).toEqual(1);
      expect(result.createdBy).toBeNull();
    });

    it('should success update (replace snapshot)', async () => {
      const jobsSnapshot = prepareJobsSnapshot({ job: mockJobIncompleteShip });
      const mockAOShip = {
        ...mockAO[0],
        status: AllocationOrderStatus.PARTIAL_RECEIVED,
        jobsSnapshot: [jobsSnapshot],
      } as AllocationOrderEntity;

      const mockJobSuccess = {
        ...mockJobIncompleteShip,
        allocationOrder: mockAOShip,
        allocationOrderId: mockAOShip.allocationOrderId,
      } as JobEntity;

      const mockAODraft = {
        ...mockAO[0],
        allocationOrderId: 'AO-0002',
        status: AllocationOrderStatus.DRAFT,
      } as AllocationOrderEntity;

      jest.spyOn(jobRepository, 'findOne').mockResolvedValue(mockJobSuccess);
      jest
        .spyOn(allocationOrderRepository, 'findOne')
        .mockResolvedValue(mockAOShip);

      jest
        .spyOn(allocationOrderRepository, 'findOne')
        .mockResolvedValue(mockAODraft);
      const result = await adminJobsService.updateIncompleteReceiving(
        mockJobSuccess.jobId,
        rejectBody,
        mockUser,
      );

      expect(result.allocationOrderId).toEqual(mockAODraft.allocationOrderId);
      expect(result.status).toEqual(AllocationOrderStatus.IN_TRANSIT);
      expect(result.quantity).toEqual(1);
      expect(result.createdBy).toBeNull();
    });

    it('should error draft', async () => {
      const mockAOShip = {
        ...mockAO[0],
        status: AllocationOrderStatus.PARTIAL_RECEIVED,
      } as AllocationOrderEntity;

      const mockJobSuccess = {
        ...mockJobIncompleteShip,
        allocationOrder: mockAOShip,
        allocationOrderId: mockAOShip.allocationOrderId,
      } as JobEntity;

      jest.spyOn(jobRepository, 'findOne').mockResolvedValue(mockJobSuccess);
      jest
        .spyOn(allocationOrderRepository, 'findOne')
        .mockResolvedValue(mockAOShip);

      jest.spyOn(allocationOrderRepository, 'findOne').mockResolvedValue(null);

      try {
        await adminJobsService.updateIncompleteReceiving(
          mockJobSuccess.jobId,
          rejectBody,
          mockUser,
        );
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.INVALID_ALLOCATION_DRAFT.code,
        );
      }
    });
  });

  describe('Patch Incomplete Lost', () => {
    const mockUserForIncompleteLost = {
      company: 'WW',
      userKey: 'mock-user-key',
      name: 'mock-name',
    } as WithUserContext;

    const mockJobIncompleteLost = {
      ...mockJob,
      jobId: 'mock-job-id',
      aoShippingStatus: AOShippingStatus.SHIPPED,
    } as JobEntity;

    const lostBody = {
      remark: 'test remark',
      videoPath: 'test-url.test',
    };

    const mockAOData = {
      rejectByShop: {
        ...mockAO[0],
        status: AllocationOrderStatus.REJECT_BY_SHOP,
      } as AllocationOrderEntity,
      partialReceive: {
        ...mockAO[0],
        status: AllocationOrderStatus.PARTIAL_RECEIVED,
      } as AllocationOrderEntity,
    };
    it.each(['rejectByShop', 'partialReceive'])(
      'should success update',
      async (testCase) => {
        const mockAOShip = mockAOData[testCase] as AllocationOrderEntity;

        const mockJobSuccess = {
          ...mockJobIncompleteLost,
          allocationOrder: mockAOShip,
          allocationOrderId: mockAOShip.allocationOrderId,
        } as JobEntity;

        jest.spyOn(jobRepository, 'findOne').mockResolvedValue(mockJobSuccess);
        jest
          .spyOn(allocationOrderRepository, 'findOne')
          .mockResolvedValue(mockAOShip);

        const result = await adminJobsService.updateIncompleteLost(
          mockJobSuccess.jobId,
          lostBody,
          mockUserForIncompleteLost,
        );

        expect(result.allocationOrderId).toEqual(mockAOShip.allocationOrderId);
        expect(result.status).toEqual(mockAOShip.status);

        if (result.jobsSnapshot) {
          expect(result.jobsSnapshot[0].videoPath).toEqual(lostBody.videoPath);
        }
      },
    );
  });

  describe('Get PresignedUrl', () => {
    it('should successfully get PresignedUrl', async () => {
      const request = {
        jobId: 'jobid-0001',
        key: 'test_video_111',
      };

      const user = {
        company: 'CompanyX',
        userKey: 'user123',
      } as WithUserContext;

      const result = await adminJobsService.getPresigned(
        request.jobId,
        request.key,
        user,
      );

      const expectedUrlPath = getS3JobUrlPath(
        user.company,
        request.jobId,
        request.key,
      );

      // Assertions
      expect(result.url).toContain(request.jobId);
      expect(result.url).toContain(request.key);
      expect(result.path).toContain(expectedUrlPath);
    });
  });

  describe('export jobs', () => {
    const mockUser = {
      company: 'WW',
      userKey: 'test-user-key',
      name: 'test-name',
    } as WithUserContext;
    const body = {
      filter: {
        transactionId: 'a',
        minUpdatedDate: '2024-01-01T00:00:00.000Z',
        maxUpdatedDate: '2024-06-20T00:00:00.000Z',
        branch: 'branchID',
      },
      headerSlug: ['jobId', 'deviceKey', 'status', 'test', 'inspectedAt'],
    };
    const today: [number, number, number] = [2024, 4, 16];
    jest.mocked(getDateFromToday).mockImplementation((add) => {
      if (!add) {
        return [...today, '2024-04-16'];
      } else {
        return [...today, '2024-04-15'];
      }
    });

    it('should get excel stream', async () => {
      const customQueryJob1 = {
        ...mockJob,
        r_model_identifiers: {
          rom: '10GB',
          model: '20x',
        },
      };
      const customQueryJob2 = {
        ...mockJob,
        r_model_identifiers: {
          rom: '10GB',
          model: '20x',
        },
        r_status: JobStatus.INSPECTION_AUTO_COMPLETED,
        r_branch_id: 100000,
        r_inspected_at: new Date(),
      };

      const user = { userKey: 'user123', name: 'test' } as UserEntity;
      const branch = { branchId: '1111', title: 'test title' } as BranchEntity;
      jest.spyOn(userRepository, 'find').mockResolvedValueOnce([user]);
      jest.spyOn(branchRepository, 'find').mockResolvedValueOnce([branch]);

      await adminJobsService.exportJobsStream(body, mockUser, [
        customQueryJob1,
        customQueryJob2,
        customQueryJob1,
      ]);
    });

    it('should validate header condition', async () => {
      const result = await adminJobsService.validateHeaderCondition({}, 'SAP');
      expect(Object.keys(result)).toHaveLength(0);
    });

    it.each(['PRODUCT_READY', 'PRODUCT_SCRAP', 'PRODUCT_ALL'])(
      'export product each case (%s)',
      async (testCase) => {
        const exportType = 'PRODUCT';
        const productBody = {
          filter: {
            transactionId: 'a',
            minUpdatedDate: '2024-01-01T00:00:00.000Z',
            maxUpdatedDate: '2024-06-20T00:00:00.000Z',
            branch: 'branchID',
            qcStatusFromTab: `${testCase}`,
          },
          headerSlug: ['jobId', 'deviceKey', 'status', 'test'],
        };
        const user = { userKey: 'user123', name: 'test' } as UserEntity;
        const branch = {
          branchId: '1111',
          title: 'test title',
        } as BranchEntity;
        jest.spyOn(userRepository, 'find').mockResolvedValueOnce([user]);
        jest.spyOn(branchRepository, 'find').mockResolvedValueOnce([branch]);

        const queryBuilder = {
          where: jest.fn().mockReturnThis(),
          getMany: jest.fn(),
        } as unknown as jest.Mocked<SelectQueryBuilder<JobEntity>>;

        await adminJobsService.exportJobsStream(
          productBody,
          mockUser,
          [mockJob, mockJob, mockJob],
          exportType,
        );
      },
    );

    it.each(['PRODUCT_READY', 'PRODUCT_SCRAP', 'PRODUCT_ALL'])(
      'export product each case (%s)',
      async (testCase) => {
        const exportType = 'SAP';
        const productBody = {
          filter: {
            transactionId: 'a',
            minUpdatedDate: '2024-01-01T00:00:00.000Z',
            maxUpdatedDate: '2024-06-20T00:00:00.000Z',
            branch: 'branchID',
            qcStatusFromTab: `${testCase}`,
          },
          headerSlug: ['jobId', 'deviceKey', 'status', 'test'],
        };
        const user = { userKey: 'user123', name: 'test' } as UserEntity;
        const branch = {
          branchId: '1111',
          title: 'test title',
        } as BranchEntity;
        jest.spyOn(userRepository, 'find').mockResolvedValueOnce([user]);
        jest.spyOn(branchRepository, 'find').mockResolvedValueOnce([branch]);

        const queryBuilder = {
          where: jest.fn().mockReturnThis(),
          getMany: jest.fn(),
        } as unknown as jest.Mocked<SelectQueryBuilder<JobEntity>>;

        await adminJobsService.exportJobsStream(
          productBody,
          mockUser,
          [mockJob, mockJob, mockJob],
          exportType,
        );
      },
    );
  });
});
