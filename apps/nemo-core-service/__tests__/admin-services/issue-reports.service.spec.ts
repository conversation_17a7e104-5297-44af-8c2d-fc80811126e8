import { getRepositoryToken } from '@nestjs/typeorm';
import { getQueueToken } from '@nestjs/bull';
import { Test } from '@nestjs/testing';
import {
  JobEntity,
  UserEntity,
  CompanyEntity,
  IssueReportEntity,
  IssueReportStatus,
  ContractEntity,
  VoucherEntity,
  BranchEntity,
  EmailActivitiesEntity,
  SystemConfigEntity,
} from '../../src/entities';

import { BaseExceptionModule } from '../../src/base-exceptions/base-exceptions.module';
import { BaseExceptionService } from '../../src/exceptions';
import { BASE_EXCEPTIONS } from '../../src/config';
import { Repository } from 'typeorm';
import { AdminIssueReportsService } from '../../src/admin/issue-reports/issue-reports.service';
import { WithUserContext } from 'src/interfaces';
import { DateTime } from 'luxon';
import { ContractsService } from '../../src/shop/contracts/contracts.service';
import { S3Service } from '../../src/storage/s3.service';
import { SmtpService } from '../../src/smtp/smtp.service';
import { CacheManagerService } from '../../src/cache-manager/cache-manager.service';
jest.mock('../../src/utils/general', () => {
  const original = jest.requireActual('../../src/utils/general');
  return {
    ...original,
    getDateFromToday: jest.fn(),
  };
});

describe('AdminIssueReportsService', () => {
  let adminIssueReportsService: AdminIssueReportsService;
  let adminIssueReportRepository: Repository<IssueReportEntity>;
  let contractRepository: Repository<ContractEntity>;
  let jobRepository: Repository<JobEntity>;

  beforeEach(async () => {
    const module = await Test.createTestingModule({
      imports: [BaseExceptionModule.register()],
      providers: [
        AdminIssueReportsService,
        ContractsService,
        {
          provide: CacheManagerService,
          useValue: {
            incrData: jest.fn(() => Promise.resolve(1)),
            getData: jest.fn(),
            setData: jest.fn(),
          },
        },
        {
          provide: S3Service,
          useValue: {
            getPreviewUrl: jest.fn(),
            uploadFile: jest.fn(),
            getFile: jest.fn(),
            uploadFileByPresignedUrl: jest.fn(),
            getBufferFile: jest.fn(),
            getFileWithSignedUrl: jest.fn(),
            getAssetFile: jest.fn(),
          },
        },
        {
          provide: SmtpService,
          useValue: {
            sendMail: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(IssueReportEntity),
          useValue: {
            save: jest.fn().mockReturnThis(),
            findOne: jest.fn().mockReturnThis(),
            findOneBy: jest.fn().mockReturnThis(),
            manager: {
              connection: {
                createQueryRunner: jest.fn().mockReturnValue({
                  manager: {
                    delete: jest.fn().mockResolvedValue({}), // mock the delete method
                    transaction: jest.fn().mockReturnThis(),
                  },
                  release: jest.fn(), // mock the release method (required after a transaction)
                  connect: jest.fn().mockReturnThis(),
                  startTransaction: jest.fn().mockReturnThis(),
                  commitTransaction: jest.fn().mockReturnThis(),
                  rollbackTransaction: jest.fn().mockReturnThis(),
                  isTransactionActive: true,
                }),
              },
              transaction: jest.fn().mockReturnThis(),
            },
          },
        },
        {
          provide: getRepositoryToken(UserEntity),
          useValue: {
            findOne: jest.fn().mockReturnThis(),
            findOneBy: jest.fn().mockReturnThis(),
          },
        },
        {
          provide: getRepositoryToken(JobEntity),
          useValue: {
            findOne: jest.fn().mockReturnThis(),
            findOneBy: jest.fn().mockReturnThis(),
          },
        },
        {
          provide: getRepositoryToken(CompanyEntity),
          useValue: {
            findOne: jest.fn().mockReturnThis(),
            findOneBy: jest.fn().mockReturnThis(),
          },
        },
        {
          provide: getRepositoryToken(ContractEntity),
          useValue: {
            findOne: jest.fn().mockReturnThis(),
            findOneBy: jest.fn().mockReturnThis(),
            save: jest.fn().mockReturnThis(),
          },
        },
        {
          provide: getRepositoryToken(VoucherEntity),
          useValue: {
            findOne: jest.fn().mockReturnThis(),
            findOneBy: jest.fn().mockReturnThis(),
          },
        },
        {
          provide: getRepositoryToken(BranchEntity),
          useValue: {
            findOne: jest.fn().mockReturnThis(),
            findOneBy: jest.fn().mockReturnThis(),
          },
        },
        {
          provide: getRepositoryToken(EmailActivitiesEntity),
          useValue: {
            findOne: jest.fn().mockReturnThis(),
            findOneBy: jest.fn().mockReturnThis(),
          },
        },
        {
          provide: getRepositoryToken(SystemConfigEntity),
          useValue: {
            save: jest.fn().mockReturnThis(),
            findOne: jest.fn().mockReturnThis(),
          },
        },
        {
          provide: getQueueToken('email-queue'),
          useValue: {
            add: jest.fn(),
            process: jest.fn(),
          },
        },
      ],
    }).compile();

    adminIssueReportsService = module.get<AdminIssueReportsService>(
      AdminIssueReportsService,
    );

    adminIssueReportRepository = module.get<Repository<IssueReportEntity>>(
      getRepositoryToken(IssueReportEntity),
    );

    contractRepository = module.get<Repository<ContractEntity>>(
      getRepositoryToken(ContractEntity),
    );
    jobRepository = module.get<Repository<JobEntity>>(
      getRepositoryToken(JobEntity),
    );
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('buildSearchQuery', () => {
    it.each`
      status        | issueType      | searchId      | expectResult
      ${['status']} | ${'issueType'} | ${'searchId'} | ${`r.status IN ('${'status'}') AND r.issueReportType = 'issueType' AND (r.jobId LIKE '%${'searchId'}%' OR r.issueReportId LIKE '%${'searchId'}%')`}
      ${['status']} | ${undefined}   | ${undefined}  | ${`r.status IN ('${'status'}')`}
      ${undefined}  | ${'issueType'} | ${undefined}  | ${`r.issueReportType = 'issueType'`}
      ${undefined}  | ${undefined}   | ${'searchId'} | ${`(r.jobId LIKE '%${'searchId'}%' OR r.issueReportId LIKE '%${'searchId'}%')`}
      ${['status']} | ${'issueType'} | ${undefined}  | ${`r.status IN ('${'status'}') AND r.issueReportType = 'issueType'`}
      ${['status']} | ${undefined}   | ${'searchId'} | ${`r.status IN ('${'status'}') AND (r.jobId LIKE '%${'searchId'}%' OR r.issueReportId LIKE '%${'searchId'}%')`}
      ${undefined}  | ${'issueType'} | ${'searchId'} | ${`r.issueReportType = 'issueType' AND (r.jobId LIKE '%${'searchId'}%' OR r.issueReportId LIKE '%${'searchId'}%')`}
      ${''}         | ${''}          | ${''}         | ${`r.branchId = 'branchId'`}
    `(
      'buildSearchQuery',
      async ({ status, searchId, issueType, expectResult }) => {
        const mockQueryBuilder = {
          andWhere: jest.fn(),
        } as unknown as any;
        const mockRequest = {
          query: {
            status,
            searchId,
            issueType,
          },
        } as unknown as any;
        const result = adminIssueReportsService.buildSearchQuery(
          mockRequest,
          mockQueryBuilder,
        );
        if (status || searchId || issueType) {
          expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(expectResult);
        }
        expect(result).toBeDefined();
      },
    );
  });

  describe('Patch Issue Report Email Change Rejected', () => {
    const mockUser: WithUserContext = {
      userKey: 'userKey',
      company: 'companyId',
    } as WithUserContext;

    const body = {
      remark: 'remark',
    };

    const mockMonthYear = DateTime.now().toFormat('yyMM');
    const mockIssueReport = {
      issueReportId: `0000000111-${mockMonthYear}-0001`,
      companyId: 'comId',
      status: IssueReportStatus.PENDING,
    } as IssueReportEntity;

    it('should error issue report not found', async () => {
      jest
        .spyOn(adminIssueReportRepository, 'findOneBy')
        .mockResolvedValue(null);
      try {
        await adminIssueReportsService.patchEmailChangeRejected(
          mockIssueReport.issueReportId,
          body,
          mockUser,
        );
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.NOT_FOUND_DATA.code,
        );
        expect((error as BaseExceptionService).message).toBe(
          BASE_EXCEPTIONS.NOT_FOUND_DATA.message,
        );
        expect((error as BaseExceptionService).data).toBe(
          'Issue report not found',
        );
      }
    });

    it('should error issue report invalid status', async () => {
      const mockIssueReportInvalidStatus = {
        ...mockIssueReport,
        status: IssueReportStatus.REJECTED,
      };
      jest
        .spyOn(adminIssueReportRepository, 'findOneBy')
        .mockResolvedValue(mockIssueReportInvalidStatus);
      try {
        await adminIssueReportsService.patchEmailChangeRejected(
          mockIssueReport.issueReportId,
          body,
          mockUser,
        );
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.INVALID_ISSUE_STATUS_TO_UPDATE.code,
        );
        expect((error as BaseExceptionService).message).toBe(
          BASE_EXCEPTIONS.INVALID_ISSUE_STATUS_TO_UPDATE.message,
        );
      }
    });

    it('should success patch', async () => {
      jest
        .spyOn(adminIssueReportRepository, 'findOneBy')
        .mockResolvedValue(mockIssueReport);

      const result = await adminIssueReportsService.patchEmailChangeRejected(
        mockIssueReport.issueReportId,
        body,
        mockUser,
      );

      expect(result).toBeNull();
    });
  });
  describe('approveEmailChange', () => {
    it('should error issue not found', async () => {
      const id = 'id';
      const user = {
        userId: 'userId',
        companyId: 'companyId',
      } as any;
      jest.spyOn(adminIssueReportRepository, 'findOne').mockResolvedValue(null);
      try {
        await adminIssueReportsService.approveEmailChange(id, user);
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.NOT_FOUND_DATA.code,
        );
        expect((error as BaseExceptionService).data).toBe(
          'Issue report not found',
        );
      }
    });
    it('should error invalid issue status to update', async () => {
      const id = 'id';
      const user = {
        userId: 'userId',
        companyId: 'companyId',
      } as any;
      jest.spyOn(adminIssueReportRepository, 'findOne').mockResolvedValue({
        status: 'status',
      } as any);
      try {
        await adminIssueReportsService.approveEmailChange(id, user);
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.INVALID_ISSUE_STATUS_TO_UPDATE.code,
        );
      }
    });
    it('should error contract not found', async () => {
      const id = 'id';
      const user = {
        userId: 'userId',
        companyId: 'companyId',
      } as any;
      const issueReport = {
        status: '00_PENDING',
        jobId: 'jobId',
      } as any;
      jest
        .spyOn(adminIssueReportRepository, 'findOne')
        .mockResolvedValue(issueReport);
      jest.spyOn(contractRepository, 'findOne').mockResolvedValue(null);
      try {
        await adminIssueReportsService.approveEmailChange(id, user);
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.NOT_FOUND_DATA.code,
        );
      }
    });
    it('should error job not found', async () => {
      const id = 'id';
      const user = {
        userId: 'userId',
        companyId: 'companyId',
      } as any;
      const issueReport = {
        status: '00_PENDING',
        jobId: 'jobId',
        data: {
          newEmail: 'newEmail',
        },
      } as any;
      const contract = {
        customerInfo: {
          email: 'email',
        },
        updatedBy: 'updatedBy',
        updatedAt: new Date(),
      } as any;
      jest
        .spyOn(adminIssueReportRepository, 'findOne')
        .mockResolvedValue(issueReport);
      jest.spyOn(contractRepository, 'findOne').mockResolvedValue(contract);
      jest.spyOn(jobRepository, 'findOne').mockResolvedValue(null);
      try {
        await adminIssueReportsService.approveEmailChange(id, user);
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.NOT_FOUND_DATA.code,
        );
      }
    });

    it('should approve email change', async () => {
      const id = 'id';
      const user = {
        userId: 'userId',
        companyId: 'companyId',
      } as any;
      const issueReport = {
        status: '00_PENDING',
        jobId: 'jobId',
        data: {
          newEmail: 'newEmail',
        },
      } as any;
      const contract = {
        customerInfo: {
          email: 'email',
        },
      } as any;
      jest
        .spyOn(adminIssueReportRepository, 'findOne')
        .mockResolvedValue(issueReport);

      jest.spyOn(contractRepository, 'findOne').mockResolvedValue(contract);
      await adminIssueReportsService.approveEmailChange(id, user);
      expect(adminIssueReportRepository.save).toHaveBeenCalled();
    });
  });
});
