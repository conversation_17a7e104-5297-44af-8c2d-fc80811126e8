import { getRepositoryToken } from '@nestjs/typeorm';
import { Test } from '@nestjs/testing';
import {
  PermissionEntity,
  RoleEntity,
  UserRoleBranchEntity,
  RolePermissionEntity,
  PermissionGroupEntity,
  SiteType,
  GeneralActivitiesEntity,
} from '../../src/entities';
import { BaseExceptionModule } from '../../src/base-exceptions/base-exceptions.module';
import { BaseExceptionService } from '../../src/exceptions';
import { BASE_EXCEPTIONS } from '../../src/config';
import { AdminRolesService } from '../../src/admin/roles/roles.service';
import { WithUserContext } from '../../src/interfaces';
import { EntityManager, Repository } from 'typeorm';
import { mock } from 'ts-mockito';
import { mockRole, mockRoleUpdate } from '../mock-data/role';

const manager = {
  transaction: jest.fn().mockReturnThis(),
  getRepository: jest.fn().mockReturnThis(),
  connection: {
    createQueryRunner: jest.fn().mockReturnThis(),
    connect: jest.fn().mockReturnThis(),
    startTransaction: jest.fn().mockReturnThis(),
    release: jest.fn().mockReturnThis(),
    rollbackTransaction: jest.fn().mockReturnThis(),
    commitTransaction: jest.fn().mockReturnThis(),
    manager: {
      getRepository: jest.fn().mockReturnThis(),
      createQueryBuilder: jest.fn().mockReturnThis(),
      select: jest.fn().mockReturnThis(),
      update: jest.fn().mockReturnThis(),
      where: jest.fn().mockReturnThis(),
      andWhere: jest.fn().mockReturnThis(),
      insert: jest.fn().mockReturnThis(),
      orIgnore: jest.fn().mockReturnThis(),
      values: jest.fn().mockReturnThis(),
      save: jest.fn().mockReturnThis(),
      execute: jest.fn().mockResolvedValue({ raw: [] }),
      delete: jest.fn().mockReturnThis(),
      count: jest.fn(),
    },
  },
};

describe('AdminRolesService', () => {
  let rolesService: AdminRolesService;
  let mockEntityManager: EntityManager;
  let roleRepository: Repository<RoleEntity>;
  let permissionRepository: Repository<PermissionEntity>;
  let rolePermissionRepository: Repository<RolePermissionEntity>;
  let generalActivitiesRepository: Repository<GeneralActivitiesEntity>;

  const mockQueryBuilder: any = {
    select: jest.fn().mockReturnThis(),
    addSelect: jest.fn().mockReturnThis(),
    leftJoin: jest.fn().mockReturnThis(),
    where: jest.fn().mockReturnThis(),
    orderBy: jest.fn().mockReturnThis(),
    groupBy: jest.fn().mockReturnThis(),
    getRawMany: jest.fn(),
    getRawOne: jest.fn(),
  };

  const mockUserContext: WithUserContext = {
    userKey: '<EMAIL>',
    company: 'WW',
    idToken: 'test1234',
  };

  beforeEach(async () => {
    mockEntityManager = mock(EntityManager);
    const module = await Test.createTestingModule({
      imports: [BaseExceptionModule.register()],
      providers: [
        AdminRolesService,
        {
          provide: getRepositoryToken(RoleEntity),
          useValue: {
            find: jest.fn().mockReturnThis(),
            findOne: jest.fn().mockReturnThis(),
            createQueryBuilder: jest.fn().mockReturnValue(mockQueryBuilder),
            manager,
          },
        },
        {
          provide: getRepositoryToken(UserRoleBranchEntity),
          useValue: {
            createQueryBuilder: jest.fn().mockReturnValue(mockQueryBuilder),
          },
        },
        {
          provide: getRepositoryToken(PermissionEntity),
          useValue: {
            find: jest.fn().mockReturnThis(),
          },
        },
        {
          provide: getRepositoryToken(RolePermissionEntity),
          useValue: {
            save: jest.fn().mockReturnThis(),
          },
        },
        {
          provide: getRepositoryToken(GeneralActivitiesEntity),
          useValue: {
            save: jest.fn().mockReturnThis(),
          },
        },
      ],
    }).compile();

    rolesService = module.get<AdminRolesService>(AdminRolesService);
    roleRepository = module.get<Repository<RoleEntity>>(
      getRepositoryToken(RoleEntity),
    );
    rolePermissionRepository = module.get<Repository<RolePermissionEntity>>(
      getRepositoryToken(RolePermissionEntity),
    );
    permissionRepository = module.get<Repository<PermissionEntity>>(
      getRepositoryToken(PermissionEntity),
    );

    generalActivitiesRepository = module.get<
      Repository<GeneralActivitiesEntity>
    >(getRepositoryToken(GeneralActivitiesEntity));
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('Get Roles', () => {
    const mockData = [
      {
        createdAt: '2024-12-03T06:14:30.509Z',
        updatedAt: '2024-12-03T05:14:30.509Z',
        companyId: 'WW',
        roleId: 'HERWTWEFWEF',
        roleName: 'FS_1',
        createdBy: 'TESTB',
        updatedUser: 'thee',
        count: '2',
      },
      {
        createdAt: '2024-12-03T05:14:30.509Z',
        updatedAt: '2024-12-03T05:14:30.509Z',
        companyId: 'WW',
        roleId: 'ERGERGGFGL',
        roleName: 'CMS_2',
        createdBy: 'TESTB',
        updatedUser: 'thee',
        count: '1',
      },
    ];

    it('should response roles with data', async () => {
      const query = {
        orderBy: 'roleId asc',
      };

      mockQueryBuilder.getRawMany.mockResolvedValueOnce(mockData);
      const result = await rolesService.getRoles({ companyId: 'WW', query });
      expect(result).not.toBeNull();
    });

    it('should response roles (orderBy)', async () => {
      const query = {};

      mockQueryBuilder.getRawMany.mockResolvedValueOnce([]);
      const result = await rolesService.getRoles({ companyId: 'WW', query });
      expect(result).toStrictEqual([]);
    });

    it('should response roles (wrong orderBy column)', async () => {
      const query = { orderBy: 'wrongColumn ascd' };

      mockQueryBuilder.getRawMany.mockResolvedValueOnce([]);
      const result = await rolesService.getRoles({ companyId: 'WW', query });
      expect(result).toStrictEqual([]);
    });
  });

  describe('Get Roles Config', () => {
    it('should response roles with data', async () => {
      const expectedResult = [
        { roleId: 'HERWTWEFWEF', roleName: 'FS_1', type: 'FRONTSHOP' },
        { roleId: 'ROLE_ID_2', roleName: 'CMS_1', type: 'CMS' },
      ];

      const mockNewRole = {
        ...mockRole,
        roleName: 'CMS_1',
        roleId: 'ROLE_ID_2',
        type: 'CMS' as SiteType,
      };
      jest
        .spyOn(roleRepository, 'find')
        .mockResolvedValueOnce([mockRole, mockNewRole]);
      const result = await rolesService.getRolesConfig('WW');

      expect(result).toStrictEqual(expectedResult);
    });
  });

  describe('Get Role By Id', () => {
    it('should response role with data', async () => {
      jest.spyOn(roleRepository, 'findOne').mockResolvedValueOnce(mockRole);
      mockQueryBuilder.getRawOne.mockResolvedValueOnce({ count: 1 });
      const result = await rolesService.getRoleById({
        companyId: 'WW',
        id: 'HERWTWEFWEF',
      });
      expect(result).not.toBeNull();
    });

    it('should response role (not found)', async () => {
      jest.spyOn(roleRepository, 'findOne').mockResolvedValueOnce(null);
      mockQueryBuilder.getRawOne.mockResolvedValueOnce({ count: 0 });

      try {
        await rolesService.getRoleById({
          companyId: 'WW',
          id: 'HERWTWEFWEF2',
        });
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.NOT_FOUND_DATA.code,
        );
        expect((error as BaseExceptionService).message).toBe(
          BASE_EXCEPTIONS.NOT_FOUND_DATA.message,
        );
      }
    });
  });

  describe('Create Role', () => {
    const mockUser = {
      company: 'test-company',
      userKey: 'test-user-key',
      name: 'test-name',
    } as WithUserContext;

    const mockPermission = {
      permissionId: 'permission-0001',
      view: true,
      create: true,
      update: true,
      delete: false,
      download: false,
      upload: false,
      permissionGroup: {
        companyId: mockUser.company,
        permissionGroupId: 'group-0001',
        type: 'CMS',
      } as PermissionGroupEntity,
    } as PermissionEntity;

    const mockInput = {
      name: 'ROLE_1',
      type: 'CMS',
      permissions: [
        {
          permissionId: 'permission-0001',
          view: true,
          create: true,
          update: true,
          delete: false,
          download: false,
          upload: false,
        },
      ],
    } as any;
    const mockInputUpdateNoType = {
      name: 'ROLE_1',
      permissions: [
        {
          permissionId: 'permission-0001',
          view: true,
          create: true,
          update: true,
          delete: false,
          download: false,
          upload: false,
        },
      ],
    } as any;

    it('should success save', async () => {
      jest.spyOn(roleRepository, 'findOne').mockResolvedValueOnce(null);
      jest
        .spyOn(permissionRepository, 'find')
        .mockResolvedValueOnce([mockPermission]);
      await rolesService.createRoles({
        user: mockUser,
        body: mockInput,
      });

      expect(manager.connection.manager.save).toHaveBeenCalled();
      expect(manager.connection.commitTransaction).toHaveBeenCalled();
      expect(manager.connection.release).toHaveBeenCalled();
      expect(generalActivitiesRepository.save).toHaveBeenCalledTimes(1);
    });

    it('should error role already exists', async () => {
      jest.spyOn(roleRepository, 'findOne').mockResolvedValueOnce(mockInput);

      try {
        await rolesService.validateRolePermission(mockUser.company, mockInput);
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.ROLE_NAME_ALREADY_EXISTS.code,
        );
        expect((error as BaseExceptionService).message).toBe(
          BASE_EXCEPTIONS.ROLE_NAME_ALREADY_EXISTS.message,
        );
      }
    });
    it('should error role already exists (update role)', async () => {
      jest.spyOn(roleRepository, 'findOne').mockResolvedValueOnce(mockRole);
      try {
        await rolesService.validateRolePermission(
          mockUser.company,
          mockInputUpdateNoType,
          'HERWTWEFWEF2',
          'CMS' as SiteType,
        );
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.ROLE_NAME_ALREADY_EXISTS.code,
        );
        expect((error as BaseExceptionService).message).toBe(
          BASE_EXCEPTIONS.ROLE_NAME_ALREADY_EXISTS.message,
        );
      }
    });

    it('should error invalid permissionId (not found permission)', async () => {
      jest.spyOn(roleRepository, 'findOne').mockResolvedValueOnce(null);
      jest.spyOn(permissionRepository, 'find').mockResolvedValueOnce([]);
      try {
        await rolesService.validateRolePermission(mockUser.company, mockInput);
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.BODY_PAYLOAD_INVALID.code,
        );
        expect((error as BaseExceptionService).message).toBe(
          BASE_EXCEPTIONS.BODY_PAYLOAD_INVALID.message,
        );
        expect((error as BaseExceptionService).data).toBe(
          'Invalid permissionId',
        );
      }
    });

    it('should error invalid permissionId (wrong type)', async () => {
      const mockInputError = {
        ...mockInput,
        type: 'WRONG_TYPE',
      };
      jest.spyOn(roleRepository, 'findOne').mockResolvedValueOnce(null);
      jest
        .spyOn(permissionRepository, 'find')
        .mockResolvedValueOnce([mockPermission]);
      try {
        await rolesService.validateRolePermission(
          mockUser.company,
          mockInputError,
        );
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.BODY_PAYLOAD_INVALID.code,
        );
        expect((error as BaseExceptionService).message).toBe(
          BASE_EXCEPTIONS.BODY_PAYLOAD_INVALID.message,
        );
        expect((error as BaseExceptionService).data).toBe(
          'Invalid permissionId',
        );
      }
    });

    it('should error invalid permission action', async () => {
      const mockInputError = {
        ...mockInput,
        permissions: [
          {
            permissionId: 'permission-0001',
            view: true,
            create: true,
            update: true,
            delete: true,
            download: true,
            upload: true,
          },
        ],
      };
      jest.spyOn(roleRepository, 'findOne').mockResolvedValueOnce(null);
      jest
        .spyOn(permissionRepository, 'find')
        .mockResolvedValueOnce([mockPermission]);
      try {
        await rolesService.validateRolePermission(
          mockUser.company,
          mockInputError,
        );
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.BODY_PAYLOAD_INVALID.code,
        );
        expect((error as BaseExceptionService).message).toBe(
          BASE_EXCEPTIONS.BODY_PAYLOAD_INVALID.message,
        );
        expect((error as BaseExceptionService).data).toBe(
          'Invalid permission action',
        );
      }
    });
  });

  describe('Update Role', () => {
    const mockUser = {
      company: 'test-company',
      userKey: 'test-user-key',
      name: 'test-name',
    } as WithUserContext;
    const mockPermission = {
      permissionId: 'permission-0001',
      view: true,
      create: true,
      update: true,
      delete: false,
      download: false,
      upload: false,
      permissionGroup: {
        companyId: mockUser.company,
        permissionGroupId: 'group-0001',
        type: 'CMS',
      } as PermissionGroupEntity,
    } as PermissionEntity;

    const mockInput = {
      name: 'ROLE_1',
      type: 'CMS',
      permissions: [
        {
          permissionId: 'permission-0001',
          view: true,
          create: true,
          update: true,
          delete: false,
          download: false,
          upload: false,
        },
      ],
    } as any;
    it('should success update', async () => {
      const id = 'TEST-1';

      jest
        .spyOn(roleRepository, 'findOne')
        .mockResolvedValueOnce(mockRoleUpdate);
      jest
        .spyOn(roleRepository, 'findOne')
        .mockResolvedValueOnce(mockRoleUpdate);
      jest
        .spyOn(permissionRepository, 'find')
        .mockResolvedValueOnce([mockPermission]);
      const result = await rolesService.updateRole({
        user: mockUser,
        id,
        body: mockInput,
      });

      expect(result).not.toBeNull();
    });
    it('should error id not found', async () => {
      const id = 'TEST-1';
      jest.spyOn(roleRepository, 'findOne').mockResolvedValueOnce(null);
      try {
        await rolesService.updateRole({
          user: mockUser,
          id,
          body: mockInput,
        });
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.NOT_FOUND_DATA.code,
        );
      }
    });
  });
});
