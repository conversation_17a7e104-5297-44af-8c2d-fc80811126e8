import {
  CompanyEntity,
  PermissionEntity,
  SiteType,
  RoleEntity,
  PermissionGroupEntity,
  RolePermissionEntity,
} from '../../src/entities';

export const mockCompany: CompanyEntity = {
  companyId: '123',
  title: 'company title',
  logoUrl: 'company logo url',
  createdBy: 'company created by',
  userKeyClaimFnName: 'company user key claim fn name',
  empUploadMapperFnName: 'company emp upload mapper fn name',
  createdAt: new Date(),
  updatedAt: new Date(),
  logoPath: 'company logo path',
};

export const mockPermissionList: PermissionEntity[] = [
  {
    companyId: 'companyId',
    permissionId: 'permissionId',
    label: 'label',
    permissionGroupId: 'permissionGroupId',
    sortIndex: 0,
    iconName: 'iconName',
    view: false,
    create: false,
    update: false,
    delete: false,
    download: false,
    upload: false,
    company: mockCompany,
    permissionGroup: new PermissionGroupEntity(),
    rolePermissions: [],
    createdAt: new Date(),
  },
];

export const mockRolePermissionList: RolePermissionEntity[] = [
  {
    companyId: 'companyId',
    roleId: 'roleId',
    permissionId: 'permissionId',
    company: mockCompany,
    role: new RoleEntity(),
    permission: new PermissionEntity(),
    createdAt: new Date(),
    updatedAt: new Date(),
    view: false,
    create: false,
    update: false,
    delete: false,
    download: false,
    upload: false,
  },
];

export const mockRole: RoleEntity = {
  createdAt: new Date(),
  updatedAt: new Date(),
  companyId: 'WW',
  company: mockCompany,
  roleId: 'HERWTWEFWEF',
  roleName: 'FS_1',
  type: 'FRONTSHOP' as SiteType,
  createdBy: 'TESTB',
  updatedBy: 'TESTB',
  rolePermissions: mockRolePermissionList,
};

export const mockRoleUpdate: RoleEntity = {
  createdAt: new Date(),
  updatedAt: new Date(),
  companyId: 'WW',
  company: mockCompany,
  roleId: 'TEST-1',
  roleName: 'FS_1',
  type: 'FRONTSHOP' as SiteType,
  createdBy: 'TESTB',
  updatedBy: 'TESTB',
  rolePermissions: mockRolePermissionList,
};
