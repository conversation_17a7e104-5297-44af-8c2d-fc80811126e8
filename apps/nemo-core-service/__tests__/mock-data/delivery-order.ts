import {
  DeliveryOrderEntity,
  BranchEntity,
  CompanyEntity,
} from '../../src/entities';

import { mockJob } from './job';
import { mockUserShopEntity } from './user';

export const mockDO = [
  {
    createdAt: new Date(),
    updatedAt: new Date(),
    deliveryOrderId: '2024020001',
    companyId: 'WW',
    branchId: '80000430',
    quantity: 1,
    status: '00_APPOINTMENT_PENDING',
    senderUserKey: 'test',
    senderMobileNumber: '0999999999',
    shopUserKey: 'test',
    jobs: [mockJob],
    branch: {
      branchId: 'branchId',
      companyId: 'companyId',
      title: 'title',
    } as BranchEntity,
    shopUser: mockUserShopEntity,
    senderUser: mockUserShopEntity,
    senderUserCompanyId: 'WW',
    company: {
      companyId: 'companyId',
      title: 'title',
      logoUrl: 'logoUrl',
    } as CompanyEntity,
  },
] as DeliveryOrderEntity[];
