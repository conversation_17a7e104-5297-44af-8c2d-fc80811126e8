export const generateAvgCostDataListMock = (total: number) => {
  const mock: any[] = [];
  for (let i = 1; i <= total; i++) {
    mock.push({
      modelKey: 'zzzzz' + i,
      matCodeSale: 'xxxxx' + i,
      brand: 'aaa',
      model: 'bbb',
      rom: 'ccc',
      insuranceCost: (1000 + i).toString(),
      averageRetailCostAA: i.toString(),
      averageWholeSaleCostAA: i.toString(),
      averageRetailCostBB: i.toString(),
      averageWholeSaleCostBB: i.toString(),
      averageRetailCostCC: i.toString(),
      averageWholeSaleCostCC: i.toString(),
      averageRetailCostAD: i.toString(),
      averageWholeSaleCostAD: i.toString(),
      averageRetailCostBD: i.toString(),
      averageWholeSaleCostBD: i.toString(),
      averageRetailCostCD: i.toString(),
      averageWholeSaleCostCD: i.toString(),
      averageRetailCostDD: i.toString(),
      averageWholeSaleCostDD: i.toString(),
    });
  }
  return mock;
};
