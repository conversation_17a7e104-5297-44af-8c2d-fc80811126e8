import { WithUserContext } from 'src/interfaces';
import {
  AllocationOrderEntity,
  AllocationOrderStatus,
  CompanyEntity,
  JobEntity,
} from '../../src/entities';

import { mockInspectionJob, mockInspectionJobWithUpdate } from './job';
import { mockUserShopEntity } from './user';
import { UserEntity } from '../../src/entities';

const currentDate = new Date();
const year = currentDate.getFullYear();
const month = (currentDate.getMonth() + 1).toString().padStart(2, '0');
export const mockAO = [
  {
    createdAt: new Date(),
    updatedAt: new Date(),
    allocationOrderId: `AO-${year}${month}0001`,
    companyId: 'WW',
    fromBranchId: '80000430',
    toBranchId: '80000548',
    quantity: 0,
    status: '00_DRAFT',
    createdBy: 'test-user-key',
    createdUser: mockUserShopEntity,
    company: {
      companyId: 'companyId',
      title: 'title',
      logoUrl: 'logoUrl',
    } as CompanyEntity,
  },
  {
    createdAt: new Date(),
    updatedAt: new Date(),
    allocationOrderId: `AO-${year}${month}0002`,
    companyId: 'WW',
    fromBranchId: '80000430',
    toBranchId: '80000548',
    quantity: 0,
    status: '00_DRAFT',
    createdBy: 'test',
    createdUser: mockUserShopEntity,
    company: {
      companyId: 'companyId',
      title: 'title',
      logoUrl: 'logoUrl',
    } as CompanyEntity,
  },
  {
    createdAt: new Date(),
    updatedAt: new Date(),
    allocationOrderId: `AO-${year}${month}0003`,
    companyId: 'WW',
    fromBranchId: '80000430',
    toBranchId: '80000548',
    quantity: 0,
    status: '00_DRAFT',
    createdBy: 'test',
    createdUser: mockUserShopEntity,
    company: {
      companyId: 'companyId',
      title: 'title',
      logoUrl: 'logoUrl',
    } as CompanyEntity,
  },
  {
    createdAt: new Date(),
    updatedAt: new Date(),
    allocationOrderId: `AO-2023010001`,
    companyId: 'WW',
    fromBranchId: '80000430',
    toBranchId: '80000548',
    quantity: 0,
    status: '00_DRAFT',
    createdBy: 'test',
    createdUser: mockUserShopEntity,
    company: {
      companyId: 'companyId',
      title: 'title',
      logoUrl: 'logoUrl',
    } as CompanyEntity,
  },
  {
    createdAt: new Date(),
    updatedAt: new Date(),
    allocationOrderId: `AO-2023010001`,
    companyId: 'WW',
    fromBranchId: '80000430',
    toBranchId: '80000548',
    quantity: 0,
    status: '05_APPOINTMENT_PENDING',
    createdBy: 'test-user-key',
    createdUser: mockUserShopEntity,
    company: {
      companyId: 'companyId',
      title: 'title',
      logoUrl: 'logoUrl',
    } as CompanyEntity,
    jobs: mockInspectionJob,
  },
] as AllocationOrderEntity[];

export const mockAOPending = {
  createdAt: new Date(),
  updatedAt: new Date(),
  allocationOrderId: `AO-${year}${month}0001`,
  companyId: 'WW',
  fromBranchId: '80000430',
  toBranchId: '80000548',
  quantity: 0,
  status: AllocationOrderStatus.APPOINTMENT_PENDING,
  createdBy: 'test',
  createdUser: mockUserShopEntity,
  company: {
    companyId: 'companyId',
    title: 'title',
    logoUrl: 'logoUrl',
  } as CompanyEntity,
} as AllocationOrderEntity;

export const mockAODraft = {
  createdAt: new Date(),
  updatedAt: new Date(),
  allocationOrderId: `AO-${year}${month}0001`,
  companyId: 'WW',
  fromBranchId: '80000430',
  toBranchId: '80000548',
  quantity: 0,
  status: AllocationOrderStatus.DRAFT,
  createdBy: 'test',
  createdUser: mockUserShopEntity,
  company: {
    companyId: 'companyId',
    title: 'title',
    logoUrl: 'logoUrl',
  } as CompanyEntity,
} as AllocationOrderEntity;

export const mockCreatedUser: WithUserContext = {
  userKey: 'created-user-key',
  name: 'test-name',
  company: 'WW',
} as WithUserContext;

export const mockCreatedUserEntity = {
  name: mockCreatedUser.name,
  userKey: mockCreatedUser.company,
  companyId: 'WW',
  roles: [
    {
      branchId: 'branchId#1',
      role: [
        'Admin',
        'AdminPriceEstimator',
        'AdminReceive',
        'AdminQC',
        'AdminRepair',
        'AdminSupplyChain',
        'AdminInspection',
        'AdminProduct',
      ],
    },
  ],
} as UserEntity;

export const mockIdAO = 'mockIdAo';

export const mockCompany: CompanyEntity = {
  companyId: '123',
  title: 'company title',
  logoUrl: 'company logo url',
  createdBy: 'company created by',
  userKeyClaimFnName: 'company user key claim fn name',
  empUploadMapperFnName: 'company emp upload mapper fn name',
  createdAt: new Date(),
  updatedAt: new Date(),
  logoPath: 'company logo path',
};

export const getMockJobs = (
  count: number,
  allocationOrderId: string,
  toUpdateJob: any,
): JobEntity[] => {
  return Array(count)
    .fill({ ...mockInspectionJobWithUpdate, allocationOrderId } as JobEntity)
    .map((item: JobEntity) => {
      const job: JobEntity = { ...item, ...toUpdateJob };
      return job;
    });
};
