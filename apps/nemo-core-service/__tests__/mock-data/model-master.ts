import { ModelMasterEntity } from '../../src/entities';

export const mockModelMasters = {
  companyId: 'WW',
  modelKey: 'apple|iphone 14 plus|128gb',
  modelIdentifiers: {
    rom: '128GB',
    brand: 'Apple',
    model: 'iPhone 14 Plus',
  },
  templateId: 'v1',
  modelMasterGrades: [
    {
      grade: 'A',
      purchasePrice: '37900.00',
      lastPurchasedOn: 'ISO8601',
      lastPurchasedPrice: 'Grade Info',
    },
    {
      grade: 'B',
      purchasePrice: '37900.00',
      lastPurchasedOn: 'ISO8601',
      lastPurchasedPrice: 'Grade Info',
    },
    {
      grade: 'C',
      purchasePrice: '37900.00',
      lastPurchasedOn: 'ISO8601',
      lastPurchasedPrice: 'Grade Info',
    },
    {
      grade: 'D',
      purchasePrice: '37900.00',
      lastPurchasedOn: 'ISO8601',
      lastPurchasedPrice: 'Grade Info',
    },
  ],
  createdBy: 'system',
  updatedBy: 'system',
} as ModelMasterEntity;
