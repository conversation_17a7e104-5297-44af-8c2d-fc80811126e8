import { CompanyRoleEntity, UserEntity } from '../../src/entities';

export const mockUserEntity = {
  name: 'test',
  userKey: 'test',
  companyId: 'ww',
  roles: [
    {
      branchId: '111',
      role: ['admin'],
    },
  ],
  userType: 'WW',
} as UserEntity;

export const mockUserShopEntity = {
  name: 'test',
  userKey: 'test',
  companyId: 'WW',
  roles: [
    {
      branchId: 'branchId#1',
      role: ['Sale'],
    },
  ],
  userType: 'WW',
} as UserEntity;

export const mockCustomUserQueryResult = [
  {
    userKey: 'key1',
    name: 'ชื่อ1 นามสกุล1',
    nameEng: 'name1 lastname1',
    updatedAt: 'updatedAt',
    roles: {
      branchId: 'branchId#1',
      role: ['Manager', 'Sale'],
    },
  },
  {
    userKey: 'key2',
    name: 'ชื่อ2 นามสกุล2',
    nameEng: 'name2 lastname2',
    updatedAt: 'updatedAt',
    roles: {
      branchId: 'branchId#noId',
      role: [],
    },
  },
  {
    userKey: 'key3',
    name: 'ชื่อ3 นามสกุล3',
    nameEng: 'name3 lastname3',
    updatedAt: 'updatedAt',
    roles: {
      branchId: '',
    },
  },
];

export const mockAfterLoadResult = [
  {
    userKey: 'key1',
    name: 'ชื่อ1 นามสกุล1',
    nameEng: 'name1 lastname1',
    updatedAt: 'updatedAt',
    branchId: 'branchId#1',
    branchTitle: 'title#1',
    roles: ['Manager', 'Sale'],
  },
  {
    userKey: 'key2',
    name: 'ชื่อ2 นามสกุล2',
    nameEng: 'name2 lastname2',
    updatedAt: 'updatedAt',
    branchId: '-',
    branchTitle: '-',
    roles: [],
  },
  {
    userKey: 'key3',
    name: 'ชื่อ3 นามสกุล3',
    nameEng: 'name3 lastname3',
    updatedAt: 'updatedAt',
    branchId: '-',
    branchTitle: '-',
    roles: [],
  },
];

export const mockCompanyRole = [
  {
    role: ['role1', 'role3'],
  },
  {
    role: ['role1', 'role2'],
  },
  {
    role: ['role1'],
  },
  {
    role: ['role1', 'role2', 'role3'],
  },
] as CompanyRoleEntity[];

export const mockCompanyRoleWithCustomLabel = [
  {
    role: ['Sale', 'role3'],
  },
  {
    role: ['Sale', 'role2'],
  },
  {
    role: ['Sale', 'Manager'],
  },
  {
    role: ['Sale', 'role2', 'role3'],
  },
] as CompanyRoleEntity[];

const nowDate = new Date();

export const mockuserAfterLoadNew = [
  {
    updatedAt: nowDate,
    companyId: 'WW',
    userKey: '<EMAIL>',
    name: 'postman',
    nameEng: null,
    userRoleBranch: [
      {
        branch: {
          branchId: 'branchId#1',
          title: 'branchName#1',
        },
        role: {
          roleId: 'role1',
          roleName: 'roleName#1',
          type: 'FRONTSHOP',
        },
      },
    ],
    email: '<EMAIL>',
  },
  {
    updatedAt: nowDate,
    companyId: 'WW',
    userKey: '<EMAIL>',
    name: 'postman2',
    nameEng: null,
    userRoleBranch: [
      {
        branch: {
          branchId: 'branchId#2',
          title: 'branchName#2',
        },
        role: {
          roleId: 'role2',
          roleName: 'roleName#2',
          type: 'FRONTSHOP',
        },
      },
      {
        branch: {
          branchId: 'ADMIN_BRANCH',
          title: 'ADMIN_BRANCH',
        },
        role: {
          roleId: 'role3',
          roleName: 'roleName#3',
          type: 'CMS',
        },
      },
      {
        branch: {
          branchId: 'branchId#4',
          title: 'branchName#4',
        },
        role: {
          roleId: 'SALES',
          roleName: 'SALES',
          type: 'FRONTSHOP',
        },
      },
      {
        branch: {
          branchId: 'branchId#5',
          title: 'branchName#5',
        },
        role: {
          roleId: 'MANAGER',
          roleName: 'MANAGER',
          type: 'FRONTSHOP',
        },
      },
    ],
    email: '<EMAIL>',
  },
];
export const mockuserAfterLoadResp = [
  {
    userKey: '<EMAIL>',
    name: 'postman',
    nameEng: '',
    updatedAt: nowDate.toISOString(),
    branchIds: ['branchId#1'],
    branchTitles: ['branchName#1'],
    roles: ['roleName#1'],
    email: '<EMAIL>',
  },
  {
    userKey: '<EMAIL>',
    name: 'postman2',
    nameEng: '',
    updatedAt: nowDate.toISOString(),
    branchIds: ['branchId#2', 'branchId#4', 'branchId#5'],
    branchTitles: ['branchName#2', 'branchName#4', 'branchName#5'],
    roles: ['roleName#2', 'SALES', 'MANAGER', 'roleName#3'],
    email: '<EMAIL>',
  },
];

export const mockuserAfterLoadNew2 = [
  {
    updatedAt: nowDate,
    companyId: 'WW',
    userKey: '<EMAIL>',
    name: 'postman',
    nameEng: null,
    userRoleBranch: [],
    email: '<EMAIL>',
  },
];

export const mockuserAfterLoadResp2 = [
  {
    userKey: '<EMAIL>',
    name: 'postman',
    nameEng: '',
    updatedAt: nowDate.toISOString(),
    branchIds: [],
    branchTitles: [],
    roles: [],
    email: '<EMAIL>',
  },
];
