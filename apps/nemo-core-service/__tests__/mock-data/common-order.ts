import { CreateOrderDto } from 'src/common/dto/create-order.dto';
import {
  BranchEntity,
  ChecklistType,
  CompanyEntity,
  EstimationActivitiesEntity,
  IModelChecklistResultItemModule,
  IModelChecklistResultItemQuestion,
  IQuestionChoice,
  ModelChecklistEntity,
  ModelMasterColorEntity,
  ModelMasterEntity,
  ModelMasterFunctionEntity,
  QuestionType,
} from '../../src/entities';
import { ILanguage } from 'src/utils/general';

// --- prompt relate entity --- //
export const mockCompanyId = 'mock-company-id';
const mockCompany = { companyId: mockCompanyId } as CompanyEntity;

export const mockColorId = 'mock-color-id';
export const mockColor = {
  id: mockColorId,
  nameEn: 'mock-color name',
  nameTh: 'ชื่อ mock-color',
} as ModelMasterColorEntity;

export const mockModelKey = 'mock-model-key';
export const mockModelMaster = {
  modelKey: mockModelKey,
  systemCode: 'mock-system-code',
  modelImageUrl: 'mock-url',
  modelIdentifiers: {
    brand: 'Apple',
    rom: '128GB', // ห้ามเปลี่ยน
    model: 'iPhone 14 Plus',
  },
  modelMasterGrades: [
    {
      grade: 'A',
      purchasePrice: '20000.00',
      lastPurchasedOn: 'ISO8601',
      lastPurchasedPrice: 'Grade Info',
    },
  ],
} as ModelMasterEntity;

export const mockStoreId = 'mock-store-id';
export const mockStore = {
  branchId: mockStoreId,
  title: 'ชื่อ mock-store',
  titleEn: 'mock-store name',
  imageUrl: 'mock-image-store',
  latitude: 0.5,
  longitude: 0.6,
  addressEn: 'mock-address-En',
  addressTh: 'mock-address-Th',
  provinceEn: 'mock-province-En',
  provinceTh: 'mock-province-Th',
} as BranchEntity;

export const mockPdpaVersion = 1.01;
export const mockLegalDocId = 'mock-legal-doc-id';

// --- modules happy case --- //
const mockRequestModules: {
  moduleId: string;
  status: 'pass' | 'fail' | 'skip';
}[] = [
  {
    moduleId: 'module-1',
    status: 'pass',
  },
  {
    moduleId: 'module-2',
    status: 'fail',
  },
  {
    moduleId: 'module-3',
    status: 'skip',
  },
];

const convertModuleValue = {
  pass: 'functional',
  fail: 'non_functional',
  skip: 'skip',
};

const mockEAModules = [
  {
    modelChecklistId: mockRequestModules[0].moduleId,
    modelChecklistNameEn: 'mock-module-1 name',
    modelChecklistNameTh: 'ชื่อ mock-module-1',
    iconImageUrl: 'mock-module-1-link',
    isRequired: false,
    modelChecklistModuleCode: 'MOCK-MODULE-1',
    moduleStatus: mockRequestModules[0].status,
    penalties: '0',
    isSkip: false,
    functionKeyCond: `functionSection.functionkey=${
      convertModuleValue[mockRequestModules[0].status]
    }`,
  } as IModelChecklistResultItemModule,
  {
    modelChecklistId: mockRequestModules[1].moduleId,
    modelChecklistNameEn: 'mock-module-2 name',
    modelChecklistNameTh: 'ชื่อ mock-module-2',
    iconImageUrl: 'mock-module-2-link',
    isRequired: false,
    modelChecklistModuleCode: 'MOCK-MODULE-2',
    moduleStatus: mockRequestModules[1].status,
    penalties: '-100',
    isSkip: false,
    functionKeyCond: `functionSection.functionkey=${
      convertModuleValue[mockRequestModules[1].status]
    }`,
  } as IModelChecklistResultItemModule,
  {
    modelChecklistId: mockRequestModules[2].moduleId,
    modelChecklistNameEn: 'mock-module-2 name',
    modelChecklistNameTh: 'ชื่อ mock-module-2',
    iconImageUrl: 'mock-module-2-link',
    isRequired: false,
    modelChecklistModuleCode: 'MOCK-MODULE-2',
    moduleStatus: mockRequestModules[2].status,
    penalties: '-50',
    isSkip: true,
    functionKeyCond: `functionSection.functionkey=${
      convertModuleValue[mockRequestModules[2].status]
    }`,
  } as IModelChecklistResultItemModule,
];

const getMockResultModulesByLang = (
  lang: ILanguage,
): {
  id: string;
  name: string;
  code: string;
  iconImageUrl: string;
  status: 'pass' | 'fail' | 'skip';
  isRequired: boolean;
}[] => {
  const nameProp =
    lang === 'th' ? 'modelChecklistNameTh' : 'modelChecklistNameEn';
  return [
    {
      id: mockEAModules[0].modelChecklistId,
      code: mockEAModules[0].modelChecklistModuleCode,
      name: mockEAModules[0][nameProp],
      iconImageUrl: mockEAModules[0].iconImageUrl ?? '',
      status: mockRequestModules[0].status,
      isRequired: false,
    },
    {
      id: mockEAModules[1].modelChecklistId,
      code: mockEAModules[1].modelChecklistModuleCode,
      name: mockEAModules[1][nameProp],
      iconImageUrl: mockEAModules[1].iconImageUrl ?? '',
      status: mockRequestModules[1].status,
      isRequired: false,
    },
    {
      id: mockEAModules[2].modelChecklistId,
      code: mockEAModules[2].modelChecklistModuleCode,
      name: mockEAModules[2][nameProp],
      iconImageUrl: mockEAModules[2].iconImageUrl ?? '',
      status: mockRequestModules[2].status,
      isRequired: false,
    },
  ];
};

// --- questions --- //
const mockQuestionChoicesChecklist = (startAt: number = 1) => {
  const question: any[] = [];
  for (let i = startAt; i < 4; i++) {
    question.push({
      id: `answer-${i}`,
      answerEn: `answer ${i}`,
      answerTh: `คำตอบ ${i}`,
      iconImageUrl: `mock-icon-image-answer-${i}`,
    });
  }
  return question;
};

const mockQuestionChoices: IQuestionChoice[] =
  mockQuestionChoicesChecklist().map((item: any) => ({
    id: item.id,
    choiceNameEn: item.answerEn,
    choiceNameTh: item.answerTh,
    iconImageUrl: item.iconImageUrl,
    isSelected: false,
  }));

const getMockQuestionChoices = (answerIds: string[]) => {
  return mockQuestionChoices.map((item: IQuestionChoice) => ({
    ...item,
    isSelected: answerIds.includes(item.id),
  }));
};
const getMockQuestionChoicesResult = (lang: ILanguage, answerIds: string[]) => {
  const nameProp = lang === 'th' ? 'choiceNameTh' : 'choiceNameEn';
  return mockQuestionChoices.map((item: IQuestionChoice) => ({
    id: item.id,
    iconImageUrl: item.iconImageUrl,
    answer: item[nameProp],
    isSelected: answerIds.includes(item.id),
  }));
};

const mockRequestQuestions: {
  questionId: string;
  isSkip: boolean;
  answerIds: string[];
  value?: string;
}[] = [
  {
    questionId: 'question-selection-1',
    isSkip: false,
    answerIds: ['answer-1'],
    value: '',
  },
  {
    questionId: 'question-option-1',
    isSkip: false,
    answerIds: ['answer-1', 'answer-2'],
    value: '',
  },
  {
    questionId: 'question-text-1',
    isSkip: false,
    answerIds: [],
    value: 'mock-value-question-text',
  },
];

const mockRequestQuestionsJob: {
  questionId: string;
  isSkip: boolean;
  answerIds: string[];
  value?: string;
}[] = [
  ...mockRequestQuestions,
  {
    questionId: 'question-option-2',
    isSkip: false,
    answerIds: ['answer-1', 'answer-2'],
    value: '',
  },
];

const mockEAQuestions: IModelChecklistResultItemQuestion[] = [
  {
    modelChecklistId: mockRequestQuestions[0].questionId,
    modelChecklistNameEn: 'mock-question-1 name',
    modelChecklistNameTh: 'ชื่อ mock-question-1',
    iconImageUrl: 'mock-question-1-link',
    answerType: QuestionType.SELECTION,
    questionChoices: getMockQuestionChoices(mockRequestQuestions[0].answerIds),
    questionAnswerText: '',
    isRequired: false,
    penalties: '-100',
    isSkip: false,
    functionKeyCond: `functionSection.functionkey=${mockRequestQuestions[0].answerIds[0]}`,
  } as IModelChecklistResultItemQuestion,
  {
    modelChecklistId: mockRequestQuestions[1].questionId,
    modelChecklistNameEn: 'mock-question-2 name',
    modelChecklistNameTh: 'ชื่อ mock-question-2',
    iconImageUrl: 'mock-question-2-link',
    answerType: QuestionType.OPTION,
    questionChoices: getMockQuestionChoices(mockRequestQuestions[1].answerIds),
    questionAnswerText: '',
    isRequired: false,
    penalties: '-100',
    isSkip: false,
    functionKeyCond: `functionSection.functionkey=select`,
  } as IModelChecklistResultItemQuestion,
  {
    modelChecklistId: mockRequestQuestions[2].questionId,
    modelChecklistNameEn: 'mock-question-3 name',
    modelChecklistNameTh: 'ชื่อ mock-question-3',
    iconImageUrl: 'mock-question-3-link',
    answerType: QuestionType.TEXT,
    questionChoices: [],
    questionAnswerText: mockRequestQuestions[2].value,
    isRequired: false,
    penalties: '0',
    isSkip: false,
    functionKeyCond: `functionSection.functionkey=freetext`,
  } as IModelChecklistResultItemQuestion,
];
export const mockEAQuestionsJob: IModelChecklistResultItemQuestion[] = [
  ...mockEAQuestions,
  {
    modelChecklistId: mockRequestQuestionsJob[3].questionId,
    modelChecklistNameEn: 'mock-question-2 name',
    modelChecklistNameTh: 'ชื่อ mock-question-2',
    iconImageUrl: 'mock-question-2-link',
    answerType: QuestionType.OPTION,
    questionChoices: getMockQuestionChoices(
      mockRequestQuestionsJob[3].answerIds,
    ),
    questionAnswerText: '',
    isRequired: false,
    penalties: '-100',
    isSkip: false,
    functionKeyCond: `functionSection.functionkey=select`,
  } as IModelChecklistResultItemQuestion,
];

const getMockResultQuestionsByLang = (
  lang: ILanguage,
): {
  id: string;
  question: string;
  description: string;
  type: QuestionType;
  isRequired: boolean;
  imageUrl: string | null;
  isSkip: boolean;
  choices: any[] | null;
  answer: string | null;
}[] => {
  const [qProp, dProp] =
    lang === 'th'
      ? ['modelChecklistNameTh', 'modelCheckListDescriptionTh', 'choiceNameTh']
      : ['modelChecklistNameEn', 'modelCheckListDescriptionEn', 'choiceNameEn'];
  return [
    {
      id: mockEAQuestions[0].modelChecklistId,
      question: mockEAQuestions[0][qProp],
      description: mockEAQuestions[0][dProp],
      type: QuestionType.SELECTION,
      isRequired: mockEAQuestions[0].isRequired,
      imageUrl: mockEAQuestions[0].iconImageUrl || null, //NOSONAR
      choices: getMockQuestionChoicesResult(
        lang,
        mockRequestQuestions[0].answerIds,
      ),
      isSkip: false,
      answer: null,
    },
    {
      id: mockEAQuestions[1].modelChecklistId,
      question: mockEAQuestions[1][qProp],
      description: mockEAQuestions[1][dProp],
      type: QuestionType.OPTION,
      isRequired: mockEAQuestions[1].isRequired,
      imageUrl: mockEAQuestions[1].iconImageUrl || null, //NOSONAR
      choices: getMockQuestionChoicesResult(
        lang,
        mockRequestQuestions[1].answerIds,
      ),
      isSkip: false,
      answer: null,
    },
    {
      id: mockEAQuestions[2].modelChecklistId,
      question: mockEAQuestions[2][qProp],
      description: mockEAQuestions[2][dProp],
      type: QuestionType.TEXT,
      isRequired: mockEAQuestions[2].isRequired,
      imageUrl: mockEAQuestions[2].iconImageUrl || null, //NOSONAR
      isSkip: false,
      choices: null,
      answer: mockEAQuestions[2].questionAnswerText,
    },
  ];
};

// --- request body create --- //
export const mockCreateOrderBody: CreateOrderDto = {
  modules: mockRequestModules,
  questions: mockRequestQuestions,
  storeId: mockStoreId,
  colorId: mockColorId,
  deviceId: 'mock-device-id',
  customerInfo: {
    firstname: 'mock-first-name',
    lastname: 'mock-last-name',
    email: '<EMAIL>',
    phoneNumber: '0123456789',
  },
  imei1: 'mock-imei-1',
  imei2: 'mock-imei-2',
  isAcceptPDPA: true,
  pdpaVersion: mockPdpaVersion,
};

// --- estimation activities --- //
export const mockOrderId = 'mock-common-order-id';
export const mockEstimationActivity = {
  id: mockOrderId,
  modelKey: mockModelKey,
  companyId: mockCompanyId,
  company: mockCompany,
  modelMaster: mockModelMaster,
  modelMasterGradeSnapshot: mockModelMaster.modelMasterGrades,
  deviceId: mockCreateOrderBody.deviceId,
  imei1: mockCreateOrderBody.imei1,
  imei2: mockCreateOrderBody.imei2,
  firstName: mockCreateOrderBody.customerInfo?.firstname,
  lastName: mockCreateOrderBody.customerInfo?.lastname,
  email: mockCreateOrderBody.customerInfo?.email,
  phoneNumber: mockCreateOrderBody.customerInfo?.phoneNumber,
  estimatedPrice: 15000,
  modelChecklistResult: { MODULE: mockEAModules, QUESTION: mockEAQuestions },
  createdAt: new Date(),
  isAcceptPDPA: true,
  PDPAVersion: mockPdpaVersion,
  legalDocumentId: mockLegalDocId,
} as EstimationActivitiesEntity;

export const getMockCommonOrderResponseByLang = (lang: ILanguage) => {
  const [colorName, storeName, address] =
    lang === 'th'
      ? [
          'nameTh',
          'title',
          'addressTh',
          'subDistrictTh',
          'districtTh',
          'provinceTh',
        ]
      : [
          'nameEn',
          'titleEn',
          'addressEn',
          'subDistrictEn',
          'districtEn',
          'provinceEn',
        ];
  return {
    id: mockEstimationActivity.id,
    deviceId: mockEstimationActivity.deviceId,
    device: {
      id: mockModelMaster.systemCode,
      deviceImageUrl: mockModelMaster.modelImageUrl,
      brand: mockModelMaster.modelIdentifiers.brand,
      name: mockModelMaster.modelIdentifiers.model,
      sku: {
        id: mockModelKey,
        storage: 128,
      },
      isPurchasable: true,
    },
    currentNet: mockEstimationActivity.estimatedPrice,
    imei1: mockEstimationActivity.imei1,
    imei2: mockEstimationActivity.imei2,
    modules: getMockResultModulesByLang(lang),
    questions: getMockResultQuestionsByLang(lang),
    color: {
      id: mockColor.id,
      name: mockColor[colorName],
    },
    customerInfo: {
      firstname: mockEstimationActivity.firstName,
      lastname: mockEstimationActivity.lastName,
      email: mockEstimationActivity.email,
      phoneNumber: mockEstimationActivity.phoneNumber,
    },
    selectedStore: {
      id: mockStore.branchId,
      name: mockStore[storeName],
      latitude: mockStore.latitude,
      longitude: mockStore.longitude,
      distance: null,
      imageUrl: mockStore.imageUrl,
      address: mockStore[address],
    },
    createdAt: mockEstimationActivity.createdAt,
  };
};

export const getMockMergeAddressResponseByLang = (lang: ILanguage) => {
  const response =
    lang === 'th'
      ? 'บ้านเลขที่ 111 บางซ่อน จตุจักร กรุงเทพ 10140'
      : 'No. 111 Bang Son Chatuchak Bangkok 10140';
  return response;
};

export const getMockReponseValue = (
  lang: ILanguage,
  key: string,
  isNull: boolean,
) => {
  if (isNull) return null;
  if (key === 'imageUrl') return 'mock-image-store';
  if (key === 'lat') return 0.5;
  if (key === 'long') return 0.6;
  if (key === 'name') {
    return lang === 'th' ? 'ชื่อ mock-store' : 'mock-store name';
  }

  return null;
};
// --- fail case
export const mockModelMasterNullable: ModelMasterEntity = {
  ...mockModelMaster,
  systemCode: undefined,
  modelImageUrl: undefined,
  modelIdentifiers: {},
};
export const mockModelMasterForNotPurchasable: ModelMasterEntity = {
  ...mockModelMaster,
  modelKey: 'default',
};

export const mockStoreNullable = {
  ...mockStore,
  title: '',
  titleEn: '',
  imageUrl: '',
  latitude: undefined,
  longitude: undefined,
};

export const mockStoreAddress = {
  branchId: mockStoreId,
  title: 'ชื่อ mock-store',
  titleEn: 'mock-store name',
  imageUrl: 'mock-image-store',
  latitude: 0.5,
  longitude: 0.6,
  provinceEn: 'Bangkok',
  provinceTh: 'กรุงเทพ',
  addressEn: 'No. 111',
  addressTh: 'บ้านเลขที่ 111',
  districtEn: 'Chatuchak',
  districtTh: 'จตุจักร',
  subDistrictEn: 'Bang Son',
  subDistrictTh: 'บางซ่อน',
  zipCode: '10140',
} as BranchEntity;

export const mockStoreAddressNullable = {
  ...mockStoreAddress,
  title: '',
  titleEn: '',
  imageUrl: '',
  latitude: undefined,
  longitude: undefined,
  provinceEn: undefined,
  provinceTh: undefined,
  addressEn: undefined,
  addressTh: undefined,
  districtEn: undefined,
  districtTh: undefined,
  subDistrictEn: undefined,
  subDistrictTh: undefined,
  zipCode: undefined,
} as BranchEntity;

export const mockRequestQuestionsSkipByType = (index: number) =>
  mockRequestQuestions.map((item, ind) => ({
    ...item,
    isSkip: index === ind,
  }));
export const mockRequestQuestionsnotSelectByType = (index: number) =>
  mockRequestQuestions.map((item, ind) => ({
    ...item,
    answerIds: ind === index ? [] : item.answerIds,
  }));
export const mockRequestQuestionsnotAnswerByType = (index: number) =>
  mockRequestQuestions.map((item, ind) => ({
    ...item,
    value: ind === index ? null : item.value,
  }));

// --- model function
export const generateModulesFunction = (
  number: string,
  notRequire: boolean = false,
) => {
  return [
    ['functional', '0'],
    ['skip', '-500'],
    ['non_functional', '-1000'],
  ].map(([status, penalties]) => {
    return {
      functionKeyCond: `moduleSection.module${number}=${status}`,
      penalties: penalties,
      checkListId: `module-${number}`,
      modelChecklist: {
        id: `module-${number}`,
        isRequired: !notRequire,
        checklistType: ChecklistType.MODULE,
        checklistNameTh: `ชื่อ ภาษาไทย${number}`,
        checklistDescriptionTh: `อธิบาย ภาษาไทย${number}`,
        checklistNameEn: `name Eng ${number}`,
        checklistDescriptionEn: `description Eng ${number}`,
        iconImageUrl: null,
        moduleCode: `module-code-${number}`,
      } as ModelChecklistEntity,
    } as ModelMasterFunctionEntity;
  });
};

export const generateQuestionOptionFunction = (
  number: string,
  startAt: number = 1,
  notRequire: boolean = false,
) => {
  return [
    ['not_select', '0'],
    ['skip', '-500'],
    ['select', '-1000'],
  ].map(([isSelect, penalties]) => {
    return {
      functionKeyCond: `questionSection.option${number}=${isSelect}`,
      penalties: penalties,
      checkListId: `question-option-${number}`,
      modelChecklist: {
        id: `question-option-${number}`,
        isRequired: !notRequire,
        checklistType: ChecklistType.QUESTION,
        checklistNameTh: `ชื่อ ภาษาไทย${number}`,
        checklistDescriptionTh: `อธิบาย ภาษาไทย${number}`,
        checklistNameEn: `name Eng ${number}`,
        checklistDescriptionEn: `description Eng ${number}`,
        iconImageUrl: null,
        questionType: QuestionType.OPTION,
        questionChoices: mockQuestionChoicesChecklist(startAt),
      } as ModelChecklistEntity,
    } as ModelMasterFunctionEntity;
  });
};

export const generateQuestionSelectionFunction = (
  number: string,
  startAt: number = 1,
  notRequire: boolean = false,
) => {
  return [
    ['answer-1', '0'],
    ['answer-2', '-500'],
    ['answer-3', '-1000'],
  ]
    .slice(startAt - 1)
    .map(([answer, penalties]) => {
      return {
        functionKeyCond: `questionSection.selection${number}=${answer}`,
        penalties: penalties,
        checkListId: `question-selection-${number}`,
        modelChecklist: {
          id: `question-selection-${number}`,
          isRequired: !notRequire,
          checklistType: ChecklistType.QUESTION,
          checklistNameTh: `ชื่อ ภาษาไทย${number}`,
          checklistDescriptionTh: `อธิบาย ภาษาไทย${number}`,
          checklistNameEn: `name Eng ${number}`,
          checklistDescriptionEn: `description Eng ${number}`,
          iconImageUrl: null,
          questionType: QuestionType.SELECTION,
          questionChoices: mockQuestionChoicesChecklist(startAt),
        } as ModelChecklistEntity,
      } as ModelMasterFunctionEntity;
    });
};
export const generateQuestionTextFunction = (
  number: string,
  notRequire: boolean = false,
) => {
  return [
    {
      functionKeyCond: `questionSection.freetext${number}=freetext`,
      penalties: '0',
      checkListId: `question-text-${number}`,
      modelChecklist: {
        id: `question-text-${number}`,
        isRequired: !notRequire,
        checklistType: ChecklistType.QUESTION,
        checklistNameTh: `ชื่อ ภาษาไทย${number}`,
        checklistDescriptionTh: `อธิบาย ภาษาไทย${number}`,
        checklistNameEn: `name Eng ${number}`,
        checklistDescriptionEn: `description Eng ${number}`,
        iconImageUrl: null,
        questionType: QuestionType.TEXT,
      } as ModelChecklistEntity,
    } as ModelMasterFunctionEntity,
  ];
};

const selectionIsRequiredUndefined = generateQuestionSelectionFunction('2')[0];

const { isRequired, ...newChecklist } =
  selectionIsRequiredUndefined.modelChecklist || {};
selectionIsRequiredUndefined.modelChecklist =
  newChecklist as ModelChecklistEntity;

export const mockModelMasterFunctionsForGetFunctionFromModelMasterFn = [
  ...generateModulesFunction('1'),
  ...generateModulesFunction('2', true),
  ...generateModulesFunction('3'),
  ...generateQuestionOptionFunction('1'),
  ...generateQuestionOptionFunction('2', 0, true),
  ...generateQuestionSelectionFunction('1'),
  ...generateQuestionTextFunction('1'),
];

const generateMasterFunctionByFunction = (
  type: 'module' | 'option' | 'selection' | 'freetext',
  masterFnArray: any[],
  isNotRequired: boolean = false,
) => {
  const data = masterFnArray.reduce((acc, current) => {
    const keyCondValue = current.functionKeyCond.split('=')[1];
    const value: any = {
      modelChecklistId: current.checkListId,
      functionKeyCond: current.functionKeyCond,
      penalties: current.penalties,
      modelChecklistNameTh: current.modelChecklist.checklistNameTh,
      modelChecklistNameEn: current.modelChecklist.checklistNameEn,
      modelCheckListDescriptionTh:
        current.modelChecklist.checklistDescriptionTh,
      modelCheckListDescriptionEn:
        current.modelChecklist.checklistDescriptionEn,
      iconImageUrl: current.modelChecklist.iconImageUrl,
      isRequired: !isNotRequired,
      isSkip: null,
      functionKey: undefined,
      tooltipEn: undefined,
      tooltipTh: undefined,
      placeholderEn: undefined,
      placeholderTh: undefined,
      errorTextEn: undefined,
      errorTextTh: undefined,
    };
    if (type === 'module') {
      value.modelChecklistModuleCode = current.modelChecklist.moduleCode;
      value.moduleStatus = null;
    } else if (type === 'option') {
      value.answerType = QuestionType.OPTION;
      value.questionChoices = current.modelChecklist.questionChoices;
      value.questionAnswerText = null;
    } else if (type === 'selection') {
      value.answerType = QuestionType.SELECTION;
      value.questionChoices = current.modelChecklist.questionChoices;
      value.questionAnswerText = null;
    } else if (type === 'freetext') {
      value.answerType = QuestionType.TEXT;
      value.questionAnswerText = null;
    }
    acc[keyCondValue] = value;
    return acc;
  }, {} as any);
  return data;
};

export const mockResultGetFunctionFromModelMasterFn = () => ({
  moduleMasterFunctionByFunction: {
    'moduleSection.module1': generateMasterFunctionByFunction(
      'module',
      generateModulesFunction('1'),
    ),
    'moduleSection.module2': generateMasterFunctionByFunction(
      'module',
      generateModulesFunction('2', true),
      true,
    ),
    'moduleSection.module3': generateMasterFunctionByFunction(
      'module',
      generateModulesFunction('3'),
    ),
  },
  questionMasterFunctionByFunction: {
    'questionSection.option1': generateMasterFunctionByFunction(
      'option',
      generateQuestionOptionFunction('1'),
    ),
    'questionSection.option2': generateMasterFunctionByFunction(
      'option',
      generateQuestionOptionFunction('2', 0, true),
      true,
    ),
    'questionSection.selection1': generateMasterFunctionByFunction(
      'selection',
      generateQuestionSelectionFunction('1'),
    ),
    'questionSection.freetext1': generateMasterFunctionByFunction(
      'freetext',
      generateQuestionTextFunction('1'),
    ),
  },
  allModuleChecklistIds: ['module-1', 'module-2', 'module-3'],
  allQuestionChecklistIds: [
    'question-option-1',
    'question-option-2',
    'question-selection-1',
    'question-text-1',
  ],
  requireModuleChecklistIds: new Set(['module-1', 'module-3']),
  requireQuestionChecklistIds: new Set([
    'question-option-1',
    'question-selection-1',
    'question-text-1',
  ]),
  moduleMasterFunctionPointer: {
    'module-1': { fnName: 'moduleSection.module1' },
    'module-2': { fnName: 'moduleSection.module2' },
    'module-3': { fnName: 'moduleSection.module3' },
  },
  questionMasterFunctionPointer: {
    'question-option-1': { fnName: 'questionSection.option1', type: 'OPTION' },
    'question-option-2': { fnName: 'questionSection.option2', type: 'OPTION' },
    'question-selection-1': {
      fnName: 'questionSection.selection1',
      type: 'SELECTION',
    },
    'question-text-1': {
      fnName: 'questionSection.freetext1',
      type: 'FREETEXT',
    },
  },
});
