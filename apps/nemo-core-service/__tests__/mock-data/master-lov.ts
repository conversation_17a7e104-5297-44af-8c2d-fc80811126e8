import { MasterLovEntity, Locale, CompanyEntity } from '../../src/entities';
import { MasterLovItem } from 'contracts';

// --- Mock Generator
type IMockMasterLovItem = {
  type: string;
  value: string;
  label: string;
  sortIndex: number;
  locale?: Locale;
  color?: string;
};

const generateMockMasterLovItem = ({
  type,
  value,
  label,
  sortIndex,
  locale,
  color,
}: IMockMasterLovItem): MasterLovEntity => {
  const returnValue: MasterLovEntity = {
    createdAt: new Date('2024-03-06T10:07:25.150Z'),
    updatedAt: new Date('2024-03-06T10:07:25.150Z'),
    companyId: 'WW',
    type,
    value,
    label,
    sortIndex,
    locale: locale || Locale.TH,
    company: {} as CompanyEntity,
  };
  if (color) {
    returnValue.additionalValue = color
      ? { chipColor: { bg: color, text: color } }
      : undefined;
  }
  return returnValue;
};

const generateMockResultMasterLov = ({
  value,
  label,
  color,
}: IMockMasterLovItem): MasterLovItem => {
  const returnValue: MasterLovItem = {
    label,
    value,
  };
  if (color) {
    returnValue.additionalValue = { chipColor: { bg: color, text: color } };
  }
  return returnValue;
};

// --- Mock Config and Result
const mockMasterLovListConfigA = [
  {
    type: 'typeA',
    value: 'AA',
    label: 'aaa',
    sortIndex: 1,
    color: 'red',
  },
  {
    type: 'typeA',
    value: 'AB',
    label: 'bbb',
    sortIndex: 2,
    color: 'blue',
  },
  {
    type: 'typeA',
    value: 'AC',
    label: 'ccc',
    sortIndex: 3,
    color: 'green',
  },
];

export const mockMasterLovResultA: MasterLovEntity[] =
  mockMasterLovListConfigA.map((item) => generateMockMasterLovItem(item));

export const resultMasterLovA: MasterLovItem[] = mockMasterLovListConfigA.map(
  (item) => generateMockResultMasterLov(item),
);
const mockMasterLovListConfigB = [
  {
    type: 'typeB',
    value: 'BA',
    label: 'aaa',
    sortIndex: 1,
  },
  {
    type: 'typeB',
    value: 'BB',
    label: 'bbb',
    sortIndex: 2,
  },
  {
    type: 'typeB',
    value: 'BC',
    label: 'ccc',
    sortIndex: 3,
  },
];

export const mockMasterLovResultB: MasterLovEntity[] =
  mockMasterLovListConfigB.map((item) => generateMockMasterLovItem(item));

export const resultMasterLovB: MasterLovItem[] = mockMasterLovListConfigB.map(
  (item) => generateMockResultMasterLov(item),
);
