import { CampaignEntity } from 'src/entities';
export const mockCampaign: CampaignEntity = {
  campaignCode: 'campaign1',
  companyId: 'CompanyX',
  campaignName: 'campaignName1',
  description: 'description1',
  remark: 'remark1',
  startDate: new Date(),
  endDate: new Date(),
  maxRedemptionCode: 1,
  createdBy: 'system',
  updatedBy: 'system',
  createdAt: new Date(),
  company: {
    companyId: 'CompanyX',
    title: 'company title',
    logoUrl: 'company logo url',
    createdBy: 'company created by',
    userKeyClaimFnName: 'company user key claim fn name',
    empUploadMapperFnName: 'company emp upload mapper fn name',
    createdAt: new Date(),
    updatedAt: new Date(),
    logoPath: 'company logo path',
  },
  isActive: true,
};

export const mockCampaignCancel: CampaignEntity = {
  campaignCode: 'campaign1',
  companyId: 'CompanyX',
  campaignName: 'campaignName1',
  description: 'description1',
  remark: 'remark1',
  startDate: new Date(),
  endDate: new Date(new Date().setMonth(new Date().getMonth() + 1)),
  maxRedemptionCode: 1,
  createdBy: 'system',
  updatedBy: 'system',
  createdAt: new Date(),
  company: {
    companyId: 'CompanyX',
    title: 'company title',
    logoUrl: 'company logo url',
    createdBy: 'company created by',
    userKeyClaimFnName: 'company user key claim fn name',
    empUploadMapperFnName: 'company emp upload mapper fn name',
    createdAt: new Date(),
    updatedAt: new Date(),
    logoPath: 'company logo path',
  },
  isActive: true,
};
