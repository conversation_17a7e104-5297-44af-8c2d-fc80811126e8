import { random } from 'lodash';

export const generateVoucherExcelDataListMock = (total: number) => {
  const mock: any[] = [];
  for (let i = 1; i <= total; i++) {
    mock.push({
      voucherValue: 100 + i,
      redemptionCode: 'xxxxx' + i,
      otherPaymentCode: 'aaa',
      refNo: i.toString(),
    });
  }
  return mock;
};

export const count = {
  count: '4',
};

export const mockVoucherCount = [
  {
    value: 5000,
    total: 2,
    available: 1,
    used: 1,
  },
  {
    value: 4000,
    total: 8,
    available: 5,
    used: 3,
  },
  {
    value: 3000,
    total: 10,
    available: 7,
    used: 3,
  },
  {
    value: 2000,
    total: 40,
    available: 28,
    used: 12,
  },
];

export const mockVoucherCountQuery = [
  { voucher_value: '5000.00', total: '2', available: '1', used: '1' },
  { voucher_value: '4000.00', total: '8', available: '5', used: '3' },
  { voucher_value: '3000.00', total: '10', available: '7', used: '3' },
  { voucher_value: '2000.00', total: '40', available: '281', used: '12' },
];
