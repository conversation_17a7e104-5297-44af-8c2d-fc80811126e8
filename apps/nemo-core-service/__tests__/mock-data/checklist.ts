export const mockCheckListValues = {
  product_images: {
    back_view: [
      {
        name: 'snap_picture.png',
        type: 'image/png',
        content:
          'http://localhost:9000/nemo-media/company/WW/jobs/2414G1TIVCGVE1GBF/back_view?d=1704360695949',
      },
    ],
    rear_view: [
      {
        name: 'snap_picture.png',
        type: 'image/png',
        content:
          'http://localhost:9000/nemo-media/company/WW/jobs/2414G1TIVCGVE1GBF/rear_view?d=1704360686798',
      },
    ],
    imei_number: [
      {
        name: 'snap_picture.png',
        type: 'image/png',
        content:
          'http://localhost:9000/nemo-media/company/WW/jobs/2414G1TIVCGVE1GBF/imei_number?d=1704360672611',
      },
    ],
    about_screen: [
      {
        name: 'snap_picture.png',
        type: 'image/png',
        content:
          'http://localhost:9000/nemo-media/company/WW/jobs/2414G1TIVCGVE1GBF/about_screen?d=1704360697399',
      },
    ],
    left_side_view: [
      {
        name: 'snap_picture.png',
        type: 'image/png',
        content:
          'http://localhost:9000/nemo-media/company/WW/jobs/2414G1TIVCGVE1GBF/left_side_view?d=1704360687708',
      },
    ],
    right_side_view: [
      {
        name: 'snap_picture.png',
        type: 'image/png',
        content:
          'http://localhost:9000/nemo-media/company/WW/jobs/2414G1TIVCGVE1GBF/right_side_view?d=1704360688592',
      },
    ],
    lower_left_angle: [
      {
        name: 'snap_picture.png',
        type: 'image/png',
        content:
          'http://localhost:9000/nemo-media/company/WW/jobs/2414G1TIVCGVE1GBF/lower_left_angle?d=1704360692902',
      },
    ],
    upper_left_angle: [
      {
        name: 'snap_picture.png',
        type: 'image/png',
        content:
          'http://localhost:9000/nemo-media/company/WW/jobs/2414G1TIVCGVE1GBF/upper_left_angle?d=1704360696282',
      },
    ],
    lower_right_angle: [
      {
        name: 'snap_picture.png',
        type: 'image/png',
        content:
          'http://localhost:9000/nemo-media/company/WW/jobs/2414G1TIVCGVE1GBF/lower_right_angle?d=1704360698704',
      },
    ],
    upper_right_angle: [
      {
        name: 'snap_picture.png',
        type: 'image/png',
        content:
          'http://localhost:9000/nemo-media/company/WW/jobs/2414G1TIVCGVE1GBF/upper_right_angle?d=1704360690579',
      },
    ],
    device_information: [
      {
        name: 'snap_picture.png',
        type: 'image/png',
        content:
          'http://localhost:9000/nemo-media/company/WW/jobs/2414G1TIVCGVE1GBF/device_information?d=1704360697070',
      },
    ],
  },
  remobie_check_list: {
    wifi: 'functional',
    image: [
      {
        name: 'snap_picture.png',
        type: 'image/png',
        content:
          'http://localhost:9000/nemo-media/company/WW/jobs/2414G1TIVCGVE1GBF/image?d=1704360673746',
      },
    ],
    speaker: 'functional',
    earpiece: 'functional',
    biometric: 'functional',
    bluetooth: 'functional',
    vibration: 'functional',
    microphone: 'functional',
    rear_camera: 'functional',
    front_camera: 'functional',
    touch_screen: 'functional',
    volume_button: 'functional',
    power_charging: 'functional',
    proximity_sensor: 'functional',
  },
  product_information: {
    device_color: 'black',
    battery_health: 'above_80_percent',
    screen_display: 'normal',
    country_of_purchase: 'th',
    additional_accessories: 'incomplete',
    icloud_or_google_account: 'cannot_sign_out',
  },
  additional_product_information: { device_condition: 'no_marks_new' },
  product_additional_information: {},
};

export const mockCheckList = [
  {
    slug: 'product_information',
    title: 'รายละเอียดสินค้า',
    title_detail: 'รายละเอียดสินค้า',
    survey_form: {
      logoPosition: 'right',
      pages: [
        {
          name: 'page1',
          elements: [
            {
              type: 'dropdown',
              name: 'device_color',
              title: 'สีเครื่อง',
              hideNumber: true,
              isRequired: true,
              requiredErrorText: 'กรุณาเลือกสีเครื่อง',
              choices: [
                { text: 'ดำ', value: 'black' },
                { text: 'ขาว', value: 'white' },
                { text: 'ทอง', value: 'gold' },
                { text: 'เงิน', value: 'silver' },
                { text: 'เขียว', value: 'green' },
                { text: 'เหลือง', value: 'yellow' },
                { text: 'ฟ้า', value: 'blue' },
                { text: 'แดง', value: 'red' },
                { text: 'ชมพู', value: 'pink' },
                { text: 'ส้ม', value: 'orange' },
                { text: 'น้ำเงิน', value: 'cyan' },
                { text: 'ม่วง', value: 'purple' },
              ],
              placeholder: 'กรุณาเลือกสี',
            },
            {
              type: 'dropdown',
              name: 'country_of_purchase',
              startWithNewLine: false,
              title: 'ประเทศที่ซื้อโทรศัพท์',
              hideNumber: true,
              isRequired: true,
              requiredErrorText: 'กรุณาเลือกประเทศที่ซื้อโทรศัพท์',
              choices: [
                { text: 'ไทย', value: 'th' },
                { text: 'อื่นๆ', value: 'etc' },
              ],
              placeholder: 'กรุณาเลือกประเทศที่ซื้อโทรศัพท์',
            },
            {
              type: 'radiogroup',
              name: 'screen_display',
              title: 'การแสดงภาพหน้าจอ',
              hideNumber: true,
              isRequired: true,
              choices: [
                {
                  value: 'normal',
                  text: 'ปกติ',
                },
                {
                  value: 'abnormal',
                  text: 'ไม่ปกติ',
                },
              ],
              colCount: 2,
              tooltip: {
                html: "<span style='font-family: 'IBM Plex Sans Thai'; font-size: 14px; line-height: 20px; font-weight: 400;'>ตรวจสอบสภาพหน้าจอสามารถแสดงภาพได้ปกติชัดเจนหรือไม่</span>",
              },
              startWithNewLine: false,
            },
            {
              type: 'radiogroup',
              name: 'icloud_or_google_account',
              title: 'สามารถออกจาก iCloud หรือ Google Account',
              hideNumber: true,
              isRequired: true,
              choices: [
                {
                  value: 'can_sign_out',
                  text: 'ได้',
                },
                {
                  value: 'cannot_sign_out',
                  text: 'ไม่ได้',
                },
              ],
              colCount: 2,
            },
            {
              type: 'radiogroup',
              name: 'additional_accessories',
              startWithNewLine: false,
              title: 'อุปกรณ์เสริม (กล่อง, หูฟัง, สายชาร์จ, หัวชาร์จ)',
              hideNumber: true,
              isRequired: true,
              choices: [
                {
                  value: 'complete',
                  text: 'อุปกรณ์ครบ',
                },
                {
                  value: 'incomplete',
                  text: 'อุปกรณ์ไม่ครบ',
                },
              ],
              colCount: 2,
            },
            {
              type: 'radiogroup',
              name: 'battery_health',
              title: 'สุขภาพแบตเตอรี่',
              hideNumber: true,
              isRequired: true,
              startWithNewLine: false,
              choices: [
                {
                  value: 'above_80_percent',
                  text: 'สูงกว่า 80%',
                },
                {
                  value: 'below_80_percent',
                  text: 'ต่ำกว่า 80%',
                },
              ],
              colCount: 2,
            },
          ],
        },
      ],
      questionDescriptionLocation: 'underInput',
    },
    fillSpaceColumnCount: 3,
  },
  {
    slug: 'additional_product_information',
    title: 'ข้อมูลสินค้าเพิ่มเติม',
    fillSpaceColumnCount: 3,
    survey_form: {
      logoPosition: 'right',
      pages: [
        {
          name: 'page1',
          elements: [
            {
              type: 'dropdown',
              name: 'device_condition',
              title: 'สภาพตัวเครื่อง',
              hideNumber: true,
              isRequired: true,
              requiredErrorText: 'กรุณากรอกสภาพตัวเครื่อง',
              choices: [
                { text: 'ลักษณะ ไม่มีรอย สภาพใหม่', value: 'no_marks_new' },
                {
                  text: 'ลักษณะ มีรอยเล็กน้อย (2-3 จุด)',
                  value: 'minor_marks',
                },
                {
                  text: 'ลักษณะ มีรอยมาก บุบตามตัวเครื่อง',
                  value: 'major_marks',
                },
              ],
              placeholder: 'กรุณาเลือกสภาพตัวเครื่อง',
              tooltip: {
                html: "<div><span style='font-size: 18px; line-height: 28px; font-weight: 600;'>สภาพตัวเครื่อง</span><br/><br/><ul style='font-size: 14px; line-height: 20px; font-weight: 400; list-style-position: outside; list-style-type: disc; padding-left:20px;'><li>ลักษณะไม่มีรอย สภาพใหม่ - มีสภาพสมบูรณ์หรือสภาพสมบูรณ์เกือบ 100%</li><li>ลักษณะมีรอยเล็กน้อย (2-3 จุด) - มีร่องรอยขีดข่วนที่เห็นได้ด้วยตาเปล่า จำนวนไม่เยอะ</li><li>ลักษณะมีรอยมาก บุบตามตัวเครื่อง - มีร่องรอยขีดข่วน ร่องรอยบุบและพื้นผิวไม่ปกติ ที่ชัดเจน</li></ul></div>",
              },
            },
          ],
        },
      ],
    },
  },
  {
    slug: 'remobie_check_list',
    title: 'สภาพสินค้า',
    title_detail: 'สภาพสินค้า',
    fillSpaceColumn: [{ path: 'condition_radio_group', count: 3 }],
    survey_form: {
      logoPosition: 'right',
      pages: [
        {
          name: 'page1',
          elements: [
            {
              type: 'file',
              name: 'image',
              hideNumber: true,
              isRequired: true,
              titleLocation: 'hidden',
              acceptedTypes: 'image/png, image/jpeg',
              storeDataAsText: false,
              needConfirmRemoveFile: true,
              sourceType: 'file',
              visibleIf: '{deviceSize} == tablet',
              startWithNewLine: false,
              maxSize: 3000000,
              previewSize: 'lg',
            },
            {
              type: 'file',
              name: 'image',
              hideNumber: true,
              isRequired: true,
              titleLocation: 'hidden',
              acceptedTypes: 'image/png, image/jpeg',
              storeDataAsText: false,
              needConfirmRemoveFile: true,
              sourceType: 'file-camera',
              visibleIf: '{deviceSize} == desktop',
              startWithNewLine: false,
              maxSize: 3000000,
              previewSize: 'lg',
            },
            {
              type: 'html',
              name: 'question1',
              visibleIf: '{image} empty',
              html: '<div style="display: flex; flex-direction: column; align-items: center; justify-content: center; height: 216px; border-radius: 8px; box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);"><img src="https://{host}/asset/company/WW/icons-remobie/ic-remobie-preview.png" alt="Example Image" style="width: 120px; height: 120px; margin-bottom: 16px;"><span style="color: #000; font-family: \'IBM Plex Sans Thai\'; font-size: 14px; font-style: normal; font-weight: 600; line-height: 20px;">ตัวอย่างรูปผลการตรวจสอบ</span></div>',
              startWithNewLine: false,
            },
            {
              type: 'html',
              name: 'question2',
              visibleIf: '{image} notempty',
              html: '<div><span style="font-family: IBM Plex Sans Thai; font-size: 18px; font-weight: 600; line-height: 26px;">ผลการตรวจสอบ (13 รายการ)</span> <span style="font-family: IBM Plex Sans Thai; font-size: 18px; font-weight: 400; line-height: 26px;">กรุณาตรวจสภาพสินค้าตามรูปภาพที่แนบ</span></div>',
            },
            {
              type: 'panel',
              name: 'condition_radio_group',
              visibleIf: '{image} notempty',
              elements: [
                {
                  type: 'custom-radio-group',
                  icon: 'ic-bluetooth',
                  urlPath:
                    'https://{host}/asset/company/WW/icons-remobie/{icon}.png',
                  label: 'Bluetooth',
                  name: 'bluetooth',
                  isRequired: true,
                  titleLocation: 'hidden',
                  radioType: 'Group',
                  choices: [
                    {
                      value: 'functional',
                      text: 'ใช้งานได้',
                    },
                    {
                      value: 'non_functional',
                      text: 'ใช้งานไม่ได้',
                    },
                  ],
                },
                {
                  type: 'custom-radio-group',
                  icon: 'ic-wifi',
                  urlPath:
                    'https://{host}/asset/company/WW/icons-remobie/{icon}.png',
                  label: 'WIFI',
                  name: 'wifi',
                  titleLocation: 'hidden',
                  isRequired: true,
                  radioType: 'Group',
                  startWithNewLine: false,
                  choices: [
                    {
                      value: 'functional',
                      text: 'ใช้งานได้',
                    },
                    {
                      value: 'non_functional',
                      text: 'ใช้งานไม่ได้',
                    },
                  ],
                },
                {
                  type: 'custom-radio-group',
                  icon: 'ic-camera',
                  urlPath:
                    'https://{host}/asset/company/WW/icons-remobie/{icon}.png',
                  label: 'กล้องหน้า',
                  name: 'front_camera',
                  isRequired: true,
                  titleLocation: 'hidden',
                  radioType: 'Group',
                  startWithNewLine: false,
                  choices: [
                    {
                      value: 'functional',
                      text: 'ใช้งานได้',
                    },
                    {
                      value: 'non_functional',
                      text: 'ใช้งานไม่ได้',
                    },
                  ],
                },
                {
                  type: 'custom-radio-group',
                  icon: 'ic-flip-camera',
                  urlPath:
                    'https://{host}/asset/company/WW/icons-remobie/{icon}.png',
                  label: 'กล้องหลัง',
                  name: 'rear_camera',
                  isRequired: true,
                  titleLocation: 'hidden',
                  radioType: 'Group',
                  choices: [
                    {
                      value: 'functional',
                      text: 'ใช้งานได้',
                    },
                    {
                      value: 'non_functional',
                      text: 'ใช้งานไม่ได้',
                    },
                  ],
                },
                {
                  type: 'custom-radio-group',
                  icon: 'ic-microphone',
                  urlPath:
                    'https://{host}/asset/company/WW/icons-remobie/{icon}.png',
                  label: 'ไมโครโฟน',
                  name: 'microphone',
                  titleLocation: 'hidden',
                  isRequired: true,
                  radioType: 'Group',
                  startWithNewLine: false,
                  choices: [
                    {
                      value: 'functional',
                      text: 'ใช้งานได้',
                    },
                    {
                      value: 'non_functional',
                      text: 'ใช้งานไม่ได้',
                    },
                  ],
                },
                {
                  type: 'custom-radio-group',
                  icon: 'ic-face-id',
                  urlPath:
                    'https://{host}/asset/company/WW/icons-remobie/{icon}.png',
                  label: 'Face ID/Touch ID',
                  name: 'biometric',
                  titleLocation: 'hidden',
                  isRequired: true,
                  radioType: 'Group',
                  startWithNewLine: false,
                  choices: [
                    {
                      value: 'functional',
                      text: 'ใช้งานได้',
                    },
                    {
                      value: 'non_functional',
                      text: 'ใช้งานไม่ได้',
                    },
                  ],
                },
                {
                  type: 'custom-radio-group',
                  icon: 'ic-battery',
                  urlPath:
                    'https://{host}/asset/company/WW/icons-remobie/{icon}.png',
                  label: 'การชาร์จไฟ',
                  name: 'power_charging',
                  isRequired: true,
                  titleLocation: 'hidden',
                  radioType: 'Group',
                  choices: [
                    {
                      value: 'functional',
                      text: 'ใช้งานได้',
                    },
                    {
                      value: 'non_functional',
                      text: 'ใช้งานไม่ได้',
                    },
                  ],
                },
                {
                  type: 'custom-radio-group',
                  icon: 'ic-sensor',
                  urlPath:
                    'https://{host}/asset/company/WW/icons-remobie/{icon}.png',
                  label: 'เซนเซอร์ระยะห่าง',
                  name: 'proximity_sensor',
                  titleLocation: 'hidden',
                  isRequired: true,
                  radioType: 'Group',
                  startWithNewLine: false,
                  choices: [
                    {
                      value: 'functional',
                      text: 'ใช้งานได้',
                    },
                    {
                      value: 'non_functional',
                      text: 'ใช้งานไม่ได้',
                    },
                  ],
                },
                {
                  type: 'custom-radio-group',
                  icon: 'ic-vibration',
                  urlPath:
                    'https://{host}/asset/company/WW/icons-remobie/{icon}.png',
                  label: 'การสั่น',
                  name: 'vibration',
                  titleLocation: 'hidden',
                  isRequired: true,
                  radioType: 'Group',
                  startWithNewLine: false,
                  choices: [
                    {
                      value: 'functional',
                      text: 'ใช้งานได้',
                    },
                    {
                      value: 'non_functional',
                      text: 'ใช้งานไม่ได้',
                    },
                  ],
                },
                {
                  type: 'custom-radio-group',
                  icon: 'ic-speaker',
                  urlPath:
                    'https://{host}/asset/company/WW/icons-remobie/{icon}.png',
                  label: 'ลำโพง',
                  name: 'speaker',
                  isRequired: true,
                  titleLocation: 'hidden',
                  radioType: 'Group',
                  choices: [
                    {
                      value: 'functional',
                      text: 'ใช้งานได้',
                    },
                    {
                      value: 'non_functional',
                      text: 'ใช้งานไม่ได้',
                    },
                  ],
                },
                {
                  type: 'custom-radio-group',
                  icon: 'ic-ear',
                  urlPath:
                    'https://{host}/asset/company/WW/icons-remobie/{icon}.png',
                  label: 'ลำโพงแนบหู',
                  name: 'earpiece',
                  titleLocation: 'hidden',
                  isRequired: true,
                  radioType: 'Group',
                  startWithNewLine: false,
                  choices: [
                    {
                      value: 'functional',
                      text: 'ใช้งานได้',
                    },
                    {
                      value: 'non_functional',
                      text: 'ใช้งานไม่ได้',
                    },
                  ],
                },
                {
                  type: 'custom-radio-group',
                  icon: 'ic-audio',
                  urlPath:
                    'https://{host}/asset/company/WW/icons-remobie/{icon}.png',
                  label: 'ปุ่มเพิ่ม-ลดเสียง',
                  name: 'volume_button',
                  titleLocation: 'hidden',
                  isRequired: true,
                  radioType: 'Group',
                  startWithNewLine: false,
                  choices: [
                    {
                      value: 'functional',
                      text: 'ใช้งานได้',
                    },
                    {
                      value: 'non_functional',
                      text: 'ใช้งานไม่ได้',
                    },
                  ],
                },
                {
                  type: 'custom-radio-group',
                  icon: 'ic-click',
                  urlPath:
                    'https://{host}/asset/company/WW/icons-remobie/{icon}.png',
                  label: 'การสัมผัสหน้าจอ',
                  name: 'touch_screen',
                  isRequired: true,
                  titleLocation: 'hidden',
                  radioType: 'Group',
                  choices: [
                    {
                      value: 'functional',
                      text: 'ใช้งานได้',
                    },
                    {
                      value: 'non_functional',
                      text: 'ใช้งานไม่ได้',
                    },
                  ],
                },
              ],
            },
          ],
        },
      ],
      questionDescriptionLocation: 'underInput',
    },
  },
  {
    slug: 'product_images',
    title: 'รูปภาพสินค้า',
    title_detail: 'รูปภาพตัวสินค้า',
    fillSpaceColumn: [
      { path: 'product_images_desktop', count: 4 },
      { path: 'product_images_tablet', count: 4 },
    ],
    survey_form: {
      logoPosition: 'right',
      pages: [
        {
          name: 'page1',
          elements: [
            {
              type: 'panel',
              name: 'product_images_desktop',
              visibleIf: '{deviceSize} == desktop',
              elements: [
                {
                  type: 'file',
                  name: 'imei_number',
                  title: 'รูปเลข IMEI',
                  hideNumber: true,
                  needConfirmRemoveFile: true,
                  isRequired: true,
                  storeDataAsText: false,
                  sourceType: 'file-camera',
                  acceptedTypes: 'image/png, image/jpeg',
                  maxSize: 3000000,
                },
                {
                  type: 'file',
                  name: 'rear_view',
                  title: 'รูปด้านหลังเครื่อง',
                  startWithNewLine: false,
                  hideNumber: true,
                  needConfirmRemoveFile: true,
                  isRequired: true,
                  storeDataAsText: false,
                  sourceType: 'file-camera',
                  acceptedTypes: 'image/png, image/jpeg',
                  maxSize: 3000000,
                },
                {
                  type: 'file',
                  name: 'left_side_view',
                  title: 'รูปด้านข้างซ้าย',
                  startWithNewLine: false,
                  hideNumber: true,
                  needConfirmRemoveFile: true,
                  isRequired: true,
                  storeDataAsText: false,
                  sourceType: 'file-camera',
                  acceptedTypes: 'image/png, image/jpeg',
                  maxSize: 3000000,
                },
                {
                  type: 'file',
                  name: 'right_side_view',
                  title: 'รูปด้านข้างขวา',
                  startWithNewLine: false,
                  hideNumber: true,
                  needConfirmRemoveFile: true,
                  isRequired: true,
                  storeDataAsText: false,
                  sourceType: 'file-camera',
                  acceptedTypes: 'image/png, image/jpeg',
                  maxSize: 3000000,
                },
                {
                  type: 'file',
                  name: 'back_view',
                  title: 'รูปท้ายเครื่อง',
                  hideNumber: true,
                  needConfirmRemoveFile: true,
                  isRequired: true,
                  storeDataAsText: false,
                  sourceType: 'file-camera',
                  acceptedTypes: 'image/png, image/jpeg',
                  maxSize: 3000000,
                },
                {
                  type: 'file',
                  name: 'upper_left_angle',
                  title: 'รูปมุมบนซ้าย',
                  startWithNewLine: false,
                  hideNumber: true,
                  needConfirmRemoveFile: true,
                  isRequired: true,
                  storeDataAsText: false,
                  sourceType: 'file-camera',
                  acceptedTypes: 'image/png, image/jpeg',
                  maxSize: 3000000,
                },
                {
                  type: 'file',
                  name: 'upper_right_angle',
                  title: 'รูปมุมบนขวา',
                  startWithNewLine: false,
                  hideNumber: true,
                  needConfirmRemoveFile: true,
                  isRequired: true,
                  storeDataAsText: false,
                  sourceType: 'file-camera',
                  acceptedTypes: 'image/png, image/jpeg',
                  maxSize: 3000000,
                },
                {
                  type: 'file',
                  name: 'lower_left_angle',
                  title: 'รูปมุมล่างซ้าย',
                  startWithNewLine: false,
                  hideNumber: true,
                  needConfirmRemoveFile: true,
                  isRequired: true,
                  storeDataAsText: false,
                  sourceType: 'file-camera',
                  acceptedTypes: 'image/png, image/jpeg',
                  maxSize: 3000000,
                },
                {
                  type: 'file',
                  name: 'lower_right_angle',
                  title: 'รูปมุมล่างขวา',
                  hideNumber: true,
                  needConfirmRemoveFile: true,
                  isRequired: true,
                  storeDataAsText: false,
                  sourceType: 'file-camera',
                  acceptedTypes: 'image/png, image/jpeg',
                  maxSize: 3000000,
                },
                {
                  type: 'file',
                  name: 'device_information',
                  title: 'Device Information',
                  startWithNewLine: false,
                  hideNumber: true,
                  needConfirmRemoveFile: true,
                  isRequired: true,
                  storeDataAsText: false,
                  sourceType: 'file-camera',
                  acceptedTypes: 'image/png, image/jpeg',
                  maxSize: 3000000,
                  tooltip: {
                    html: "<img style='max-height:221px; max-width:351px; margin: auto;' src='https://{host}/asset/company/WW/icons-remobie/ic-device-info.png'/><div style='font-family: IBM Plex Sans Thai;'><div style='font-size: 18px; line-height: 28px; font-weight: 600;'>Device Information</div><br/><div style='font-size: 14px; font-weight: 400;'>วิธีการ > กด *#06# จากแป้นโทรศัพท์ (ถ้าเครื่องปลอม หรือดัดแปลงจะกดไม่ได้)</div></div>",
                  },
                },
                {
                  type: 'file',
                  name: 'about_screen',
                  title: 'รูป About',
                  startWithNewLine: false,
                  hideNumber: true,
                  needConfirmRemoveFile: true,
                  isRequired: true,
                  storeDataAsText: false,
                  sourceType: 'file-camera',
                  acceptedTypes: 'image/png, image/jpeg',
                  maxSize: 3000000,
                  tooltip: {
                    html: "<div><img style='max-height:221px; max-width:351px; margin: auto;' src='https://{host}/asset/company/WW/icons-remobie/ic-about.png'/><div style='font-size: 18px; line-height: 28px; font-weight: 600;'>About</div><br/><ul style='font-size: 14px; line-height: 20px; font-weight: 400; list-style-position: outside; list-style-type: disc; padding-left:20px;'><li>IOS: วิธีการ > เข้าเมนู การตั้งค่า > ทั่วไป > เกี่ยวกับ</li><li>Android : วิธีการ > เข้าเมนู การตั้งค่า > เกี่ยวกับ โทรศัพท์</li></ul></div>",
                  },
                },
              ],
            },
            {
              type: 'panel',
              name: 'product_images_tablet',
              visibleIf: '{deviceSize} == tablet',
              elements: [
                {
                  type: 'file',
                  name: 'imei_number',
                  title: 'รูปเลข IMEI',
                  hideNumber: true,
                  needConfirmRemoveFile: true,
                  isRequired: true,
                  storeDataAsText: false,
                  sourceType: 'file',
                  acceptedTypes: 'image/png, image/jpeg',
                  maxSize: 3000000,
                },
                {
                  type: 'file',
                  name: 'rear_view',
                  title: 'รูปด้านหลังเครื่อง',
                  startWithNewLine: false,
                  hideNumber: true,
                  needConfirmRemoveFile: true,
                  isRequired: true,
                  storeDataAsText: false,
                  sourceType: 'file',
                  acceptedTypes: 'image/png, image/jpeg',
                  maxSize: 3000000,
                },
                {
                  type: 'file',
                  name: 'left_side_view',
                  title: 'รูปด้านข้างซ้าย',
                  startWithNewLine: false,
                  hideNumber: true,
                  needConfirmRemoveFile: true,
                  isRequired: true,
                  storeDataAsText: false,
                  sourceType: 'file',
                  acceptedTypes: 'image/png, image/jpeg',
                  maxSize: 3000000,
                },
                {
                  type: 'file',
                  name: 'right_side_view',
                  title: 'รูปด้านข้างขวา',
                  startWithNewLine: false,
                  hideNumber: true,
                  needConfirmRemoveFile: true,
                  isRequired: true,
                  storeDataAsText: false,
                  sourceType: 'file',
                  acceptedTypes: 'image/png, image/jpeg',
                  maxSize: 3000000,
                },
                {
                  type: 'file',
                  name: 'back_view',
                  title: 'รูปท้ายเครื่อง',
                  hideNumber: true,
                  needConfirmRemoveFile: true,
                  isRequired: true,
                  storeDataAsText: false,
                  sourceType: 'file',
                  acceptedTypes: 'image/png, image/jpeg',
                  maxSize: 3000000,
                },
                {
                  type: 'file',
                  name: 'upper_left_angle',
                  title: 'รูปมุมบนซ้าย',
                  startWithNewLine: false,
                  hideNumber: true,
                  needConfirmRemoveFile: true,
                  isRequired: true,
                  storeDataAsText: false,
                  sourceType: 'file',
                  acceptedTypes: 'image/png, image/jpeg',
                  maxSize: 3000000,
                },
                {
                  type: 'file',
                  name: 'upper_right_angle',
                  title: 'รูปมุมบนขวา',
                  startWithNewLine: false,
                  hideNumber: true,
                  needConfirmRemoveFile: true,
                  isRequired: true,
                  storeDataAsText: false,
                  sourceType: 'file',
                  acceptedTypes: 'image/png, image/jpeg',
                  maxSize: 3000000,
                },
                {
                  type: 'file',
                  name: 'lower_left_angle',
                  title: 'รูปมุมล่างซ้าย',
                  startWithNewLine: false,
                  hideNumber: true,
                  needConfirmRemoveFile: true,
                  isRequired: true,
                  storeDataAsText: false,
                  sourceType: 'file',
                  acceptedTypes: 'image/png, image/jpeg',
                  maxSize: 3000000,
                },
                {
                  type: 'file',
                  name: 'lower_right_angle',
                  title: 'รูปมุมล่างขวา',
                  hideNumber: true,
                  needConfirmRemoveFile: true,
                  isRequired: true,
                  storeDataAsText: false,
                  sourceType: 'file',
                  acceptedTypes: 'image/png, image/jpeg',
                  maxSize: 3000000,
                },
                {
                  type: 'file',
                  name: 'device_information',
                  title: 'Device Information',
                  startWithNewLine: false,
                  hideNumber: true,
                  needConfirmRemoveFile: true,
                  isRequired: true,
                  storeDataAsText: false,
                  sourceType: 'file',
                  acceptedTypes: 'image/png, image/jpeg',
                  maxSize: 3000000,
                  tooltip: {
                    html: "<img style='max-height:221px; max-width:351px; margin: auto;' src='https://{host}/asset/company/WW/icons-remobie/ic-device-info.png'/><div style='font-family: IBM Plex Sans Thai;'><div style='font-size: 18px; line-height: 28px; font-weight: 600;'>Device Information</div><br/><div style='font-size: 14px; font-weight: 400;'>วิธีการ > กด *#06# จากแป้นโทรศัพท์ (ถ้าเครื่องปลอม หรือดัดแปลงจะกดไม่ได้)</div></div>",
                  },
                },
                {
                  type: 'file',
                  name: 'about_screen',
                  title: 'รูป About',
                  startWithNewLine: false,
                  hideNumber: true,
                  needConfirmRemoveFile: true,
                  isRequired: true,
                  storeDataAsText: false,
                  sourceType: 'file',
                  acceptedTypes: 'image/png, image/jpeg',
                  maxSize: 3000000,
                  tooltip: {
                    html: "<div><img style='max-height:221px; max-width:351px; margin: auto;' src='https://{host}/asset/company/WW/icons-remobie/ic-about.png'/><div style='font-size: 18px; line-height: 28px; font-weight: 600;'>About</div><br/><ul style='font-size: 14px; line-height: 20px; font-weight: 400; list-style-position: outside; list-style-type: disc; padding-left:20px;'><li>IOS: วิธีการ > เข้าเมนู การตั้งค่า > ทั่วไป > เกี่ยวกับ</li><li>Android : วิธีการ > เข้าเมนู การตั้งค่า > เกี่ยวกับ โทรศัพท์</li></ul></div>",
                  },
                },
              ],
            },
          ],
        },
      ],
      questionDescriptionLocation: 'underInput',
    },
  },
  {
    slug: 'product_additional_information',
    title: 'ข้อมูลเพิ่มเติม',
    fillSpaceColumnCount: 1,
    survey_form: {
      logoPosition: 'right',
      pages: [
        {
          name: 'page1',
          elements: [
            {
              type: 'comment',
              name: 'additional_information',
              title: 'ข้อมูลเพิ่มเติม (ถ้ามี)',
              placeholder: 'กรุณากรอกข้อมูลเพิ่มเติม',
              hideNumber: true,
              maxLength: 3000,
            },
          ],
        },
      ],
      questionDescriptionLocation: 'underInput',
    },
  },
  {
    slug: 'product_additional_images',
    title: 'รูปภาพเพิ่มเติม',
    isAdditionalCheckList: true,
    survey_form: {
      logoPosition: 'right',
      pages: [
        {
          name: 'page1',
          elements: [
            {
              type: 'file',
              visibleIf: '{deviceSize} == desktop',
              allowMultiple: true,
              name: 'additional-image',
              title: 'รูปสินค้าเพิ่มเติม',
              startWithNewLine: false,
              hideNumber: true,
              needConfirmRemoveFile: true,
              storeDataAsText: false,
              sourceType: 'file-camera',
              acceptedTypes: 'image/png, image/jpeg',
              maxSize: 3000000,
            },
            {
              type: 'file',
              visibleIf: '{deviceSize} == tablet',
              allowMultiple: true,
              name: 'additional-image',
              title: 'รูปสินค้าเพิ่มเติม',
              startWithNewLine: false,
              hideNumber: true,
              needConfirmRemoveFile: true,
              storeDataAsText: false,
              sourceType: 'file',
              acceptedTypes: 'image/png, image/jpeg',
              maxSize: 3000000,
            },
          ],
        },
      ],
      questionDescriptionLocation: 'underInput',
    },
  },
];
