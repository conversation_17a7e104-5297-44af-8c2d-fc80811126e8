import { mockCompany } from './allocation-order';
import { mockCampaign } from './campaign';

export const mockCampaignRedemptionCodeList = [
  {
    campaignCode: 'campaign1',
    companyId: 'CompanyX',
    redemptionCode: 'redemptionCode1',
    supporter: 'supporter1',
    grade: ['A', 'B', 'C', 'D'],
    value: 1000,
    order: 1,
    jobId: null,
    createdBy: 'system',
  },
];

export const mockCampaignRedemptionCode = {
  campaignCode: 'campaign1',
  companyId: 'CompanyX',
  redemptionCode: 'redemptionCode1',
  supporter: 'supporter1',
  grade: ['A', 'B', 'C', 'D'],
  value: 1000,
  order: 1,
  jobId: null,
  createdBy: 'system',
};
export const mockCampaignRedemptionCodeHaveJobId = {
  campaignCode: 'campaign1',
  companyId: 'CompanyX',
  redemptionCode: 'redemptionCode1',
  supporter: 'supporter1',
  grade: ['A', 'B', 'C', 'D'],
  value: 1000,
  order: 1,
  jobId: 'jobId1',
  createdBy: 'system',
  company: mockCompany,
  campaign: mockCampaign,
  createdAt: new Date(),
};
