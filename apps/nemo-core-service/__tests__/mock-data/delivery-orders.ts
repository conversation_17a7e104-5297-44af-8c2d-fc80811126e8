import { DeliveryOrderStatus } from 'contracts';
import { DeliveryOrderEntity } from '../../src/entities';

export const mockDeliveryOrderEntity = {
  deliveryOrderId: 'validDoId',
  companyId: 'company',
  branchId: 'branchId',
  quantity: 10,
  status: DeliveryOrderStatus.APPOINTMENT_PENDING,
  updatedAt: new Date('2024-02-14T00:00:00.000Z'),
} as unknown as DeliveryOrderEntity;

export const mockInputDeliveryOrderEntity = {
  deliveryOrderId: 'validDoId',
  companyId: 'company',
  branchId: 'branchId',
  quantity: 10,
  status: DeliveryOrderStatus.APPOINTMENT_PENDING,
  updatedAt: new Date('2024-02-14T00:00:00.000Z'),
  appointmentDate: new Date('9999-01-01T00:00:00.000Z'),
  editConfirmAppointmentAt: new Date(),
  editConfirmAppointmentUserKey: 'userKey',
  awbNumber: '*********',
};

export const validPatchBody = {
  awbNumber: '*********',
  appointmentDate: '9999-01-01T00:00:00.000Z',
  lastUpdate: '2024-02-14T00:00:00.000Z',
};

export const validPatchDoId = 'validDoId';
