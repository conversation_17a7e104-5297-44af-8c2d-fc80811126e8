import { BranchEntity } from '../../src/entities';

export const mockBranchEntity = {
  companyId: 'WW',
  branchId: 'branchId#1',
  title: 'title#1',
} as BranchEntity;

export const mockBranchListEntity = [
  {
    companyId: 'WW',
    branchId: 'branchId#1',
    title: 'title#1',
  },
] as BranchEntity[];

export const mockGetBranchesRequest = {
  branchId: {
    request: {
      query: { branchId: '100000' },
      withUserContext: {
        name: 'test-name',
        userKey: 'test-user-key',
        companyId: 'ww',
      },
    },
    result: `r.branchId ILIKE '%100000%'`,
  },
  title: {
    request: {
      query: { title: 'test title' },
      withUserContext: {
        name: 'test-name',
        userKey: 'test-user-key',
        companyId: 'ww',
      },
    },
    result: `r.title ILIKE '%test title%'`,
  },
  branchType: {
    request: {
      query: { branchType: ['Shop'] },
      withUserContext: {
        name: 'test-name',
        userKey: 'test-user-key',
        companyId: 'ww',
      },
    },
    result: `r.branchType IN ('Shop')`,
  },
  label: {
    request: {
      query: { label: 'test-branch' },
      withUserContext: {
        name: 'test-name',
        userKey: 'test-user-key',
        companyId: 'ww',
      },
    },
    result: `(r.branchId ILIKE '%test-branch%' OR r.title ILIKE '%test-branch%')`,
  },
  labelAndBranch: {
    request: {
      query: { label: 'test-branch', branchType: ['Shop'] },
      withUserContext: {
        name: 'test-name',
        userKey: 'test-user-key',
        companyId: 'ww',
      },
    },
    result: `(r.branchId ILIKE '%test-branch%' OR r.title ILIKE '%test-branch%') AND r.branchType IN ('Shop')`,
  },
};

export const mockImporrtError = [
  {
    branchId: 'branchId1',
    title: 'title1',
    branchType: 'Shop',
    titleEn: 'titleEn',
    addressTh: 'ทดสอบที่อยู่',
    provinceTh: 'ทดสอบจังหวัด',
    districtTh: 'ทดสอบอำเภอ',
    subDistrictTh: 'ทดสอบตำบล',
    addressEn: 'test address',
    provinceEn: 'test province',
    districtEn: 'test district',
    subDistrictEn: 'test subdistrict',
    zipCode: '100000',
    latitude: 50.1111,
    longitude: 150.122,
  },
  {
    branchId: 'branchId2',
    title: 'title2',
    branchType: 'Shop',
    titleEn: 'titleEn',
    addressTh: 'ทดสอบที่อยู่',
    provinceTh: 'ทดสอบจังหวัด',
    districtTh: 'ทดสอบอำเภอ',
    subDistrictTh: 'ทดสอบตำบล',
    addressEn: 'test address',
    provinceEn: 'test province',
    districtEn: 'test district',
    subDistrictEn: 'test subdistrict',
    zipCode: '10000',
    latitude: 100.1111,
    longitude: 50.122,
  },
  {
    branchId: 'branchId3',
    title: 'title3',
    branchType: 'Shop',
    titleEn: 'titleEn',
    addressTh: 'ทดสอบที่อยู่',
    provinceTh: 'ทดสอบจังหวัด',
    districtTh: 'ทดสอบอำเภอ',
    subDistrictTh: 'ทดสอบตำบล',
    addressEn: 'test address',
    provinceEn: 'test province',
    districtEn: 'test district',
    subDistrictEn: 'test subdistrict',
    zipCode: '10000',
    latitude: 10.1111,
    longitude: 250.120992,
  },
  {
    branchId: 'branchId2',
    title: 'title2',
    branchType: 'Shop',
    titleEn: 'titleEn',
    addressTh: 'ทดสอบที่อยู่',
    provinceTh: 'ทดสอบจังหวัด',
    districtTh: 'ทดสอบอำเภอ',
    subDistrictTh: 'ทดสอบตำบล',
    addressEn: 'test address',
    provinceEn: 'test province',
    districtEn: 'test district',
    subDistrictEn: 'test subdistrict',
    zipCode: '10000',
    latitude: 100.11156456454561,
    longitude: 50.122,
  },
  {
    branchId: 'branchId3',
    title: 'title3',
    branchType: 'Shop',
    titleEn: 'titleEn',
    addressTh: 'ทดสอบที่อยู่',
    provinceTh: 'ทดสอบจังหวัด',
    districtTh: 'ทดสอบอำเภอ',
    subDistrictTh: 'ทดสอบตำบล',
    addressEn: 'test address',
    provinceEn: 'test province',
    districtEn: 'test district',
    subDistrictEn: 'test subdistrict',
    zipCode: '10000',
    latitude: 10.1111,
    longitude: 250.1204564564565992,
  },
] as BranchEntity[];
