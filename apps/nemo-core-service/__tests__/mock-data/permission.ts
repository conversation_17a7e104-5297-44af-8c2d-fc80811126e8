import { PermissionGroupEntity, SiteType } from '../../src/entities';

export const mockPermission = [
  {
    permissionGroupId: 'TEST-0001',
    createdAt: new Date(),
    updatedAt: new Date(),
    companyId: 'TEST-COMPANY',
    label: 'Label-G-1',
    sortIndex: 0,
    isInMenu: true,
    type: SiteType.CMS,
    permissions: [
      {
        permissionId: 'TEST-PERMISSION-0003',
        createdAt: new Date(),
        updatedAt: new Date(),
        companyId: 'TEST-COMPANY',
        label: 'Label-P-3',
        permissionGroupId: 'TEST-0001',
        sortIndex: 2,
        iconName: 'TEST-IC-3',
        view: true,
        create: false,
        update: false,
        delete: false,
        download: true,
        upload: false,
      },
      {
        permissionId: 'TEST-PERMISSION-0002',
        createdAt: new Date(),
        updatedAt: new Date(),
        companyId: 'TEST-COMPANY',
        label: 'Label-P-2',
        permissionGroupId: 'TEST-0001',
        sortIndex: 1,
        iconName: 'TEST-IC-2',
        view: true,
        create: false,
        update: false,
        delete: false,
        download: false,
        upload: true,
      },
      {
        permissionId: 'TEST-PERMISSION-0001',
        createdAt: new Date(),
        updatedAt: new Date(),
        companyId: 'TEST-COMPANY',
        label: 'Label-P-1',
        permissionGroupId: 'TEST-0001',
        sortIndex: 0,
        iconName: 'TEST-IC-1',
        view: true,
        create: true,
        update: true,
        delete: false,
        download: false,
        upload: false,
      },
    ],
  },
  {
    permissionGroupId: 'TEST-0002',
    createdAt: new Date(),
    updatedAt: new Date(),
    companyId: 'TEST-COMPANY',
    label: 'Label-G-2',
    sortIndex: 1,
    isInMenu: true,
    type: SiteType.CMS,
    permissions: [
      {
        permissionId: 'TEST-PERMISSION-0005',
        createdAt: new Date(),
        updatedAt: new Date(),
        companyId: 'TEST-COMPANY',
        label: 'Label-P-5',
        permissionGroupId: 'TEST-0002',
        sortIndex: 1,
        iconName: 'TEST-IC-5',
        view: true,
        create: false,
        update: true,
        delete: false,
        download: false,
        upload: false,
      },
      {
        permissionId: 'TEST-PERMISSION-0004',
        createdAt: new Date(),
        updatedAt: new Date(),
        companyId: 'TEST-COMPANY',
        label: 'Label-P-4',
        permissionGroupId: 'TEST-0002',
        sortIndex: 0,
        iconName: 'TEST-IC-4',
        view: true,
        create: false,
        update: true,
        delete: false,
        download: false,
        upload: false,
      },
    ],
  },
  {
    permissionGroupId: 'TEST-0003',
    createdAt: new Date(),
    updatedAt: new Date(),
    companyId: 'TEST-COMPANY',
    label: 'Label-G-3',
    sortIndex: 0,
    isInMenu: true,
    type: SiteType.FRONTSHOP,
    permissions: [
      {
        permissionId: 'TEST-PERMISSION-0006',
        createdAt: new Date(),
        updatedAt: new Date(),
        companyId: 'TEST-COMPANY',
        label: 'Label-P-6',
        permissionGroupId: 'TEST-0003',
        sortIndex: 1,
        iconName: 'TEST-IC-6',
        view: true,
        create: false,
        update: false,
        delete: false,
        download: true,
        upload: false,
      },
      {
        permissionId: 'TEST-PERMISSION-0007',
        createdAt: new Date(),
        updatedAt: new Date(),
        companyId: 'TEST-COMPANY',
        label: 'Label-P-7',
        permissionGroupId: 'TEST-0003',
        sortIndex: 0,
        iconName: 'TEST-IC-7',
        view: true,
        create: true,
        update: true,
        delete: false,
        download: false,
        upload: false,
      },
    ],
  },
  {
    permissionGroupId: 'TEST-0004',
    createdAt: new Date(),
    updatedAt: new Date(),
    companyId: 'TEST-COMPANY',
    label: 'Label-G-4',
    sortIndex: 1,
    isInMenu: true,
    type: SiteType.FRONTSHOP,
    permissions: [
      {
        permissionId: 'TEST-PERMISSION-0008',
        createdAt: new Date(),
        updatedAt: new Date(),
        companyId: 'TEST-COMPANY',
        label: 'Label-P-8',
        permissionGroupId: 'TEST-0004',
        sortIndex: 1,
        iconName: 'TEST-IC-8',
        view: true,
        create: true,
        update: true,
        delete: false,
        download: false,
        upload: false,
      },
      {
        permissionId: 'TEST-PERMISSION-0009',
        createdAt: new Date(),
        updatedAt: new Date(),
        companyId: 'TEST-COMPANY',
        label: 'Label-P-9',
        permissionGroupId: 'TEST-0004',
        sortIndex: 0,
        iconName: 'TEST-IC-3',
        view: true,
        create: false,
        update: false,
        delete: false,
        download: false,
        upload: false,
      },
    ],
  },
  {
    permissionGroupId: 'TEST-9999',
    createdAt: new Date(),
    updatedAt: new Date(),
    companyId: 'TEST-COMPANY',
    label: 'Label-G-9999',
    sortIndex: 1,
    isInMenu: true,
    type: 'TEST',
    permissions: [
      {
        permissionId: 'TEST-PERMISSION-9999',
        createdAt: new Date(),
        updatedAt: new Date(),
        companyId: 'TEST-COMPANY',
        label: 'Label-P-9999',
        permissionGroupId: 'TEST-9999',
        sortIndex: 1,
        iconName: 'TEST-IC-9999',
        view: true,
        create: true,
        update: true,
        delete: false,
        download: false,
        upload: false,
      },
    ],
  },
] as PermissionGroupEntity[];
