import { IConvertToType } from '../../src/excel/excel-manager.service';

export const mockMasterFunctionQueryResult = [
  {
    modelKey: '1',
    aggFunctionKeyCond: 'condition1,condition2,condition3',
    aggPenalties: '-100,-200,-300',
    modelIdentifiers: { rom: '512GB', brand: 'A1', model: 'Phone1' },
    ownerName: 'WW',
  },
  {
    modelKey: '2',
    aggFunctionKeyCond: 'condition1,condition2,condition3',
    aggPenalties: '-200,-400,-600',
    modelIdentifiers: { rom: '256GB', brand: 'A2', model: 'Phone2' },
    ownerName: 'WW',
  },
  {
    modelKey: '3',
    aggFunctionKeyCond: 'condition1,condition2,condition3',
    aggPenalties: '-300,-600,-900',
    modelIdentifiers: { rom: '1TB', brand: 'A3', model: 'Phone3' },
    ownerName: 'WW',
  },
];

export const mockAfterLoadResult = [
  {
    id: '1',
    modelIdentifiers: { rom: '512GB', brand: 'A1', model: 'Phone1' },
    ownerName: 'WW',
    penalty: {
      condition1: '-100',
      condition2: '-200',
      condition3: '-300',
    },
  },
  {
    id: '2',
    modelIdentifiers: { rom: '256GB', brand: 'A2', model: 'Phone2' },
    ownerName: 'WW',
    penalty: {
      condition1: '-200',
      condition2: '-400',
      condition3: '-600',
    },
  },
  {
    id: '3',
    modelIdentifiers: { rom: '1TB', brand: 'A3', model: 'Phone3' },
    ownerName: 'WW',
    penalty: {
      condition1: '-300',
      condition2: '-600',
      condition3: '-900',
    },
  },
];

export const mockAggFunctionKeyCond = 'condition1,condition2,condition3';
export const mockAggPenalties = '-100,-200,-300';
export const mockMapArrayResult = {
  condition1: '-100',
  condition2: '-200',
  condition3: '-300',
};

export const count = {
  count: '3',
};

export const mockOptionsHeader = {
  headers: {
    PRODUCT_ID: {
      keyName: 'modelKey',
      type: IConvertToType.string,
      isRequired: true,
      subHeader: 'ID',
    },
    CONDITION1: {
      keyName: 'condition1',
      type: IConvertToType.numString,
      isRequired: true,
      subHeader: 'ID',
    },
    CONDITION2: {
      keyName: 'condition2',
      type: IConvertToType.numString,
      isRequired: true,
      subHeader: 'ราคาเครื่อง',
    },
    CONDITION3: {
      keyName: 'condition3',
      type: IConvertToType.numString,
      isRequired: true,
      subHeader: '% รับซื้อ',
    },
  },
};

export const mockDictAllData = [
  { modelKey: '1', functionKeyCond: 'condition1' },
  { modelKey: '1', functionKeyCond: 'condition2' },
  { modelKey: '2', functionKeyCond: 'condition1' },
  { modelKey: '2', functionKeyCond: 'condition2' },
  { modelKey: '3', functionKeyCond: 'condition1' },
  { modelKey: '3', functionKeyCond: 'condition2' },
];

export const mockModelMasterFunction = [
  {
    companyId: 'WW',
    modelKey: 'apple|iphone 14|128gb',
    functionKeyCond: 'product_information.country_of_purchase=th',
    penalties: '-3000',
    checkListId: '2475E0GE4AH0J201',
    createdBy: 'AXN0007',
    updatedBy: 'AXN0007',
    modelChecklist: {
      id: '2475E0GE4AH0J201',
      companyId: 'WW',
      functionKey: 'country_of_purchase',
      functionSection: 'product_information',
      isRequired: true,
      checklistType: 'QUESTION',
      checklistNameTh: 'ประเทศที่ซื้อโทรศัพท์',
      checklistNameEn: 'Purchase Country',
      checklistDescriptionTh: 'ประเทศร้านที่ออกจำหน่ายเครื่อง',
      checklistDescriptionEn: 'The country that sells this product.',
      tooltipTh: null,
      tooltipEn: null,
      placeholderTh: null,
      placeholderEn: null,
      errorTextTh: null,
      errorTextEn: null,
      iconImageUrl: null,
      moduleCode: null,
      questionType: 'SELECTION',
      questionChoices: [
        {
          id: 'th',
          answerEn: 'Thai',
          answerTh: 'เครื่องไทย',
          iconImageUrl:
            'https://ww-remobile.axonstech.com/asset/company/WW/icons/country_of_purchase-thai.png',
        },
        {
          id: 'etc',
          answerEn: 'Foreign device',
          answerTh: 'เครื่องนอก',
          iconImageUrl:
            'https://ww-remobile.axonstech.com/asset/company/WW/icons/country_of_purchase-etc.png',
        },
      ],
    },
  },
  {
    companyId: 'WW',
    modelKey: 'apple|iphone 14|128gb',
    functionKeyCond: 'product_information.accessories_check=select',
    penalties: '-1000.00',
    checkListId: '2475E0GE5HT8W611',
    createdBy: 'AXN0007',
    updatedBy: 'AXN0007',
    modelChecklist: {
      id: '2475E0GE5HT8W611',
      companyId: 'WW',
      functionKey: 'accessories_check',
      functionSection: 'product_information',
      isRequired: true,
      checklistType: 'QUESTION',
      checklistNameTh: 'อุปกรณ์เสริมที่คุณไม่มี',
      checklistNameEn: 'Accessories Check',
      checklistDescriptionTh: 'โปรดเลือกเฉพาะรายการที่คุณไม่มี',
      checklistDescriptionEn: 'Please select accessories that not existed.',
      tooltipTh: null,
      tooltipEn: null,
      placeholderTh: null,
      placeholderEn: null,
      errorTextTh: null,
      errorTextEn: null,
      iconImageUrl: null,
      moduleCode: null,
      questionType: 'OPTION',
      questionChoices: [
        {
          id: 'box',
          answerEn: 'Box Case',
          answerTh: 'กล่อง',
          iconImageUrl:
            'https://ww-remobile.axonstech.com/asset/company/WW/icons/additional_accessories-complete.png',
        },
        {
          id: 'headphone',
          answerEn: 'Headphone',
          answerTh: 'หูฟัง',
          iconImageUrl:
            'https://ww-remobile.axonstech.com/asset/company/WW/icons/additional_accessories-complete.png',
        },
        {
          id: 'charging_cable',
          answerEn: 'Charging cable',
          answerTh: 'สายชาร์จ',
          iconImageUrl:
            'https://ww-remobile.axonstech.com/asset/company/WW/icons/additional_accessories-complete.png',
        },
        {
          id: 'charger_head',
          answerEn: 'Charging head ',
          answerTh: 'หัวชาร์จ',
          iconImageUrl:
            'https://ww-remobile.axonstech.com/asset/company/WW/icons/additional_accessories-complete.png',
        },
      ],
    },
  },
  {
    companyId: 'WW',
    modelKey: 'apple|iphone 14|128gb',
    functionKeyCond: 'product_information.accessories_check=skip',
    penalties: '-1000.00',
    checkListId: '2475E0GE5HT8W611',
    createdBy: 'AXN0007',
    updatedBy: 'AXN0007',
    modelChecklist: {
      id: '2475E0GE5HT8W611',
      companyId: 'WW',
      functionKey: 'accessories_check',
      functionSection: 'product_information',
      isRequired: true,
      checklistType: 'QUESTION',
      checklistNameTh: 'อุปกรณ์เสริมที่คุณไม่มี',
      checklistNameEn: 'Accessories Check',
      checklistDescriptionTh: 'โปรดเลือกเฉพาะรายการที่คุณไม่มี',
      checklistDescriptionEn: 'Please select accessories that not existed.',
      tooltipTh: null,
      tooltipEn: null,
      placeholderTh: null,
      placeholderEn: null,
      errorTextTh: null,
      errorTextEn: null,
      iconImageUrl: null,
      moduleCode: null,
      questionType: 'OPTION',
      questionChoices: [
        {
          id: 'box',
          answerEn: 'Box Case',
          answerTh: 'กล่อง',
          iconImageUrl:
            'https://ww-remobile.axonstech.com/asset/company/WW/icons/additional_accessories-complete.png',
        },
        {
          id: 'headphone',
          answerEn: 'Headphone',
          answerTh: 'หูฟัง',
          iconImageUrl:
            'https://ww-remobile.axonstech.com/asset/company/WW/icons/additional_accessories-complete.png',
        },
        {
          id: 'charging_cable',
          answerEn: 'Charging cable',
          answerTh: 'สายชาร์จ',
          iconImageUrl:
            'https://ww-remobile.axonstech.com/asset/company/WW/icons/additional_accessories-complete.png',
        },
        {
          id: 'charger_head',
          answerEn: 'Charging head ',
          answerTh: 'หัวชาร์จ',
          iconImageUrl:
            'https://ww-remobile.axonstech.com/asset/company/WW/icons/additional_accessories-complete.png',
        },
      ],
    },
  },
  {
    companyId: 'WW',
    modelKey: 'apple|iphone 14|128gb',
    functionKeyCond: 'product_information.accessories_check=not_select',
    penalties: '0',
    checkListId: '2475E0GE5HT8W611',
    createdBy: 'AXN0007',
    updatedBy: 'AXN0007',
    modelChecklist: {
      id: '2475E0GE5HT8W611',
      companyId: 'WW',
      functionKey: 'accessories_check',
      functionSection: 'product_information',
      isRequired: true,
      checklistType: 'QUESTION',
      checklistNameTh: 'อุปกรณ์เสริมที่คุณไม่มี',
      checklistNameEn: 'Accessories Check',
      checklistDescriptionTh: 'โปรดเลือกเฉพาะรายการที่คุณไม่มี',
      checklistDescriptionEn: 'Please select accessories that not existed.',
      tooltipTh: null,
      tooltipEn: null,
      placeholderTh: null,
      placeholderEn: null,
      errorTextTh: null,
      errorTextEn: null,
      iconImageUrl: null,
      moduleCode: null,
      questionType: 'OPTION',
      questionChoices: [
        {
          id: 'box',
          answerEn: 'Box Case',
          answerTh: 'กล่อง',
          iconImageUrl:
            'https://ww-remobile.axonstech.com/asset/company/WW/icons/additional_accessories-complete.png',
        },
        {
          id: 'headphone',
          answerEn: 'Headphone',
          answerTh: 'หูฟัง',
          iconImageUrl:
            'https://ww-remobile.axonstech.com/asset/company/WW/icons/additional_accessories-complete.png',
        },
        {
          id: 'charging_cable',
          answerEn: 'Charging cable',
          answerTh: 'สายชาร์จ',
          iconImageUrl:
            'https://ww-remobile.axonstech.com/asset/company/WW/icons/additional_accessories-complete.png',
        },
        {
          id: 'charger_head',
          answerEn: 'Charging head ',
          answerTh: 'หัวชาร์จ',
          iconImageUrl:
            'https://ww-remobile.axonstech.com/asset/company/WW/icons/additional_accessories-complete.png',
        },
      ],
    },
  },
  {
    companyId: 'WW',
    modelKey: 'apple|iphone 14|128gb',
    functionKeyCond: 'product_information.country_of_purchase=etc',
    penalties: '-3000',
    checkListId: '2475E0GE4AH0J201',
    createdBy: 'system',
    updatedBy: 'AXN0007',
    modelChecklist: {
      id: '2475E0GE4AH0J201',
      companyId: 'WW',
      functionKey: 'country_of_purchase',
      functionSection: 'product_information',
      isRequired: true,
      checklistType: 'QUESTION',
      checklistNameTh: 'ประเทศที่ซื้อโทรศัพท์',
      checklistNameEn: 'Purchase Country',
      checklistDescriptionTh: 'ประเทศร้านที่ออกจำหน่ายเครื่อง',
      checklistDescriptionEn: 'The country that sells this product.',
      tooltipTh: null,
      tooltipEn: null,
      placeholderTh: null,
      placeholderEn: null,
      errorTextTh: null,
      errorTextEn: null,
      iconImageUrl: null,
      moduleCode: null,
      questionType: 'SELECTION',
      questionChoices: [
        {
          id: 'th',
          answerEn: 'Thai',
          answerTh: 'เครื่องไทย',
          iconImageUrl:
            'https://ww-remobile.axonstech.com/asset/company/WW/icons/country_of_purchase-thai.png',
        },
        {
          id: 'etc',
          answerEn: 'Foreign device',
          answerTh: 'เครื่องนอก',
          iconImageUrl:
            'https://ww-remobile.axonstech.com/asset/company/WW/icons/country_of_purchase-etc.png',
        },
      ],
    },
  },
];

// upload mock data

export const mockcheckListFunction = [
  {
    id: '249HD1VNCOCFD1U03',
    createdAt: '2024-09-17T06:31:48.239Z',
    updatedAt: '2024-09-17T06:31:48.239Z',
    companyId: 'WW',
    functionKey: 'bluetooth',
    functionSection: 'remobie_check_list',
    isRequired: true,
    checklistType: 'MODULE',
    checklistNameTh: 'Bluetooth',
    checklistNameEn: 'Bluetooth',
    checklistDescriptionTh: null,
    checklistDescriptionEn: null,
    tooltipTh: null,
    tooltipEn: null,
    placeholderTh: null,
    placeholderEn: null,
    errorTextTh: null,
    errorTextEn: null,
    iconImageUrl:
      'https://ww-remobile.axonstech.com/asset/company/WW/icons/bluetooth.svg',
    moduleCode: 'BLUETOOTH',
    questionType: null,
    questionChoices: null,
    isIncludeVideo: false,
    tooltip: null,
    popup: null,
  },
  {
    id: '249HD1VNMB3MKEEB3',
    createdAt: '2024-09-17T06:31:48.239Z',
    updatedAt: '2024-09-17T06:31:48.239Z',
    companyId: 'WW',
    functionKey: 'country_of_purchase',
    functionSection: 'product_information',
    isRequired: false,
    checklistType: 'QUESTION',
    checklistNameTh: 'ประเทศที่ซื้อโทรศัพท์',
    checklistNameEn: 'Purchase Country',
    checklistDescriptionTh: 'ประเทศร้านที่ออกจำหน่ายเครื่อง',
    checklistDescriptionEn: 'The country that sells this product.',
    tooltipTh: null,
    tooltipEn: null,
    placeholderTh: null,
    placeholderEn: null,
    errorTextTh: null,
    errorTextEn: null,
    iconImageUrl:
      'https://ww-remobile.axonstech.com/asset/company/WW/icons/country_of_purchase-thai.png',
    moduleCode: null,
    questionType: 'SELECTION',
    questionChoices: [Array],
    isIncludeVideo: false,
    tooltip: null,
    popup: null,
  },
];

export const mockfileData = [
  {
    PRODUCT_ID: 'apple|iphone 11 pro|256gb',
    'remobie_check_list.bluetooth=functional': 0,
    'remobie_check_list.bluetooth=non_functional': -1000,
    'remobie_check_list.bluetooth=skip': 0,
    'product_information.country_of_purchase=th': 1000,
    'product_information.country_of_purchase=etc': 0,
  },
];

export const mockresultData = [
  {
    companyId: 'WW',
    modelKey: 'apple|iphone 11 pro|256gb',
    functionKeyCond: 'remobie_check_list.bluetooth=functional',
    penalties: '0',
    updatedBy: 'AXN0030',
    updatedAt: new Date(),
    createdBy: 'AXN0030',
    checkListId: '249HD1VNCOCFD1U03',
  },
  {
    companyId: 'WW',
    modelKey: 'apple|iphone 11 pro|256gb',
    functionKeyCond: 'remobie_check_list.bluetooth=non_functional',
    penalties: '-1000',
    updatedBy: 'AXN0030',
    updatedAt: new Date(),
    createdBy: 'AXN0030',
    checkListId: '249HD1VNCOCFD1U03',
  },
  {
    companyId: 'WW',
    modelKey: 'apple|iphone 11 pro|256gb',
    functionKeyCond: 'remobie_check_list.bluetooth=skip',
    penalties: '0',
    updatedBy: 'AXN0030',
    updatedAt: new Date(),
    createdBy: 'AXN0030',
    checkListId: '249HD1VNCOCFD1U03',
  },
  {
    companyId: 'WW',
    modelKey: 'apple|iphone 11 pro|256gb',
    functionKeyCond: 'product_information.country_of_purchase=th',
    penalties: '1000',
    updatedBy: 'AXN0030',
    updatedAt: new Date(),
    createdBy: 'AXN0030',
    checkListId: '249HD1VNMB3MKEEB3',
  },
  {
    companyId: 'WW',
    modelKey: 'apple|iphone 11 pro|256gb',
    functionKeyCond: 'product_information.country_of_purchase=etc',
    penalties: '0',
    updatedBy: 'AXN0030',
    updatedAt: new Date(),
    createdBy: 'AXN0030',
    checkListId: '249HD1VNMB3MKEEB3',
  },
  {
    companyId: 'WW',
    modelKey: 'apple|iphone 11 pro|256gb',
    functionKeyCond: 'product_information.country_of_purchase=skip',
    penalties: '0',
    updatedBy: 'AXN0030',
    updatedAt: new Date(),
    createdBy: 'AXN0030',
    checkListId: '249HD1VNMB3MKEEB3',
  },
];
