export enum QuestionType {
  SELECTION = 'SELECTION',
  OPTION = 'OPTION',
  TEXT = 'FREETEXT',
}

export interface GetModelMasterFunctionRequest {
  pagination?: string
  orderBy?: string
  page?: string
  pageSize?: string
}

export interface GetModelMasterFunctionResponse extends ColumnName {
  items: ModelMasterFunctionAdjustResult[]
}

export interface ColumnName {
  column: string[]
}

export interface ModelMasterFunctionAdjustResult {
  modelIdentifiers: ModelIdentifiers
  penalty: { [key: string]: string }
  id: string
  ownerName: string
}

export interface ModelMasterFunctionQueryResult {
  id: string
  modelKey: string
  aggFunctionKeyCond: string
  aggPenalties: string
  modelIdentifiers: ModelIdentifiers
  ownerName: string
}

export interface ModelIdentifiers {
  brand: string
  model: string
  rom: string
}

export interface ModelMasterFunctionByModelKeyResult {
  checkListId: string
  companyId: string
  modelKey: string
  functionKey: string
  functionSection: string
  checklistType: string
  checklistNameTh: string
  checklistNameEn: string
  functionalPenalties: string
  functionalKeyCond: string
  nonFunctionalPenalties: string
  nonFunctionalKeyCond: string
  skipPenalties: string
  skipKeyCond: string
  checked: boolean
}

export interface ModelMasterQuestionByModelKeyResult {
  checkListId: string
  companyId: string
  modelKey: string
  functionKey: string
  functionSection: string
  checklistType: string
  checklistNameTh: string
  checklistNameEn: string
  questionType?: QuestionType | null
  questionChoices: QuestionChoices[]
  checked: boolean
  skipPenalties: string
  skipKeyCond: string
}

export interface QuestionChoices {
  id: string
  answerEn: string
  answerTh: string
  penalties: string
  keyCond: string
}
