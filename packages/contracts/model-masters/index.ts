export interface GetModelMastersQuery {
  brand?: string
  model?: string
  rom?: string
  modelKey?: string
}

export interface GetModelMasterQuery {
  brand: string
  model: string
  rom: string
}

export interface GetModelMastersParam {
  key: string
}

export interface ModelMasterGradeDetailResponse {
  grade: string
  purchasePrice: string
  lastPurchasedPrice: string
  lastPurchasedOn: string
}

export interface ModelMasterResponse {
  companyId: string
  modelKey: string
  modelIdentifiers: any
  templateId: string
  modelMasterGrades: ModelMasterGradeDetailResponse[]
  lastPurchasedPrice?: number
  createdBy: string
  updatedBy?: string
}
export interface ModelMastersResponse {
  key: string
  label: string
}

export interface GetModelMastersRequestAdmin {
  orderBy?: string
  page?: string
  pageSize?: string
}

export interface GetModelMastersTableResponse {
  createdAt?: Date
  updatedAt?: Date
  companyId?: string
  modelKey?: string
  //modelIdentifiers?: any
  //templateId?: string
  //modelMasterGrades?: any[]
  //modelExportDetails?: any
  createdBy?: string
  updatedBy?: string
  matCode?: string
  referencePrice?: number
  modelYear?: string
  purchasedRatio?: number
  brand?: string
  model?: string
  rom?: string
  gradeA?: string
  gradeB?: string
  gradeC?: string
  gradeD?: string
  matCodeSale?: string
  insuranceCost?: number
  averageRetailCost?: any
  averageWholeSaleCost?: any
  ownerName?: string
}
export interface AvgCostTable {
  modelKey?: string
  matCodeSale?: string
  brand?: string
  model?: string
  rom?: string
  insuranceCost?: number
  avgRetailAA?: number
  avgWholesaleAA?: number
  avgRetailBB?: number
  avgWholesaleBB?: number
  avgRetailCC?: number
  avgWholesaleCC?: number
  avgRetailAD?: number
  avgWholesaleAD?: number
  avgRetailBD?: number
  avgWholesaleBD?: number
  avgRetailCD?: number
  avgWholesaleCD?: number
  avgRetailDD?: number
  avgWholesaleDD?: number
}

export interface GetModelMastersExport {
  modelKey?: string
  matCode?: string
  brand?: string
  model?: string
  rom?: string
  referencePrice?: number
  modelYear?: string
  purchasedRatio?: number
  gradeA?: string
  gradeB?: string
  gradeC?: string
  gradeD?: string
}
